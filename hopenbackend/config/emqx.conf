# EMQX v5.4.1 Configuration for Hopen Backend
# Enhanced with best practices for social app production deployment

# Node configuration
node {
  name = "emqx@10.0.0.81"
  cookie = "hopen-emqx-secure-cookie-2024"
  data_dir = "data"
}

# Dashboard configuration
dashboard {
  default_username = "admin"
  default_password = "hopen123"
  listeners {
    http {
      bind = 18083
    }
  }
}

# MQTT Listeners
listeners {
  tcp {
    default {
      bind = "0.0.0.0:1883"
      max_connections = 10000
    }
  }
  
  ssl {
    default {
      bind = "0.0.0.0:8883"
      max_connections = 10000
      ssl_options {
        keyfile = "/etc/ssl/private/emqx.key"
        certfile = "/etc/ssl/certs/emqx.crt"
        cacertfile = "/etc/ssl/certs/ca.crt"
        verify = verify_peer
        fail_if_no_peer_cert = false
        versions = ["tlsv1.3", "tlsv1.2"]
      }
    }
  }
}

# Cluster configuration
cluster {
  name = "hopen_cluster"
  discovery_strategy = "manual"
  core_nodes = ["emqx@10.0.0.81"]
}

# HTTP Authentication Hook for backend validation
auth.http {
  enable = true
  auth_req {
    url = "http://backend:4000/api/v1/auth/mqtt"
    method = "POST"
    headers {
      "Content-Type" = "application/json"
      "Accept" = "application/json"
    }
    body {
      username = "${username}"
      password = "${password}"
      clientid = "${clientid}"
      topic = "${topic}"
      action = "${action}"
    }
    request_timeout = "5s"
    connect_timeout = "5s"
    pool_size = 8
  }
  
  super_req {
    enable = false
  }
  
  acl_req {
    enable = false
  }
}

# Authorization
authorization {
  sources.1 {
    type = "built_in_database"
    enable = true
  }
}

# Enhanced logging
log {
  console_handler {
    enable = true
    level = "info"
    formatter = "text"
  }
  
  file_handlers {
    default {
      enable = true
      level = "info"
      formatter = "json"
      path = "log/emqx.log"
      rotation {
        enable = true
        count = 10
        max_size = "50MB"
      }
    }
    
    error {
      enable = true
      level = "error"
      formatter = "json"
      path = "log/emqx_error.log"
      rotation {
        enable = true
        count = 5
        max_size = "100MB"
      }
    }
  }
}

# Prometheus metrics
prometheus {
  enable = true
  interval = 15s
}
