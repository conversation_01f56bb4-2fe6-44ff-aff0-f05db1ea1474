# TLS Configuration for Development
tls:
  enabled: true
  cert_file: "/app/certs/server.crt"
  key_file: "/app/certs/server.key"
  domain: "hopen.local"
  
  # HTTP/3 (QUIC) Configuration
  http3:
    enabled: true
    port: 8443
    
  # HTTP/2 Configuration  
  http2:
    enabled: true
    port: 8443
    
  # Security settings
  min_version: "1.2"
  max_version: "1.3"
  
  # Cipher suites optimized for HTTP/3
  cipher_suites:
    - "TLS_AES_128_GCM_SHA256"
    - "TLS_AES_256_GCM_SHA384"
    - "TLS_CHACHA20_POLY1305_SHA256"
    
  # QUIC specific settings
  quic:
    max_idle_timeout: "30s"
    max_receive_stream_flow_control_window: 6291456
    max_receive_connection_flow_control_window: 15728640
    allow_connection_migration: true
    keep_alive_period: "10s"
