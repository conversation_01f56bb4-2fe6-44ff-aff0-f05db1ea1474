dsn: *****************************************/hopen?sslmode=disable
log:
  level: info
  format: text
serve:
  public:
    base_url: http://*********:4433/
    cors:
      enabled: true
  admin:
    base_url: http://*********:4434/
selfservice:
  default_browser_return_url: http://localhost:3000/
  methods:
    password:
      enabled: true
  flows:
    error:
      ui_url: http://localhost:3000/error
    settings:
      ui_url: http://localhost:3000/settings
    verification:
      ui_url: http://localhost:3000/verify
      enabled: false
    login:
      ui_url: http://localhost:3000/login
    registration:
      ui_url: http://localhost:3000/register
    recovery:
      ui_url: http://localhost:3000/recovery
      enabled: false
identity:
  default_schema_id: default
  schemas:
    - id: default
      url: file:///etc/config/identity.default.schema.json
courier:
  smtp:
    connection_uri: smtp://mail.example.com:25/?skip_ssl_verify=true 