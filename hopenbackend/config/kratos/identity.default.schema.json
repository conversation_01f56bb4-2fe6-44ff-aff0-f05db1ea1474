{"$id": "https://schemas.hopen.app/identity.default.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "Default Identity Schema", "type": "object", "properties": {"traits": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}}}, "username": {"type": "string", "minLength": 3, "maxLength": 64, "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}}}, "first_name": {"type": "string", "minLength": 1, "maxLength": 100}, "last_name": {"type": "string", "minLength": 1, "maxLength": 100}}, "required": ["email"], "additionalProperties": false}}, "required": ["traits"], "additionalProperties": false}