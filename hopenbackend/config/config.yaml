# Database Configuration
database:
  host: ${DB_HOST:localhost}
  port: ${DB_PORT:5432}
  name: ${DB_NAME:hopen}
  user: ${DB_USER:postgres}
  password: ${DB_PASSWORD:}
  ssl_mode: ${DB_SSL_MODE:disable}

# Valkey Configuration (NOT Redis)
valkey:
  host: ${VALKEY_HOST:localhost}
  port: ${VALKEY_PORT:6379}
  address: ${VALKEY_ADDRESS:localhost:6379}
  password: ${VALKEY_PASSWORD:}
  database: ${VALKEY_DATABASE:0}
  pool_size: ${VALKEY_POOL_SIZE:10}
  min_idle_connections: ${VALKEY_MIN_IDLE:5}
  max_retries: ${VALKEY_MAX_RETRIES:3}
  dial_timeout: ${VALKEY_DIAL_TIMEOUT:5s}
  read_timeout: ${VALKEY_READ_TIMEOUT:3s}
  write_timeout: ${VALKEY_WRITE_TIMEOUT:3s}

# Server Configuration
server:
  host: ${SERVER_HOST:0.0.0.0}
  port: ${SERVER_PORT:8080}
  mode: ${GIN_MODE:debug}

# Logging
logging:
  level: ${LOG_LEVEL:info}
  format: ${LOG_FORMAT:json}