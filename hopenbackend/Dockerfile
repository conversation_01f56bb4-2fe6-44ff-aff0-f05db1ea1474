# =============================================================================
# Build stage - Optimized for dependency caching
# =============================================================================
FROM golang:1.23-alpine AS builder

# Set Go environment variables
ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

WORKDIR /app

# Install only essential build dependencies
RUN apk add --no-cache git ca-certificates curl

# Install a specific version of golang-migrate for reproducible builds
ENV MIGRATE_VERSION v4.17.1
RUN curl -L https://github.com/golang-migrate/migrate/releases/download/${MIGRATE_VERSION}/migrate.linux-amd64.tar.gz | tar xvz && \
    mv migrate /usr/local/bin/

# Copy dependency files first for better Docker layer caching
# This layer will only be rebuilt when dependencies change
COPY go.mod go.sum ./

# Download dependencies - this layer is cached unless go.mod/go.sum changes
RUN go mod download && go mod verify

# Copy source code (this layer changes frequently)
COPY . .

# Build the binary with optimizations
RUN go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o hopenbackend \
    ./cmd/main.go

# =============================================================================
# Production stage - Minimal runtime image
# =============================================================================
FROM alpine:3.19

# Create a non-root user and group for security
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Install minimal runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Copy the binary and migrate tool
COPY --from=builder /app/hopenbackend /usr/local/bin/hopenbackend
COPY --from=builder /usr/local/bin/migrate /usr/local/bin/migrate

# Copy only essential configuration files and migrations
COPY --from=builder /app/config.yaml /config.yaml
COPY --from=builder /app/migrations /migrations

# Set ownership to the non-root user
RUN chown -R appuser:appgroup /usr/local/bin/hopenbackend /config.yaml /migrations

# Switch to the non-root user
USER appuser

# Expose the application port
EXPOSE 4000

# Use the binary as entrypoint
ENTRYPOINT ["/usr/local/bin/hopenbackend"]