version: '3.8'
services:
  # PostgreSQL Database - Primary database for users, bubbles, social relationships
  postgresql:
    image: postgres:15-alpine
    container_name: hopen_postgresql
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: hopen_db
      POSTGRES_USER: hopen
      POSTGRES_PASSWORD: hopen123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hopen -d hopen_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # ScyllaDB Database - High-performance NoSQL database for chat and real-time messaging
  scylladb:
    image: scylladb/scylla:6.1.2
    container_name: hopen_scylladb
    ports:
      - "9042:9042"      # CQL port
      - "9160:9160"      # Thrift port (legacy)
      - "7000:7000"      # Inter-node communication
      - "7001:7001"      # TLS inter-node communication
      - "9180:9180"      # Prometheus metrics
      - "19042:19042"    # Shard-aware port
    command:
      - --cluster-name=hopen_cluster
      - --endpoint-snitch=GossipingPropertyFileSnitch
      - --seeds=scylladb
      - --smp=2
      - --memory=2G
      - --overprovisioned=1
      - --api-address=0.0.0.0
      - --rpc-address=0.0.0.0
      - --listen-address=0.0.0.0
      - --broadcast-address=scylladb
      - --broadcast-rpc-address=scylladb
      - --enable-user-defined-functions=true
      - --experimental-features=udf
    volumes:
      - scylladb_data:/var/lib/scylla
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD-SHELL", "cqlsh -e 'describe cluster'"]
      interval: 30s
      timeout: 15s
      retries: 10
      start_period: 120s
    ulimits:
      memlock: -1
      nofile: 65536
    cap_add:
      - SYS_NICE

  # Valkey Cache - Redis-compatible cache for rate limiting
  valkey:
    image: valkey/valkey:7.2-alpine
    container_name: hopen_valkey
    ports:
      - "6379:6379"
    environment:
      - VALKEY_PASSWORD=${VALKEY_PASSWORD:-}
    volumes:
      - valkey_data:/data
    command: valkey-server --appendonly yes
    networks:
      - hopen-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "valkey-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NATS JetStream - Event-driven architecture
  nats:
    image: nats:2.10-alpine
    container_name: hopen_nats
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["--jetstream", "--store_dir=/data", "--http_port=8222"]
    volumes:
      - nats_data:/data
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: hopen_minio
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:9000:9000"
      - "${DOCKER_HOST_IP:-0.0.0.0}:9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin123}
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # EMQX MQTT5 Broker - Real-time communication (MQTT v5 ONLY)
  emqx:
    image: emqx/emqx:5.4.1
    container_name: hopen_emqx
    ports:
      - "1883:1883"   # MQTT v5 TCP
      - "8883:8883"   # MQTT v5 SSL/TLS
      - "18083:18083" # Dashboard
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=emqx
      - EMQX_NODE__COOKIE=hopen-emqx-secure-cookie-2024
    volumes:
      - emqx_data:/opt/emqx/data
      - emqx_log:/opt/emqx/log
      - ./config/emqx.conf:/opt/emqx/etc/emqx.conf:ro
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD", "sh", "-c", "nc -z localhost 1883"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Database Migration Services
  # These services run once to initialize the databases and then exit

  # Kratos Migration Service
  kratos-migrate:
    image: oryd/kratos:v1.1.0
    container_name: hopen_kratos_migrate
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      - DSN=*****************************************/hopen_db?sslmode=disable
    volumes:
      - ./config/kratos:/etc/config
    command: ["migrate", "sql", "-e", "--yes", "--config", "/etc/config/kratos.yml"]
    networks:
      - hopen-network
    restart: "no"

  # Hydra Migration Service
  hydra-migrate:
    image: oryd/hydra:v2.2.0
    container_name: hopen_hydra_migrate
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      - DSN=*****************************************/hopen_hydra?sslmode=disable
    command: ["migrate", "sql", "-e", "--yes"]
    networks:
      - hopen-network
    restart: "no"

  # Backend Migration Service
  backend-migrate:
    image: migrate/migrate:v4.16.2
    container_name: hopen_backend_migrate
    depends_on:
      postgresql:
        condition: service_healthy
    volumes:
      - ./migrations/postgresql:/migrations
    command: [
      "-path", "/migrations",
      "-database", "*****************************************/hopen_db?sslmode=disable",
      "up"
    ]
    networks:
      - hopen-network
    restart: "no"



  # PostgreSQL Migration Service
  postgresql-migrate:
    image: postgres:15-alpine
    container_name: hopen_postgresql_migrate
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      POSTGRES_DB: hopen_db
      POSTGRES_USER: hopen
      POSTGRES_PASSWORD: hopen123
    volumes:
      - ./migrations/postgresql:/migrations
    command: ["sh", "-c", "sleep 5 && psql -h postgresql -U hopen -d hopen_db -f /migrations/000001_initial_schema.up.sql"]
    networks:
      - hopen-network
    restart: "no"

  # Unified Migration Service
  migrate:
    build: .
    container_name: hopen_migrate
    depends_on:
      postgresql:
        condition: service_healthy
      scylladb:
        condition: service_healthy
    command: ["./hopenbackend", "migrate", "-action=up", "-database=all"]
    volumes:
      - ./migrations:/app/migrations
    networks:
      - hopen-network
    restart: "no"





  # Ory Kratos (Identity Management)
  kratos:
    image: oryd/kratos:v1.1.0
    container_name: hopen_kratos
    depends_on:
      postgresql:
        condition: service_healthy
      kratos-migrate:
        condition: service_completed_successfully
    environment:
      - DSN=*****************************************/hopen_db?sslmode=disable
    volumes:
      - ./config/kratos:/etc/config
    command: ["serve", "--dev", "--config", "/etc/config/kratos.yml"]
    ports:
      - "4433:4433"
      - "4434:4434"
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4433/health/ready"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Ory Hydra (OAuth2/OIDC)
  hydra:
    image: oryd/hydra:v2.2.0
    container_name: hopen_hydra
    depends_on:
      postgresql:
        condition: service_healthy
      hydra-migrate:
        condition: service_completed_successfully
    environment:
      - DSN=*****************************************/hopen_hydra?sslmode=disable
      - SECRETS_SYSTEM=this-is-the-primary-secret
      - SECRETS_COOKIE=this-is-the-cookie-secret
    command: ["serve", "all", "--dev"]
    ports:
      - "4444:4444"
      - "4445:4445"
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4445/health/ready"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Hopen Backend - All 12 microservices in one Go binary
  # Includes: auth, user, bubble, contact, bubble_analytics, friendship,
  # social_analytics, call, notification, realtime, media, sync services
  backend:
    build: .
    container_name: hopen_backend
    depends_on:
      # Wait for all infrastructure to be healthy
      postgresql:
        condition: service_healthy
      scylladb:
        condition: service_healthy
      valkey:
        condition: service_healthy
      nats:
        condition: service_healthy
      minio:
        condition: service_healthy


      # Wait for auth services to be healthy
      kratos:
        condition: service_healthy
      hydra:
        condition: service_healthy

      # Wait for unified migrations to complete
      migrate:
        condition: service_completed_successfully

    ports:
      - "8080:8080"  # gRPC port
      - "8081:8081"  # HTTP health check port
    volumes:
      - ./config.yaml:/app/config.yaml:ro
      - ./config:/app/config:ro
      - ./certs:/app/certs:ro
    environment:
      - ENV=development
      - LOG_LEVEL=debug
      - DB_HOST=postgresql
      - DB_PORT=5432
      - DB_NAME=hopen_db
      - DB_USER=hopen
      - DB_PASSWORD=hopen123
      - VALKEY_HOST=valkey
      - VALKEY_PORT=6379
      - VALKEY_ADDRESS=valkey:6379
      - NATS_URL=nats://nats:4222
      - VALKEY_PASSWORD=${VALKEY_PASSWORD:-}
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "--no-check-certificate", "https://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Envoy gRPC-Web Proxy - Transcodes HTTP/1.1 requests to gRPC for web clients
  envoy:
    image: envoyproxy/envoy:v1.28-latest
    container_name: hopen_envoy
    depends_on:
      backend:
        condition: service_healthy
    ports:
      - "80:8080"      # HTTP port for web clients
      - "9901:9901"    # Admin interface
    volumes:
      - ./envoy.yaml:/etc/envoy/envoy.yaml:ro
    command: ["envoy", "-c", "/etc/envoy/envoy.yaml", "--service-cluster", "hopen"]
    networks:
      - hopen-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9901/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  postgres_data:
  scylladb_data:
  valkey_data:
  nats_data:
  minio_data:
  emqx_data:
  emqx_log:

networks:
  hopen-network:
    driver: bridge
