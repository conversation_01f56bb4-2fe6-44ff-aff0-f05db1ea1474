services:
  # Backend service with proper SSL certificates
  backend:
    volumes:
      - ./certs/hopen-dev.crt:/app/certs/server.crt:ro
      - ./certs/hopen-dev.key:/app/certs/server.key:ro
    environment:
      - SSL_CERT_PATH=${SSL_CERT_PATH}
      - SSL_KEY_PATH=${SSL_KEY_PATH}
      - ENABLE_HTTPS=${ENABLE_HTTPS}
      - ENABLE_HTTP3=${ENABLE_HTTP3}
      - TLS_DOMAIN=${TLS_DOMAIN}
      - DOCKER_HOST_IP=${DOCKER_HOST_IP}
    ports:
      - "0.0.0.0:4000:4000"  # Main API port - bind to all interfaces for emulator support
