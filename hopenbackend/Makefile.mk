# Hopen Backend Makefile

# ---------------------------------------------------------------------------
# Infrastructure
# ---------------------------------------------------------------------------
COMPOSE=docker-compose

infra-up:
	$(COMPOSE) up -d --build --remove-orphans
	$(MAKE) migrate

infra-down:
	$(COMPOSE) down -v

# ---------------------------------------------------------------------------
# Database seeding (placeholder – extend with real seeders)
# ---------------------------------------------------------------------------
seed-db:
	@echo "Running database seeders (stub)"
	go run scripts/seed/main.go || true

# ---------------------------------------------------------------------------
# Performance Tests (k6)
# ---------------------------------------------------------------------------
K6 ?= k6
K6_FILE=tests/load/k6_scenario.js
K6_FLAGS=--vus 5000 --duration 5m

k6-steady:
	$(K6) run $(K6_FLAGS) $(K6_FILE)

k6-spike:
	$(K6) run --executor spike $(K6_FILE)

# ---------------------------------------------------------------------------
# JetStream test
# ---------------------------------------------------------------------------
jetstream-test:
	go test ./tests/performance -run TestJetStream*

# ---------------------------------------------------------------------------
# Static analysis & linting
# ---------------------------------------------------------------------------
lint: ## Run linter
	golangci-lint run ./...

vet:
	go vet ./...

# ---------------------------------------------------------------------------
# Comprehensive CI test (minus GitHub Action wrapper)
# ---------------------------------------------------------------------------
ci-test: infra-up seed-db vet lint test-all

# ---------------------------------------------------------------------------
# Database Migrations (using golang-migrate)
# ---------------------------------------------------------------------------

POSTGRES_CONTAINER=hopen_postgresql
POSTGRES_URL=postgres://hopen:hopen123@localhost:5432/hopen_db?sslmode=disable
MIGRATIONS_PATH=migrations/postgresql

# Install golang-migrate CLI tool
OS := $(shell go env GOOS)
ARCH := $(shell go env GOARCH)
MIGRATE_URL := https://github.com/golang-migrate/migrate/releases/latest/download/migrate.$(OS)-$(ARCH).tar.gz

install-migrate:
	@if ! command -v migrate &> /dev/null; then \
		echo "Installing golang-migrate CLI to ./bin for $(OS)/$(ARCH)..."; \
		mkdir -p ./bin; \
		curl -L $(MIGRATE_URL) | tar xvz; \
		mv migrate ./bin/migrate; \
		echo "✅ migrate installed in ./bin. Please add '$(shell pwd)/bin' to your PATH."; \
	fi

# Create a new migration file
migrate-create:
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir $(MIGRATIONS_PATH) -seq $name

# Run all pending migrations
migrate-up: install-migrate
	@echo "Running database migrations..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" up

# Rollback the last migration
migrate-down: install-migrate
	@echo "Rolling back last migration..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" down 1

# Rollback all migrations
migrate-reset: install-migrate
	@echo "Rolling back all migrations..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" down

# Check migration status
migrate-status: install-migrate
	@echo "Checking migration status..."
	@migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" version

# Force migration version (use with caution)
migrate-force: install-migrate
	@read -p "Enter version to force: " version; \
	migrate -path $(MIGRATIONS_PATH) -database "$(POSTGRES_URL)" force $version

# Legacy migrate target for backward compatibility
migrate: migrate-up

# ---------------------------------------------------------------------------
# Linkerd CLI tooling
# ---------------------------------------------------------------------------

linkerd-install:
	@echo "Installing Linkerd CLI ..."
	@curl -sL https://run.linkerd.io/install | sh -- --stable --skip-verify >/dev/null 2>&1
	@mkdir -p ./bin
	@cp $HOME/.linkerd2/bin/linkerd ./bin/ || true

linkerd-check: linkerd-install
	PATH="./bin:$PATH" bash scripts/check_linkerd_mtls.sh

# ---------------------------------------------------------------------------
# Clean build artifacts and docker junk
# ---------------------------------------------------------------------------

clean: ## Clean build artifacts
	rm -rf bin/
	@echo "Cleaning Go build cache ..."
	go clean -cache
	@echo "Cleaning Go module cache ..."
	go clean -modcache
	@echo "Cleaning Go test cache ..."
	go clean -testcache
	@echo "Removing compiled binaries (if any) ..."
	rm -f hopenbackend || true
	@echo "Removing Go binaries from bin directory ..."
	rm -f bin/* || true
	@echo "Pruning unused Docker images, containers, volumes, and networks ..."
	docker system prune -f --volumes
	@echo "Cleanup complete!"

# ---------------------------------------------------------------------------
# Protobuf Generation
# ---------------------------------------------------------------------------

# Install protoc compiler
install-protoc:
	@echo "Installing protoc compiler..."
	@which protoc >/dev/null || (echo "Installing protoc..." && \
		curl -L https://github.com/protocolbuffers/protobuf/releases/download/v25.3/protoc-25.3-linux-x86_64.zip -o protoc.zip && \
		unzip -o protoc.zip -d /usr/local && \
		rm protoc.zip)

# Install Go protobuf plugins
install-protoc-go:
	@echo "Installing Go protobuf plugins..."
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Generate Go protobuf code
generate-protos-go: install-protoc install-protoc-go
	@echo "Generating Go protobuf code..."
	@mkdir -p protos/gen
	@for proto in protos/*.proto; do \
		if [ -f "$$proto" ]; then \
			echo "Generating from $$proto..."; \
			protoc --go_out=protos/gen --go_opt=module=hopenbackend/protos/gen \
				--go-grpc_out=protos/gen --go-grpc_opt=module=hopenbackend/protos/gen \
				--proto_path=protos \
				"$$proto"; \
		fi \
	done
	@echo "✅ Go protobuf code generated!"

# Generate gRPC client code for Flutter
generate-grpc-flutter:
	@echo "Generating gRPC client code for Flutter..."
	@mkdir -p ../hopen/lib/generated/grpc
	@protoc \
		--dart_out=grpc:../hopen/lib/generated/grpc \
		-I./protos \
		./protos/common.proto \
		./protos/user.proto \
		./protos/bubble.proto \
		./protos/contact.proto \
		./protos/social_analytics.proto \
		./protos/notification.proto \
		./protos/sync.proto \
		./protos/realtime.proto
	@echo "gRPC client code generation for Flutter completed."

# Clean generated protobuf code
clean-protos:
	@echo "Cleaning generated protobuf code..."
	rm -rf protos/gen
	@echo "✅ Protobuf code cleaned!"

# ---------------------------------------------------------------------------
# Additional targets
# ---------------------------------------------------------------------------

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $1, $2}' $(MAKEFILE_LIST)

build: ## Build the application
	go build -o bin/hopenbackend ./cmd/server

run: ## Run the application
	go run ./cmd/server

test: ## Run tests
	go test -v ./...

test-integration: ## Run integration tests (including Valkey)
	go test -v ./tests/...

test-all: ## Run all tests (unit, integration, performance)
	@echo "🧪 Running all tests..."
	go test -v ./...
	go test -v ./tests/...
	$(MAKE) jetstream-test
	@echo "✅ All tests completed!"

docker-build: ## Build Docker image
	docker build -t hopen-backend .

docker-run: ## Run with Docker Compose
	docker-compose up -d

docker-stop: ## Stop Docker Compose
	docker-compose down

valkey-start: ## Start Valkey container
	docker run -d --name hopen-valkey -p 6379:6379 valkey/valkey:7.2-alpine

valkey-stop: ## Stop Valkey container
	docker stop hopen-valkey && docker rm hopen-valkey

valkey-cli: ## Connect to Valkey CLI
	docker exec -it hopen-valkey valkey-cli

deps: ## Download dependencies
	go mod download
	go mod tidy

format: ## Format code
	go fmt ./...

verify-valkey: ## Verify Valkey migration is complete
	@echo "🔍 Checking for Redis references..."
	@if grep -r "redis" --include="*.go" .; then \
		echo "❌ Redis references found!"; \
		exit 1; \
	else \
		echo "✅ No Redis references found"; \
	fi
	@echo "📦 Checking Valkey dependency..."
	@if grep -q "valkey-io/valkey-go" go.mod; then \
		echo "✅ Valkey dependency found"; \
	else \
		echo "❌ Valkey dependency missing!"; \
		exit 1; \
	fi

.PHONY: infra-up infra-down seed-db k6-steady k6-spike jetstream-test lint vet ci-test migrate migrate-up migrate-down migrate-reset migrate-status migrate-force migrate-create install-migrate linkerd-install linkerd-check clean help build run test clean docker-build docker-run valkey-start valkey-stop valkey-cli deps format verify-valkey install-protoc install-protoc-go generate-protos-go clean-protos
