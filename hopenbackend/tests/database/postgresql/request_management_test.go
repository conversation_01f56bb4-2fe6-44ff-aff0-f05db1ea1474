package database_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

// RequestManagementTestSuite provides a test suite for request management operations
type RequestManagementTestSuite struct {
	suite.Suite
	client *database.PostgreSQLClient
	logger *zap.Logger
	ctx    context.Context
}

// SetupSuite runs once before all tests
func (suite *RequestManagementTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.logger = zaptest.NewLogger(suite.T())

	// Get test database configuration from environment
	host := getEnvOrDefault("TEST_DB_HOST", "localhost")
	port := getEnvOrDefault("TEST_DB_PORT", "5432")
	dbName := getEnvOrDefault("TEST_DB_NAME", "hopen_test")
	username := getEnvOrDefault("TEST_DB_USER", "postgres")
	password := getEnvOrDefault("TEST_DB_PASSWORD", "postgres")

	cfg := &config.PostgreSQLConfig{
		Host:                  host,
		Port:                  parseInt(port),
		Database:              dbName,
		Username:              username,
		Password:              password,
		SSLMode:               "disable",
		MaxConnections:        10,
		MinConnections:        2,
		MaxConnectionLifetime: 5 * time.Minute,
		MaxConnectionIdleTime: 1 * time.Minute,
		HealthCheckPeriod:     30 * time.Second,
	}

	var err error
	suite.client, err = database.NewPostgreSQLClient(cfg, suite.logger)
	require.NoError(suite.T(), err, "Failed to create PostgreSQL client")
}

// TearDownSuite runs once after all tests
func (suite *RequestManagementTestSuite) TearDownSuite() {
	if suite.client != nil {
		suite.client.Close()
	}
}

// SetupTest runs before each test
func (suite *RequestManagementTestSuite) SetupTest() {
	// Initialize schema before each test
	err := suite.client.InitializeSchema(suite.ctx)
	require.NoError(suite.T(), err, "Failed to initialize schema")
}

// TearDownTest runs after each test
func (suite *RequestManagementTestSuite) TearDownTest() {
	// Clean up test data
	suite.cleanupTestData()
}

// TestContactRequestManagement tests contact request operations
func (suite *RequestManagementTestSuite) TestContactRequestManagement() {
	// Create test users
	requester := suite.createTestUser("requester", "<EMAIL>")
	recipient := suite.createTestUser("recipient", "<EMAIL>")

	// Test creating contact request
	contactRequest := &database.ContactRequest{
		ID:          "contact-request-1",
		RequesterID: requester.ID,
		RecipientID: recipient.ID,
		Status:      "pending",
		ExpiresAt:   time.Now().Add(7 * 24 * time.Hour), // 7 days
	}

	err := suite.client.CreateContactRequest(suite.ctx, contactRequest)
	require.NoError(suite.T(), err)
	assert.NotZero(suite.T(), contactRequest.CreatedAt)
	assert.NotZero(suite.T(), contactRequest.UpdatedAt)

	// Test creating duplicate contact request
	err = suite.client.CreateContactRequest(suite.ctx, contactRequest)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "duplicate key value")

	// Test getting sent contact requests
	sentRequests, err := suite.client.GetContactRequestsByUser(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), sentRequests, 1)
	assert.Equal(suite.T(), contactRequest.ID, sentRequests[0].ID)
	assert.Equal(suite.T(), "pending", sentRequests[0].Status)

	// Test getting received contact requests
	receivedRequests, err := suite.client.GetContactRequestsByUser(suite.ctx, recipient.ID, "received")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), receivedRequests, 1)
	assert.Equal(suite.T(), contactRequest.ID, receivedRequests[0].ID)

	// Test updating contact request status
	err = suite.client.UpdateContactRequestStatus(suite.ctx, contactRequest.ID, "accepted")
	require.NoError(suite.T(), err)

	// Verify status was updated
	updatedRequests, err := suite.client.GetContactRequestsByUser(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), updatedRequests, 1)
	assert.Equal(suite.T(), "accepted", updatedRequests[0].Status)

	// Test getting pending contact requests count
	sentPendingCount, err := suite.client.GetPendingContactRequestsCount(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 0, sentPendingCount) // Should be 0 since we accepted it

	receivedPendingCount, err := suite.client.GetPendingContactRequestsCount(suite.ctx, recipient.ID, "received")
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 0, receivedPendingCount) // Should be 0 since we accepted it

	// Test invalid request type
	_, err = suite.client.GetContactRequestsByUser(suite.ctx, requester.ID, "invalid")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid request type")

	// Test updating non-existent contact request
	err = suite.client.UpdateContactRequestStatus(suite.ctx, "non-existent", "accepted")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestFriendRequestManagement tests friend request operations
func (suite *RequestManagementTestSuite) TestFriendRequestManagement() {
	// Create test users and bubble
	requester := suite.createTestUser("friend-requester", "<EMAIL>")
	recipient := suite.createTestUser("friend-recipient", "<EMAIL>")
	bubble := suite.createTestBubble("friend-bubble", requester.ID)

	// Test creating friend request
	friendRequest := &database.FriendRequest{
		ID:             "friend-request-1",
		RequesterID:    requester.ID,
		RecipientID:    recipient.ID,
		SourceBubbleID: bubble,
		AutoGenerated:  true,
		Status:         "pending",
		ExpiresAt:      time.Now().Add(7 * 24 * time.Hour), // 7 days
	}

	err := suite.client.CreateFriendRequest(suite.ctx, friendRequest)
	require.NoError(suite.T(), err)
	assert.NotZero(suite.T(), friendRequest.CreatedAt)
	assert.NotZero(suite.T(), friendRequest.UpdatedAt)

	// Test creating duplicate friend request
	err = suite.client.CreateFriendRequest(suite.ctx, friendRequest)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "duplicate key value")

	// Test getting sent friend requests
	sentRequests, err := suite.client.GetFriendRequestsByUser(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), sentRequests, 1)
	assert.Equal(suite.T(), friendRequest.ID, sentRequests[0].ID)
	assert.Equal(suite.T(), "pending", sentRequests[0].Status)
	assert.Equal(suite.T(), bubble, sentRequests[0].SourceBubbleID)
	assert.True(suite.T(), sentRequests[0].AutoGenerated)

	// Test getting received friend requests
	receivedRequests, err := suite.client.GetFriendRequestsByUser(suite.ctx, recipient.ID, "received")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), receivedRequests, 1)
	assert.Equal(suite.T(), friendRequest.ID, receivedRequests[0].ID)

	// Test updating friend request status
	err = suite.client.UpdateFriendRequestStatus(suite.ctx, friendRequest.ID, "accepted")
	require.NoError(suite.T(), err)

	// Verify status was updated
	updatedRequests, err := suite.client.GetFriendRequestsByUser(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), updatedRequests, 1)
	assert.Equal(suite.T(), "accepted", updatedRequests[0].Status)

	// Test invalid request type
	_, err = suite.client.GetFriendRequestsByUser(suite.ctx, requester.ID, "invalid")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid request type")

	// Test updating non-existent friend request
	err = suite.client.UpdateFriendRequestStatus(suite.ctx, "non-existent", "accepted")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestBubbleRequestManagement tests bubble request operations
func (suite *RequestManagementTestSuite) TestBubbleRequestManagement() {
	// Create test users and bubble
	requester := suite.createTestUser("bubble-requester", "<EMAIL>")
	targetUser := suite.createTestUser("bubble-target", "<EMAIL>")
	_ = suite.createTestBubble("bubble-request-bubble", requester.ID)

	// Test getting bubble requests (initially empty)
	sentRequests, err := suite.client.GetBubbleRequestsByUser(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), sentRequests, 0)

	receivedRequests, err := suite.client.GetBubbleRequestsByUser(suite.ctx, targetUser.ID, "received")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), receivedRequests, 0)

	// Test getting pending bubble requests count (initially 0)
	sentPendingCount, err := suite.client.GetPendingBubbleRequestsCount(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 0, sentPendingCount)

	receivedPendingCount, err := suite.client.GetPendingBubbleRequestsCount(suite.ctx, targetUser.ID, "received")
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 0, receivedPendingCount)

	// Test invalid request type
	_, err = suite.client.GetBubbleRequestsByUser(suite.ctx, requester.ID, "invalid")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid request type")

	_, err = suite.client.GetPendingBubbleRequestsCount(suite.ctx, requester.ID, "invalid")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid request type")
}

// TestPendingRequestsSummary tests the pending requests summary functionality
func (suite *RequestManagementTestSuite) TestPendingRequestsSummary() {
	// Create test users and bubble
	user := suite.createTestUser("pending-user", "<EMAIL>")
	requester := suite.createTestUser("pending-requester", "<EMAIL>")
	_ = suite.createTestUser("pending-recipient", "<EMAIL>")
	bubble := suite.createTestBubble("pending-bubble", requester.ID)

	// Create various pending requests
	contactRequest := &database.ContactRequest{
		ID:          "pending-contact-1",
		RequesterID: requester.ID,
		RecipientID: user.ID,
		Status:      "pending",
		ExpiresAt:   time.Now().Add(7 * 24 * time.Hour),
	}
	err := suite.client.CreateContactRequest(suite.ctx, contactRequest)
	require.NoError(suite.T(), err)

	friendRequest := &database.FriendRequest{
		ID:             "pending-friend-1",
		RequesterID:    requester.ID,
		RecipientID:    user.ID,
		SourceBubbleID: bubble,
		AutoGenerated:  true,
		Status:         "pending",
		ExpiresAt:      time.Now().Add(7 * 24 * time.Hour),
	}
	err = suite.client.CreateFriendRequest(suite.ctx, friendRequest)
	require.NoError(suite.T(), err)

	// Test getting pending requests summary
	summary, err := suite.client.GetPendingRequestsForUser(suite.ctx, user.ID)
	require.NoError(suite.T(), err)

	// Verify received contact requests
	assert.Len(suite.T(), summary.ReceivedContactRequests, 1)
	assert.Equal(suite.T(), contactRequest.ID, summary.ReceivedContactRequests[0].ID)

	// Verify received friend requests
	assert.Len(suite.T(), summary.ReceivedFriendRequests, 1)
	assert.Equal(suite.T(), friendRequest.ID, summary.ReceivedFriendRequests[0].ID)

	// Verify sent requests are empty
	assert.Len(suite.T(), summary.SentContactRequests, 0)
	assert.Len(suite.T(), summary.SentFriendRequests, 0)
	assert.Len(suite.T(), summary.SentBubbleRequests, 0)
	assert.Len(suite.T(), summary.ReceivedBubbleRequests, 0)

	// Test getting pending requests count
	count, err := suite.client.GetPendingRequestsCount(suite.ctx, user.ID)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), 0, count.SentContactRequests)
	assert.Equal(suite.T(), 1, count.ReceivedContactRequests)
	assert.Equal(suite.T(), 0, count.SentBubbleRequests)
	assert.Equal(suite.T(), 0, count.ReceivedBubbleRequests)
	assert.Equal(suite.T(), 0, count.SentFriendRequests)
	assert.Equal(suite.T(), 1, count.ReceivedFriendRequests)
	assert.Equal(suite.T(), 2, count.Total) // 1 contact + 1 friend request
}

// TestUserRelationshipManagement tests user relationship operations
func (suite *RequestManagementTestSuite) TestUserRelationshipManagement() {
	// Create test users
	user1 := suite.createTestUser("relationship-user1", "<EMAIL>")
	user2 := suite.createTestUser("relationship-user2", "<EMAIL>")
	user3 := suite.createTestUser("relationship-user3", "<EMAIL>")

	// Test creating user relationship
	createdBy := "system"
	reason := "Test relationship"
	err := suite.client.CreateUserRelationship(suite.ctx, user1.ID, user2.ID, "friend", &createdBy, &reason)
	require.NoError(suite.T(), err)

	// Test creating relationship with same users but different type
	err = suite.client.CreateUserRelationship(suite.ctx, user1.ID, user2.ID, "contact", &createdBy, &reason)
	require.NoError(suite.T(), err)

	// Test getting user relationships
	relationships, err := suite.client.GetUserRelationships(suite.ctx, user1.ID, nil)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), relationships, 1) // Should only have one active relationship type
	assert.Contains(suite.T(), relationships, "contact")
	assert.Len(suite.T(), relationships["contact"], 1)
	assert.Equal(suite.T(), user2.ID, relationships["contact"][0])

	// Test getting specific relationship type
	friendRelationships, err := suite.client.GetUserRelationships(suite.ctx, user1.ID, stringPtr("friend"))
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), friendRelationships, 0) // Should be empty since we changed to contact

	contactRelationships, err := suite.client.GetUserRelationships(suite.ctx, user1.ID, stringPtr("contact"))
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), contactRelationships, 1)
	assert.Contains(suite.T(), contactRelationships, "contact")
	assert.Len(suite.T(), contactRelationships["contact"], 1)
	assert.Equal(suite.T(), user2.ID, contactRelationships["contact"][0])

	// Test bidirectional relationship
	user2Relationships, err := suite.client.GetUserRelationships(suite.ctx, user2.ID, nil)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), user2Relationships, 1)
	assert.Contains(suite.T(), user2Relationships, "contact")
	assert.Len(suite.T(), user2Relationships["contact"], 1)
	assert.Equal(suite.T(), user1.ID, user2Relationships["contact"][0])

	// Test creating relationship with non-existent user
	err = suite.client.CreateUserRelationship(suite.ctx, user1.ID, "non-existent-user", "friend", &createdBy, &reason)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "foreign key")

	// Test creating relationship with invalid type
	err = suite.client.CreateUserRelationship(suite.ctx, user1.ID, user3.ID, "invalid-type", &createdBy, &reason)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid input value")
}

// TestRequestExpiration tests request expiration scenarios
func (suite *RequestManagementTestSuite) TestRequestExpiration() {
	// Create test users
	requester := suite.createTestUser("expiration-requester", "<EMAIL>")
	recipient := suite.createTestUser("expiration-recipient", "<EMAIL>")

	// Create contact request with past expiration
	expiredContactRequest := &database.ContactRequest{
		ID:          "expired-contact-1",
		RequesterID: requester.ID,
		RecipientID: recipient.ID,
		Status:      "pending",
		ExpiresAt:   time.Now().Add(-1 * time.Hour), // Expired 1 hour ago
	}

	err := suite.client.CreateContactRequest(suite.ctx, expiredContactRequest)
	require.NoError(suite.T(), err)

	// Create friend request with past expiration
	bubble := suite.createTestBubble("expiration-bubble", requester.ID)
	expiredFriendRequest := &database.FriendRequest{
		ID:             "expired-friend-1",
		RequesterID:    requester.ID,
		RecipientID:    recipient.ID,
		SourceBubbleID: bubble,
		AutoGenerated:  true,
		Status:         "pending",
		ExpiresAt:      time.Now().Add(-1 * time.Hour), // Expired 1 hour ago
	}

	err = suite.client.CreateFriendRequest(suite.ctx, expiredFriendRequest)
	require.NoError(suite.T(), err)

	// Test that expired requests are still retrievable but marked as expired
	// Note: The actual expiration logic would typically be handled by a background job
	// This test just verifies the data structure supports expiration
	sentContactRequests, err := suite.client.GetContactRequestsByUser(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), sentContactRequests, 1)
	assert.Equal(suite.T(), "pending", sentContactRequests[0].Status) // Still pending until processed

	sentFriendRequests, err := suite.client.GetFriendRequestsByUser(suite.ctx, requester.ID, "sent")
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), sentFriendRequests, 1)
	assert.Equal(suite.T(), "pending", sentFriendRequests[0].Status) // Still pending until processed
}

// Helper functions

func (suite *RequestManagementTestSuite) createTestUser(username, email string) *database.User {
	user := &database.User{
		ID:          "test-user-" + username,
		Username:    stringPtr(username),
		Email:       email,
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("User"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)
	return user
}

func (suite *RequestManagementTestSuite) createTestBubble(name, creatorID string) string {
	// Note: This is a simplified bubble creation for testing
	// TODO : In a real implementation, you would have a proper Bubble struct and creation method
	bubbleID := "test-bubble-" + name

	// Insert bubble directly into database for testing
	query := `INSERT INTO bubbles (id, creator_id, name, capacity, member_count, status, expires_at) 
			  VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := suite.client.Pool.Exec(suite.ctx, query,
		bubbleID, creatorID, name, 5, 1, "active",
		time.Now().Add(30*24*time.Hour)) // 30 days from now

	require.NoError(suite.T(), err)

	return bubbleID
}

func (suite *RequestManagementTestSuite) cleanupTestData() {
	// Clean up all test data
	tables := []string{
		"user_relationships", "user_roles", "roles", "contact_requests",
		"friend_requests", "request_votes", "bubble_requests", "call_sessions",
		"media_files", "notifications", "bubble_members", "bubbles", "users",
	}

	for _, table := range tables {
		_, err := suite.client.Pool.Exec(suite.ctx, "DELETE FROM "+table)
		if err != nil {
			suite.logger.Warn("Failed to clean up table", zap.String("table", table), zap.Error(err))
		}
	}
}

// Run the test suite
func TestRequestManagementTestSuite(t *testing.T) {
	suite.Run(t, new(RequestManagementTestSuite))
}
