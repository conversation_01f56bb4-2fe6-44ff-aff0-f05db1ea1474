# Database Tests Makefile

# Default values for test database
TEST_DB_HOST ?= localhost
TEST_DB_PORT ?= 5432
TEST_DB_NAME ?= hopen_test
TEST_DB_USER ?= postgres
TEST_DB_PASSWORD ?= postgres

# Go test flags
TEST_FLAGS = -v -timeout=5m

# Export environment variables for tests
export TEST_DB_HOST
export TEST_DB_PORT
export TEST_DB_NAME
export TEST_DB_USER
export TEST_DB_PASSWORD

.PHONY: help test test-all test-postgresql test-users test-requests clean setup

help: ## Show this help message
	@echo "Database Tests - Available Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

setup: ## Set up test database (requires PostgreSQL running)
	@echo "Setting up test database..."
	@createdb -h $(TEST_DB_HOST) -p $(TEST_DB_PORT) -U $(TEST_DB_USER) $(TEST_DB_NAME) 2>/dev/null || echo "Database $(TEST_DB_NAME) already exists or creation failed"

clean: ## Clean up test database
	@echo "Cleaning up test database..."
	@dropdb -h $(TEST_DB_HOST) -p $(TEST_DB_PORT) -U $(TEST_DB_USER) $(TEST_DB_NAME) 2>/dev/null || echo "Database $(TEST_DB_NAME) does not exist or drop failed"

test: setup ## Run all database tests
	@echo "Running all database tests..."
	go test ./tests/database/... $(TEST_FLAGS)

test-all: setup ## Run all database tests with coverage
	@echo "Running all database tests with coverage..."
	go test ./tests/database/... $(TEST_FLAGS) -coverprofile=coverage.out
	@echo "Coverage report generated: coverage.out"

test-postgresql: setup ## Run only PostgreSQL connection and schema tests
	@echo "Running PostgreSQL tests..."
	go test ./tests/database/ -run TestPostgreSQLTestSuite $(TEST_FLAGS)

test-users: setup ## Run only user management tests
	@echo "Running user management tests..."
	go test ./tests/database/ -run TestUserManagementTestSuite $(TEST_FLAGS)

test-requests: setup ## Run only request management tests
	@echo "Running request management tests..."
	go test ./tests/database/ -run TestRequestManagementTestSuite $(TEST_FLAGS)

test-unit: setup ## Run individual unit tests
	@echo "Running individual unit tests..."
	go test ./tests/database/ -run "TestNewPostgreSQLClient|TestCreateUser|TestContactRequestManagement" $(TEST_FLAGS)

test-verbose: setup ## Run tests with verbose output
	@echo "Running tests with verbose output..."
	go test ./tests/database/... $(TEST_FLAGS) -v

test-race: setup ## Run tests with race detection
	@echo "Running tests with race detection..."
	go test ./tests/database/... $(TEST_FLAGS) -race

test-short: setup ## Run tests in short mode
	@echo "Running tests in short mode..."
	go test ./tests/database/... $(TEST_FLAGS) -short

coverage: test-all ## Generate and display coverage report
	@echo "Generating coverage report..."
	@go tool cover -func=coverage.out
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage HTML report generated: coverage.html"

lint: ## Run linting on test files
	@echo "Running linting on test files..."
	@golangci-lint run ./tests/database/...

fmt: ## Format test files
	@echo "Formatting test files..."
	@go fmt ./tests/database/...

vet: ## Run go vet on test files
	@echo "Running go vet on test files..."
	@go vet ./tests/database/...

check: fmt vet lint ## Run all code quality checks

# Development helpers
dev-setup: ## Set up development environment
	@echo "Setting up development environment..."
	@echo "1. Ensure PostgreSQL is running"
	@echo "2. Create test database: make setup"
	@echo "3. Run tests: make test"

dev-clean: clean ## Clean up development environment
	@echo "Development environment cleaned up"

# CI/CD helpers
ci-test: ## Run tests for CI/CD pipeline
	@echo "Running CI/CD tests..."
	@make test-race
	@make coverage

# Database helpers
db-status: ## Check database connection status
	@echo "Checking database connection..."
	@pg_isready -h $(TEST_DB_HOST) -p $(TEST_DB_PORT) -U $(TEST_DB_USER)

db-info: ## Show database information
	@echo "Database Information:"
	@echo "Host: $(TEST_DB_HOST)"
	@echo "Port: $(TEST_DB_PORT)"
	@echo "Database: $(TEST_DB_NAME)"
	@echo "User: $(TEST_DB_USER)"
	@echo "Password: $(shell echo $(TEST_DB_PASSWORD) | sed 's/./*/g')"

# Default target
.DEFAULT_GOAL := help 