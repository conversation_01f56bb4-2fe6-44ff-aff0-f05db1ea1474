package database_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

// UserManagementTestSuite provides a test suite for user management operations
type UserManagementTestSuite struct {
	suite.Suite
	client *database.PostgreSQLClient
	logger *zap.Logger
	ctx    context.Context
}

// SetupSuite runs once before all tests
func (suite *UserManagementTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.logger = zaptest.NewLogger(suite.T())

	// Get test database configuration from environment
	host := getEnvOrDefault("TEST_DB_HOST", "localhost")
	port := getEnvOrDefault("TEST_DB_PORT", "5432")
	dbName := getEnvOrDefault("TEST_DB_NAME", "hopen_test")
	username := getEnvOrDefault("TEST_DB_USER", "postgres")
	password := getEnvOrDefault("TEST_DB_PASSWORD", "postgres")

	cfg := &config.PostgreSQLConfig{
		Host:                  host,
		Port:                  parseInt(port),
		Database:              dbName,
		Username:              username,
		Password:              password,
		SSLMode:               "disable",
		MaxConnections:        10,
		MinConnections:        2,
		MaxConnectionLifetime: 5 * time.Minute,
		MaxConnectionIdleTime: 1 * time.Minute,
		HealthCheckPeriod:     30 * time.Second,
	}

	var err error
	suite.client, err = database.NewPostgreSQLClient(cfg, suite.logger)
	require.NoError(suite.T(), err, "Failed to create PostgreSQL client")
}

// TearDownSuite runs once after all tests
func (suite *UserManagementTestSuite) TearDownSuite() {
	if suite.client != nil {
		suite.client.Close()
	}
}

// SetupTest runs before each test
func (suite *UserManagementTestSuite) SetupTest() {
	// Initialize schema before each test
	err := suite.client.InitializeSchema(suite.ctx)
	require.NoError(suite.T(), err, "Failed to initialize schema")
}

// TearDownTest runs after each test
func (suite *UserManagementTestSuite) TearDownTest() {
	// Clean up test data
	suite.cleanupTestData()
}

// TestCreateUser tests user creation
func (suite *UserManagementTestSuite) TestCreateUser() {
	// Test successful user creation
	user := &database.User{
		ID:          "test-user-1",
		Username:    stringPtr("testuser1"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("User"),
		AvatarURL:   stringPtr("https://example.com/avatar1.jpg"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
		NotificationSettings: map[string]interface{}{
			"email_notifications": true,
			"push_notifications":  false,
		},
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)
	assert.NotZero(suite.T(), user.CreatedAt)
	assert.NotZero(suite.T(), user.UpdatedAt)

	// Verify user was created in database
	retrievedUser, err := suite.client.GetUserByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), user.ID, retrievedUser.ID)
	assert.Equal(suite.T(), user.Username, retrievedUser.Username)
	assert.Equal(suite.T(), user.Email, retrievedUser.Email)
	assert.Equal(suite.T(), user.FirstName, retrievedUser.FirstName)
	assert.Equal(suite.T(), user.LastName, retrievedUser.LastName)
	assert.Equal(suite.T(), user.AvatarURL, retrievedUser.AvatarURL)
	assert.Equal(suite.T(), user.IsPrivate, retrievedUser.IsPrivate)
	assert.Equal(suite.T(), user.NotificationSettings, retrievedUser.NotificationSettings)

	// Test creating user with duplicate ID
	err = suite.client.CreateUser(suite.ctx, user)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "duplicate key value")

	// Test creating user with duplicate email
	duplicateEmailUser := &database.User{
		ID:          "test-user-2",
		Username:    stringPtr("testuser2"),
		Email:       "<EMAIL>", // Same email
		FirstName:   stringPtr("Test2"),
		LastName:    stringPtr("User2"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err = suite.client.CreateUser(suite.ctx, duplicateEmailUser)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "duplicate key value")

	// Test creating user with duplicate username
	duplicateUsernameUser := &database.User{
		ID:          "test-user-3",
		Username:    stringPtr("testuser1"), // Same username
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test3"),
		LastName:    stringPtr("User3"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err = suite.client.CreateUser(suite.ctx, duplicateUsernameUser)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "duplicate key value")
}

// TestGetUserByID tests retrieving user by ID
func (suite *UserManagementTestSuite) TestGetUserByID() {
	// Create a test user
	user := &database.User{
		ID:          "test-user-get-by-id",
		Username:    stringPtr("testusergetbyid"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("GetByID"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Test successful retrieval
	retrievedUser, err := suite.client.GetUserByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), user.ID, retrievedUser.ID)
	assert.Equal(suite.T(), user.Username, retrievedUser.Username)
	assert.Equal(suite.T(), user.Email, retrievedUser.Email)

	// Test retrieval of non-existent user
	_, err = suite.client.GetUserByID(suite.ctx, "non-existent-user")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestGetUserByEmail tests retrieving user by email
func (suite *UserManagementTestSuite) TestGetUserByEmail() {
	// Create a test user
	user := &database.User{
		ID:          "test-user-get-by-email",
		Username:    stringPtr("testusergetbyemail"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("GetByEmail"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Test successful retrieval
	retrievedUser, err := suite.client.GetUserByEmail(suite.ctx, user.Email)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), user.ID, retrievedUser.ID)
	assert.Equal(suite.T(), user.Email, retrievedUser.Email)

	// Test retrieval of non-existent email
	_, err = suite.client.GetUserByEmail(suite.ctx, "<EMAIL>")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")

	// Test retrieval of inactive user
	inactiveUser := &database.User{
		ID:          "test-user-inactive",
		Username:    stringPtr("testuserinactive"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("Inactive"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsActive:    false,
		IsPrivate:   false,
	}

	err = suite.client.CreateUser(suite.ctx, inactiveUser)
	require.NoError(suite.T(), err)

	// Deactivate the user
	err = suite.client.SoftDeleteUser(suite.ctx, inactiveUser.ID)
	require.NoError(suite.T(), err)

	// Should not find inactive user
	_, err = suite.client.GetUserByEmail(suite.ctx, inactiveUser.Email)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestGetUserByUsername tests retrieving user by username
func (suite *UserManagementTestSuite) TestGetUserByUsername() {
	// Create a test user
	user := &database.User{
		ID:          "test-user-get-by-username",
		Username:    stringPtr("testusergetbyusername"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("GetByUsername"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Test successful retrieval
	retrievedUser, err := suite.client.GetUserByUsername(suite.ctx, *user.Username)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), user.ID, retrievedUser.ID)
	assert.Equal(suite.T(), user.Username, retrievedUser.Username)

	// Test retrieval of non-existent username
	_, err = suite.client.GetUserByUsername(suite.ctx, "nonexistentuser")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestUpdateUser tests user updates
func (suite *UserManagementTestSuite) TestUpdateUser() {
	// Create a test user
	user := &database.User{
		ID:          "test-user-update",
		Username:    stringPtr("testuserupdate"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("Update"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Update user fields
	user.FirstName = stringPtr("Updated")
	user.LastName = stringPtr("Name")
	user.AvatarURL = stringPtr("https://example.com/new-avatar.jpg")
	user.IsPrivate = true
	user.NotificationSettings = map[string]interface{}{
		"email_notifications": false,
		"push_notifications":  true,
	}

	err = suite.client.UpdateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Verify updates
	retrievedUser, err := suite.client.GetUserByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated", *retrievedUser.FirstName)
	assert.Equal(suite.T(), "Name", *retrievedUser.LastName)
	assert.Equal(suite.T(), "https://example.com/new-avatar.jpg", *retrievedUser.AvatarURL)
	assert.True(suite.T(), retrievedUser.IsPrivate)
	assert.Equal(suite.T(), user.NotificationSettings, retrievedUser.NotificationSettings)

	// Test updating non-existent user
	nonExistentUser := &database.User{
		ID:          "non-existent-user",
		Username:    stringPtr("nonexistent"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Non"),
		LastName:    stringPtr("Existent"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err = suite.client.UpdateUser(suite.ctx, nonExistentUser)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestSoftDeleteUser tests soft deletion of users
func (suite *UserManagementTestSuite) TestSoftDeleteUser() {
	// Create a test user
	user := &database.User{
		ID:          "test-user-soft-delete",
		Username:    stringPtr("testusersoftdelete"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("SoftDelete"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Soft delete the user
	err = suite.client.SoftDeleteUser(suite.ctx, user.ID)
	require.NoError(suite.T(), err)

	// Verify user is inactive
	retrievedUser, err := suite.client.GetUserByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.False(suite.T(), retrievedUser.IsActive)

	// Should not find by email (GetUserByEmail filters by is_active = true)
	_, err = suite.client.GetUserByEmail(suite.ctx, user.Email)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")

	// Should not find by username (GetUserByUsername filters by is_active = true)
	_, err = suite.client.GetUserByUsername(suite.ctx, *user.Username)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")

	// Test soft deleting non-existent user
	err = suite.client.SoftDeleteUser(suite.ctx, "non-existent-user")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestBanUser tests user banning
func (suite *UserManagementTestSuite) TestBanUser() {
	// Create a test user
	user := &database.User{
		ID:          "test-user-ban",
		Username:    stringPtr("testuserban"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("Ban"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Ban the user
	err = suite.client.BanUser(suite.ctx, user.ID)
	require.NoError(suite.T(), err)

	// Verify user is banned
	retrievedUser, err := suite.client.GetUserByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), retrievedUser.IsBanned)
	assert.NotNil(suite.T(), retrievedUser.BannedAt)

	// Test banning non-existent user
	err = suite.client.BanUser(suite.ctx, "non-existent-user")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestUnbanUser tests user unbanning
func (suite *UserManagementTestSuite) TestUnbanUser() {
	// Create a test user
	user := &database.User{
		ID:          "test-user-unban",
		Username:    stringPtr("testuserunban"),
		Email:       "<EMAIL>",
		FirstName:   stringPtr("Test"),
		LastName:    stringPtr("Unban"),
		DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
		IsPrivate:   false,
	}

	err := suite.client.CreateUser(suite.ctx, user)
	require.NoError(suite.T(), err)

	// Ban the user first
	err = suite.client.BanUser(suite.ctx, user.ID)
	require.NoError(suite.T(), err)

	// Unban the user
	err = suite.client.UnbanUser(suite.ctx, user.ID)
	require.NoError(suite.T(), err)

	// Verify user is not banned
	retrievedUser, err := suite.client.GetUserByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.False(suite.T(), retrievedUser.IsBanned)
	assert.Nil(suite.T(), retrievedUser.BannedAt)

	// Test unbanning non-existent user
	err = suite.client.UnbanUser(suite.ctx, "non-existent-user")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "not found")
}

// TestSearchUsers tests user search functionality
func (suite *UserManagementTestSuite) TestSearchUsers() {
	// Create test users
	users := []*database.User{
		{
			ID:          "test-user-search-1",
			Username:    stringPtr("johnsmith"),
			Email:       "<EMAIL>",
			FirstName:   stringPtr("John"),
			LastName:    stringPtr("Smith"),
			DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
			IsPrivate:   false,
		},
		{
			ID:          "test-user-search-2",
			Username:    stringPtr("johndoe"),
			Email:       "<EMAIL>",
			FirstName:   stringPtr("John"),
			LastName:    stringPtr("Doe"),
			DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
			IsPrivate:   false,
		},
		{
			ID:          "test-user-search-3",
			Username:    stringPtr("janesmith"),
			Email:       "<EMAIL>",
			FirstName:   stringPtr("Jane"),
			LastName:    stringPtr("Smith"),
			DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
			IsPrivate:   false,
		},
		{
			ID:          "test-user-search-4",
			Username:    stringPtr("privateuser"),
			Email:       "<EMAIL>",
			FirstName:   stringPtr("Private"),
			LastName:    stringPtr("User"),
			DateOfBirth: timePtr(time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)),
			IsPrivate:   true, // Private user
		},
	}

	for _, user := range users {
		err := suite.client.CreateUser(suite.ctx, user)
		require.NoError(suite.T(), err)
	}

	// Test search by username
	results, err := suite.client.SearchUsers(suite.ctx, "john", 10)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 2)                          // johnsmith and johndoe
	assert.Equal(suite.T(), "johnsmith", *results[0].Username) // Should be first due to username match
	assert.Equal(suite.T(), "johndoe", *results[1].Username)

	// Test search by first name
	results, err = suite.client.SearchUsers(suite.ctx, "John", 10)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 2) // John Smith and John Doe

	// Test search by last name
	results, err = suite.client.SearchUsers(suite.ctx, "Smith", 10)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 2) // John Smith and Jane Smith

	// Test search with limit
	results, err = suite.client.SearchUsers(suite.ctx, "john", 1)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 1)

	// Test search for non-existent user
	results, err = suite.client.SearchUsers(suite.ctx, "nonexistent", 10)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 0)

	// Test that private users are not included in search results
	results, err = suite.client.SearchUsers(suite.ctx, "private", 10)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 0) // Private user should not be included
}

// Helper functions

func (suite *UserManagementTestSuite) cleanupTestData() {
	// Clean up all test data
	tables := []string{
		"user_relationships", "user_roles", "roles", "contact_requests",
		"friend_requests", "request_votes", "bubble_requests", "call_sessions",
		"media_files", "notifications", "bubble_members", "bubbles", "users",
	}

	for _, table := range tables {
		_, err := suite.client.Pool.Exec(suite.ctx, "DELETE FROM "+table)
		if err != nil {
			suite.logger.Warn("Failed to clean up table", zap.String("table", table), zap.Error(err))
		}
	}
}

func stringPtr(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}

// Run the test suite
func TestUserManagementTestSuite(t *testing.T) {
	suite.Run(t, new(UserManagementTestSuite))
}
