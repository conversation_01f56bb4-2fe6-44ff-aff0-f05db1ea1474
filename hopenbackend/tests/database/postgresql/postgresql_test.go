package database_test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

// PostgreSQLTestSuite provides a test suite for PostgreSQL operations
type PostgreSQLTestSuite struct {
	suite.Suite
	client *database.PostgreSQLClient
	logger *zap.Logger
	ctx    context.Context
}

// SetupSuite runs once before all tests
func (suite *PostgreSQLTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.logger = zaptest.NewLogger(suite.T())

	// Get test database configuration from environment
	host := getEnvOrDefault("TEST_DB_HOST", "localhost")
	port := getEnvOrDefault("TEST_DB_PORT", "5432")
	dbName := getEnvOrDefault("TEST_DB_NAME", "hopen_test")
	username := getEnvOrDefault("TEST_DB_USER", "postgres")
	password := getEnvOrDefault("TEST_DB_PASSWORD", "postgres")

	cfg := &config.PostgreSQLConfig{
		Host:                  host,
		Port:                  parseInt(port),
		Database:              dbName,
		Username:              username,
		Password:              password,
		SSLMode:               "disable",
		MaxConnections:        10,
		MinConnections:        2,
		MaxConnectionLifetime: 5 * time.Minute,
		MaxConnectionIdleTime: 1 * time.Minute,
		HealthCheckPeriod:     30 * time.Second,
	}

	var err error
	suite.client, err = database.NewPostgreSQLClient(cfg, suite.logger)
	require.NoError(suite.T(), err, "Failed to create PostgreSQL client")
}

// TearDownSuite runs once after all tests
func (suite *PostgreSQLTestSuite) TearDownSuite() {
	if suite.client != nil {
		suite.client.Close()
	}
}

// SetupTest runs before each test
func (suite *PostgreSQLTestSuite) SetupTest() {
	// Initialize schema before each test
	err := suite.client.InitializeSchema(suite.ctx)
	require.NoError(suite.T(), err, "Failed to initialize schema")
}

// TearDownTest runs after each test
func (suite *PostgreSQLTestSuite) TearDownTest() {
	// Clean up test data
	suite.cleanupTestData()
}

// TestNewPostgreSQLClient tests client creation
func (suite *PostgreSQLTestSuite) TestNewPostgreSQLClient() {
	// Test with valid configuration
	cfg := &config.PostgreSQLConfig{
		Host:                  "localhost",
		Port:                  5432,
		Database:              "hopen_test",
		Username:              "postgres",
		Password:              "postgres",
		SSLMode:               "disable",
		MaxConnections:        5,
		MinConnections:        1,
		MaxConnectionLifetime: 1 * time.Minute,
		MaxConnectionIdleTime: 30 * time.Second,
		HealthCheckPeriod:     10 * time.Second,
	}

	client, err := database.NewPostgreSQLClient(cfg, suite.logger)
	require.NoError(suite.T(), err)
	assert.NotNil(suite.T(), client)
	assert.NotNil(suite.T(), client.Pool)

	// Clean up
	client.Close()

	// Test with invalid configuration
	invalidCfg := &config.PostgreSQLConfig{
		Host:     "invalid-host",
		Port:     5432,
		Database: "invalid_db",
		Username: "invalid_user",
		Password: "invalid_pass",
		SSLMode:  "disable",
	}

	_, err = database.NewPostgreSQLClient(invalidCfg, suite.logger)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "failed to create PostgreSQL pool")
}

// TestPostgreSQLClient_Health tests health check functionality
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_Health() {
	// Test healthy connection
	err := suite.client.Health(suite.ctx)
	assert.NoError(suite.T(), err)

	// Test with cancelled context
	cancelledCtx, cancel := context.WithCancel(suite.ctx)
	cancel()
	err = suite.client.Health(cancelledCtx)
	assert.Error(suite.T(), err)
}

// TestPostgreSQLClient_Stats tests connection pool statistics
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_Stats() {
	stats := suite.client.Stats()
	assert.NotNil(suite.T(), stats)
	assert.GreaterOrEqual(suite.T(), stats.TotalConns(), int32(0))
	assert.GreaterOrEqual(suite.T(), stats.IdleConns(), int32(0))
}

// TestPostgreSQLClient_InitializeSchema tests schema initialization
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_InitializeSchema() {
	// Test successful schema initialization
	err := suite.client.InitializeSchema(suite.ctx)
	assert.NoError(suite.T(), err)

	// Verify that tables were created
	tables := []string{"users", "bubbles", "bubble_members", "notifications", "media_files", "call_sessions", "bubble_requests", "request_votes", "friend_requests", "contact_requests", "roles", "user_roles", "user_relationships"}

	for _, table := range tables {
		var exists bool
		query := `SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)`
		err := suite.client.Pool.QueryRow(suite.ctx, query, table).Scan(&exists)
		assert.NoError(suite.T(), err, "Failed to check table existence: %s", table)
		assert.True(suite.T(), exists, "Table %s should exist", table)
	}

	// Verify that enums were created
	enums := []string{"bubble_status", "bubble_member_status", "bubble_request_type", "request_status", "vote_type", "friend_request_status", "contact_request_status", "notification_type"}

	for _, enum := range enums {
		var exists bool
		query := `SELECT EXISTS (SELECT FROM pg_type WHERE typname = $1)`
		err := suite.client.Pool.QueryRow(suite.ctx, query, enum).Scan(&exists)
		assert.NoError(suite.T(), err, "Failed to check enum existence: %s", enum)
		assert.True(suite.T(), exists, "Enum %s should exist", enum)
	}

	// Verify that indexes were created
	indexes := []string{"idx_users_email", "idx_bubbles_creator", "idx_bubble_members_bubble_id", "idx_notifications_user_id_created_at"}

	for _, index := range indexes {
		var exists bool
		query := `SELECT EXISTS (SELECT FROM pg_indexes WHERE indexname = $1)`
		err := suite.client.Pool.QueryRow(suite.ctx, query, index).Scan(&exists)
		assert.NoError(suite.T(), err, "Failed to check index existence: %s", index)
		assert.True(suite.T(), exists, "Index %s should exist", index)
	}

	// Test that schema can be initialized multiple times (idempotent)
	err = suite.client.InitializeSchema(suite.ctx)
	assert.NoError(suite.T(), err, "Schema initialization should be idempotent")
}

// TestPostgreSQLClient_InitializeSchema_WithInvalidSQL tests schema initialization with invalid SQL
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_InitializeSchema_WithInvalidSQL() {
	// Create a client with a mock pool that will fail
	// This test verifies error handling in schema initialization
	// Note: This is a simplified test - in a real scenario, you might use a mock or test container
}

// TestPostgreSQLClient_Close tests client cleanup
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_Close() {
	// Create a separate client for this test
	cfg := &config.PostgreSQLConfig{
		Host:                  "localhost",
		Port:                  5432,
		Database:              "hopen_test",
		Username:              "postgres",
		Password:              "postgres",
		SSLMode:               "disable",
		MaxConnections:        5,
		MinConnections:        1,
		MaxConnectionLifetime: 1 * time.Minute,
		MaxConnectionIdleTime: 30 * time.Second,
		HealthCheckPeriod:     10 * time.Second,
	}

	client, err := database.NewPostgreSQLClient(cfg, suite.logger)
	require.NoError(suite.T(), err)

	// Test that close doesn't panic
	assert.NotPanics(suite.T(), func() {
		client.Close()
	})

	// Test that close can be called multiple times
	assert.NotPanics(suite.T(), func() {
		client.Close()
	})
}

// TestPostgreSQLClient_ConnectionPool tests connection pool behavior
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_ConnectionPool() {
	// Test concurrent connections
	const numGoroutines = 10
	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			// Perform a simple query
			var result int
			err := suite.client.Pool.QueryRow(suite.ctx, "SELECT 1").Scan(&result)
			assert.NoError(suite.T(), err)
			assert.Equal(suite.T(), 1, result)
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Check pool statistics
	stats := suite.client.Stats()
	assert.GreaterOrEqual(suite.T(), stats.TotalConns(), int32(0))
	assert.LessOrEqual(suite.T(), stats.TotalConns(), int32(10)) // Max connections
}

// TestPostgreSQLClient_ContextCancellation tests context cancellation behavior
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_ContextCancellation() {
	// Test with cancelled context
	cancelledCtx, cancel := context.WithCancel(suite.ctx)
	cancel()

	// Health check should fail with cancelled context
	err := suite.client.Health(cancelledCtx)
	assert.Error(suite.T(), err)

	// Query should fail with cancelled context
	var result int
	err = suite.client.Pool.QueryRow(cancelledCtx, "SELECT 1").Scan(&result)
	assert.Error(suite.T(), err)
}

// TestPostgreSQLClient_Timeout tests timeout behavior
func (suite *PostgreSQLTestSuite) TestPostgreSQLClient_Timeout() {
	// Test with very short timeout
	timeoutCtx, cancel := context.WithTimeout(suite.ctx, 1*time.Nanosecond)
	defer cancel()

	// Wait a bit to ensure timeout
	time.Sleep(1 * time.Millisecond)

	// Health check should fail with timeout
	err := suite.client.Health(timeoutCtx)
	assert.Error(suite.T(), err)
}

// Helper functions

func (suite *PostgreSQLTestSuite) cleanupTestData() {
	// Clean up all test data
	tables := []string{
		"user_relationships", "user_roles", "roles", "contact_requests",
		"friend_requests", "request_votes", "bubble_requests", "call_sessions",
		"media_files", "notifications", "bubble_members", "bubbles", "users",
	}

	for _, table := range tables {
		_, err := suite.client.Pool.Exec(suite.ctx, fmt.Sprintf("DELETE FROM %s", table))
		if err != nil {
			suite.logger.Warn("Failed to clean up table", zap.String("table", table), zap.Error(err))
		}
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func parseInt(s string) int {
	var i int
	fmt.Sscanf(s, "%d", &i)
	return i
}

// Run the test suite
func TestPostgreSQLTestSuite(t *testing.T) {
	suite.Run(t, new(PostgreSQLTestSuite))
}
