//go:build bench
// +build bench

package performance

import (
    "net/http/httptest"
    "testing"

    "hopenbackend/cmd" // assuming main package path
)

// BenchmarkAPI exercises a representative set of endpoints under httptest.
// It requires Postgres etc. running via `make infra-up`.
func BenchmarkAPI(b *testing.B) {
    app, err := cmd.NewTestApplication() // helper that builds router with mocks
    if err != nil {
        b.<PERSON>p("router build failed: ", err)
    }
    router := app.Router()
    srv := httptest.NewServer(router)
    defer srv.Close()

    endpoints := []string{"/health", "/api/v1/auth/login", "/api/v1/bubbles"}

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        url := srv.URL + endpoints[i%len(endpoints)]
        resp, err := app.Client().Get(url)
        if err != nil || resp.StatusCode >= 500 {
            b.Fatalf("request failed: %v status=%d", err, resp.StatusCode)
        }
        resp.Body.Close()
    }
} 