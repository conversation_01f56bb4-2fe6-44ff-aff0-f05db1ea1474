//go:build integration
// +build integration

package performance

import (
    "context"
    "testing"
    "time"

    "github.com/nats-io/nats.go"
)

func TestJetStreamExpiryFlow(t *testing.T) {
    nc, err := nats.Connect(nats.DefaultURL)
    if err != nil {
        t.Skip("NATS not running: ", err)
    }
    defer nc.Drain()

    js, _ := nc.JetStream()

    // Ensure stream exists
    _, _ = js.AddStream(&nats.StreamConfig{Name: "TEST_CRON", Subjects: []string{"test.expire"}, Retention: nats.WorkQueuePolicy, Storage: nats.MemoryStorage})

    // Schedule message 2s in the future
    hdr := nats.Header{}
    expiry := time.Now().Add(2 * time.Second)
    hdr.Set("Nats-Not-Before", expiry.UTC().Format(time.RFC3339))
    _, err = js.PublishMsg(&nats.Msg{Subject: "test.expire", Header: hdr, Data: []byte("test")})
    if err != nil {
        t.Fatalf("publish failed: %v", err)
    }

    sub, _ := js.PullSubscribe("test.expire", "durable")
    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()

    msgs, err := sub.Fetch(1, nats.Context(ctx))
    if err != nil {
        t.Fatalf("fetch err: %v", err)
    }
    if len(msgs) != 1 {
        t.Fatalf("expected 1 msg got %d", len(msgs))
    }
} 