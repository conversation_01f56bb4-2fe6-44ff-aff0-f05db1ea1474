import http from 'k6/http';
import { sleep, check } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';

// Custom metrics
const authSuccessRate = new Rate('auth_success_rate');
const mqttAuthSuccessRate = new Rate('mqtt_auth_success_rate');
const mediaUploadSuccessRate = new Rate('media_upload_success_rate');
const notificationSuccessRate = new Rate('notification_success_rate');
const responseTimeAuth = new Trend('response_time_auth');
const responseTimeMedia = new Trend('response_time_media');
const responseTimeNotification = new Trend('response_time_notification');

export const options = {
  scenarios: {
    // Light load test
    light_load: {
      executor: 'constant-vus',
      vus: 10,
      duration: '2m',
      tags: { test_type: 'light' },
    },
    // Stress test
    stress_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 50 },
        { duration: '2m', target: 100 },
        { duration: '1m', target: 0 },
      ],
      tags: { test_type: 'stress' },
    },
    // Spike test
    spike_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '30s', target: 20 },
        { duration: '30s', target: 200 }, // Spike
        { duration: '30s', target: 20 },
      ],
      tags: { test_type: 'spike' },
    },
  },
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests should be below 2s
    http_req_failed: ['rate<0.1'], // Error rate should be below 10%
    auth_success_rate: ['rate>0.8'], // Auth success rate should be above 80%
    mqtt_auth_success_rate: ['rate>0.8'], // MQTT auth success rate should be above 80%
  },
};

const BASE_URL = __ENV.BASE_URL || 'https://10.0.0.81:4000';
const KRATOS_URL = __ENV.KRATOS_URL || 'http://10.0.0.81:4433';

// Test data
const testUsers = [];
let userCounter = 0;

export function setup() {
  console.log('Setting up load test...');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Kratos URL: ${KRATOS_URL}`);
  
  // Create some test users for the load test
  const setupUsers = [];
  for (let i = 0; i < 5; i++) {
    const timestamp = Date.now() + i;
    const user = {
      email: `loadtest${timestamp}@example.com`,
      username: `loaduser${timestamp}`,
      password: 'LoadTest123!',
      first_name: 'Load',
      last_name: 'Test',
    };
    
    // Register the user
    const response = http.post(`${BASE_URL}/api/v1/auth/register`, JSON.stringify(user), {
      headers: { 'Content-Type': 'application/json' },
    });
    
    if (response.status === 201) {
      const result = JSON.parse(response.body);
      setupUsers.push({
        ...user,
        access_token: result.access_token,
        user_id: result.user.id,
      });
      console.log(`Created test user: ${user.email}`);
    }
  }
  
  return { users: setupUsers };
}

export default function (data) {
  const testType = __ENV.TEST_TYPE || 'mixed';
  
  switch (testType) {
    case 'auth':
      testAuthentication(data);
      break;
    case 'mqtt':
      testMQTTAuth(data);
      break;
    case 'media':
      testMediaService(data);
      break;
    case 'notifications':
      testNotificationService(data);
      break;
    default:
      testMixedWorkload(data);
  }
  
  sleep(Math.random() * 2 + 1); // Random sleep between 1-3 seconds
}

function testAuthentication(data) {
  const user = data.users[userCounter % data.users.length];
  userCounter++;
  
  // Test login
  const loginPayload = {
    email: user.email,
    password: user.password,
  };
  
  const startTime = Date.now();
  const response = http.post(`${BASE_URL}/api/v1/auth/login`, JSON.stringify(loginPayload), {
    headers: { 'Content-Type': 'application/json' },
  });
  const duration = Date.now() - startTime;
  
  responseTimeAuth.add(duration);
  
  const success = check(response, {
    'login status is 200': (r) => r.status === 200,
    'login response has access_token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.access_token !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  authSuccessRate.add(success);
  
  if (success) {
    const result = JSON.parse(response.body);
    const accessToken = result.access_token;
    
    // Test authenticated endpoint
    const profileResponse = http.get(`${BASE_URL}/api/v1/auth/profile`, {
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
    });
    
    check(profileResponse, {
      'profile status is 200': (r) => r.status === 200,
    });
  }
}

function testMQTTAuth(data) {
  const user = data.users[userCounter % data.users.length];
  userCounter++;
  
  if (!user.access_token) return;
  
  const mqttPayload = {
    username: user.user_id,
    password: user.access_token,
    clientid: `loadtest_client_${Date.now()}`,
    topic: `hopen/requests/${user.user_id}`,
    action: 'subscribe',
  };
  
  const response = http.post(`${BASE_URL}/api/v1/auth/mqtt`, JSON.stringify(mqttPayload), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  const success = check(response, {
    'mqtt auth status is 200': (r) => r.status === 200,
    'mqtt auth result is allow': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.result === 'allow';
      } catch {
        return false;
      }
    },
  });
  
  mqttAuthSuccessRate.add(success);
}

function testMediaService(data) {
  const user = data.users[userCounter % data.users.length];
  userCounter++;
  
  if (!user.access_token) return;
  
  // Test generate upload URL
  const uploadPayload = {
    file_name: 'loadtest.png',
    content_type: 'image/png',
    file_size: 1024,
  };
  
  const startTime = Date.now();
  const response = http.post(`${BASE_URL}/api/v1/media/generate-upload-url`, JSON.stringify(uploadPayload), {
    headers: { 
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${user.access_token}`,
    },
  });
  const duration = Date.now() - startTime;
  
  responseTimeMedia.add(duration);
  
  const success = check(response, {
    'media upload url status is 200': (r) => r.status === 200,
    'media upload url response has upload_url': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.upload_url !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  mediaUploadSuccessRate.add(success);
  
  // Test user media list
  const listResponse = http.get(`${BASE_URL}/api/v1/media/user/${user.user_id}`, {
    headers: { 
      'Authorization': `Bearer ${user.access_token}`,
    },
  });
  
  check(listResponse, {
    'media list status is 200': (r) => r.status === 200,
  });
}

function testNotificationService(data) {
  const user = data.users[userCounter % data.users.length];
  userCounter++;
  
  if (!user.access_token) return;
  
  // Test FCM token registration
  const tokenPayload = {
    token: `fake_fcm_token_loadtest_${Date.now()}`,
    device_id: `loadtest_device_${Date.now()}`,
    platform: 'android',
  };
  
  const startTime = Date.now();
  const response = http.post(`${BASE_URL}/api/v1/notifications/fcm/token`, JSON.stringify(tokenPayload), {
    headers: { 
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${user.access_token}`,
    },
  });
  const duration = Date.now() - startTime;
  
  responseTimeNotification.add(duration);
  
  const success = check(response, {
    'fcm token registration status is 200': (r) => r.status === 200,
  });
  
  notificationSuccessRate.add(success);
  
  // Test get notifications
  const notificationsResponse = http.get(`${BASE_URL}/api/v1/notifications`, {
    headers: { 
      'Authorization': `Bearer ${user.access_token}`,
    },
  });
  
  check(notificationsResponse, {
    'notifications status is 200': (r) => r.status === 200,
  });
  
  // Test unread count
  const unreadResponse = http.get(`${BASE_URL}/api/v1/notifications/unread-count`, {
    headers: { 
      'Authorization': `Bearer ${user.access_token}`,
    },
  });
  
  check(unreadResponse, {
    'unread count status is 200': (r) => r.status === 200,
  });
}

function testMixedWorkload(data) {
  const workloadType = Math.random();
  
  if (workloadType < 0.4) {
    testAuthentication(data);
  } else if (workloadType < 0.6) {
    testMQTTAuth(data);
  } else if (workloadType < 0.8) {
    testMediaService(data);
  } else {
    testNotificationService(data);
  }
}

export function teardown(data) {
  console.log('Tearing down load test...');
  console.log(`Created ${data.users.length} test users`);
}
