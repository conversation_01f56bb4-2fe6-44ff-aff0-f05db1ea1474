#!/bin/bash

# Load Testing Script for Hopen Backend
# This script runs various load tests using K6

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="${BASE_URL:-https://*********:4000}"
KRATOS_URL="${KRATOS_URL:-http://*********:4433}"
RESULTS_DIR="/Users/<USER>/Hopen/hopenbackend/results"

echo -e "${BLUE}🚀 Hopen Backend Load Testing Suite${NC}"
echo -e "${BLUE}====================================${NC}"
echo ""
echo -e "Base URL: ${GREEN}$BASE_URL${NC}"
echo -e "Kratos URL: ${GREEN}$KRATOS_URL${NC}"
echo ""

# Create results directory
mkdir -p "/Users/<USER>/Hopen/hopenbackend/$RESULTS_DIR"

# Function to run a specific test
run_test() {
    local test_name="$1"
    local test_type="$2"
    local script="$3"
    local duration="$4"
    
    echo -e "${YELLOW}📊 Running $test_name...${NC}"
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local result_file="/Users/<USER>/Hopen/hopenbackend/$RESULTS_DIR/${test_name}_${timestamp}.json"
    
    # Set environment variables
    export BASE_URL="$BASE_URL"
    export KRATOS_URL="$KRATOS_URL"
    export TEST_TYPE="$test_type"
    
    # Run K6 test
    if command -v k6 >/dev/null 2>&1; then
        k6 run \
            --out json="$result_file" \
            --duration="$duration" \
            "$script"
        
        echo -e "${GREEN}✅ $test_name completed. Results saved to: $result_file${NC}"
    else
        echo -e "${RED}❌ K6 not found. Please install K6 to run load tests.${NC}"
        echo -e "${YELLOW}💡 Install K6: https://k6.io/docs/getting-started/installation/${NC}"
        return 1
    fi
    
    echo ""
}

# Function to check if backend is accessible
check_backend() {
    echo -e "${YELLOW}🔍 Checking backend accessibility...${NC}"
    
    if curl -k -s "$BASE_URL/api/v1/social/test" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend is accessible${NC}"
        return 0
    else
        echo -e "${RED}❌ Backend is not accessible at $BASE_URL${NC}"
        echo -e "${YELLOW}💡 Make sure the backend is running and accessible${NC}"
        return 1
    fi
}

# Function to run basic health check
run_health_check() {
    echo -e "${YELLOW}🏥 Running basic health check...${NC}"
    
    # Test basic endpoint
    local response=$(curl -k -s -w "%{http_code}" "$BASE_URL/api/v1/social/test" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ Basic health check passed${NC}"
    else
        echo -e "${RED}❌ Basic health check failed (HTTP $response)${NC}"
        return 1
    fi
    
    echo ""
}

# Function to display usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [TEST_TYPE]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -u, --url      Set base URL (default: https://*********:4000)"
    echo "  -k, --kratos   Set Kratos URL (default: http://*********:4433)"
    echo ""
    echo "Test Types:"
    echo "  all            Run all load tests (default)"
    echo "  light          Run light load test (10 VUs for 2 minutes)"
    echo "  auth           Run authentication load test"
    echo "  mqtt           Run MQTT authentication load test"
    echo "  media          Run media service load test"
    echo "  notifications  Run notification service load test"
    echo "  stress         Run stress test (ramping up to 100 VUs)"
    echo "  spike          Run spike test (sudden load increase)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Run all tests"
    echo "  $0 light                     # Run light load test"
    echo "  $0 auth                      # Run authentication test"
    echo "  $0 -u https://api.hopen.app  # Use custom URL"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        -k|--kratos)
            KRATOS_URL="$2"
            shift 2
            ;;
        *)
            TEST_TYPE="$1"
            shift
            ;;
    esac
done

# Default test type
TEST_TYPE="${TEST_TYPE:-all}"

# Check if backend is accessible
if ! check_backend; then
    exit 1
fi

# Run health check
if ! run_health_check; then
    exit 1
fi

echo -e "${BLUE}🎯 Starting load tests...${NC}"
echo ""

# Run tests based on type
case "$TEST_TYPE" in
    all)
        echo -e "${YELLOW}🔄 Running all load tests...${NC}"
        run_test "light_load" "mixed" "comprehensive_load_test.js" "2m"
        run_test "auth_test" "auth" "comprehensive_load_test.js" "1m"
        run_test "mqtt_test" "mqtt" "comprehensive_load_test.js" "1m"
        run_test "media_test" "media" "comprehensive_load_test.js" "1m"
        run_test "notification_test" "notifications" "comprehensive_load_test.js" "1m"
        ;;
    light)
        run_test "light_load" "mixed" "comprehensive_load_test.js" "2m"
        ;;
    auth)
        run_test "auth_test" "auth" "comprehensive_load_test.js" "2m"
        ;;
    mqtt)
        run_test "mqtt_test" "mqtt" "comprehensive_load_test.js" "2m"
        ;;
    media)
        run_test "media_test" "media" "comprehensive_load_test.js" "2m"
        ;;
    notifications)
        run_test "notification_test" "notifications" "comprehensive_load_test.js" "2m"
        ;;
    stress)
        run_test "stress_test" "mixed" "comprehensive_load_test.js" "4m"
        ;;
    spike)
        run_test "spike_test" "mixed" "comprehensive_load_test.js" "2m"
        ;;
    *)
        echo -e "${RED}❌ Unknown test type: $TEST_TYPE${NC}"
        show_usage
        exit 1
        ;;
esac

echo -e "${GREEN}🎉 Load testing completed!${NC}"
echo -e "${BLUE}📊 Results are saved in: $RESULTS_DIR${NC}"
echo ""
echo -e "${YELLOW}💡 To analyze results, you can use:${NC}"
echo -e "   - K6 HTML report: k6 run --out web-dashboard script.js"
echo -e "   - JSON analysis tools or custom scripts"
echo ""
