package load

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	baseURL = "https://10.0.0.81:4000"
	kratosURL = "http://10.0.0.81:4433"
)

// LoadTestResult represents the results of a load test
type LoadTestResult struct {
	TotalRequests    int
	SuccessfulReqs   int
	FailedReqs       int
	AverageLatency   time.Duration
	MinLatency       time.Duration
	MaxLatency       time.Duration
	RequestsPerSec   float64
	ErrorRate        float64
}

// TestUser represents a test user for load testing
type TestUser struct {
	Email       string `json:"email"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	FirstName   string `json:"first_name"`
	LastName    string `json:"last_name"`
	AccessToken string
	UserID      string
}

// TestLightLoad runs a light load test with 10 concurrent users
func TestLightLoad(t *testing.T) {
	t.Log("🚀 Starting Light Load Test (10 concurrent users for 30 seconds)")
	
	result := runLoadTest(t, 10, 30*time.Second, testMixedWorkload)
	
	t.Logf("📊 Light Load Test Results:")
	t.Logf("   Total Requests: %d", result.TotalRequests)
	t.Logf("   Successful: %d (%.2f%%)", result.SuccessfulReqs, (float64(result.SuccessfulReqs)/float64(result.TotalRequests))*100)
	t.Logf("   Failed: %d (%.2f%%)", result.FailedReqs, result.ErrorRate*100)
	t.Logf("   Average Latency: %v", result.AverageLatency)
	t.Logf("   Min/Max Latency: %v / %v", result.MinLatency, result.MaxLatency)
	t.Logf("   Requests/sec: %.2f", result.RequestsPerSec)
	
	// Assertions for light load
	assert.True(t, result.ErrorRate < 0.1, "Error rate should be less than 10%")
	assert.True(t, result.AverageLatency < 2*time.Second, "Average latency should be less than 2 seconds")
}

// TestAuthenticationLoad tests authentication endpoints under load
func TestAuthenticationLoad(t *testing.T) {
	t.Log("🔐 Starting Authentication Load Test (20 concurrent users for 20 seconds)")
	
	result := runLoadTest(t, 20, 20*time.Second, testAuthenticationWorkload)
	
	t.Logf("📊 Authentication Load Test Results:")
	t.Logf("   Total Requests: %d", result.TotalRequests)
	t.Logf("   Successful: %d (%.2f%%)", result.SuccessfulReqs, (float64(result.SuccessfulReqs)/float64(result.TotalRequests))*100)
	t.Logf("   Failed: %d (%.2f%%)", result.FailedReqs, result.ErrorRate*100)
	t.Logf("   Average Latency: %v", result.AverageLatency)
	t.Logf("   Requests/sec: %.2f", result.RequestsPerSec)
	
	// Assertions for auth load
	assert.True(t, result.ErrorRate < 0.15, "Auth error rate should be less than 15%")
}

// TestMQTTAuthLoad tests MQTT authentication under load
func TestMQTTAuthLoad(t *testing.T) {
	t.Log("📡 Starting MQTT Authentication Load Test (15 concurrent users for 20 seconds)")
	
	result := runLoadTest(t, 15, 20*time.Second, testMQTTWorkload)
	
	t.Logf("📊 MQTT Auth Load Test Results:")
	t.Logf("   Total Requests: %d", result.TotalRequests)
	t.Logf("   Successful: %d (%.2f%%)", result.SuccessfulReqs, (float64(result.SuccessfulReqs)/float64(result.TotalRequests))*100)
	t.Logf("   Failed: %d (%.2f%%)", result.FailedReqs, result.ErrorRate*100)
	t.Logf("   Average Latency: %v", result.AverageLatency)
	t.Logf("   Requests/sec: %.2f", result.RequestsPerSec)
	
	// Assertions for MQTT auth load
	assert.True(t, result.ErrorRate < 0.2, "MQTT auth error rate should be less than 20%")
}

// TestMediaServiceLoad tests media service endpoints under load
func TestMediaServiceLoad(t *testing.T) {
	t.Log("📁 Starting Media Service Load Test (10 concurrent users for 20 seconds)")
	
	result := runLoadTest(t, 10, 20*time.Second, testMediaWorkload)
	
	t.Logf("📊 Media Service Load Test Results:")
	t.Logf("   Total Requests: %d", result.TotalRequests)
	t.Logf("   Successful: %d (%.2f%%)", result.SuccessfulReqs, (float64(result.SuccessfulReqs)/float64(result.TotalRequests))*100)
	t.Logf("   Failed: %d (%.2f%%)", result.FailedReqs, result.ErrorRate*100)
	t.Logf("   Average Latency: %v", result.AverageLatency)
	t.Logf("   Requests/sec: %.2f", result.RequestsPerSec)
	
	// Assertions for media service load
	assert.True(t, result.ErrorRate < 0.2, "Media service error rate should be less than 20%")
}

// TestNotificationServiceLoad tests notification service endpoints under load
func TestNotificationServiceLoad(t *testing.T) {
	t.Log("🔔 Starting Notification Service Load Test (15 concurrent users for 20 seconds)")
	
	result := runLoadTest(t, 15, 20*time.Second, testNotificationWorkload)
	
	t.Logf("📊 Notification Service Load Test Results:")
	t.Logf("   Total Requests: %d", result.TotalRequests)
	t.Logf("   Successful: %d (%.2f%%)", result.SuccessfulReqs, (float64(result.SuccessfulReqs)/float64(result.TotalRequests))*100)
	t.Logf("   Failed: %d (%.2f%%)", result.FailedReqs, result.ErrorRate*100)
	t.Logf("   Average Latency: %v", result.AverageLatency)
	t.Logf("   Requests/sec: %.2f", result.RequestsPerSec)
	
	// Assertions for notification service load
	assert.True(t, result.ErrorRate < 0.2, "Notification service error rate should be less than 20%")
}

// runLoadTest runs a load test with specified parameters
func runLoadTest(t *testing.T, concurrentUsers int, duration time.Duration, workloadFunc func(*TestUser) bool) LoadTestResult {
	// Setup test users
	users := setupTestUsers(t, concurrentUsers)
	require.True(t, len(users) > 0, "Should have at least one test user")
	
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	results := make([]time.Duration, 0)
	successCount := 0
	failCount := 0
	
	startTime := time.Now()
	endTime := startTime.Add(duration)
	
	// Start concurrent workers
	for i := 0; i < concurrentUsers; i++ {
		wg.Add(1)
		go func(userIndex int) {
			defer wg.Done()
			
			user := &users[userIndex%len(users)]
			
			for time.Now().Before(endTime) {
				reqStart := time.Now()
				success := workloadFunc(user)
				reqDuration := time.Since(reqStart)
				
				mu.Lock()
				results = append(results, reqDuration)
				if success {
					successCount++
				} else {
					failCount++
				}
				mu.Unlock()
				
				// Small delay between requests
				time.Sleep(100 * time.Millisecond)
			}
		}(i)
	}
	
	wg.Wait()
	totalDuration := time.Since(startTime)
	
	// Calculate statistics
	totalRequests := len(results)
	if totalRequests == 0 {
		return LoadTestResult{}
	}
	
	var totalLatency time.Duration
	minLatency := results[0]
	maxLatency := results[0]
	
	for _, latency := range results {
		totalLatency += latency
		if latency < minLatency {
			minLatency = latency
		}
		if latency > maxLatency {
			maxLatency = latency
		}
	}
	
	avgLatency := totalLatency / time.Duration(totalRequests)
	requestsPerSec := float64(totalRequests) / totalDuration.Seconds()
	errorRate := float64(failCount) / float64(totalRequests)
	
	return LoadTestResult{
		TotalRequests:  totalRequests,
		SuccessfulReqs: successCount,
		FailedReqs:     failCount,
		AverageLatency: avgLatency,
		MinLatency:     minLatency,
		MaxLatency:     maxLatency,
		RequestsPerSec: requestsPerSec,
		ErrorRate:      errorRate,
	}
}

// setupTestUsers creates test users for load testing
func setupTestUsers(t *testing.T, count int) []TestUser {
	users := make([]TestUser, 0, count)
	
	for i := 0; i < count && i < 5; i++ { // Limit to 5 users to avoid overwhelming the system
		timestamp := time.Now().UnixNano() + int64(i)
		user := TestUser{
			Email:     fmt.Sprintf("<EMAIL>", timestamp),
			Username:  fmt.Sprintf("loaduser%d", timestamp),
			Password:  "LoadTest123!",
			FirstName: "Load",
			LastName:  "Test",
		}
		
		// Register the user
		if registerUser(t, &user) {
			users = append(users, user)
		}
	}
	
	t.Logf("Created %d test users for load testing", len(users))
	return users
}

// registerUser registers a test user and gets access token
func registerUser(t *testing.T, user *TestUser) bool {
	payload, _ := json.Marshal(user)
	
	resp, err := makeLoadTestRequest("POST", baseURL+"/api/v1/auth/register", bytes.NewBuffer(payload), "")
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	if resp.StatusCode == 201 {
		var result map[string]interface{}
		if json.NewDecoder(resp.Body).Decode(&result) == nil {
			if accessToken, ok := result["access_token"].(string); ok {
				user.AccessToken = accessToken
				if userObj, ok := result["user"].(map[string]interface{}); ok {
					if userID, ok := userObj["id"].(string); ok {
						user.UserID = userID
						return true
					}
				}
			}
		}
	}
	
	return false
}

// Workload functions
func testMixedWorkload(user *TestUser) bool {
	// Randomly choose a workload type
	switch time.Now().UnixNano() % 4 {
	case 0:
		return testAuthenticationWorkload(user)
	case 1:
		return testMQTTWorkload(user)
	case 2:
		return testMediaWorkload(user)
	default:
		return testNotificationWorkload(user)
	}
}

func testAuthenticationWorkload(user *TestUser) bool {
	// Test login
	loginPayload := map[string]string{
		"email":    user.Email,
		"password": user.Password,
	}
	
	payload, _ := json.Marshal(loginPayload)
	resp, err := makeLoadTestRequest("POST", baseURL+"/api/v1/auth/login", bytes.NewBuffer(payload), "")
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == 200
}

func testMQTTWorkload(user *TestUser) bool {
	if user.AccessToken == "" {
		return false
	}
	
	mqttPayload := map[string]interface{}{
		"username": user.UserID,
		"password": user.AccessToken,
		"clientid": fmt.Sprintf("loadtest_client_%d", time.Now().UnixNano()),
		"topic":    fmt.Sprintf("hopen/requests/%s", user.UserID),
		"action":   "subscribe",
	}
	
	payload, _ := json.Marshal(mqttPayload)
	resp, err := makeLoadTestRequest("POST", baseURL+"/api/v1/auth/mqtt", bytes.NewBuffer(payload), "")
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == 200
}

func testMediaWorkload(user *TestUser) bool {
	if user.AccessToken == "" {
		return false
	}
	
	// Test generate upload URL
	uploadPayload := map[string]interface{}{
		"file_name":    "loadtest.png",
		"content_type": "image/png",
		"file_size":    1024,
	}
	
	payload, _ := json.Marshal(uploadPayload)
	resp, err := makeLoadTestRequest("POST", baseURL+"/api/v1/media/generate-upload-url", bytes.NewBuffer(payload), user.AccessToken)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == 200
}

func testNotificationWorkload(user *TestUser) bool {
	if user.AccessToken == "" {
		return false
	}
	
	// Test get notifications
	resp, err := makeLoadTestRequest("GET", baseURL+"/api/v1/notifications", nil, user.AccessToken)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == 200
}

// makeLoadTestRequest makes an HTTP request for load testing
func makeLoadTestRequest(method, url string, body io.Reader, token string) (*http.Response, error) {
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: tr,
	}
	
	return client.Do(req)
}
