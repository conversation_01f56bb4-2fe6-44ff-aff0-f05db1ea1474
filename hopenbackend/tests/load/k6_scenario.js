import http from 'k6/http';
import { sleep } from 'k6';
import { check } from 'k6';

export const options = {
  scenarios: {
    steady: {
      executor: 'constant-arrival-rate',
      rate: 1000,
      timeUnit: '1s',
      duration: '5m',
      preAllocatedVUs: 100,
      maxVUs: 2000,
    },
  },
};

const BASE = __ENV.HOST || 'http://localhost:8080';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || '';

function authHeaders() {
  return AUTH_TOKEN ? { headers: { Authorization: `Bearer ${AUTH_TOKEN}` } } : {};
}

export default function () {
  http.get(`${BASE}/health`);
  // Hit a representative authenticated endpoint if token provided
  if (AUTH_TOKEN) {
    const res = http.get(`${BASE}/api/v1/bubbles`, authHeaders());
    check(res, {
      'bubbles status 200': (r) => r.status === 200,
    });
  }
  sleep(0.1);
} 