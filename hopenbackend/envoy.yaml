# Envoy gRPC-Web Proxy Configuration
# This configuration enables <PERSON>voy to act as a gRPC-Web proxy,
# transcoding HTTP/1.1 requests from web clients to gRPC calls
# for the Hopen backend services.

admin:
  access_log_path: /tmp/admin_access.log
  address:
    socket_address:
      protocol: TCP
      address: 0.0.0.0
      port_value: 9901

static_resources:
  listeners:
  - name: listener_0
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 8080  # HTTP port for web clients
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: ingress_http
          route_config:
            name: local_route
            virtual_hosts:
            - name: backend
              domains:
              - "*"
              routes:
              # Health check endpoints
              - match:
                  prefix: "/health"
                route:
                  cluster: health_check
                  timeout: 5s
              
              - match:
                  prefix: "/ready"
                route:
                  cluster: health_check
                  timeout: 5s
              
              # User Service gRPC-Web transcoding
              - match:
                  prefix: "/hopen.user.v1.UserService/"
                route:
                  cluster: user_service
                  timeout: 30s
              
              # Bubble Service gRPC-Web transcoding
              - match:
                  prefix: "/hopen.bubble.v1.BubbleService/"
                route:
                  cluster: bubble_service
                  timeout: 30s
              
              # Contact Service gRPC-Web transcoding
              - match:
                  prefix: "/hopen.contact.v1.ContactService/"
                route:
                  cluster: contact_service
                  timeout: 30s
              
              # Sync Service gRPC-Web transcoding
              - match:
                  prefix: "/hopen.sync.v1.SyncService/"
                route:
                  cluster: sync_service
                  timeout: 30s
              
              # Social Analytics Service gRPC-Web transcoding
              - match:
                  prefix: "/hopen.social_analytics.v1.SocialAnalyticsService/"
                route:
                  cluster: social_analytics_service
                  timeout: 60s
              
              # Notification Service gRPC-Web transcoding
              - match:
                  prefix: "/hopen.notification.v1.NotificationService/"
                route:
                  cluster: notification_service
                  timeout: 30s
              
              # CORS preflight requests
              - match:
                  prefix: "/"
                  headers:
                  - name: ":method"
                    exact_match: "OPTIONS"
                route:
                  cluster: cors_cluster
                  timeout: 5s
              
              # Default route for any other requests
              - match:
                  prefix: "/"
                direct_response:
                  status: 404
                  body:
                    inline_string: "Service not found"
          
          http_filters:
          # CORS filter for web browser compatibility
          - name: envoy.filters.http.cors
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.cors.v3.Cors
          
          # gRPC-Web filter for transcoding
          - name: envoy.filters.http.grpc_web
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.grpc_web.v3.GrpcWeb
          
          # Router filter (must be last)
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

  clusters:
  # Health check cluster for lightweight HTTP endpoints
  - name: health_check
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: health_check
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8081  # HTTP port for health checks (gRPC port + 1)

  # User Service cluster
  - name: user_service
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    http2_protocol_options: {}  # Enable HTTP/2 for gRPC
    load_assignment:
      cluster_name: user_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080  # gRPC port for user service
  
  # Bubble Service cluster
  - name: bubble_service
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    http2_protocol_options: {}
    load_assignment:
      cluster_name: bubble_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080
  
  # Contact Service cluster
  - name: contact_service
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    http2_protocol_options: {}
    load_assignment:
      cluster_name: contact_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080
  
  # Sync Service cluster
  - name: sync_service
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    http2_protocol_options: {}
    load_assignment:
      cluster_name: sync_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080
  
  # Social Analytics Service cluster
  - name: social_analytics_service
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    http2_protocol_options: {}
    load_assignment:
      cluster_name: social_analytics_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080
  
  # Notification Service cluster
  - name: notification_service
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    http2_protocol_options: {}
    load_assignment:
      cluster_name: notification_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080
  
  # CORS cluster for OPTIONS requests
  - name: cors_cluster
    connect_timeout: 5s
    type: STATIC
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: cors_cluster
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080

