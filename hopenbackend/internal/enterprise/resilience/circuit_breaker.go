package resilience

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"hopenbackend/pkg/config"
)

// Global metrics shared across all circuit breakers
var (
	globalMetrics     *CircuitBreakerMetrics
	globalMetricsOnce sync.Once
)

// State represents the circuit breaker state
type State int

const (
	StateClosed State = iota
	StateHalfOpen
	StateOpen
)

func (s State) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateHalfOpen:
		return "HALF_OPEN"
	case StateOpen:
		return "OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	name          string
	config        Config
	state         State
	failureCount  int
	successCount  int
	lastFailTime  time.Time
	nextAttempt   time.Time
	mutex         sync.RWMutex
	metrics       *CircuitBreakerMetrics
	onStateChange func(name string, from State, to State)
}

// Config holds circuit breaker configuration
type Config struct {
	Name          string
	MaxRequests   uint32
	Interval      time.Duration
	Timeout       time.Duration
	ReadyToTrip   func(counts Counts) bool
	OnStateChange func(name string, from State, to State)
}

// Counts holds the circuit breaker counts
type Counts struct {
	Requests             uint32
	TotalSuccesses       uint32
	TotalFailures        uint32
	ConsecutiveSuccesses uint32
	ConsecutiveFailures  uint32
}

// CircuitBreakerMetrics holds metrics for circuit breakers
type CircuitBreakerMetrics struct {
	requests     *prometheus.CounterVec
	failures     *prometheus.CounterVec
	successes    *prometheus.CounterVec
	stateChanges *prometheus.CounterVec
	currentState *prometheus.GaugeVec
	duration     *prometheus.HistogramVec
}

// getGlobalMetrics returns the global metrics instance (singleton)
func getGlobalMetrics() *CircuitBreakerMetrics {
	globalMetricsOnce.Do(func() {
		globalMetrics = &CircuitBreakerMetrics{
			requests: promauto.NewCounterVec(prometheus.CounterOpts{
				Name: "hopen_circuit_breaker_requests_total",
				Help: "Total number of requests through circuit breaker",
			}, []string{"name", "state"}),
			failures: promauto.NewCounterVec(prometheus.CounterOpts{
				Name: "hopen_circuit_breaker_failures_total",
				Help: "Total number of failures in circuit breaker",
			}, []string{"name"}),
			successes: promauto.NewCounterVec(prometheus.CounterOpts{
				Name: "hopen_circuit_breaker_successes_total",
				Help: "Total number of successes in circuit breaker",
			}, []string{"name"}),
			stateChanges: promauto.NewCounterVec(prometheus.CounterOpts{
				Name: "hopen_circuit_breaker_state_changes_total",
				Help: "Total number of state changes in circuit breaker",
			}, []string{"name", "from", "to"}),
			currentState: promauto.NewGaugeVec(prometheus.GaugeOpts{
				Name: "hopen_circuit_breaker_current_state",
				Help: "Current state of circuit breaker (0=CLOSED, 1=HALF_OPEN, 2=OPEN)",
			}, []string{"name"}),
			duration: promauto.NewHistogramVec(prometheus.HistogramOpts{
				Name:    "hopen_circuit_breaker_duration_seconds",
				Help:    "Duration of requests through circuit breaker",
				Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0},
			}, []string{"name", "state", "result"}),
		}
	})
	return globalMetrics
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(config Config) *CircuitBreaker {
	// Use global metrics to avoid duplicate registration
	metrics := getGlobalMetrics()

	// Set default values
	if config.MaxRequests == 0 {
		config.MaxRequests = 1
	}
	if config.Interval == 0 {
		config.Interval = 60 * time.Second
	}
	if config.Timeout == 0 {
		config.Timeout = 60 * time.Second
	}
	if config.ReadyToTrip == nil {
		config.ReadyToTrip = func(counts Counts) bool {
			return counts.ConsecutiveFailures > 5
		}
	}

	cb := &CircuitBreaker{
		name:          config.Name,
		config:        config,
		state:         StateClosed,
		metrics:       metrics,
		onStateChange: config.OnStateChange,
	}

	// Initialize metrics
	cb.metrics.currentState.WithLabelValues(cb.name).Set(float64(StateClosed))

	return cb
}

// Execute executes the given function with circuit breaker protection
func (cb *CircuitBreaker) Execute(fn func() (interface{}, error)) (interface{}, error) {
	start := time.Now()

	cb.mutex.Lock()
	state := cb.state
	cb.mutex.Unlock()

	// Record request metric
	cb.metrics.requests.WithLabelValues(cb.name, state.String()).Inc()

	// Check if circuit breaker allows the request
	if !cb.allowRequest() {
		cb.metrics.duration.WithLabelValues(cb.name, state.String(), "rejected").Observe(time.Since(start).Seconds())
		return nil, fmt.Errorf("circuit breaker %s is open", cb.name)
	}

	// Execute the function
	result, err := fn()
	duration := time.Since(start).Seconds()

	// Record the result
	if err != nil {
		cb.onFailure()
		cb.metrics.failures.WithLabelValues(cb.name).Inc()
		cb.metrics.duration.WithLabelValues(cb.name, state.String(), "failure").Observe(duration)
	} else {
		cb.onSuccess()
		cb.metrics.successes.WithLabelValues(cb.name).Inc()
		cb.metrics.duration.WithLabelValues(cb.name, state.String(), "success").Observe(duration)
	}

	return result, err
}

// allowRequest checks if the request should be allowed
func (cb *CircuitBreaker) allowRequest() bool {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		if time.Now().After(cb.nextAttempt) {
			cb.setState(StateHalfOpen)
			return true
		}
		return false
	case StateHalfOpen:
		return cb.successCount < int(cb.config.MaxRequests)
	default:
		return false
	}
}

// onSuccess handles successful execution
func (cb *CircuitBreaker) onSuccess() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount = 0
	cb.lastFailTime = time.Time{}

	switch cb.state {
	case StateHalfOpen:
		cb.successCount++
		if cb.successCount >= int(cb.config.MaxRequests) {
			cb.setState(StateClosed)
		}
	}
}

// onFailure handles failed execution
func (cb *CircuitBreaker) onFailure() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount++
	cb.lastFailTime = time.Now()

	switch cb.state {
	case StateClosed:
		counts := cb.getCounts()
		if cb.config.ReadyToTrip(counts) {
			cb.setState(StateOpen)
		}
	case StateHalfOpen:
		cb.setState(StateOpen)
	}
}

// setState changes the circuit breaker state
func (cb *CircuitBreaker) setState(state State) {
	if cb.state == state {
		return
	}

	oldState := cb.state
	cb.state = state

	// Reset counters based on new state
	switch state {
	case StateClosed:
		cb.failureCount = 0
		cb.successCount = 0
	case StateOpen:
		cb.nextAttempt = time.Now().Add(cb.config.Timeout)
		cb.successCount = 0
	case StateHalfOpen:
		cb.successCount = 0
	}

	// Record state change
	cb.metrics.stateChanges.WithLabelValues(cb.name, oldState.String(), state.String()).Inc()
	cb.metrics.currentState.WithLabelValues(cb.name).Set(float64(state))

	// Call state change callback
	if cb.onStateChange != nil {
		go cb.onStateChange(cb.name, oldState, state)
	}
}

// getCounts returns the current counts
func (cb *CircuitBreaker) getCounts() Counts {
	return Counts{
		Requests:             uint32(cb.failureCount + cb.successCount),
		TotalFailures:        uint32(cb.failureCount),
		TotalSuccesses:       uint32(cb.successCount),
		ConsecutiveFailures:  uint32(cb.failureCount),
		ConsecutiveSuccesses: uint32(cb.successCount),
	}
}

// GetState returns the current state
func (cb *CircuitBreaker) GetState() State {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// GetName returns the circuit breaker name
func (cb *CircuitBreaker) GetName() string {
	return cb.name
}

// CircuitBreakerManager manages multiple circuit breakers
type CircuitBreakerManager struct {
	circuitBreakers map[string]*CircuitBreaker
	logger          *zap.Logger
	mutex           sync.RWMutex
}

// NewCircuitBreakerManager creates a new circuit breaker manager
func NewCircuitBreakerManager(config *config.Config, logger *zap.Logger) *CircuitBreakerManager {
	manager := &CircuitBreakerManager{
		circuitBreakers: make(map[string]*CircuitBreaker),
		logger:          logger,
	}

	// Initialize circuit breakers for different services
	if config.Enterprise.Resilience.Enabled {
		for name, cbConfig := range config.Enterprise.Resilience.CircuitBreakers {
			manager.AddCircuitBreaker(name, Config{
				Name:        name,
				MaxRequests: 3,
				Interval:    60 * time.Second,
				Timeout:     cbConfig.Timeout,
				ReadyToTrip: func(counts Counts) bool {
					return counts.ConsecutiveFailures >= uint32(cbConfig.MaxFailures)
				},
				OnStateChange: func(name string, from State, to State) {
					logger.Info("Circuit breaker state changed",
						zap.String("name", name),
						zap.String("from", from.String()),
						zap.String("to", to.String()))
				},
			})
		}
	}

	return manager
}

// AddCircuitBreaker adds a new circuit breaker
func (cbm *CircuitBreakerManager) AddCircuitBreaker(name string, config Config) {
	cbm.mutex.Lock()
	defer cbm.mutex.Unlock()

	config.Name = name
	cbm.circuitBreakers[name] = NewCircuitBreaker(config)
	cbm.logger.Info("Circuit breaker added", zap.String("name", name))
}

// GetCircuitBreaker returns a circuit breaker by name
func (cbm *CircuitBreakerManager) GetCircuitBreaker(name string) *CircuitBreaker {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	return cbm.circuitBreakers[name]
}

// ExecuteWithCircuitBreaker executes a function with circuit breaker protection
func (cbm *CircuitBreakerManager) ExecuteWithCircuitBreaker(name string, fn func() (interface{}, error)) (interface{}, error) {
	cb := cbm.GetCircuitBreaker(name)
	if cb == nil {
		return fn() // Execute without circuit breaker if not found
	}

	return cb.Execute(fn)
}

// MiddlewareHandler returns a Gin middleware for circuit breaker protection
func (cbm *CircuitBreakerManager) MiddlewareHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Determine service name from path
		serviceName := cbm.getServiceNameFromPath(c.Request.URL.Path)

		if serviceName != "" {
			cb := cbm.GetCircuitBreaker(serviceName)
			if cb != nil && cb.GetState() == StateOpen {
				c.JSON(503, gin.H{"error": "Service temporarily unavailable"})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// getServiceNameFromPath determines service name from request path
func (cbm *CircuitBreakerManager) getServiceNameFromPath(path string) string {
	// Map paths to service names for circuit breaker protection
	if len(path) < 8 { // "/api/v1/"
		return ""
	}

	pathSegments := path[8:] // Remove "/api/v1/"

	switch {
	case pathSegments == "auth" || pathSegments[:4] == "auth":
		return "auth"
	case pathSegments == "users" || pathSegments[:5] == "users":
		return "postgresql"
	case pathSegments == "bubbles" || pathSegments[:7] == "bubbles":
		return "postgresql"
	case pathSegments == "contact" || pathSegments[:7] == "contact":
		return "postgresql" // Changed from arangodb
	case pathSegments == "friendship" || pathSegments[:10] == "friendship":
		return "postgresql" // Changed from arangodb
	case pathSegments == "social" || pathSegments[:6] == "social":
		return "postgresql" // Changed from arangodb
	case pathSegments == "realtime" || pathSegments[:8] == "realtime":
		return "scylladb"
	case pathSegments == "media" || pathSegments[:5] == "media":
		return "minio"
	default:
		return ""
	}
}

// UnaryServerInterceptor returns a gRPC unary server interceptor for circuit breaker protection
func (cbm *CircuitBreakerManager) UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		serviceName := cbm.getServiceNameFromMethod(info.FullMethod)

		if serviceName == "" {
			// No circuit breaker configured for this service
			return handler(ctx, req)
		}

		cb := cbm.GetCircuitBreaker(serviceName)
		if cb == nil {
			// Circuit breaker not found, execute normally
			return handler(ctx, req)
		}

		// Execute with circuit breaker protection
		result, err := cb.Execute(func() (interface{}, error) {
			return handler(ctx, req)
		})

		// Convert circuit breaker errors to gRPC errors
		if err != nil && strings.Contains(err.Error(), "circuit breaker") && strings.Contains(err.Error(), "is open") {
			return nil, status.Errorf(codes.Unavailable, "Service temporarily unavailable due to circuit breaker")
		}

		return result, err
	}
}

// getServiceNameFromMethod determines service name from gRPC method
func (cbm *CircuitBreakerManager) getServiceNameFromMethod(method string) string {
	// gRPC method format: /package.service/method
	// Example: /hopen.user.v1.UserService/GetUser

	if !strings.HasPrefix(method, "/") {
		return ""
	}

	// Remove leading slash
	method = method[1:]

	// Split by slash to get service and method
	parts := strings.Split(method, "/")
	if len(parts) != 2 {
		return ""
	}

	servicePart := parts[0]

	// Extract service name from package.service format
	// Example: hopen.user.v1.UserService -> user
	serviceParts := strings.Split(servicePart, ".")
	if len(serviceParts) < 2 {
		return ""
	}

	// Get the service name (second to last part before "Service")
	for i := len(serviceParts) - 1; i >= 0; i-- {
		part := serviceParts[i]
		if strings.HasSuffix(part, "Service") {
			// Remove "Service" suffix and convert to lowercase
			serviceName := strings.TrimSuffix(part, "Service")
			return strings.ToLower(serviceName)
		}
	}

	return ""
}
