package monitoring

import (
	"context"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// ProductionMetrics holds all production-ready metrics
type ProductionMetrics struct {
	// gRPC metrics
	GRPCRequestsTotal     *prometheus.CounterVec
	GRPCRequestDuration   *prometheus.HistogramVec
	GRPCRequestSize       *prometheus.HistogramVec
	GRPCResponseSize      *prometheus.HistogramVec
	GRPCActiveConnections prometheus.Gauge
	GRPCErrorsTotal       *prometheus.CounterVec

	// Database metrics
	DatabaseConnectionsActive prometheus.Gauge
	DatabaseConnectionsIdle   prometheus.Gauge
	DatabaseQueryDuration     *prometheus.HistogramVec
	DatabaseTransactionsTotal *prometheus.CounterVec
	DatabaseDeadlocksTotal    prometheus.Counter
	DatabaseSlowQueriesTotal  prometheus.Counter

	// External service metrics
	ExternalServiceRequestsTotal   *prometheus.CounterVec
	ExternalServiceRequestDuration *prometheus.HistogramVec
	ExternalServiceErrorsTotal     *prometheus.CounterVec
	ExternalServiceTimeoutsTotal   *prometheus.CounterVec

	// Circuit breaker metrics
	CircuitBreakerStateChanges *prometheus.CounterVec
	CircuitBreakerRequests     *prometheus.CounterVec
	CircuitBreakerFailures     *prometheus.CounterVec

	// Rate limiting metrics
	RateLimitHits     *prometheus.CounterVec
	RateLimitAllowed  *prometheus.CounterVec
	RateLimitRejected *prometheus.CounterVec

	// Security metrics
	AuthenticationAttempts  *prometheus.CounterVec
	AuthorizationFailures   *prometheus.CounterVec
	ValidationFailures      *prometheus.CounterVec
	SuspiciousActivityTotal *prometheus.CounterVec

	// Business metrics
	FileUploadsTotal         *prometheus.CounterVec
	FileUploadSize           *prometheus.HistogramVec
	MediaProcessingDuration  *prometheus.HistogramVec
	ThumbnailGenerationTotal *prometheus.CounterVec

	// System metrics
	MemoryUsage      prometheus.Gauge
	CPUUsage         prometheus.Gauge
	GoroutinesActive prometheus.Gauge
	GCDuration       prometheus.Histogram
}

// NewProductionMetrics creates and registers all production metrics
func NewProductionMetrics() *ProductionMetrics {
	return &ProductionMetrics{
		// gRPC metrics
		GRPCRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "grpc_requests_total",
				Help: "Total number of gRPC requests",
			},
			[]string{"service", "method", "status"},
		),
		GRPCRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_request_duration_seconds",
				Help:    "Duration of gRPC requests",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"service", "method"},
		),
		GRPCRequestSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_request_size_bytes",
				Help:    "Size of gRPC requests in bytes",
				Buckets: prometheus.ExponentialBuckets(1024, 2, 10), // 1KB to 512MB
			},
			[]string{"service", "method"},
		),
		GRPCResponseSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "grpc_response_size_bytes",
				Help:    "Size of gRPC responses in bytes",
				Buckets: prometheus.ExponentialBuckets(1024, 2, 10),
			},
			[]string{"service", "method"},
		),
		GRPCActiveConnections: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "grpc_active_connections",
				Help: "Number of active gRPC connections",
			},
		),
		GRPCErrorsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "grpc_errors_total",
				Help: "Total number of gRPC errors",
			},
			[]string{"service", "method", "code"},
		),

		// Database metrics
		DatabaseConnectionsActive: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "database_connections_active",
				Help: "Number of active database connections",
			},
		),
		DatabaseConnectionsIdle: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "database_connections_idle",
				Help: "Number of idle database connections",
			},
		),
		DatabaseQueryDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "database_query_duration_seconds",
				Help:    "Duration of database queries",
				Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10},
			},
			[]string{"operation", "table"},
		),
		DatabaseTransactionsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "database_transactions_total",
				Help: "Total number of database transactions",
			},
			[]string{"status"},
		),
		DatabaseDeadlocksTotal: promauto.NewCounter(
			prometheus.CounterOpts{
				Name: "database_deadlocks_total",
				Help: "Total number of database deadlocks",
			},
		),
		DatabaseSlowQueriesTotal: promauto.NewCounter(
			prometheus.CounterOpts{
				Name: "database_slow_queries_total",
				Help: "Total number of slow database queries",
			},
		),

		// External service metrics
		ExternalServiceRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "external_service_requests_total",
				Help: "Total number of external service requests",
			},
			[]string{"service", "operation"},
		),
		ExternalServiceRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "external_service_request_duration_seconds",
				Help:    "Duration of external service requests",
				Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30},
			},
			[]string{"service", "operation"},
		),
		ExternalServiceErrorsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "external_service_errors_total",
				Help: "Total number of external service errors",
			},
			[]string{"service", "operation", "error_type"},
		),
		ExternalServiceTimeoutsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "external_service_timeouts_total",
				Help: "Total number of external service timeouts",
			},
			[]string{"service", "operation"},
		),

		// Circuit breaker metrics
		CircuitBreakerStateChanges: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "circuit_breaker_state_changes_total",
				Help: "Total number of circuit breaker state changes",
			},
			[]string{"service", "from_state", "to_state"},
		),
		CircuitBreakerRequests: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "circuit_breaker_requests_total",
				Help: "Total number of circuit breaker requests",
			},
			[]string{"service", "state", "result"},
		),
		CircuitBreakerFailures: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "circuit_breaker_failures_total",
				Help: "Total number of circuit breaker failures",
			},
			[]string{"service"},
		),

		// Rate limiting metrics
		RateLimitHits: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "rate_limit_hits_total",
				Help: "Total number of rate limit checks",
			},
			[]string{"key_type", "operation"},
		),
		RateLimitAllowed: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "rate_limit_allowed_total",
				Help: "Total number of allowed requests",
			},
			[]string{"key_type", "operation"},
		),
		RateLimitRejected: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "rate_limit_rejected_total",
				Help: "Total number of rejected requests",
			},
			[]string{"key_type", "operation"},
		),

		// Security metrics
		AuthenticationAttempts: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "authentication_attempts_total",
				Help: "Total number of authentication attempts",
			},
			[]string{"method", "result"},
		),
		AuthorizationFailures: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "authorization_failures_total",
				Help: "Total number of authorization failures",
			},
			[]string{"service", "method", "reason"},
		),
		ValidationFailures: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "validation_failures_total",
				Help: "Total number of validation failures",
			},
			[]string{"service", "field", "error_type"},
		),
		SuspiciousActivityTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "suspicious_activity_total",
				Help: "Total number of suspicious activities detected",
			},
			[]string{"type", "severity"},
		),

		// Business metrics
		FileUploadsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "file_uploads_total",
				Help: "Total number of file uploads",
			},
			[]string{"file_type", "status"},
		),
		FileUploadSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "file_upload_size_bytes",
				Help:    "Size of uploaded files in bytes",
				Buckets: prometheus.ExponentialBuckets(1024, 2, 20), // 1KB to 512GB
			},
			[]string{"file_type"},
		),
		MediaProcessingDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "media_processing_duration_seconds",
				Help:    "Duration of media processing operations",
				Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60},
			},
			[]string{"operation", "file_type"},
		),
		ThumbnailGenerationTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "thumbnail_generation_total",
				Help: "Total number of thumbnail generation attempts",
			},
			[]string{"status"},
		),

		// System metrics
		MemoryUsage: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "memory_usage_bytes",
				Help: "Current memory usage in bytes",
			},
		),
		CPUUsage: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "cpu_usage_percent",
				Help: "Current CPU usage percentage",
			},
		),
		GoroutinesActive: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "goroutines_active",
				Help: "Number of active goroutines",
			},
		),
		GCDuration: promauto.NewHistogram(
			prometheus.HistogramOpts{
				Name:    "gc_duration_seconds",
				Help:    "Duration of garbage collection cycles",
				Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1},
			},
		),
	}
}

// gRPC Interceptors for automatic metrics collection

// UnaryServerInterceptor returns a gRPC unary server interceptor for metrics collection
func (pm *ProductionMetrics) UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		start := time.Now()

		// Extract service and method names
		service, method := extractServiceMethod(info.FullMethod)

		// Increment active connections
		pm.GRPCActiveConnections.Inc()
		defer pm.GRPCActiveConnections.Dec()

		// Call the handler
		resp, err := handler(ctx, req)

		// Record metrics
		duration := time.Since(start).Seconds()
		pm.GRPCRequestDuration.WithLabelValues(service, method).Observe(duration)

		// Record status
		grpcStatus := "OK"
		grpcCode := codes.OK
		if err != nil {
			if st, ok := status.FromError(err); ok {
				grpcCode = st.Code()
				grpcStatus = grpcCode.String()
			} else {
				grpcStatus = "UNKNOWN"
				grpcCode = codes.Unknown
			}
			pm.GRPCErrorsTotal.WithLabelValues(service, method, grpcStatus).Inc()
		}

		pm.GRPCRequestsTotal.WithLabelValues(service, method, grpcStatus).Inc()

		return resp, err
	}
}

// StreamServerInterceptor returns a gRPC stream server interceptor for metrics collection
func (pm *ProductionMetrics) StreamServerInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		start := time.Now()

		// Extract service and method names
		service, method := extractServiceMethod(info.FullMethod)

		// Increment active connections
		pm.GRPCActiveConnections.Inc()
		defer pm.GRPCActiveConnections.Dec()

		// Call the handler
		err := handler(srv, ss)

		// Record metrics
		duration := time.Since(start).Seconds()
		pm.GRPCRequestDuration.WithLabelValues(service, method).Observe(duration)

		// Record status
		grpcStatus := "OK"
		if err != nil {
			if st, ok := status.FromError(err); ok {
				grpcStatus = st.Code().String()
			} else {
				grpcStatus = "UNKNOWN"
			}
			pm.GRPCErrorsTotal.WithLabelValues(service, method, grpcStatus).Inc()
		}

		pm.GRPCRequestsTotal.WithLabelValues(service, method, grpcStatus).Inc()

		return err
	}
}

// Helper functions

// extractServiceMethod extracts service and method names from gRPC full method
func extractServiceMethod(fullMethod string) (service, method string) {
	// gRPC method format: /package.service/method
	// Example: /hopen.user.v1.UserService/GetUser

	if len(fullMethod) == 0 || fullMethod[0] != '/' {
		return "unknown", "unknown"
	}

	// Remove leading slash
	fullMethod = fullMethod[1:]

	// Split by slash
	parts := strings.Split(fullMethod, "/")
	if len(parts) != 2 {
		return "unknown", "unknown"
	}

	servicePart := parts[0]
	methodPart := parts[1]

	// Extract service name from package.service format
	serviceParts := strings.Split(servicePart, ".")
	if len(serviceParts) >= 2 {
		for i := len(serviceParts) - 1; i >= 0; i-- {
			part := serviceParts[i]
			if strings.HasSuffix(part, "Service") {
				service = strings.TrimSuffix(part, "Service")
				service = strings.ToLower(service)
				break
			}
		}
	}

	if service == "" {
		service = "unknown"
	}

	return service, methodPart
}

// Business metrics helpers

// RecordFileUpload records file upload metrics
func (pm *ProductionMetrics) RecordFileUpload(fileType string, size int64, success bool) {
	status := "success"
	if !success {
		status = "failure"
	}

	pm.FileUploadsTotal.WithLabelValues(fileType, status).Inc()
	if success {
		pm.FileUploadSize.WithLabelValues(fileType).Observe(float64(size))
	}
}

// RecordMediaProcessing records media processing metrics
func (pm *ProductionMetrics) RecordMediaProcessing(operation, fileType string, duration time.Duration) {
	pm.MediaProcessingDuration.WithLabelValues(operation, fileType).Observe(duration.Seconds())
}

// RecordThumbnailGeneration records thumbnail generation metrics
func (pm *ProductionMetrics) RecordThumbnailGeneration(success bool) {
	status := "success"
	if !success {
		status = "failure"
	}
	pm.ThumbnailGenerationTotal.WithLabelValues(status).Inc()
}

// Security metrics helpers

// RecordAuthenticationAttempt records authentication attempt metrics
func (pm *ProductionMetrics) RecordAuthenticationAttempt(method string, success bool) {
	result := "success"
	if !success {
		result = "failure"
	}
	pm.AuthenticationAttempts.WithLabelValues(method, result).Inc()
}

// RecordAuthorizationFailure records authorization failure metrics
func (pm *ProductionMetrics) RecordAuthorizationFailure(service, method, reason string) {
	pm.AuthorizationFailures.WithLabelValues(service, method, reason).Inc()
}

// RecordValidationFailure records validation failure metrics
func (pm *ProductionMetrics) RecordValidationFailure(service, field, errorType string) {
	pm.ValidationFailures.WithLabelValues(service, field, errorType).Inc()
}

// RecordSuspiciousActivity records suspicious activity metrics
func (pm *ProductionMetrics) RecordSuspiciousActivity(activityType, severity string) {
	pm.SuspiciousActivityTotal.WithLabelValues(activityType, severity).Inc()
}

// External service metrics helpers

// RecordExternalServiceRequest records external service request metrics
func (pm *ProductionMetrics) RecordExternalServiceRequest(service, operation string, duration time.Duration, err error) {
	pm.ExternalServiceRequestsTotal.WithLabelValues(service, operation).Inc()
	pm.ExternalServiceRequestDuration.WithLabelValues(service, operation).Observe(duration.Seconds())

	if err != nil {
		errorType := "unknown"
		if strings.Contains(err.Error(), "timeout") {
			errorType = "timeout"
			pm.ExternalServiceTimeoutsTotal.WithLabelValues(service, operation).Inc()
		} else if strings.Contains(err.Error(), "connection") {
			errorType = "connection"
		} else if strings.Contains(err.Error(), "auth") {
			errorType = "authentication"
		}
		pm.ExternalServiceErrorsTotal.WithLabelValues(service, operation, errorType).Inc()
	}
}

// Database metrics helpers

// RecordDatabaseQuery records database query metrics
func (pm *ProductionMetrics) RecordDatabaseQuery(operation, table string, duration time.Duration, slow bool) {
	pm.DatabaseQueryDuration.WithLabelValues(operation, table).Observe(duration.Seconds())
	if slow {
		pm.DatabaseSlowQueriesTotal.Inc()
	}
}

// RecordDatabaseTransaction records database transaction metrics
func (pm *ProductionMetrics) RecordDatabaseTransaction(success bool) {
	status := "success"
	if !success {
		status = "failure"
	}
	pm.DatabaseTransactionsTotal.WithLabelValues(status).Inc()
}

// RecordDatabaseDeadlock records database deadlock metrics
func (pm *ProductionMetrics) RecordDatabaseDeadlock() {
	pm.DatabaseDeadlocksTotal.Inc()
}

// Rate limiting metrics helpers

// RecordRateLimit records rate limiting metrics
func (pm *ProductionMetrics) RecordRateLimit(keyType, operation string, allowed bool) {
	pm.RateLimitHits.WithLabelValues(keyType, operation).Inc()
	if allowed {
		pm.RateLimitAllowed.WithLabelValues(keyType, operation).Inc()
	} else {
		pm.RateLimitRejected.WithLabelValues(keyType, operation).Inc()
	}
}
