package monitoring

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// SocialMetrics holds all social app-specific metrics
type SocialMetrics struct {
	// Bubble metrics
	BubbleCreationRate      prometheus.Counter
	BubbleExpiryEvents      prometheus.Counter
	BubbleExtensionEvents   prometheus.Counter
	BubbleMembershipChanges *prometheus.CounterVec
	BubbleCapacityUtilization *prometheus.GaugeVec
	BubbleTimeToExpiry      *prometheus.GaugeVec

	// Friendship metrics
	FriendRequestsGenerated prometheus.Counter
	FriendRequestsAccepted  prometheus.Counter
	FriendshipCreationRate  prometheus.Counter
	BubbleToFriendshipConversion *prometheus.CounterVec
	FriendshipGenerationLatency prometheus.Histogram

	// Contact metrics
	ContactRequestsSent     prometheus.Counter
	ContactRequestsAccepted prometheus.Counter
	ContactRelationships    prometheus.Gauge

	// Chat metrics
	MessagesSent            *prometheus.CounterVec
	MessageDeliveryLatency  *prometheus.HistogramVec
	ActiveChatSessions      prometheus.Gauge

	// User engagement metrics
	UserEngagementScore     *prometheus.GaugeVec
	DailyActiveUsers        prometheus.Gauge
	BubbleParticipationRate prometheus.Histogram

	// API metrics
	HTTPRequestsTotal       *prometheus.CounterVec
	HTTPRequestDuration     *prometheus.HistogramVec
	HTTPRequestSize         *prometheus.HistogramVec
	HTTPResponseSize        *prometheus.HistogramVec
}

// NewSocialMetrics creates a new social metrics instance
func NewSocialMetrics() *SocialMetrics {
	return &SocialMetrics{
		// Bubble metrics
		BubbleCreationRate: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_bubbles_created_total",
			Help: "Total number of bubbles created",
		}),
		BubbleExpiryEvents: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_bubbles_expired_total",
			Help: "Total number of bubbles that expired",
		}),
		BubbleExtensionEvents: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_bubble_extensions_total",
			Help: "Total number of bubble expiry extensions",
		}),
		BubbleMembershipChanges: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_bubble_membership_changes_total",
			Help: "Total number of bubble membership changes",
		}, []string{"action", "bubble_id"}),
		BubbleCapacityUtilization: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_bubble_capacity_utilization_ratio",
			Help: "Current capacity utilization of bubbles",
		}, []string{"current_members", "max_members"}),
		BubbleTimeToExpiry: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_bubble_time_to_expiry_hours",
			Help: "Time until bubble expiry in hours",
		}, []string{"bubble_id"}),

		// Friendship metrics
		FriendRequestsGenerated: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_friend_requests_generated_total",
			Help: "Total number of auto-generated friend requests",
		}),
		FriendRequestsAccepted: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_friend_requests_accepted_total",
			Help: "Total number of friend requests accepted",
		}),
		FriendshipCreationRate: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_friendships_created_total",
			Help: "Total number of friendships created",
		}),
		BubbleToFriendshipConversion: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_bubble_to_friendship_conversions_total",
			Help: "Total number of bubble expiry to friendship conversions",
		}, []string{"bubble_id"}),
		FriendshipGenerationLatency: promauto.NewHistogram(prometheus.HistogramOpts{
			Name: "hopen_friendship_generation_duration_seconds",
			Help: "Time taken to generate friendships from bubble expiry",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0},
		}),

		// Contact metrics
		ContactRequestsSent: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_contact_requests_sent_total",
			Help: "Total number of contact requests sent",
		}),
		ContactRequestsAccepted: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_contact_requests_accepted_total",
			Help: "Total number of contact requests accepted",
		}),
		ContactRelationships: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_contact_relationships_current",
			Help: "Current number of active contact relationships",
		}),

		// Chat metrics
		MessagesSent: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_messages_sent_total",
			Help: "Total number of messages sent",
		}, []string{"bubble_id", "message_type"}),
		MessageDeliveryLatency: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name: "hopen_message_delivery_duration_seconds",
			Help: "Message delivery latency",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0},
		}, []string{"delivery_method"}),
		ActiveChatSessions: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_active_chat_sessions_current",
			Help: "Current number of active chat sessions",
		}),

		// User engagement metrics
		UserEngagementScore: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_user_engagement_score",
			Help: "User engagement score (0-100)",
		}, []string{"user_id"}),
		DailyActiveUsers: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_daily_active_users_current",
			Help: "Current number of daily active users",
		}),
		BubbleParticipationRate: promauto.NewHistogram(prometheus.HistogramOpts{
			Name: "hopen_bubble_participation_rate",
			Help: "Rate of user participation in bubbles",
			Buckets: []float64{0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0},
		}),

		// API metrics
		HTTPRequestsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_http_requests_total",
			Help: "Total number of HTTP requests",
		}, []string{"method", "endpoint", "status_code"}),
		HTTPRequestDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name: "hopen_http_request_duration_seconds",
			Help: "HTTP request duration",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0},
		}, []string{"method", "endpoint"}),
		HTTPRequestSize: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name: "hopen_http_request_size_bytes",
			Help: "HTTP request size in bytes",
			Buckets: []float64{1024, 10240, 102400, 1048576, 10485760},
		}, []string{"method", "endpoint"}),
		HTTPResponseSize: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name: "hopen_http_response_size_bytes",
			Help: "HTTP response size in bytes",
			Buckets: []float64{1024, 10240, 102400, 1048576, 10485760},
		}, []string{"method", "endpoint"}),
	}
}

// MetricsMiddleware returns a Gin middleware for collecting HTTP metrics
func (sm *SocialMetrics) MetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Record request size
		if c.Request.ContentLength > 0 {
			sm.HTTPRequestSize.WithLabelValues(c.Request.Method, c.FullPath()).Observe(float64(c.Request.ContentLength))
		}

		// Process request
		c.Next()

		// Record metrics after request processing
		duration := time.Since(start).Seconds()
		statusCode := strconv.Itoa(c.Writer.Status())

		sm.HTTPRequestsTotal.WithLabelValues(c.Request.Method, c.FullPath(), statusCode).Inc()
		sm.HTTPRequestDuration.WithLabelValues(c.Request.Method, c.FullPath()).Observe(duration)

		// Record response size
		responseSize := c.Writer.Size()
		if responseSize > 0 {
			sm.HTTPResponseSize.WithLabelValues(c.Request.Method, c.FullPath()).Observe(float64(responseSize))
		}
	}
}

// RecordBubbleCreation records bubble creation metrics
func (sm *SocialMetrics) RecordBubbleCreation(bubbleID string, maxMembers int) {
	sm.BubbleCreationRate.Inc()
	sm.BubbleCapacityUtilization.WithLabelValues("1", strconv.Itoa(maxMembers)).Set(1.0 / float64(maxMembers))
}

// RecordBubbleExpiry records bubble expiry metrics
func (sm *SocialMetrics) RecordBubbleExpiry(bubbleID string, memberCount int) {
	sm.BubbleExpiryEvents.Inc()
	
	// Calculate expected friend requests (n*(n-1) for bidirectional)
	expectedRequests := memberCount * (memberCount - 1)
	sm.FriendRequestsGenerated.Add(float64(expectedRequests))
	sm.BubbleToFriendshipConversion.WithLabelValues(bubbleID).Inc()
}

// RecordBubbleJoin records bubble join metrics with expiry extension
func (sm *SocialMetrics) RecordBubbleJoin(bubbleID string, currentMembers, maxMembers int, extended bool) {
	sm.BubbleMembershipChanges.WithLabelValues("join", bubbleID).Inc()
	
	if extended {
		sm.BubbleExtensionEvents.Inc()
	}
	
	// Update capacity utilization
	utilization := float64(currentMembers) / float64(maxMembers)
	sm.BubbleCapacityUtilization.WithLabelValues(strconv.Itoa(currentMembers), strconv.Itoa(maxMembers)).Set(utilization)
}

// RecordBubbleLeave records bubble leave metrics
func (sm *SocialMetrics) RecordBubbleLeave(bubbleID string, remainingMembers, maxMembers int) {
	sm.BubbleMembershipChanges.WithLabelValues("leave", bubbleID).Inc()
	
	// Update capacity utilization
	utilization := float64(remainingMembers) / float64(maxMembers)
	sm.BubbleCapacityUtilization.WithLabelValues(strconv.Itoa(remainingMembers), strconv.Itoa(maxMembers)).Set(utilization)
}

// RecordContactRequest records contact request metrics
func (sm *SocialMetrics) RecordContactRequest(accepted bool) {
	sm.ContactRequestsSent.Inc()
	if accepted {
		sm.ContactRequestsAccepted.Inc()
	}
}

// RecordFriendshipCreation records friendship creation metrics
func (sm *SocialMetrics) RecordFriendshipCreation(sourceBubbleID string, processingTime time.Duration) {
	sm.FriendshipCreationRate.Inc()
	sm.FriendshipGenerationLatency.Observe(processingTime.Seconds())
}

// RecordMessageSent records message sending metrics
func (sm *SocialMetrics) RecordMessageSent(bubbleID, messageType string, deliveryLatency time.Duration) {
	sm.MessagesSent.WithLabelValues(bubbleID, messageType).Inc()
	sm.MessageDeliveryLatency.WithLabelValues("mqtt").Observe(deliveryLatency.Seconds())
}

// UpdateUserEngagement updates user engagement score
func (sm *SocialMetrics) UpdateUserEngagement(userID string, score float64) {
	sm.UserEngagementScore.WithLabelValues(userID).Set(score)
}

// UpdateDailyActiveUsers updates the daily active users count
func (sm *SocialMetrics) UpdateDailyActiveUsers(count int) {
	sm.DailyActiveUsers.Set(float64(count))
}

// UpdateActiveChatSessions updates the active chat sessions count
func (sm *SocialMetrics) UpdateActiveChatSessions(count int) {
	sm.ActiveChatSessions.Set(float64(count))
}

// UpdateContactRelationships updates the contact relationships count
func (sm *SocialMetrics) UpdateContactRelationships(count int) {
	sm.ContactRelationships.Set(float64(count))
}

// RecordBubbleTimeToExpiry records time until bubble expiry
func (sm *SocialMetrics) RecordBubbleTimeToExpiry(bubbleID string, hoursUntilExpiry float64) {
	sm.BubbleTimeToExpiry.WithLabelValues(bubbleID).Set(hoursUntilExpiry)
}

// DashboardMetrics holds metrics for real-time monitoring dashboard
type DashboardMetrics struct {
	// Live activity metrics
	ActiveBubbles           prometheus.Gauge
	OnlineUsers             prometheus.Gauge
	MessagesPerSecond       prometheus.Gauge
	
	// Health metrics
	ServiceHealthScore      *prometheus.GaugeVec
	DatabaseConnectionHealth *prometheus.GaugeVec
	ExternalServiceHealth   *prometheus.GaugeVec
	
	// Performance metrics
	APIResponseTime         *prometheus.HistogramVec
	DatabaseQueryTime       *prometheus.HistogramVec
	CacheHitRate           *prometheus.GaugeVec
}

// NewDashboardMetrics creates a new dashboard metrics instance
func NewDashboardMetrics() *DashboardMetrics {
	return &DashboardMetrics{
		ActiveBubbles: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_active_bubbles_current",
			Help: "Current number of active bubbles",
		}),
		OnlineUsers: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_online_users_current",
			Help: "Current number of online users",
		}),
		MessagesPerSecond: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_messages_per_second_current",
			Help: "Current messages per second rate",
		}),
		ServiceHealthScore: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_service_health_score",
			Help: "Health score of services (0-100)",
		}, []string{"service"}),
		DatabaseConnectionHealth: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_database_connection_health",
			Help: "Database connection health (0=unhealthy, 1=healthy)",
		}, []string{"database"}),
		ExternalServiceHealth: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_external_service_health",
			Help: "External service health (0=unhealthy, 1=healthy)",
		}, []string{"service"}),
		APIResponseTime: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name: "hopen_api_response_time_seconds",
			Help: "API response time in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0},
		}, []string{"endpoint"}),
		DatabaseQueryTime: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name: "hopen_database_query_time_seconds",
			Help: "Database query time in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0},
		}, []string{"database", "operation"}),
		CacheHitRate: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_cache_hit_rate",
			Help: "Cache hit rate (0-1)",
		}, []string{"cache_type"}),
	}
}
