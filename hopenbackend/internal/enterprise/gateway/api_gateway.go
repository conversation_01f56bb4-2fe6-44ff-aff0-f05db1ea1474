package gateway

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// APIGateway handles service discovery and intelligent routing
type APIGateway struct {
	logger   *zap.Logger
	config   *config.Config
	services map[string]*ServiceInstance
	mutex    sync.RWMutex
}

// ServiceInstance represents a registered service instance
type ServiceInstance struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Health   string            `json:"health"` // healthy, unhealthy, unknown
	LastSeen time.Time         `json:"last_seen"`
	Metadata map[string]string `json:"metadata"`
}

// RouteConfig defines routing configuration for services
type RouteConfig struct {
	Path        string `json:"path"`
	ServiceName string `json:"service_name"`
	StripPrefix bool   `json:"strip_prefix"`
	Timeout     int    `json:"timeout_seconds"`
}

// New creates a new API Gateway instance
func New(cfg *config.Config, logger *zap.Logger) *APIGateway {
	return &APIGateway{
		logger:   logger,
		config:   cfg,
		services: make(map[string]*ServiceInstance),
	}
}

// RegisterService registers a service instance
func (gw *APIGateway) RegisterService(service *ServiceInstance) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	service.LastSeen = time.Now()
	gw.services[service.ID] = service

	gw.logger.Info("Service registered",
		zap.String("service_id", service.ID),
		zap.String("service_name", service.Name),
		zap.String("host", service.Host),
		zap.Int("port", service.Port))
}

// DeregisterService removes a service instance
func (gw *APIGateway) DeregisterService(serviceID string) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	if service, exists := gw.services[serviceID]; exists {
		delete(gw.services, serviceID)
		gw.logger.Info("Service deregistered",
			zap.String("service_id", serviceID),
			zap.String("service_name", service.Name))
	}
}

// GetHealthyService returns a healthy service instance for the given service name
func (gw *APIGateway) GetHealthyService(serviceName string) (*ServiceInstance, error) {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	var healthyServices []*ServiceInstance
	for _, service := range gw.services {
		if service.Name == serviceName && service.Health == "healthy" {
			healthyServices = append(healthyServices, service)
		}
	}

	if len(healthyServices) == 0 {
		return nil, fmt.Errorf("no healthy instances found for service: %s", serviceName)
	}

	// Use load balancer if enabled, otherwise simple round-robin
	if gw.config.Enterprise.Gateway.LoadBalancing {
		return gw.selectServiceWithLoadBalancer(healthyServices), nil
	}

	// Simple round-robin selection
	return healthyServices[0], nil
}

// selectServiceWithLoadBalancer selects a service using the configured load balancer
func (gw *APIGateway) selectServiceWithLoadBalancer(services []*ServiceInstance) *ServiceInstance {
	// For now, use round-robin. This could be enhanced with weighted load balancing
	// based on service metadata (CPU, memory, response time, etc.)
	return services[0]
}

// HealthCheck performs health checks on registered services
func (gw *APIGateway) HealthCheck(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			gw.performHealthChecks()
		}
	}
}

// performHealthChecks checks the health of all registered services
func (gw *APIGateway) performHealthChecks() {
	gw.mutex.RLock()
	services := make([]*ServiceInstance, 0, len(gw.services))
	for _, service := range gw.services {
		services = append(services, service)
	}
	gw.mutex.RUnlock()

	// Use a channel to collect health check results
	resultChan := make(chan healthCheckResult, len(services))

	for _, service := range services {
		go gw.checkServiceHealth(service, resultChan)
	}

	// Collect all results and update service states
	for i := 0; i < len(services); i++ {
		result := <-resultChan
		gw.updateServiceHealth(result)
	}
}

// healthCheckResult represents the result of a health check
type healthCheckResult struct {
	serviceID string
	healthy   bool
	err       error
}

// checkServiceHealth checks the health of a single service
func (gw *APIGateway) checkServiceHealth(service *ServiceInstance, resultChan chan<- healthCheckResult) {
	healthURL := fmt.Sprintf("http://%s:%d/health", service.Host, service.Port)

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(healthURL)

	healthy := err == nil && resp != nil && resp.StatusCode == http.StatusOK

	if resp != nil {
		resp.Body.Close()
	}

	if !healthy {
		gw.logger.Warn("Service health check failed",
			zap.String("service_id", service.ID),
			zap.String("service_name", service.Name),
			zap.Error(err))
	}

	resultChan <- healthCheckResult{
		serviceID: service.ID,
		healthy:   healthy,
		err:       err,
	}
}

// updateServiceHealth updates the health status of a service
func (gw *APIGateway) updateServiceHealth(result healthCheckResult) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	if service, exists := gw.services[result.serviceID]; exists {
		if result.healthy {
			service.Health = "healthy"
			service.LastSeen = time.Now()
		} else {
			service.Health = "unhealthy"
		}
	}
}

// ProxyMiddleware creates a middleware for proxying requests to services
func (gw *APIGateway) ProxyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// Find matching route from configuration
		route := gw.findMatchingRoute(path)
		if route == nil {
			c.Next()
			return
		}

		// Get healthy service instance
		service, err := gw.GetHealthyService(route.ServiceName)
		if err != nil {
			gw.logger.Error("No healthy service instance found",
				zap.String("service_name", route.ServiceName),
				zap.String("path", path),
				zap.Error(err))
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "Service temporarily unavailable",
			})
			c.Abort()
			return
		}

		// Create reverse proxy
		target, err := url.Parse(fmt.Sprintf("http://%s:%d", service.Host, service.Port))
		if err != nil {
			gw.logger.Error("Failed to parse service URL",
				zap.String("service_name", route.ServiceName),
				zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			c.Abort()
			return
		}

		proxy := httputil.NewSingleHostReverseProxy(target)
		proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
			gw.logger.Error("Proxy error",
				zap.String("service_name", route.ServiceName),
				zap.String("target", target.String()),
				zap.Error(err))

			w.WriteHeader(http.StatusBadGateway)
			w.Write([]byte("Bad Gateway"))
		}

		// Handle path stripping if configured
		if route.StripPrefix {
			c.Request.URL.Path = strings.TrimPrefix(c.Request.URL.Path, route.Path)
			if c.Request.URL.Path == "" {
				c.Request.URL.Path = "/"
			}
		}

		// Add service routing headers
		c.Request.Header.Set("X-Forwarded-Service", route.ServiceName)
		c.Request.Header.Set("X-Gateway-Request-ID", c.GetString("request_id"))

		proxy.ServeHTTP(c.Writer, c.Request)
		c.Abort()
	}
}

// findMatchingRoute finds the best matching route for the given path
func (gw *APIGateway) findMatchingRoute(path string) *config.GatewayRoute {
	var bestMatch *config.GatewayRoute
	longestMatch := 0

	for _, route := range gw.config.Enterprise.Gateway.Routes {
		if strings.HasPrefix(path, route.Path) && len(route.Path) > longestMatch {
			bestMatch = &route
			longestMatch = len(route.Path)
		}
	}

	return bestMatch
}

// GetServices returns all registered services
func (gw *APIGateway) GetServices() map[string]*ServiceInstance {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	services := make(map[string]*ServiceInstance)
	for k, v := range gw.services {
		services[k] = v
	}
	return services
}

// GetServiceStats returns statistics about registered services
func (gw *APIGateway) GetServiceStats() map[string]interface{} {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	stats := make(map[string]interface{})
	serviceCounts := make(map[string]int)
	healthyCounts := make(map[string]int)
	totalServices := 0
	totalHealthy := 0

	for _, service := range gw.services {
		serviceCounts[service.Name]++
		totalServices++

		if service.Health == "healthy" {
			healthyCounts[service.Name]++
			totalHealthy++
		}
	}

	stats["total_services"] = totalServices
	stats["total_healthy"] = totalHealthy
	stats["total_unhealthy"] = totalServices - totalHealthy
	stats["service_counts"] = serviceCounts
	stats["healthy_counts"] = healthyCounts
	stats["health_ratio"] = float64(totalHealthy) / float64(totalServices)

	return stats
}

// LoadBalancer interface for different load balancing strategies
type LoadBalancer interface {
	SelectService(services []*ServiceInstance) *ServiceInstance
}

// RoundRobinBalancer implements round-robin load balancing
type RoundRobinBalancer struct {
	counter int
	mutex   sync.Mutex
}

// SelectService selects a service using round-robin algorithm
func (rb *RoundRobinBalancer) SelectService(services []*ServiceInstance) *ServiceInstance {
	if len(services) == 0 {
		return nil
	}

	rb.mutex.Lock()
	defer rb.mutex.Unlock()

	service := services[rb.counter%len(services)]
	rb.counter++
	return service
}

// WeightedRoundRobinBalancer implements weighted round-robin load balancing
type WeightedRoundRobinBalancer struct {
	weights map[string]int
	current map[string]int
	mutex   sync.Mutex
}

// NewWeightedRoundRobinBalancer creates a new weighted round-robin balancer
func NewWeightedRoundRobinBalancer(weights map[string]int) *WeightedRoundRobinBalancer {
	return &WeightedRoundRobinBalancer{
		weights: weights,
		current: make(map[string]int),
	}
}

// SelectService selects a service using weighted round-robin algorithm
func (wrb *WeightedRoundRobinBalancer) SelectService(services []*ServiceInstance) *ServiceInstance {
	if len(services) == 0 {
		return nil
	}

	wrb.mutex.Lock()
	defer wrb.mutex.Unlock()

	var selected *ServiceInstance
	maxWeight := -1

	for _, service := range services {
		weight := wrb.weights[service.ID]
		if weight == 0 {
			weight = 1 // Default weight
		}

		wrb.current[service.ID] += weight
		if wrb.current[service.ID] > maxWeight {
			maxWeight = wrb.current[service.ID]
			selected = service
		}
	}

	if selected != nil {
		wrb.current[selected.ID] -= wrb.getTotalWeight(services)
	}

	return selected
}

// getTotalWeight calculates the total weight of all services
func (wrb *WeightedRoundRobinBalancer) getTotalWeight(services []*ServiceInstance) int {
	total := 0
	for _, service := range services {
		weight := wrb.weights[service.ID]
		if weight == 0 {
			weight = 1
		}
		total += weight
	}
	return total
}

// ServiceRegistry interface for service discovery
type ServiceRegistry interface {
	Register(service *ServiceInstance) error
	Deregister(serviceID string) error
	Discover(serviceName string) ([]*ServiceInstance, error)
	Watch(serviceName string) (<-chan []*ServiceInstance, error)
}

// InMemoryRegistry implements an in-memory service registry
type InMemoryRegistry struct {
	services map[string]*ServiceInstance
	watchers map[string][]chan []*ServiceInstance
	mutex    sync.RWMutex
}

// NewInMemoryRegistry creates a new in-memory service registry
func NewInMemoryRegistry() *InMemoryRegistry {
	return &InMemoryRegistry{
		services: make(map[string]*ServiceInstance),
		watchers: make(map[string][]chan []*ServiceInstance),
	}
}

// Register registers a service instance
func (r *InMemoryRegistry) Register(service *ServiceInstance) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.services[service.ID] = service
	r.notifyWatchers(service.Name)
	return nil
}

// Deregister removes a service instance
func (r *InMemoryRegistry) Deregister(serviceID string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if service, exists := r.services[serviceID]; exists {
		delete(r.services, serviceID)
		r.notifyWatchers(service.Name)
	}
	return nil
}

// Discover returns all instances of a service
func (r *InMemoryRegistry) Discover(serviceName string) ([]*ServiceInstance, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var instances []*ServiceInstance
	for _, service := range r.services {
		if service.Name == serviceName {
			instances = append(instances, service)
		}
	}
	return instances, nil
}

// Watch returns a channel that receives updates for a service
func (r *InMemoryRegistry) Watch(serviceName string) (<-chan []*ServiceInstance, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	ch := make(chan []*ServiceInstance, 1)
	r.watchers[serviceName] = append(r.watchers[serviceName], ch)

	// Send initial state
	instances, _ := r.Discover(serviceName)
	ch <- instances

	return ch, nil
}

// notifyWatchers notifies all watchers of a service about changes
func (r *InMemoryRegistry) notifyWatchers(serviceName string) {
	if watchers, exists := r.watchers[serviceName]; exists {
		instances, _ := r.Discover(serviceName)
		for _, watcher := range watchers {
			select {
			case watcher <- instances:
			default:
				// Channel is full, skip
			}
		}
	}
}
