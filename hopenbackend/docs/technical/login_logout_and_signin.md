---
description: End-to-end description of login, logout and signup flows in the Flutter client
globs: docs/technical/login_logout_and_signin.md
alwaysApply: false
---

# Login, Logout & Signup (Authentication) – Mobile Client Flow

> Covers everything that happens inside the Flutter codebase whenever a user **logs in**, **logs out** or **signs up**.  
> Key references are inline using `[filename](mdc:path)` links.

---
## 1. Actors & Layers

| Actor | Layer | Responsibility |
|-------|-------|----------------|
| **UI Widgets** | Presentation | Collect credentials / show feedback |
| **AuthBloc** ([auth_bloc.dart](mdc:lib/statefulbusinesslogic/bloc/auth/auth_bloc.dart)) | BLoC | Orchestrates authentication actions & state |
| **AuthRepository** ([auth_repository.dart](mdc:lib/repositories/auth/auth_repository.dart)) | Domain | Exposes high-level login/logout/signup APIs |
| **HttpApiService** ([http_api_service.dart](mdc:lib/provider/services/api/http_api_service.dart)) | Data | Performs HTTPS requests to backend auth endpoints |
| **SecureStorageService** (provider) | Data | Persists JWT / refresh token securely |
| **HomeNavigationBloc** ([home_navigation_bloc.dart](mdc:lib/statefulbusinesslogic/bloc/home_navigation/home_navigation_bloc.dart)) | BLoC | Decides first page right after successful login |

---
## 2. Login Flow (`Email‒or‒Username + Password`)

1. **User Input** – `LoginPage` [login_page.dart](mdc:lib/presentation/pages/auth/login_page.dart) validates fields and dispatches:
   ```dart
   context.read<AuthBloc>().add(LoginEvent(email: email, password: pwd));
   ```
2. **AuthBloc** receives `LoginEvent`  → emits `AuthLoading`  → calls `AuthRepository.login()`.
3. **AuthRepository** forwards to `HttpApiService.post('/auth/login', body)`.
4. **HttpApiService** manages TLS, error handling, certificate bypass in dev, then parses response → returns `JwtPair`.
5. **AuthRepository** stores tokens via `SecureStorageService` + caches user profile.
6. **AuthBloc** emits `AuthAuthenticated` with `userId`, names, email…
7. **LoginPage** `BlocListener` catches status change → triggers `DetermineHomeNavigation` on `HomeNavigationBloc`.
8. **HomeNavigationBloc** queries:
   * `ActiveBubbleRepository.getActiveBubble(userId)`
   * `UserRepository.getFriends(userId)`
   and chooses route (`/bubble`, `/friends`, `/contacts`).
9. **Router / GoRouter** navigates accordingly.  

### Error Handling
* Any network / validation error bubbles up as `AuthError`; UI shows `CustomToast.showError` and stays on login screen.

---
## 3. Logout Flow

1. Any page can call `context.read<AuthBloc>().add(const LogoutEvent());`
2. **AuthBloc** clears tokens via `SecureStorageService.deleteAll()` and resets internal state → emits `AuthUnauthenticated`.
3. Global router guard (in `createRouter()` redirect) notices unauthenticated state and routes user to `/login`.
4. Optional: `HomeNavigationBloc` is disposed by DI; NavBar visibility is reset by `NavBarVisibilityNotifier`.

---
## 4. Signup Flow (Multi-Step)

1. **Entry** – `/multi-step-signup` opens `MultiStepSignupPage` ([multi_step_signup_page.dart](mdc:lib/presentation/pages/auth/multi_step_signup/multi_step_signup_page.dart)).
2. Each step collects part of the required data (email, name, dob, avatar…).
3. Final step dispatches `SignupEvent` on **AuthBloc** with collected payload.
4. **AuthRepository.signUp()** POSTs `/auth/signup`; on success immediately logs the user in by calling `/auth/login` (or receives tokens directly depending on backend).
5. Remaining path identical to Login (store tokens → emit authenticated → decide home screen).

---
## 5. Token Refresh (Silent Sign-in)

* On app launch, `AuthBloc` starts in `AuthStatus.initialising`.  
* It asks `SecureStorageService` for refresh token; if available it calls `/auth/refresh` through `AuthRepository.refreshToken()`.
* On success it continues as authenticated (flow continues at step 7 above without UI interaction).  
* On failure it emits `AuthUnauthenticated` and router shows `/login`.

---
## 6. Edge-cases & Notes

* **Network-offline:** `HttpApiService` detects offline via `ConnectivityService` and throws `NetworkFailure`; UI shows toast.
* **Password reset:** handled by `ForgotPasswordPage` and `ResetPasswordVerificationPage` routes.
* **Account deletion:** Emits `LogoutEvent` after remote delete, so flow equals logout.
* **Four-Layer Rule Compliance:** Presentation never calls Data layer directly; all traffic is through BLoC → Repository → Service.

---
## 7. Sequence Diagram (Login)
```mermaid
sequenceDiagram
  participant U as User
  participant UI as LoginPage
  participant B as AuthBloc
  participant R as AuthRepository
  participant S as HttpApiService
  U->>UI: enter credentials
  UI->>B: LoginEvent
  B->>R: login()
  R->>S: POST /auth/login
  S-->>R: JwtPair
  R-->>B: AuthSuccess(user)
  B-->>UI: state=authenticated
  UI->>HN: DetermineHomeNavigation
  HN->>Repositories: bubble? friends?
  HN-->>UI: destination route
  UI-->>Router: context.go(route)
```

---
## 8. Testing Checklist

- Unit: `AuthBloc` transitions, token storage, failure mapping.
- Widget: `LoginPage` validation & button states.
- Integration: Full login-to-home navigation on Firebase TestLab.
- E2E: Detox/Kotlin UI test logs in, verifies landing page.
