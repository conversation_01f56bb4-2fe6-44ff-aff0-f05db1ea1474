# Cassandra Table Structure Changes

## 🎯 **Objective**
Rename and restructure Cassandra tables to properly reflect domain concepts and separate different types of messaging.

## 🔄 **Table Name Changes**

### **Before → After**
1. **`messages`** → **`bubble_chat`**
   - Purpose: Messages within bubble group chats
   - Reason: More descriptive name that clearly indicates bubble-specific messaging

2. **`conversations`** → **`friends_chat`**
   - Purpose: Conversation metadata between friends
   - Reason: Clarifies this is specifically for friend-to-friend conversations

3. **`conversation_messages`** → **`contact_chat`**
   - Purpose: Messages between contacts (not yet friends)
   - Reason: Distinguishes contact messaging from friend messaging

## 📋 **Updated Cassandra Schema**

### **1. Bubble Chat Messages (`bubble_chat`)**
```cql
CREATE TABLE IF NOT EXISTS bubble_chat (
    bubble_id UUID,
    message_id UUID,
    sender_id UUID,
    content TEXT,
    message_type TEXT,
    timestamp TIMESTAMP,
    metadata MAP<TEXT, TEXT>,
    edited_at TIMESTAMP,
    deleted_at TIMESTAMP,
    reply_to_message_id UUID,
    PRIMARY KEY (bubble_id, timestamp, message_id)
) WITH CLUSTERING ORDER BY (timestamp DESC);
```

### **2. Friends Chat Conversations (`friends_chat`)**
```cql
CREATE TABLE IF NOT EXISTS friends_chat (
    conversation_id UUID,
    participant1_id UUID,
    participant2_id UUID,
    last_message_id UUID,
    last_message_at TIMESTAMP,
    created_at TIMESTAMP,
    PRIMARY KEY (conversation_id)
);
```

### **3. Contact Chat Messages (`contact_chat`)**
```cql
CREATE TABLE IF NOT EXISTS contact_chat (
    conversation_id UUID,
    message_id UUID,
    sender_id UUID,
    recipient_id UUID,
    content TEXT,
    message_type TEXT,
    timestamp TIMESTAMP,
    metadata MAP<TEXT, TEXT>,
    edited_at TIMESTAMP,
    deleted_at TIMESTAMP,
    read_at TIMESTAMP,
    PRIMARY KEY (conversation_id, timestamp, message_id)
) WITH CLUSTERING ORDER BY (timestamp DESC);
```

### **4. User Friends Chat List (`user_friends_chat`)**
```cql
CREATE TABLE IF NOT EXISTS user_friends_chat (
    user_id UUID,
    conversation_id UUID,
    friend_id UUID,
    last_message_at TIMESTAMP,
    unread_count INT,
    created_at TIMESTAMP,
    PRIMARY KEY (user_id, last_message_at, conversation_id)
) WITH CLUSTERING ORDER BY (last_message_at DESC);
```

### **5. User Contact Chat List (`user_contact_chat`)**
```cql
CREATE TABLE IF NOT EXISTS user_contact_chat (
    user_id UUID,
    conversation_id UUID,
    contact_id UUID,
    last_message_at TIMESTAMP,
    unread_count INT,
    created_at TIMESTAMP,
    PRIMARY KEY (user_id, last_message_at, conversation_id)
) WITH CLUSTERING ORDER BY (last_message_at DESC);
```

### **6. Bubble Chat Message Counts (`bubble_chat_counts`)**
```cql
CREATE TABLE IF NOT EXISTS bubble_chat_counts (
    bubble_id UUID PRIMARY KEY,
    total_messages counter
);
```

### **7. Friends Chat Unread Counts (`friends_chat_unread_counts`)**
```cql
CREATE TABLE IF NOT EXISTS friends_chat_unread_counts (
    conversation_id UUID,
    user_id UUID,
    unread_count counter,
    PRIMARY KEY (conversation_id, user_id)
);
```

### **8. Contact Chat Unread Counts (`contact_chat_unread_counts`)**
```cql
CREATE TABLE IF NOT EXISTS contact_chat_unread_counts (
    conversation_id UUID,
    user_id UUID,
    unread_count counter,
    PRIMARY KEY (conversation_id, user_id)
);
```

## 🔧 **Updated API Models**

### **Frontend Models Created:**
1. **`ApiBubbleChatMessage`** - For bubble_chat table
2. **`ApiFriendsChat`** - For friends_chat table
3. **`ApiContactChatMessage`** - For contact_chat table
4. **`ApiUserFriendsChat`** - For user_friends_chat table
5. **`ApiUserContactChat`** - For user_contact_chat table
6. **`ApiBubbleChatCount`** - For bubble_chat_counts table
7. **`ApiFriendsChatUnreadCount`** - For friends_chat_unread_counts table
8. **`ApiContactChatUnreadCount`** - For contact_chat_unread_counts table

## 🎯 **Domain Separation Benefits**

### **1. Clear Messaging Types**
- **Bubble Chat**: Group messaging within bubbles
- **Friends Chat**: Private messaging between friends
- **Contact Chat**: Private messaging between contacts

### **2. Proper Data Isolation**
- Each messaging type has its own table structure
- Separate unread counters for different chat types
- Independent scaling and optimization per chat type

### **3. Better Query Performance**
- Optimized partition keys for each use case
- Separate clustering for different access patterns
- Reduced cross-table queries

### **4. Enhanced Security**
- Clear boundaries between different relationship types
- Easier to implement different privacy rules
- Better audit trails per messaging type

## 🔄 **Migration Impact**

### **Backend Changes Required:**
1. Update Cassandra schema creation scripts
2. Update all messaging service implementations
3. Update API endpoints to use new table names
4. Migrate existing data (if any) to new table structure

### **Frontend Changes Required:**
1. ✅ **API Models Updated** - All new models created
2. 🔄 **Repository Interfaces** - Need to create messaging repositories
3. 🔄 **Repository Implementations** - Need to implement new messaging logic
4. 🔄 **BLoC Updates** - Update messaging BLoCs to use new models
5. 🔄 **UI Updates** - Update chat UI to handle different message types

## 📋 **Next Steps**

### **High Priority:**
1. **Update Backend Schema** - Apply new Cassandra table structure
2. **Create Repository Interfaces** - Define messaging repository contracts
3. **Implement Repositories** - Create concrete messaging implementations
4. **Update Datasources** - Create new messaging datasources

### **Medium Priority:**
1. **Update BLoCs** - Migrate messaging BLoCs to new models
2. **Update UI** - Adapt chat interfaces to new structure
3. **Integration Testing** - Test all messaging flows

### **Low Priority:**
1. **Performance Optimization** - Tune Cassandra queries
2. **Monitoring** - Add metrics for new table structure
3. **Documentation** - Update API documentation

## ✅ **Validation Checklist**

- ✅ All Cassandra tables have clear, descriptive names
- ✅ Each messaging type has dedicated table structure
- ✅ All API models match new table schemas exactly
- ✅ Proper separation between bubble, friends, and contact messaging
- ✅ Counter tables aligned with new naming convention
- 🔄 Backend schema updated to match new structure
- 🔄 All repository implementations created
- 🔄 Full messaging flow tested end-to-end
