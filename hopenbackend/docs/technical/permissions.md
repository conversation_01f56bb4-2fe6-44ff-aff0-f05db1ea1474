# Application Permissions

This document outlines the permissions requested by the Hopen application on Android and iOS platforms, along with their justifications.

## Android Permissions (`AndroidManifest.xml`)

The following permissions are declared in `hopen/android/app/src/main/AndroidManifest.xml`:

| Permission                          | Purpose                                                                                | Status      |
| ----------------------------------- | -------------------------------------------------------------------------------------- | ----------- |
| `android.permission.CAMERA`         | Required to allow users to take photos and videos for chat attachments and profile pictures. | Required    |
| `android.permission.RECORD_AUDIO`   | Required for capturing audio during voice and video calls.                             | Required    |
| `android.permission.INTERNET`       | Required for all network communication, including API calls, MQTT connections for real-time chat, Firebase Cloud Messaging (FCM), <PERSON><PERSON> authentication, and MinIO file transfers. | Required    |
| `android.permission.FOREGROUND_SERVICE` | Required to run services (like screen sharing via MediaProjection) reliably even when the app is in the background. Needed for Android 9+. | Required    |
| `android.permission.MODIFY_AUDIO_SETTINGS` | Allows the app to modify global audio settings, such as ringer mode and volume, often used for call audio management. | Required    |
| `android.permission.BLUETOOTH` / `BLUETOOTH_ADMIN` | (For Android 11 and below) Allows the app to discover, pair, and connect to Bluetooth devices like headsets. | Required    |
| `android.permission.BLUETOOTH_SCAN` / `BLUETOOTH_CONNECT` | (For Android 12 and above) Allows the app to discover (`_SCAN`) and connect to (`_CONNECT`) nearby Bluetooth devices. `_SCAN` needs `usesPermissionFlags="neverForLocation"` if not used for location. | Required    |

## iOS Permissions & Capabilities (`Info.plist`)

The following keys in `hopen/ios/Runner/Info.plist` define permissions or capabilities. For permissions requiring user consent, the specified string is displayed in the permission request dialog.

### User-Facing Permission Descriptions

| Info.plist Key                   | Purpose                                                                                      | User-Facing String Provided | Status      |
| -------------------------------- | -------------------------------------------------------------------------------------------- | --------------------------- | ----------- |
| `NSCameraUsageDescription`       | Allows the app to access the device camera for taking photos/videos (chat, profile).         | Yes                         | Required    |
| `NSMicrophoneUsageDescription`   | Allows the app to access the device microphone for voice and video calls.                  | Yes                         | Required    |
| `NSPhotoLibraryUsageDescription` | Allows the app to access the user's photo library for selecting photos/videos (chat, profile). | Yes                         | Required    |
| `NSMicrophoneUsageDescription`      | Reason for needing microphone access (e.g., "This app needs access to your microphone for voice and video calls and to record voice messages."). | Required    |
| `NSPhotoLibraryUsageDescription`    | Reason for needing photo library access (e.g., "This app needs access to your photo library to allow you to select and share photos in chat messages and for your profile picture."). | Required    |
| `NSBluetoothAlwaysUsageDescription` | Reason for needing Bluetooth access (e.g., "This app needs access to Bluetooth to connect to audio devices (like headsets) during calls."). | Required    |

*Note: The quality and clarity of the user-facing strings are crucial for user trust and App Store review.* 

### Background Modes

The `UIBackgroundModes` key declares the app's background execution capabilities:

| Background Mode       | Purpose                                                                        |
| --------------------- | ------------------------------------------------------------------------------ |
| `fetch`               | Allows the app to perform background data fetching (Usage needs verification). |
| `remote-notification` | Enables the app to receive and process push notifications (FCM) in the background. |
| `voip`                | Enables the app to receive and process VoIP push notifications and manage calls in the background, improving call reliability. |

## General Principles

- **Minimalism**: Only permissions essential for core application functionality should be requested.
- **Clarity**: Permissions, especially on iOS, require clear, user-understandable justifications.
- **Review**: This list should be reviewed and updated whenever new features requiring additional permissions are introduced. 