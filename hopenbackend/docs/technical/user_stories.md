# Hopen User Stories

This document outlines user stories for <PERSON><PERSON>, categorized by feature area, reflecting the current understanding of the implemented system. Each story follows the format: "As a [user type], I want to [action] so that [benefit]."

## Authentication & Onboarding

1.  **Account Creation & Initial Setup**
    *   As a new user, I want to create an account by providing my first name, last name, email, a unique username, a secure password, and my date of birth through a multi-step process, so that I can establish my identity on the Hopen platform.
    *   As a new user, during the multi-step signup, I want to be able to upload or select a profile picture, so that other users can visually identify me.
    *   As a new user, during the multi-step signup, I want to set my initial notification preferences (e.g., enable/disable all), so that I have baseline control over alerts from the start.
    *   As a new user, after successfully completing the multi-step signup, I want to be directed to an introductory onboarding flow, so that I can learn about <PERSON><PERSON>'s key features.
    *   As a new user, I want to navigate (next, back, skip) the introductory onboarding screens which present images and descriptions of app functionalities, so that I can understand the app's purpose at my own pace.

2.  **Login & Session Management**
    *   As a registered user, I want to log in securely using my email and password, so that I can access my account and its features.
    *   As a registered user, I want to be able to log in using my Google account, so that I have an alternative and convenient way to access Hopen.
    *   As a registered user, I want to be able to log in using my Apple account, so that I have another alternative and convenient way to access Hopen.
    *   As a registered user, I want my login session to persist across app uses (unless explicitly logged out), so that I don't need to re-enter my credentials frequently.
    *   As a registered user, if I've forgotten my password, I want to be able to initiate a password reset process [Future Scope: Full flow TBD], so that I can regain access to my account.
    *   As a user, I want the application to check my authentication status upon launch, so that I am automatically logged in if my session is still valid or directed to login if not.

## Profile Management

1.  **User Profile Viewing & Editing**
    *   As a user, I want to view my own profile, which displays my full name, username, profile picture, current online visibility status (e.g., "Online Visible," "Online Hidden"), and current bubble affiliation (e.g., "In a Bubble," "No Bubble"), so that I can see how my profile appears to myself and potentially others.
    *   As a user, I want to navigate to an "Edit Profile" page from my profile.
    *   As a user, on the "Edit Profile" page, I want to be able to update my first name and last name, so that my personal details remain accurate.
    *   As a user, on the "Edit Profile" page, I want to be able to change my profile picture [Image Picker TODO], so that I can update my visual representation.
    *   As a user, I want to save the changes made on the "Edit Profile" page [Save Logic TODO], so that my profile information is updated.
    *   (Note: Username, email, and birthday are set at signup and do not appear to be editable via the current "Edit Profile" page implementation.)

2.  **Account Settings & Preferences**
    *   As a user, I want to access a dedicated "Profile" screen that serves as a hub for viewing my profile card and navigating to various settings sub-pages.
    *   As a user, from my profile screen, I want to navigate to a "Notifications" settings page [Content of Page TBD], so that I can manage my notification preferences in detail.
    *   As a user, from my profile screen, I want to navigate to a "Privacy" settings page [Content of Page TBD, `UserPrivacySettingsModel` exists], so that I can control who sees my information and interacts with me.
    *   As a user, from my profile screen, I want to navigate to a "Language" selection page [Content of Page TBD], so that I can choose my preferred application language.
    *   As a user, from my profile screen, I want to navigate to a "Security" settings page [Content of Page TBD], so that I can manage aspects like my password.
    *   As a user, from my profile screen, I want to navigate to a "Help & Support" page [Content of Page TBD], so that I can find assistance or report issues.
    *   As a user, from my profile screen, I want to navigate to an "About" page [Content of Page TBD], so that I can learn more about the Hopen application.
    *   As a user, I want a "Logout" option on my profile screen, with a clear icon and a consistent trailing arrow indicator, which securely ends my session and navigates me to the login screen [Logout Logic TODO, currently navigates].

## Unified Profile Viewing (Other Users)

1.  **Viewing Other User Profiles**
    *   As a user, I want to view the profile of another Hopen user (e.g., by navigating from a contacts list, friends list, or bubble member list).
    *   As a user, when viewing another user's profile, I want to see their full name, username, profile picture, online visibility status, and bubble affiliation status.
    *   As a user, when viewing another user's profile, I want to understand my relationship with them (e.g., "Self," "Friend," "Contact," "Contact Request Sent," "Contact Request Received," "Stranger," "Blocked by me," "Blocked by them").
    *   As a user, when viewing another user's profile, I want to see any mutual friends or mutual contacts we share.
    *   As a user, I want the main action button on another user's profile to be contextually relevant to our relationship (e.g., "Edit Profile" for self, "Unblock User," "Message," "Send contact request," "Accept/Decline request,"Start Bubble Together").

2.  **Profile Actions**
    *   As a user, from another user's profile, I want to be able to send them a contact request if we are strangers.
    *   As a user, from another user's profile, if I have a pending incoming contact request, I want to be able to accept or decline it.
    *   As a user, from another user's profile, I want to be able to block them if they are not already blocked by me.
    *   As a user, from another user's profile, I want to be able to unblock them if I have previously blocked them.
    *   As a user, from a friend's profile, I want to be able to unfriend them.
    *   (Note: Mute and Report user functionalities are present as events in `UnifiedProfileBloc` but their UI triggers and full flow are TBD).

## Bubble System

1.  **Bubble Creation & Lifecycle Management**
    *   As a user, I want to be able to create a new bubble, specifying its name, start/end dates, and inviting initial members from my contacts, so that I can form a time-limited social group.
    *   As a user, I want to be able to join a bubble if I have been invited.
    *   As a user, I want to be able to leave a bubble I am a member of.
    *   As a user (creator/admin), I want to be able to remove a member from a bubble. [Admin/Creator distinction TBD]
    *   As a user (creator/admin), I want to be able to update the bubble's information (name, dates). [Admin/Creator distinction TBD]
    *   As a user, I want to view the details of my active bubble, including its name, duration (start/end dates or time remaining), and list of members.

2.  **Active Bubble Interactions & Features**
    *   As a member of an active bubble, I want to see a list of all current members, their avatars, names, and their online status in real-time.
    *   As a member of an active bubble, I want to see unread message counts for each member within our bubble chat, updated in real-time.
    *   As a member of an active bubble (if the bubble is not full), I want to be able to "propose a new bubbler," which currently navigates me to my contacts list with a prompt to invite someone, so that we can potentially add new members.
    *   As a member of an active bubble, I want to be able to "vote" for a member [UI and full mechanism/purpose of voting TBD, event exists in `ActiveBubbleBloc`].
    *   As a user, from the main bubble page, I want an option to "Join more bubbles," which currently navigates to an `AnnouncementBannerPage` [Purpose of this page and actual flow for joining other bubbles TBD].
    *   As a bubble member, I want to navigate to the profile of any other bubble member by tapping on their entry in the member list.

## Social Connections

1.  **Contact Management**
    *   As a user, I want to view a list of my contacts, displaying their name, avatar, online status, and bubble status.
    *   As a user, I want to be able to search my contacts by name or username.
    *   As a user, I want to filter my contacts by their bubble status (e.g., "No Bubble," "In a Bubble," "Bubble Full") and relationship type (e.g., "contact," "no relation" for search results).
    *   As a user, I want to be able to initiate adding a new contact (which likely sends a contact request).
    *   As a user, I want to be able to remove an existing contact (which might also cancel a friendship).
    *   As a user, I want to be able to block a contact.
    *   As a user, I want to manage contact relationships (e.g., accept/decline incoming requests, implicitly handled via `UnifiedProfilePage` actions based on `ContactsBloc` data).

2.  **Friend Management**
    *   As a user, I want to view a separate list of my "friends."
    *   As a user, I want to load the profile of a specific friend.
    *   As a user, I want to be able to add a user as a friend (distinct from just being a contact).
    *   As a user, I want to be able to remove a friend (unfriend).
    *   As a user, I want to be able to block a friend (which also removes them as a friend).
    *   As a user, I want to search my friends list by name or username.
    *   As a user, I want to see the online presence of my friends updated in real-time in my friends list.
    *   As a user, I want to be able to sort my friends list by various criteria (e.g., online status, alphabetically) [Specific sort options TBD by UI].

## Communication

1.  **Direct & Group Messaging (Chat)**
    *   As a user, I want to engage in one-on-one text-based chats with my contacts/friends.
    *   As a user, I want to participate in group chats within my active bubble.
    *   As a user in a chat, I want to send messages containing text and optionally media attachments (images/videos).
    *   As a user in a chat, I want to see when other participants are typing.
    *   As a user in a chat, I want to receive messages in real-time.
    *   As a user in a chat, I want to see message status updates (e.g., read receipts) [UI for read receipts TBD].
    *   As a user in a chat, I want to be able to delete messages I've sent (for myself or for everyone) [UI for "for everyone" TBD].
    *   As a user in a chat, I want to load older messages when I scroll to the top of the conversation (pagination).
    *   As a user in a chat, I want to be able to search for specific messages within that chat.
    *   (Note: Replying to specific messages is not explicitly indicated by BLoC events but could be a UI-level feature or part of message metadata.)

2.  **Real-time Calling (Audio & Video)**
    *   As a user, I want to initiate a one-on-one audio or video call with a contact/friend.
    *   As a user, I want to initiate a group audio or video call within my active bubble.
    *   As a user, I want to be able to accept or reject incoming calls.
    *   As a user in a call, I want to be able to end the call.
    *   As a user in a call, I want to toggle my microphone (mute/unmute).
    *   As a user in a video call, I want to toggle my camera (video on/off).
    *   As a user in a video call, I want to be able to switch between my front and rear cameras.
    *   As a user in a call (1-on-1 or group), I want the option to share my screen.
    *   As a user, I want to see the duration of an active call.
    *   As a user, I want to be able to join an ongoing group call in my bubble.
    *   (Note: Picture-in-Picture (PiP) mode for calls is a common advanced feature but not explicitly in BLoC states/events; it would be a UI/OS integration.)

## Notifications

1.  **System Alerts & Activity Updates**
    *   As a user, I want to receive notifications for new incoming contact requests.
    *   As a user, I want to be notified when a sent contact request is accepted.
    *   As a user, I want to receive notifications for invitations to join a bubble.
    *   As a user, I want to receive notifications for new unread messages in my chats.
    *   As a user, I want to be notified about significant bubble-related events (e.g., a new member joins, bubble expiration warnings) [Specific events TBD].
    *   As a user, I want to receive notifications for incoming calls.
    *   (Note: Detailed management of *which* notifications to receive is tied to the "Notifications" settings page, content of which is TBD).

## User Discovery & Network Growth

1.  **Finding and Inviting Connections**
    *   As a user, I want to search for other Hopen users globally by username or name (supported by `ContactsPage` search if not filtered to existing contacts).
    *   As a user, I want to find people I may know [Feature/UI TBD, no specific "suggestions" BLoC seen].
    *   As a user, I want to see suggested contacts based on mutual connections [Feature/UI TBD, `UnifiedProfileBloc` fetches mutuals for a specific profile, but no general suggestion feed].
    *   As a user, I want to invite external contacts (e.g., from my device's address book) to join Hopen [Feature/UI TBD].

## Accessibility & User Preferences

1.  **Application Customization & Accessibility Features**
    *   As a user, I want to choose between a light and dark theme for the application interface [Theme switching mechanism TBD].
    *   As a user with visual accessibility needs, I want the application to respect system-level text size adjustments or provide in-app options to scale text [Implementation TBD].
    *   As a user, I want to have control over media autoplay settings [Implementation TBD].

## Security & Privacy

1.  **Data Protection & User Control**
    *   As a user, I expect that my direct messages are secured (e.g., via end-to-end encryption) [E2EE implementation details TBD].
    *   As a user, I want control over who can see my online status (indicated by "Online Visible" / "Online Hidden" on `UserProfileCard`, management UI TBD in Privacy Settings).
    *   As a user, I want the option to delete my message history [ChatBloc supports deleting messages, UI control over entire history TBD].
    *   As a user, I want a security feature to log out from all active sessions on other devices [Feature TBD].

This user story document provides a comprehensive guide for feature development and prioritization based on the current understanding of the codebase. It should be treated as a living document, subject to review and updates as the Hopen application evolves.
