# Hopen Technical Overview

## Project Vision

Hopen is a real-time social communication platform that enables users to create temporary group spaces called "bubbles" for voice/video calls and messaging. The application emphasizes spontaneous connections, privacy, and high-performance real-time communication.

## Core Features

### 🫧 Bubble System
- **Temporary Group Spaces**: Create time-limited social spaces for focused conversations
- **Capacity Management**: Configurable member limits (2-5 participants)
- **Real-time Synchronization**: MQTT5-powered state updates across all participants
- **Smart Lifecycle**: Automatic cleanup and archival of expired bubbles

### 💬 Real-time Messaging
- **MQTT5 Protocol**: Ultra-low latency messaging with EMQX broker
- **Isolate Processing**: Background message processing to prevent UI blocking
- **Message Types**: Text, media, system notifications, and custom message types
- **Offline Support**: Local message storage with sync on reconnection

### 📞 Voice & Video Calls
- **WebRTC Integration**: Peer-to-peer audio/video communication
- **Call Management**: Initiate, answer, hold, and transfer calls
- **Quality Optimization**: Adaptive bitrate and network-aware quality adjustment
- **Background Processing**: Call state management in separate isolates

### 👥 Contact Management
- **Smart Contact System**: Add contacts via email, phone, or invitation codes
- **Request Management**: Send, receive, and manage contact requests
- **Privacy Controls**: Granular privacy settings for contact visibility
- **Real-time Updates**: Live contact status and availability updates

### 🔐 Authentication & Security
- **Ory Stack Integration**: Kratos for identity, Hydra for OAuth2/OIDC
- **Multi-provider Auth**: Google, Apple, and email/password authentication
- **JWT Token Management**: Secure token handling with refresh rotation
- **Session Security**: Valkey-backed session management with encryption
- **OryAuthService (Flutter)**: Centralized authentication client featuring
  - **Stream-based Auth State**: `authStateStream` and `userStream` for reactive UI updates
  - **Email/Password Sign-Up & Login** via Kratos public API flows
  - **Google Sign-In** using `google_sign_in` with server client-ID on mobile
  - **Apple Sign-In** leveraging `sign_in_with_apple` for iOS/macOS
  - **Session Restoration** on app launch (`/sessions/whoami`)
  - **Automatic Token Refresh** scheduled ahead of expiry with silent refresh
  - **Logout / Revoke** support that clears local session and Valkey cache
  - **Typed Error Handling** with `AuthenticationException`, `NetworkError`, etc.
  - **Single-instance Singleton Design** ensuring one global auth context

### 📱 Mobile-First Design
- **Flutter 3.x**: Modern UI with null safety and performance optimizations
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Platform Integration**: Native iOS and Android features
- **Offline Capability**: Local data storage with intelligent sync

## Technical Architecture

### Backend Infrastructure

#### Go Microservices (Gin Framework)
```go
// Example Bubble creation endpoint (Gin)
func (h *Handler) CreateBubble(c *gin.Context) {
    var req struct {
        Name      string `json:"name" binding:"required,min=1,max=50"`
        Capacity  int    `json:"capacity" binding:"required,min=2,max=5"`
    }
    if err := c.ShouldBindJSON(&req); err != nil {
        c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    bubbleID, inviteCode, err := h.service.CreateBubble(c.Request.Context(), req.Name, req.Capacity)
    if err != nil {
        c.AbortWithError(http.StatusInternalServerError, err)
        return
    }

    c.JSON(http.StatusCreated, gin.H{"bubbleId": bubbleID, "inviteCode": inviteCode})
}
```

#### Database Layer
- **PostgreSQL**: Primary relational database managed via Kubernetes StatefulSet & sqlc
- **Drift (SQLite)**: Local mobile database for offline capability
- **Valkey**: High-performance caching and session storage
- **Data Consistency**: ACID transactions with optimistic locking

#### File Storage
- **MinIO**: S3-compatible object storage for user content
- **CDN Integration**: Global content delivery for media files
- **Lazy Loading**: Deferred initialization to improve startup performance
- **Compression**: Automatic image and video compression

### Frontend Architecture

#### Clean Architecture Pattern
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐        │
│  │   Pages     │   │  Widgets    │   │  Router     │        │
│  └─────────────┘   └─────────────┘   └─────────────┘        │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                     BLoC Layer                              │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐        │
│  │   Events    │──▶│    BLoC     │──▶│   States    │        │
│  └─────────────┘   └─────────────┘   └─────────────┘        │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                   Repository Layer                          │
│  ┌─────────────┐                                            │
│  │  Abstract   │   Domain-focused repository contracts      │
│  │ Repository  │   with clean separation of concerns        │
│  │ Interfaces  │                                            │
│  └─────────────┘                                            │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                    Provider Layer                           │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐        │
│  │ Repository  │   │   Mappers   │   │ Data Sources│        │
│  │   Impls     │──▶│DTO→Entity   │──▶│External APIs│        │
│  └─────────────┘   └─────────────┘   └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
+
+**Provider Layer Directory Structure (lib/provider/)**
+
+- `repositories/` – Concrete implementations of domain repository interfaces
+- `datasources/` – External data sources (Go REST APIs, Drift DB, MinIO, EMQX, etc.)
+- `services/` – Integration wrappers (API client, MQTT5, WebRTC, asset pipeline)
+- `websocket/` – Real-time WebSocket and MQTT abstractions
+- `utils/` – Shared helpers and utilities (debouncers, formatters, etc.)
+- `mappers/` – DTO ↔︎ Entity mapping logic
+- `local_storage/` – Drift database adapters and cache helpers
+- `exceptions/` – Provider-level exception definitions
+- `theme/` – Design tokens & theming extensions consumed by widgets
+- `config/` – Provider-specific configuration and constants
+
```

#### State Management with BLoC
```dart
// Example BLoC implementation
class BubbleBloc extends Bloc<BubbleEvent, BubbleState> {
  final BubbleRepository _repository;
  final MqttService _mqttService;

  BubbleBloc({
    required BubbleRepository repository,
    required MqttService mqttService,
  }) : _repository = repository,
       _mqttService = mqttService,
       super(BubbleInitial()) {
    on<LoadBubble>(_onLoadBubble);
    on<CreateBubble>(_onCreateBubble);
    on<BubbleUpdated>(_onBubbleUpdated);
    
    // Listen to real-time updates
    _mqttService.messageStream
        .where((msg) => msg.topic.startsWith('bubble/'))
        .listen(_handleRealTimeUpdate);
  }
}
```

### Performance Optimizations

#### Startup Performance
- **Lazy Service Initialization**: Critical services initialize only when needed
- **Performance Monitoring**: Real-time tracking of startup phases
- **Asset Optimization**: Smart loading of 532+ flag SVGs and media assets
- **Modular Dependency Injection**: Organized service registration

#### Runtime Performance
- **MQTT Isolate**: Background message processing prevents UI blocking
- **Asset Caching**: LRU cache with automatic cleanup for media files
- **Frame Monitoring**: Real-time detection of UI performance issues
- **Memory Management**: Proactive cleanup and optimization

#### Build Optimizations
- **Tree Shaking**: Eliminates unused code and assets
- **Code Splitting**: Modular architecture for optimal bundle size
- **Compression**: SVG optimization and media compression
- **Release Configuration**: Production-optimized build settings

## Real-time Communication

### MQTT5 Messaging Architecture
```dart
// MQTT service with isolate support
class MqttIsolateService {
  static const String _isolateName = 'mqtt_isolate';
  
  Future<void> connect() async {
    final message = MqttIsolateMessage(
      type: MqttIsolateMessageType.connect,
      data: {
        'broker': AppConfig.mqttBroker,
        'port': AppConfig.mqttPort,
        'clientId': await _generateClientId(),
        'username': await _getJwtToken(),
      },
    );
    
    await _sendToIsolate(message);
  }
  
  Stream<MqttMessage> get messageStream => _messageController.stream;
}
```

### WebRTC Call System
```dart
// WebRTC service for peer-to-peer calls
class WebRTCService {
  late RTCPeerConnection _peerConnection;
  final StreamController<CallState> _callStateController = StreamController();
  
  Future<void> createCall(String targetUserId) async {
    _peerConnection = await createPeerConnection({
      'iceServers': [
        {'urls': 'stun:stun.l.google.com:19302'},
        {'urls': 'turn:turn.hopen.app:3478', 'username': '...', 'credential': '...'}
      ]
    });
    
    // Setup media streams
    final localStream = await navigator.mediaDevices.getUserMedia({
      'audio': true,
      'video': true,
    });
    
    await _peerConnection.addStream(localStream);
    
    // Create and send offer through MQTT
    final offer = await _peerConnection.createOffer();
    await _peerConnection.setLocalDescription(offer);
    
    await _mqttService.publish(
      'call/offer/$targetUserId',
      jsonEncode({
        'callerId': _currentUserId,
        'offer': offer.toMap(),
        'timestamp': DateTime.now().toIso8601String(),
      }),
    );
  }
}
```

## Data Models

### Domain Entities

#### BubbleEntity
```dart
class BubbleEntity extends Equatable {
  final BubbleId id;
  final BubbleName name;
  final UserId creatorId;
  final MemberCapacity capacity;
  final List<BubbleMemberEntity> members;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final bool isActive;
  final BubbleSettings settings;

  const BubbleEntity({
    required this.id,
    required this.name,
    required this.creatorId,
    required this.capacity,
    required this.members,
    required this.createdAt,
    this.expiresAt,
    required this.isActive,
    required this.settings,
  });

  // Domain business logic
  bool canAddMember() => members.length < capacity.value && isActive;
  
  Result<void> addMember(BubbleMemberEntity member) {
    if (!canAddMember()) {
      return Result.failure(BubbleCapacityExceededException());
    }
    // Add member logic
    return Result.success(null);
  }
  
  bool isCreator(UserId userId) => creatorId == userId;
  int get availableSlots => capacity.value - members.length;
  bool get isExpired => expiresAt?.isBefore(DateTime.now()) ?? false;
}
```

#### UserModel
```dart
class UserModel extends Equatable {
  final String id;
  final String email;
  final String? displayName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime? lastActiveAt;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final UserStatus status;
  final Map<String, dynamic> preferences;
  final List<String> roles;

  const UserModel({
    required this.id,
    required this.email,
    this.displayName,
    this.phoneNumber,
    this.profileImageUrl,
    required this.createdAt,
    this.lastActiveAt,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.status,
    required this.preferences,
    required this.roles,
  });

  // Domain methods
  bool get isActive => status == UserStatus.active;
  String get displayNameOrEmail => displayName ?? email;
  bool canReceiveCalls() => isActive && preferences['allowCalls'] == true;
  bool get isOnline => lastActiveAt?.isAfter(
    DateTime.now().subtract(const Duration(minutes: 5))
  ) ?? false;
}
```

### Value Objects
```dart
// Type-safe identifiers
class BubbleId extends ValueObject<String> {
  const BubbleId(String value) : super(value);
  
  factory BubbleId.generate() => BubbleId(const Uuid().v4());
  
  @override
  Result<void> validate() {
    if (value.isEmpty || !Uuid.isValidUUID(fromString: value)) {
      return Result.failure(InvalidBubbleIdException());
    }
    return Result.success(null);
  }
}

class BubbleName extends ValueObject<String> {
  const BubbleName(String value) : super(value);
  
  @override
  Result<void> validate() {
    if (value.isEmpty || value.length > 50) {
      return Result.failure(InvalidBubbleNameException());
    }
    return Result.success(null);
  }
}

class MemberCapacity extends ValueObject<int> {
  const MemberCapacity(int value) : super(value);
  
  @override
  Result<void> validate() {
    if (value < 2 || value > 5) {
      return Result.failure(InvalidCapacityException());
    }
    return Result.success(null);
  }
}
```

## Security Implementation

### Authentication Flow
```dart
// Ory integration for authentication
class AuthService {
  final OryKratosClient _kratosClient;
  final OryHydraClient _hydraClient;
  
  Future<Result<AuthToken>> signInWithEmail(String email, String password) async {
    try {
      // Initiate login flow with Kratos
      final loginFlow = await _kratosClient.initializeSelfServiceLoginFlowForBrowsers();
      
      // Submit credentials
      final result = await _kratosClient.submitSelfServiceLoginFlow(
        flow: loginFlow.id,
        submitSelfServiceLoginFlowBody: SubmitSelfServiceLoginFlowWithPasswordMethodBody(
          method: 'password',
          identifier: email,
          password: password,
        ),
      );
      
      // Extract session token
      final sessionToken = result.sessionToken;
      
      return Result.success(AuthToken(
        accessToken: sessionToken,
        refreshToken: result.session?.identity?.recoveryAddresses?.first?.value,
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
      ));
    } catch (e) {
      return Result.failure(AuthenticationException(e.toString()));
    }
  }
}
```

### Data Encryption
- **At Rest**: AES-256 encryption for sensitive data in PostgreSQL
- **In Transit**: TLS 1.3 for all network communication
- **Local Storage**: Encrypted SQLite database with Drift
- **File Storage**: MinIO with server-side encryption

## Testing Strategy

### Unit Testing
```dart
// Example unit test for domain logic
group('BubbleEntity', () {
  test('should allow adding member when capacity available', () {
    // Arrange
    final bubble = BubbleEntity(
      id: BubbleId.generate(),
      name: const BubbleName('Test Bubble'),
      capacity: const MemberCapacity(5),
      members: [testMember1, testMember2],
      // ... other properties
    );
    
    final newMember = BubbleMemberEntity(
      userId: UserId.generate(),
      role: MemberRole.participant,
      joinedAt: DateTime.now(),
    );
    
    // Act
    final result = bubble.addMember(newMember);
    
    // Assert
    expect(result.isSuccess, isTrue);
  });
});
```

### Widget Testing
```dart
// Example widget test
testWidgets('BubbleCard displays bubble information', (tester) async {
  // Arrange
  const bubble = BubbleEntity(/* test data */);
  
  // Act
  await tester.pumpWidget(
    MaterialApp(
      home: BubbleCard(bubble: bubble),
    ),
  );
  
  // Assert
  expect(find.text(bubble.name.value), findsOneWidget);
  expect(find.text('${bubble.members.length}/${bubble.capacity.value}'), findsOneWidget);
});
```

### Integration Testing
```dart
// Example integration test
testWidgets('complete bubble creation flow', (tester) async {
  // Setup test environment
  await setupTestDI();
  
  // Navigate to bubble creation
  await tester.pumpWidget(MyApp());
  await tester.tap(find.byKey(const Key('create_bubble_button')));
  await tester.pumpAndSettle();
  
  // Fill bubble form
  await tester.enterText(find.byKey(const Key('bubble_name_field')), 'Test Bubble');
  await tester.tap(find.byKey(const Key('capacity_5_button')));
  
  // Submit form
  await tester.tap(find.byKey(const Key('create_button')));
  await tester.pumpAndSettle();
  
  // Verify bubble was created
  expect(find.text('Bubble created successfully'), findsOneWidget);
});
```

## Deployment & DevOps

### Development Environment
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: hopen_dev
      POSTGRES_USER: hopen
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  valkey:
    image: valkey/valkey:7.2
    ports:
      - "6379:6379"
    volumes:
      - valkey_data:/data

  emqx:
    image: emqx/emqx:5.1
    ports:
      - "1883:1883"      # MQTT
      - "8083:8083"      # WebSocket
      - "18083:18083"    # Dashboard
    environment:
      EMQX_NAME: hopen_emqx
      EMQX_HOST: 127.0.0.1

  minio:
    image: minio/minio
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"      # API
      - "9001:9001"      # Console
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password
    volumes:
      - minio_data:/data

  nats:
    image: nats:2.9
    ports:
      - "4222:4222"      # Client connections
      - "8222:8222"      # HTTP monitoring
      - "6222:6222"      # Routing

  ory-kratos:
    image: oryd/kratos:v1.0.0
    ports:
      - "4433:4433"      # Public API
      - "4434:4434"      # Admin API
    environment:
      DSN: ***************************************/hopen_dev?sslmode=disable
    volumes:
      - ./kratos:/etc/config/kratos
    command: serve -c /etc/config/kratos/kratos.yml --dev --watch-courier

volumes:
  postgres_data:
  valkey_data:
  minio_data:
```

### CI/CD Pipeline
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Run tests
        run: flutter test --coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Build Android APK
        run: flutter build apk --release --tree-shake-icons
      
      - name: Build iOS
        run: flutter build ios --release --no-codesign
      
      - name: Deploy to staging
        run: |
          # Deployment script
          ./scripts/deploy.sh staging

  performance:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run performance analysis
        run: dart scripts/analyze_performance.dart
      
      - name: Performance regression check
        run: flutter test integration_test/performance_test.dart
```

### Production Deployment
- **Container Orchestration**: Docker with health checks and auto-restart
- **Load Balancing**: OVHcloud load balancer with SSL termination
- **Auto Scaling**: Horizontal scaling based on CPU/memory metrics
- **Backup Strategy**: Automated database and file storage backups
- **Monitoring**: Prometheus + Grafana with custom dashboards

## Monitoring & Analytics

### Performance Metrics
```dart
// Custom performance monitoring
class PerformanceMonitor {
  static final Map<String, Stopwatch> _timers = {};
  
  static void startTimer(String operation) {
    _timers[operation] = Stopwatch()..start();
  }
  
  static void endTimer(String operation) {
    final timer = _timers.remove(operation);
    if (timer != null) {
      timer.stop();
      LoggingService.info(
        'Performance: $operation took ${timer.elapsedMilliseconds}ms',
        tag: 'Performance',
      );
      
      // Send to analytics
      PostHog.capture(
        eventName: 'performance_metric',
        properties: {
          'operation': operation,
          'duration_ms': timer.elapsedMilliseconds,
        },
      );
    }
  }
}
```

### Error Tracking
```dart
// Structured error handling
class ErrorTracker {
  static void captureException(
    Exception exception, {
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    // Log locally
    LoggingService.failure(
      'Exception caught: ${exception.toString()}',
      error: exception,
      stackTrace: stackTrace,
      tag: 'ErrorTracker',
    );
    
    // Send to monitoring service
    PostHog.capture(
      eventName: 'error_occurred',
      properties: {
        'error_type': exception.runtimeType.toString(),
        'error_message': exception.toString(),
        'stack_trace': stackTrace?.toString(),
        'context': context,
      },
    );
  }
}
```

## Future Roadmap

### Short Term (Q1 2024)
- [ ] Enhanced WebRTC call quality with adaptive bitrate
- [ ] Advanced bubble scheduling and recurring bubbles
- [ ] Rich media messaging (images, videos, files)
- [ ] Push notification optimization

### Medium Term (Q2-Q3 2024)
- [ ] Web application with Flutter Web
- [ ] Advanced analytics dashboard
- [ ] AI-powered content moderation
- [ ] Multi-language support

### Long Term (Q4 2024+)
- [ ] Enterprise features for organizations
- [ ] Advanced security features (E2E encryption)
- [ ] Integration with external calendars
- [ ] Machine learning for smart recommendations

This technical overview provides a comprehensive understanding of the Hopen application's architecture, implementation details, and operational considerations. The system is designed for scalability, maintainability, and high performance while ensuring security and user privacy.