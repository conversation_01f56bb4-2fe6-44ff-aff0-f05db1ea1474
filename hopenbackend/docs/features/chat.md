# Chat Feature Technical Documentation

## Overview

The Chat feature provides real-time messaging capabilities between Hopen users. It offers a modern, visually appealing interface with custom styling elements that maintain consistency with the overall application design language. The implementation includes a full suite of contemporary messaging features including typing indicators, reactions, rich media support, message search, and reply/forwarding capabilities.

## Backend Architecture

### Chat Microservice

The chat functionality is powered by a dedicated microservice (`hopenbackend/microservices/chat/`) that handles:

**Key Components:**
- `chat.go`: Core messaging operations and real-time coordination
- `message.go`: Message persistence and retrieval
- `typing.go`: Typing indicator management
- `api_types.go`: Generated API type definitions

**Core Chat Operations:**
```go
// Send message endpoint
func (s *Service) SendMessage(ctx context.Context, req *SendMessageRequest) (*SendMessageResponse, error) {
    userID := auth.UserID()
    if userID == "" {
        return nil, errors.ErrUnauthenticated
    }
    
    // Validate friendship exists
    if !s.friendship.AreFriends(ctx, userID, req.RecipientID) {
        return nil, errors.ErrPermissionDenied
    }
    
    // Create message entity
    message := &Message{
        ID:          generateMessageID(),
        SenderID:    userID,
        RecipientID: req.RecipientID,
        Content:     req.Content,
        MessageType: req.MessageType,
        MediaURL:    req.MediaURL,
        CreatedAt:   time.Now(),
        DeliveredAt: nil,
        ReadAt:      nil,
    }
    
    // Store in Cassandra for persistence
    err := s.cassandra.StoreMessage(ctx, message)
    if err != nil {
        return nil, err
    }
    
    // Cache in Valkey for real-time access
    err = s.valkey.CacheMessage(ctx, message)
    if err != nil {
        log.Error("Failed to cache message", err)
    }
    
    // Publish to MQTT for real-time delivery
    err = s.mqtt.PublishMessage(ctx, message)
    if err != nil {
        return nil, err
    }
    
    return &SendMessageResponse{
        MessageID: message.ID,
        SentAt:    message.CreatedAt,
    }, nil
}

// Get conversation history
func (s *Service) GetMessages(ctx context.Context, req *GetMessagesRequest) (*GetMessagesResponse, error) {
    userID := auth.UserID()
    if userID == "" {
        return nil, errors.ErrUnauthenticated
    }
    
    // Check if users are friends
    if !s.friendship.AreFriends(ctx, userID, req.FriendID) {
        return nil, errors.ErrPermissionDenied
    }
    
    // Try cache first (recent messages)
    messages, err := s.valkey.GetRecentMessages(ctx, userID, req.FriendID, req.Limit)
    if err == nil && len(messages) >= req.Limit {
        return &GetMessagesResponse{Messages: messages}, nil
    }
    
    // Fallback to Cassandra for full history
    messages, err = s.cassandra.GetMessages(ctx, userID, req.FriendID, req.Limit, req.Offset)
    if err != nil {
        return nil, err
    }
    
    return &GetMessagesResponse{Messages: messages}, nil
}
```

### Data Persistence Strategy

**Three-Tier Storage Architecture:**

1. **Cassandra (Primary Storage)**
   - Persistent message storage optimized for time-series data
   - Partitioned by conversation_id for efficient retrieval
   - Handles message history, media metadata, and reactions
   - Optimized for high write throughput and horizontal scaling

2. **Valkey (Cache Layer)**
   - Recent messages (last 24 hours) for instant access
   - Typing indicators and presence status
   - Message delivery confirmations
   - Real-time conversation state

3. **Drift (Local Storage)**
   - Offline message queue for sending when reconnected
   - Local message cache for instant UI updates
   - Draft messages and conversation state
   - Sync status tracking

### Real-time Communication

**MQTT Integration:**
- Topic structure: `/chat/{user_id}/messages`
- Message delivery confirmations via MQTT
- Typing indicators broadcast in real-time
- Presence status updates
- Message reactions synchronized across devices

## Technical Implementation

### File Structure

- **Primary Components**
  - `lib/presentation/pages/chat/chat_page.dart`: Main chat UI container
  - `lib/presentation/widgets/chat_bubble.dart`: Message bubble renderer
  - `lib/statefulbusinesslogic/core/models/chat_message.dart`: Data model for messages

### UI Architecture

The chat interface follows a classic messaging app layout with several key custom components:

#### 1. Custom App Bar (`_buildCustomAppBar`)

- **Design**: Gradient background with rounded bottom corners (`[Color(0xFF00A3FF), Color(0xFF0094FF)]`)
- **Components**:
  - Back arrow (left-aligned, vertically centered with avatar)
  - User avatar (displays profile photo or initial)
  - User information (name and online status)
  - Action icons (right-aligned):
    - Phone call (`phone.svg`)
    - Video call (`video.svg`)
    - Screen sharing (`smartphone.svg`)
  - Search button to trigger message search
- **Responsiveness**: Heights, sizes, and spacing scale proportionally based on screen dimensions
- **Visual Details**:
  - Icons use white color filter with consistent sizing (22% of app bar height)
  - Spacing between icons is 15px
  - Status indicator uses `greenAccent[400]` color
  - Padding provides comfortable spacing between elements

#### 2. Message List

- **Implementation**: `ListView.builder` with `reverse: true` to show newest messages at bottom
- **Scrolling**: Managed by `ScrollController` with infrastructure for future pagination
- **Date Separators**: Visual dates (e.g., "February 12, 2022") inserted when date changes between messages
  - Uses `Chip` widget with translucent background matching bubble member styling
  - Custom `RoundedSuperellipseBorder` with consistent border and corner radius (20.0)
- **Typing Indicator**: Animated ellipsis (three dots) shown when simulated typing is in progress
  - Matches incoming message bubble styling
  - Uses `TweenAnimationBuilder` for smooth bouncing animation

#### 3. Message Bubbles (`ChatBubble` Widget)

- **Styling**:
  - **Outgoing Messages**:
    - Gradient background matching app bar (`[Color(0xFF00A3FF), Color(0xFF0094FF)]`)
    - Right-aligned in container
    - Read receipts with double check icon in cyan (`Color(0xFF00FFFF)`) when read
  - **Incoming Messages**:
    - Translucent white background (`Colors.white.withOpacity(0.1)`)
    - Border with `Colors.white.withOpacity(0.3)` at 1.0 width
    - Left-aligned in container
  - **Common Properties**:
    - Rounded corners (20.0 radius)
    - Max width of 75% screen width
    - White text
    - Timestamp in bottom-right (HH:mm format)
    - BoxDecoration for background and border
- **Interactions**:
  - Long-press to show message options menu (Reply, Forward, React)
  - Tap on media to preview

#### 4. Message Input Area (`_buildMessageInputArea`)

- **Design**: Dark background (`Color(0xFF1A2B4D)`) with translucent input field
- **Components**:
  - Text field with rounded corners and multi-line support
  - Attachment button (`Icons.attach_file`)
  - Camera button (`Icons.camera_alt_outlined`)
  - Context-aware send/mic button:
    - Empty: Microphone icon (`Icons.mic`, gray color)
    - With text: Send icon (`send.svg`, cyan color `0xFF00FFFF`)
- **Reply Bar**: Appears above input area when replying to a message
  - Shows preview of original message with vertical accent line
  - Includes cancel button to dismiss reply
  - Auto-focuses text field when reply is initiated

### Enhanced Features

#### 1. Typing Indicators

- Real-time typing status via MQTT (`/chat/{user_id}/typing`)
- Animated ellipsis bubble with staggered animation
- Timer-based cleanup for stale typing indicators
- Debounced typing detection to reduce network traffic

#### 2. Message Reactions

- Support for emoji reactions to messages
- Real-time synchronization via MQTT
- Reactions stored in Cassandra with message references
- Count tracking and aggregation for each emoji type
- Visually consistent with overall app styling

#### 3. Rich Media Previews

- **Media Types**:
  - **Images**: Full-width preview with error handling and loading state
  - **Videos**: Thumbnail with play button overlay
  - **Links**: Rich preview with thumbnail, title, description, and URL
- **Model Support**: Enhanced `ChatMessage` model with media properties:
  - `mediaType` enum (none, image, video, link)
  - `mediaUrl`, `mediaThumbnail`, `mediaTitle`, `mediaDescription`
- **Error Handling**: Fallback UI when media fails to load
- **Storage**: Media files stored in MinIO with CDN delivery

#### 4. Message Search

- Dedicated search UI with custom app bar
- Real-time filtering as user types
- Search across message text, media titles, and descriptions
- Results displayed in custom list with highlighting
- Navigation to found messages when selected
- Backend search via Cassandra secondary indexes

#### 5. Reply & Forwarding

- **Reply Feature**:
  - Visual reply context shown above message text
  - Links original and reply messages in data model
  - Reply bar UI shows active reply context
  - "You" vs sender distinction in UI
- **Forwarding**:
  - Preserves original message content and media
  - Visually indicates forwarded status
  - Creates new message object with updated metadata
- **Interactions**:
  - Long-press menu for Reply/Forward/React options
  - Tap gesture handling for all interactions

### State Management

- **Current Approach**: `StatefulWidget` (`_ChatPageState`) with local message list.
- **Message Model**: Enhanced data model (`ChatMessage`) located in `lib/statefulbusinesslogic/core/models/` with properties for:
  - Core: id, text, timestamp, sender, delivery status
  - Media: type, URLs, metadata
  - Reactions: Map of emoji to count
  - Reply context: reference to original message
  - Forwarding: flag and helper methods
- **Future**: A dedicated `ChatBloc` (in `lib/statefulbusinesslogic/bloc/chat/`) would manage message fetching, sending, real-time updates, and interactions with a `ChatRepository`.

### Background

- Uses `GradientBackground` widget for consistent app styling
- Message containers use translucent backgrounds creating depth against the gradient

## Integration Points

The current implementation integrates with the Go micro-services backend:

1. **Message Persistence**: Messages stored in Cassandra via chat microservice
2. **Real-time Updates**: MQTT integration for instant message delivery
3. **Media Handling**: MinIO integration for file storage and CDN delivery
4. **Message Delivery**: Real-time delivery confirmations via MQTT
5. **Online Status**: Presence information from Valkey cache
6. **Pagination**: Efficient pagination via Cassandra time-series queries

## Usage

The `ChatPage` widget takes three parameters:
- `friendId`: Unique identifier for the conversation partner
- `friendName`: Display name shown in the app bar
- `friendProfilePhoto`: Optional path to the profile image

Navigation to the page is handled through the app's router, typically from the `FriendsPage` or direct messaging contexts.

## CSS-Equivalent Styling Guidelines

For consistency across the app, the Chat feature follows these styling principles:

1. **Colors**:
   - Gradient Blue: `[Color(0xFF00A3FF), Color(0xFF0094FF)]` - App bar, outgoing bubbles
   - Accent Cyan: `Color(0xFF00FFFF)` - Read receipts, send button
   - Translucent White: `Colors.white.withOpacity(0.1)` - Input field, incoming bubbles
   - Dark Background: `Color(0xFF1A2B4D)` - Input area background

2. **Borders**:
   - Light borders: `Border.all(color: Colors.white.withOpacity(0.3), width: 1.0)`
   - Border radius: `BorderRadius.circular(20.0)` for consistent rounding

3. **Text**:
   - Primary text: White with appropriate opacity
   - Status text: `Colors.greenAccent[400]` for online indicator
   - Timestamps: `Colors.white.withOpacity(0.7)` for subtle appearance

4. **Icons**:
   - Standard size: ~22% of app bar height
   - Spacing between icons: 15 logical pixels
   - Color: White for app bar icons, context-specific for input area

## Future Considerations

While the front-end implementation now offers a full suite of messaging features, these areas remain for future work:

1. Backend integration for true persistence and real-time updates
2. Proper WebSocket implementation for typing indicators and online status
3. Media upload capabilities with progress tracking
4. Performance optimization for large chat histories
5. Push notifications and background message handling

## Message Input Logic

- **Attachment Picker**: Shows options for gallery, camera, location, contact.
- **Rich Content**: Handles link previews, embeds (future).

## Data Models

- `ChatMessage`: Represents a single message.
- `User`: Represents a user in the chat (custom implementation from `lib/statefulbusinesslogic/core/models/user.dart`).

## Related Documentation

- See `FriendsPage` documentation for navigation source.
- See `BubblePage` documentation for navigation source.
