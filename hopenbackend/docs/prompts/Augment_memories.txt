

















# Codebase Structure
- User prefers to delete the hopen/lib/domain folder and use models from hopen/lib/statefulbusinesslogic/models instead of having a separate domain layer.
- Tests in /test/ directory should be counterparts of elements in /lib/ directory, maintaining parallel structure.
- The Hopen codebase no longer uses Encore.go - all EncoreRemoteDataSource, EncoreApiService, and other Encore-related functions in /lib/provider/ need to be updated to use alternative implementations.

# Architecture & Design
- User prefers comprehensive architecture documentation that follows existing structure patterns and covers all social app use cases when creating new backend architecture files.
- User prefers to properly implement four-layer dependency rule by creating proper BLoC architecture rather than removing functionality or finding workarounds.
- User prefers to follow the four-layer dependency rule when fixing Flutter app errors.
- User requires token refresh logic to follow OAuth 2.0/OIDC standards with automatic refresh and proper error handling.
- User prefers to use existing connectivity_service.dart instead of creating new network testing utilities.
- User prefers to use HTTP3 when possible for network requests.
- User requires GitHub Actions for CI/CD automation.
- User aims for maintainability, dependability, efficiency, speed, security, reliability, usability, and scalability in the backend architecture.
- User wants to replace Encore.go backend with new stack: Gin/Echo HTTP router, pgx+sqlc for PostgreSQL, NATS JetStream for pub/sub, <PERSON><PERSON> (Kratos/Hydra/Oathkeeper/Keto) for auth, MinIO for storage, EMQX MQTT5 for real-time chat, Valkey for caching, and Kubernetes/Helm for deployment.
- User prefers Valkey-backed token-bucket for rate limiting over go-chi/httprate in the new backend stack.
- Hopen backend uses Go microservices with PostgreSQL (primary), ArangoDB (social graph), Cassandra (chat), Valkey (cache), NATS (events), Ory Stack (auth), MinIO (storage), EMQX MQTT5 (realtime), with specific service boundaries where bubble service handles ALL membership operations in PostgreSQL while ArangoDB is purely for analytics via event-driven updates.

# Data Storage
- User requires ArangoDB for all user relationship data: friends in common, contacts in common, bubble members, bubble requests, and friendship requests - friendship/contact/bubble microservices may need refactoring to consolidate relationship logic. ArangoDB is used for relational data like bubble members in common and friends in common as seen in unified_profile_page.
- User requires using MinIO for object storage (not Encore object storage).
- Hopen uses a multi-database architecture: PostgreSQL for user/bubble content, ArangoDB for social relationships (contacts, bubble_memberships, friendships), Cassandra for chat messages, Valkey (cache), NATS (events), with friendships auto-generated only when bubbles expire (no manual friend requests), and specific service-to-database mappings for each microservice.
- User prefers atomic operations in single database (PostgreSQL) for core transactional data like bubble membership, with ArangoDB reserved for analytics and derived relationships that can be eventually consistent via events.
- The users table needs an is_private column for search privacy.
- User requires Drift for local storage.

# Feature Prioritization
- User prefers to exclude language management, API key management, and user report submission from current implementation phases.

# Chat Functionality
- User prefers to deactivate/disable reactions functionality in chat and bubble chat components.
- Chat reactions should not be implemented.

# Testing
- User requires comprehensive unit test coverage and wants tests created by systematically analyzing all files in the lib directory.
- User requires at least 80% test coverage for the codebase.
- User prefers to fix failing tests by studying passing test patterns to understand proper mock setup and configuration.
- User requires that every file in /lib/presentation/pages/ directory should have corresponding unit tests.
- User requires that every file in lib/presentation directory should have a corresponding unit test in the unit_tests folder.
- User prefers to fix actual implementation (app/backend) before fixing test files when there are compilation errors.

# Implementation Preferences
- User requires complete Ory Stack integration (not just Kratos/Hydra but all Ory tools) for authentication, replacing custom JWT handling, and consistent enterprise middleware usage across all endpoints for production-ready implementation.
- User requires actual production-ready implementations without TODOs/placeholders, proper ACID transactions with pgx.Tx, full Ory Stack integration instead of custom JWT handling, consistent enterprise middleware usage across all services, and real external system integrations rather than mocked implementations.
- User requires complete implementations without TODOs or placeholders when instructed to implement features properly.
- User prefers to use placeholder implementations when real implementations are not available or when instructed to do so.
- User prefers password reset functionality to be implemented without delays.
- User requires never commenting out code and always reading full documentation (MinIO, Encore.go) before implementing features.
- User requires real implementations for user service authentication.
- User requires full implementation of the voting system for bubble requests, referencing technical documentation for contact/bubble/friends request system and bubble kickout request dialog for implementation guidance.

# UI/UX Preferences
- User prefers central button animation speed to match the text animation speed in BubbleCallDialog for UI consistency.
- User prefers to use default Flutter splash screen instead of another_flutter_splash_screen package for splash screen implementation.

# Notifications
- User requires that the notification permission request should appear automatically when navigating to step6_notifications_page.dart and when clicking 'Enable notifications' button, and the UI of the 'Enable notifications' button should never change.
- User requires native iOS/Android notification permission dialogs (not custom Flutter dialogs) to appear both automatically on page navigation and when clicking buttons, while maintaining existing UI appearance and ensuring account creation proceeds regardless of permission choice.
- User requires FCM for push notifications (token-based for individuals, topic-based for groups).

# Service Configuration
- Contact service should not include phone/email sync functionality.
- bubble_membership_service handles all bubble-related requests.
- User prefers bubble memberships to have no 'Role' field.
- User prefers to remove the role field from bubble membership tables while keeping user system roles for platform administration.
- friendship_service should not include friend recommendations.
- recommendation_service should be deleted as there are no recommendations in the app.
- social_analytics_service should consider mutual contacts.
- Friend requests are only generated automatically when a bubble expires, not through manual user actions.
- User prefers NATS.io only for message queuing.
- User prefers AWS only for email notifications (not push notifications).
- Bubble expiry starts at 90 days with +30 day extensions capped at 90 days.
- Bubbles have no privacy settings - they are neither private nor public, only basic properties like name, capacity, and expiry.
- User requires MQTT5 with EMQX server using JWT-as-password authentication via EMQX API Auth.
- User requires NATS JetStream for message queuing.

# WebRTC
- User requires WebRTC for calls.
- User requires implementation of call recording and advanced group features for WebRTC calls.
- In Hopen bubbles, ALL members are completely equal with no creator management rights - only system admins/moderators can manage users, not bubble creators.
- Bubble members cannot update or delete bubbles - only system admins and moderators have these privileges.