===== WORKFLOW TIPS =====

* LAUNCH COMMANDS *
- Before saving the project or entering the git 'push' command, and if you want to reduce the size of the project, run 'cd hopen && flutter clean' command.
- After this command has be done, if you want to launch the app again, run 'cd hopen && flutter pub get'.

* DEVELOPER NOTE *
- To actually use a <developer_note>, just place it in the final user message, like this:
--
{user_prompt_here}

<developer_note>
{developer_note_here}
</developer_note>
--

* RUNNING THE APP (OR FRONTEND)*
- launch the app : flutter run
- launch the app on chrome with hot reload : flutter run -d chrome
- launch the app on chrome with device preview : flutter run -t lib/main_dev.dart -d chrome
- launch the app with verbose functionnality : flutter run -d chrome --verbose
- launch the app on the Samsung device : flutter run -d RFCNC10CWAJ --hot
- launch the app on the Samsung device of Kevin (wireless) : flutter run -d adb-RFCNC10CWAJ-4MFQq4._adb-tls-connect._tcp --hot
- launch the app on the iPhone device of Felana (wireless) : flutter run -d 00008020-001315311A79002E --hot


- launch the Samsung S24 emulator : flutter emulators --launch Samsung_Galaxy_S24
- launch the app on the Samsung S24 simulator : flutter run -d emulator-5554
- launch the app on the iPhone 16 Pro simulator : flutter run -d A01C80C5-B639-48D0-A220-D7A055DD760D --hot
- launch the app on the iPhone 16 Pro Max simulator : flutter run -d 24942F86-3060-4B2A-962B-60BD9614F1F4 --hot

* RUNNING THE SERVER (OR BACKEND) *

- docker compose up --build --remove-orphans

FOR DEV :
flutter run -d RFCNC10CWAJ --dart-define=DOCKER_HOST_IP=********* --dart-define=ENVIRONMENT=development

* CLEANING *
cd hopen 
flutter clean

in Makefile, there is a clean function to clean the backend
cd hopenbackend && make clean

* COMMITING AND PUSHING TO REPO *
- git add .
- git status
- git commit -am "???"
- git push origin main

* BEST PROMPTS *
- When you run into problems, ask cursor to write a report with all files
listed and what they do and the problems encountered.
- Always end your prompts with something like: "If you need clarification or have any questions, feel free to ask.”

1. THE FEWER LINES OF CODE, THE BETTER  
The fewer lines of code, the better.
2. PROCEED LIKE A SENIOR DEVELOPER  
Proceed like a senior developer.  
(Alternate version: Proceed like a 10x engineer.)
3. DO NOT STOP UNTIL COMPLETE  
Do not stop working on this until you've implemented this feature fully and completely.
4. THREE REASONING PARAGRAPHS  
Start by writing three reasoning paragraphs analyzing what the error might be. Do not jump to conclusions.
5. ANSWER IN SHORT  
Answer in short.
6. DO NOT DELETE COMMENTS  
Do not delete comments.
7. SUMMARY OF CURRENT STATE  
Before we proceed, I need you to give me a summary of the current state. Summarize what we just did, which files were updated, and what didn’t work. Do not include assumptions or theories—only the facts.
8. UNBIASED 50/50  
Before you answer, I want you to write two detailed paragraphs, one for each solution. Do not jump to conclusions or commit to either solution until you have fully considered both. Then tell me which solution is obviously better and why.
9. PROPERLY FORMED SEARCH QUERY  
Your task is to write a one-paragraph search query as if you were instructing a human researcher. Clearly state what to find and request code snippets or technical details when relevant.
10. START WITH UNCERTAINTY  
You should start the reasoning paragraph with lots of uncertainty and slowly gain confidence as you think about the item more.
11. BE CAREFUL WITH RED HERRINGS  
Give me the TL;DR of the search results, but be careful. Often the search results contain dangerous and distracting red herrings.
12. ONLY INCLUDE TRULY NECESSARY STEPS  
Break this large change into the required steps. Only include the truly necessary steps.

* TESTING WORKFLOW *
- Run all tests: flutter test
- Run a specific test file: flutter test test/path/to/file_test.dart
- Run all tests in a directory: flutter test test/statefulbusinesslogic/
- Run tests with coverage: flutter test --coverage
- Generate coverage report: genhtml coverage/lcov.info -o coverage/html
- Debug tests: flutter test --start-paused path/to/test.dart

* TEST NAMING CONVENTIONS *
- Test files should end with _test.dart
- Test groups should describe the component being tested
- Test cases should describe the expected behavior in a "when/should" format
- Example: "AuthBloc emits [Loading, Authenticated] when LoginEvent is added and login succeeds"








