# Example Gateway Configuration
# This file shows how to configure the API Gateway routing

enterprise:
  gateway:
    enabled: true
    port: 8080
    service_discovery: true
    load_balancing: true
    routes:
      # Authentication service
      - path: "/api/v1/auth"
        service_name: "auth-service"
        strip_prefix: true
        timeout_seconds: 30
      
      # User management service
      - path: "/api/v1/users"
        service_name: "user-service"
        strip_prefix: true
        timeout_seconds: 30
      
      # Bubble management service
      - path: "/api/v1/bubbles"
        service_name: "bubble-service"
        strip_prefix: true
        timeout_seconds: 30
      
      # Contact management service
      - path: "/api/v1/contact"
        service_name: "contact-service"
        strip_prefix: true
        timeout_seconds: 30
      
      # Friendship management service
      - path: "/api/v1/friendship"
        service_name: "friendship-service"
        strip_prefix: true
        timeout_seconds: 30
      
      # Social analytics service
      - path: "/api/v1/social"
        service_name: "social-analytics-service"
        strip_prefix: true
        timeout_seconds: 60
      
      # Call management service
      - path: "/api/v1/calls"
        service_name: "call-service"
        strip_prefix: true
        timeout_seconds: 120
      
      # Notification service
      - path: "/api/v1/notifications"
        service_name: "notification-service"
        strip_prefix: true
        timeout_seconds: 30
      
      # Real-time service
      - path: "/api/v1/realtime"
        service_name: "realtime-service"
        strip_prefix: true
        timeout_seconds: 30
      
      # Media service
      - path: "/api/v1/media"
        service_name: "media-service"
        strip_prefix: true
        timeout_seconds: 60
      
      # Sync service
      - path: "/api/v1/sync"
        service_name: "sync-service"
        strip_prefix: true
        timeout_seconds: 30

# Example service registration
# Services can be registered programmatically or via configuration
services:
  auth-service:
    - id: "auth-1"
      host: "localhost"
      port: 8001
      health: "healthy"
  
  user-service:
    - id: "user-1"
      host: "localhost"
      port: 8002
      health: "healthy"
    - id: "user-2"
      host: "localhost"
      port: 8003
      health: "healthy"
  
  bubble-service:
    - id: "bubble-1"
      host: "localhost"
      port: 8004
      health: "healthy" 