# Bubble Badge Performance Optimization - Hybrid Approach

## Overview

The bubble badge feature has been optimized using a **hybrid approach** that combines:
- **Rich Analytics**: Full immutable audit trail with `UNIQUE(bubble_id, user_id, status)`
- **High Performance**: Denormalized `member_count` with intelligent triggers

This solves the N+1 query problem while preserving complete historical data for analytics.

## The Hybrid Approach: Best of Both Worlds

### Why Hybrid?
- **Rich Analytics**: The `UNIQUE(bubble_id, user_id, status)` constraint creates an immutable audit trail
- **High Performance**: The `member_count` denormalization eliminates expensive COUNT operations
- **Data Integrity**: Partial unique index prevents multiple 'active' records while allowing full history

### Key Components:
1. **Immutable Log Pattern**: Full history of all membership status changes
2. **Denormalized Performance**: Pre-calculated member counts for instant reads
3. **Intelligent Triggers**: Smart logic that handles the log pattern correctly
4. **Partial Unique Index**: Prevents data corruption while preserving history

## What Was Implemented

### 1. Database Schema Changes (in `postgresql.go`)

#### Added `member_count` column to bubbles table:
```sql
member_count INTEGER NOT NULL DEFAULT 0 CHECK (member_count >= 0)
```

#### Added index for performance:
```sql
CREATE INDEX IF NOT EXISTS idx_bubbles_member_count ON bubbles(member_count);
```

#### Added trigger system to maintain accuracy:
```sql
-- Trigger function that automatically updates member_count
CREATE OR REPLACE FUNCTION update_bubble_member_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Increment when user becomes active
    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') AND NEW.status = 'active' 
       AND (TG_OP = 'INSERT' OR OLD.status <> 'active') THEN
        UPDATE bubbles SET member_count = member_count + 1 WHERE id = NEW.bubble_id;
    
    -- Decrement when active user leaves/removed
    ELSIF (TG_OP = 'DELETE' OR TG_OP = 'UPDATE') AND OLD.status = 'active' 
          AND (TG_OP = 'DELETE' OR NEW.status <> 'active') THEN
        UPDATE bubbles SET member_count = member_count - 1 WHERE id = OLD.bubble_id;
    END IF;

    IF (TG_OP = 'UPDATE') THEN
        RETURN NEW;
    ELSE
        RETURN OLD;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Trigger that fires on bubble_members changes
CREATE TRIGGER sync_member_count
AFTER INSERT OR UPDATE OR DELETE ON bubble_members
FOR EACH ROW
EXECUTE FUNCTION update_bubble_member_count();
```

### 2. Database Functions (in `postgresql.go`)

#### `GetBubbleBadgeStatusForUsers(ctx, userIDs []string)`
- Optimized query for multiple users
- Uses pre-calculated `member_count` 
- Avoids expensive COUNT operations
- Returns `[]BubbleBadgeStatus`

#### `GetBubbleBadgeStatusForUser(ctx, userID string)`
- Optimized query for single user
- Returns badge status string directly

### 3. API Endpoints (in `bubble/service.go`)

#### `POST /api/v1/bubbles/badge-status`
```json
{
  "user_ids": ["uuid1", "uuid2", "uuid3"]
}
```

Response:
```json
{
  "bubble_badge_statuses": [
    {"user_id": "uuid1", "status": "no_bubble"},
    {"user_id": "uuid2", "status": "in_a_bubble"},
    {"user_id": "uuid3", "status": "bubble_full"}
  ],
  "count": 3
}
```

#### `GET /api/v1/bubbles/badge-status/{user_id}`
Response:
```json
{
  "user_id": "uuid1",
  "status": "no_bubble"
}
```

## Badge Status Values

- **`"no_bubble"`** - User is not in any active bubble
- **`"in_a_bubble"`** - User is in a bubble that is not full
- **`"bubble_full"`** - User is in a bubble that has reached capacity

## Performance Benefits

### Before Optimization
- **20 users**: 21 database queries (N+1 problem)
- **100 users**: 101 database queries
- Expensive COUNT operations for each user

### After Optimization  
- **20 users**: 1 database query
- **100 users**: 1 database query
- No COUNT operations (uses pre-calculated values)
- **20x faster** for typical search results

## The Optimized Query

```sql
SELECT
    u.id AS user_id,
    CASE
        WHEN b.id IS NULL THEN 'no_bubble'
        WHEN b.member_count = b.capacity THEN 'bubble_full'
        ELSE 'in_a_bubble'
    END AS bubble_badge_status
FROM
    unnest($1::uuid[]) AS u(id)
LEFT JOIN bubble_members bm ON u.id = bm.user_id AND bm.status = 'active'
LEFT JOIN bubbles b ON bm.bubble_id = b.id AND b.status = 'active';
```

## Application Logic Patterns

### Working with the Immutable Log Pattern

The hybrid approach requires understanding how to work with the immutable log:

#### When a user is invited to a bubble:
```sql
INSERT INTO bubble_members (bubble_id, user_id, status)
VALUES (?, ?, 'pending');
-- Trigger: No change to member_count (status is not 'active')
```

#### When they accept the invitation:
```sql
-- Option A: Pure Log Pattern (INSERT new record)
INSERT INTO bubble_members (bubble_id, user_id, status)
VALUES (?, ?, 'active');
-- Trigger: Increments member_count by 1

-- Option B: Hybrid Pattern (UPDATE existing record)
UPDATE bubble_members
SET status = 'active', joined_at = NOW()
WHERE bubble_id = ? AND user_id = ? AND status = 'pending';
-- Trigger: Increments member_count by 1 (pending -> active)
```

#### When they leave the bubble:
```sql
-- Option A: Pure Log Pattern (INSERT new record)
INSERT INTO bubble_members (bubble_id, user_id, status)
VALUES (?, ?, 'left');
-- Trigger: Decrements member_count by 1

-- Option B: Hybrid Pattern (UPDATE existing record)
UPDATE bubble_members
SET status = 'left', left_at = NOW()
WHERE bubble_id = ? AND user_id = ? AND status = 'active';
-- Trigger: Decrements member_count by 1 (active -> left)
```

#### Analytics Queries (The Power of the Log):
```sql
-- Get complete membership history for a user
SELECT * FROM bubble_members
WHERE user_id = ?
ORDER BY created_at;

-- Count how many times a user has joined bubbles
SELECT COUNT(*) FROM bubble_members
WHERE user_id = ? AND status = 'active';

-- Average time users spend in bubbles
SELECT AVG(left_at - joined_at) FROM bubble_members
WHERE status = 'left' AND left_at IS NOT NULL;
```

## Usage Examples

### Frontend Integration
```javascript
// Get badge status for search results
const userIds = searchResults.map(user => user.id);
const response = await fetch('/api/v1/bubbles/badge-status', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ user_ids: userIds })
});
const { bubble_badge_statuses } = await response.json();

// Map status to users
const statusMap = new Map(
  bubble_badge_statuses.map(item => [item.user_id, item.status])
);
```

### Backend Integration
```go
// Get status for multiple users
userIDs := []string{"uuid1", "uuid2", "uuid3"}
statuses, err := db.GetBubbleBadgeStatusForUsers(ctx, userIDs)
if err != nil {
    return err
}

// Get status for single user
status, err := db.GetBubbleBadgeStatusForUser(ctx, userID)
if err != nil {
    return err
}
```

## Data Integrity

The trigger system ensures that `member_count` is always accurate:
- Automatically increments when users join bubbles
- Automatically decrements when users leave bubbles
- Handles all status changes (active, left, removed, etc.)
- Race condition safe

## Monitoring

To verify data integrity:
```sql
-- Check for inconsistencies
SELECT 
    b.id,
    b.member_count as stored_count,
    COUNT(bm.user_id) as actual_count
FROM bubbles b
LEFT JOIN bubble_members bm ON b.id = bm.bubble_id AND bm.status = 'active'
WHERE b.status = 'active'
GROUP BY b.id, b.member_count
HAVING b.member_count != COUNT(bm.user_id);
```

## Implementation Notes

1. **Automatic Initialization**: The schema includes a query to initialize `member_count` for existing bubbles
2. **Validation**: API endpoints validate UUID format and limit batch size to 100 users
3. **Error Handling**: Proper error responses for invalid requests
4. **Logging**: Comprehensive logging for debugging and monitoring
5. **Type Safety**: Proper Go structs for request/response handling

This optimization transforms the bubble badge feature from a performance bottleneck into a lightning-fast operation that scales linearly with the application.
