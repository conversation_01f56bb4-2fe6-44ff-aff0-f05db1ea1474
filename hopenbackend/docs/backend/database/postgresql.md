# PostgreSQL Database Layer Documentation

**Author:** Senior Backend Engineer
**Date:** 2025-01-26
**Last Updated:** 2025-01-26

## 1. Overview

This document provides a comprehensive technical overview of the `database` package, specifically the `postgresql.go` file. This package serves as the dedicated Data Access Layer (DAL) for all interactions with the PostgreSQL database in the Hopen social platform.

The core design centers around the `PostgreSQLClient`, which encapsulates a `pgxpool` connection pool. This approach provides a robust, efficient, and centralized interface for all database operations, from initial connection and schema management to executing CRUD (Create, Read, Update, Delete) queries.

### Key Features
- **Enterprise-grade connection pooling** with pgx/v5
- **Atomic schema management** with transactional DDL
- **Comprehensive relationship system** supporting contacts, friends, and bubble members
- **Request management system** for contact, bubble, and friend requests
- **Advanced constraint enforcement** at the database level
- **Performance-optimized indexing strategy**

## 2. Core Components

### 2.1. `PostgreSQLClient` Struct

The `PostgreSQLClient` is the primary struct for all database interactions. It is designed to be instantiated once at application startup and shared as a dependency.

```go
type PostgreSQLClient struct {
	Pool   *pgxpool.Pool
	logger *zap.Logger
	config *config.PostgreSQLConfig
}
```

-   `Pool *pgxpool.Pool`: An instance of a `pgx/v5` connection pool. Utilizing a connection pool is a critical performance best practice. It mitigates the high latency of establishing new database connections by maintaining and reusing a set of open connections for concurrent requests.
-   `logger *zap.Logger`: An instance of the Zap structured logger. This is used for logging critical events such as successful connections, pool closure, and errors, providing essential observability.
-   `config *config.PostgreSQLConfig`: A reference to the PostgreSQL configuration struct, containing all parameters for connecting to and tuning the database pool.

### 2.2. `NewPostgreSQLClient(cfg, logger)`

This constructor function is the sole entry point for creating a `PostgreSQLClient`. Its execution flow is as follows:

1.  **Configuration Parsing**: It first calls `pgxpool.ParseConfig` to parse and validate the DSN (Data Source Name) from the configuration. This ensures the connection string is well-formed before proceeding.
2.  **Pool Tuning**: It then applies fine-grained tuning parameters to the pool configuration (`MaxConns`, `MinConns`, `MaxConnLifetime`, etc.). These settings are crucial for optimizing database resource utilization and application performance under various load profiles.
3.  **Pool Creation**: `pgxpool.NewWithConfig` creates the connection pool. It's important to note that this function does not necessarily establish all connections immediately; connections are typically created on-demand up to `MinConns`.
4.  **Connection Verification**: A `pool.Ping()` is performed with a 10-second timeout. This is a vital startup health check. It ensures the application fails fast if the database is unreachable, preventing it from starting in a broken state. If the ping fails, any partially created pool resources are cleaned up via `pool.Close()`.
5.  **Logging**: Upon success, it logs key connection details (host, port, database name), which is invaluable for debugging and operational monitoring.

## 3. Schema Management

### 3.1. `InitializeSchema(ctx)`

This method is responsible for idempotently setting up the entire database schema.

#### Design Philosophy

The entire schema is defined within a single, large SQL string and executed within an explicit transaction (`BEGIN; ... COMMIT;`). This has two key advantages:

1.  **Atomicity**: The entire schema setup is an all-or-nothing operation. If any statement fails, the transaction is rolled back, leaving the database in its previous state.
2.  **Idempotency**: The script heavily uses `CREATE ... IF NOT EXISTS` and `DO $$ ... EXCEPTION WHEN duplicate_object THEN null; END $$` blocks. This allows the initialization logic to be run safely on every application start. It will create missing objects on the first run and do nothing on subsequent runs.

#### Schema Breakdown

1.  **Extensions and Types**:
    -   `uuid-ossp`: Enabled to use `uuid_generate_v4()` for default primary key generation, a standard for modern applications.
    -   **ENUM Types**: Comprehensive set of custom ENUM types for data integrity:
        - `bubble_status`: ('active', 'expired', 'archived')
        - `bubble_member_status`: ('active', 'left', 'removed', 'pending', 'accepted', 'declined')
        - `bubble_request_type`: ('invite', 'join', 'kick', 'start')
        - `request_status`: ('pending', 'approved', 'rejected', 'expired')
        - `vote_type`: ('approve', 'reject')
        - `friend_request_status`: ('pending', 'accepted', 'declined', 'expired')
        - `contact_request_status`: ('pending', 'accepted', 'declined', 'expired')
        - `notification_type`: Comprehensive enum with 51 notification types covering all scenarios

2.  **Core Table Architecture**:

    **Users Table (`users`)**:
    - UUID primary keys with comprehensive user profile data
    - Support for both bucket-based media storage (`avatar_bucket_name`, `avatar_object_key`) and legacy URL storage
    - Premium user flags and ban management
    - JSONB notification settings for flexible configuration

    **Bubbles System (`bubbles`, `bubble_members`)**:
    - Capacity constraints (2-5 members) enforced at database level
    - Automatic member count maintenance via triggers
    - Expiration tracking with friend request generation flags
    - Status-based member management with unique constraints

    **Request Management System**:
    - **`contact_requests`**: Manual contact requests between users
    - **`friend_requests`**: Auto-generated requests from expired bubbles only
    - **`bubble_requests`**: All bubble-related operations (invite, join, kick, start)
    - **`request_votes`**: Voting system for bubble requests requiring consensus

    **Relationship System (`user_relationships`)**:
    - Bidirectional relationship tracking with single active relationship constraint
    - Relationship types: 'none', 'contact', 'bubbler', 'friend', 'maybefriend', 'block'
    - Status management: 'active', 'inactive', 'expired'
    - Metadata support for relationship context

3.  **Advanced Indexing Strategy**:
    -   **Standard B-Tree Indexes**: On all foreign keys and frequently filtered columns
    -   **Unique Indexes**: Business uniqueness constraints (usernames, emails)
    -   **Partial (Filtered) Indexes**:
        - `idx_unique_active_member`: Ensures one active bubble membership per user per bubble
        - `idx_unique_active_relationship`: Enforces single active relationship between users
    -   **Expression-based Indexes**:
        - `idx_unique_active_relationship` uses `LEAST/GREATEST` for bidirectional uniqueness
    -   **Composite Indexes**: Optimized for common query patterns (user_id + status combinations)

4.  **Business Logic Enforcement via Triggers**:
    -   **`trigger_set_timestamp()`**: Automatic `updated_at` maintenance across all tables
    -   **`check_user_bubble_limit()`**: Enforces bubble limits (1 for free, 5 for premium users)
    -   **`update_bubble_member_count_for_log()`**: Maintains denormalized member counts
    -   **Constraint Triggers**: Automatic relationship lifecycle management

## 4. Comprehensive Data Access Layer (DAL)

The implementation provides a complete DAL with specialized methods for each domain area:

### 4.1. User Management
-   **Read Operations** (`GetUserByID`, `GetUserByEmail`, `GetUserByUsername`): Standard retrieval with optimized single-row queries
-   **Search Operation** (`SearchUsers`): Advanced search with ranking algorithm prioritizing username matches
-   **Write Operations** (`CreateUser`, `UpdateUser`): Efficient operations using `RETURNING` clauses
-   **State Management** (`SoftDeleteUser`, `BanUser`, `UnbanUser`): Non-destructive state changes with audit trails

### 4.2. Contact Request Management
-   **`CreateContactRequest()`**: Creates manual contact requests between users
-   **`GetContactRequestsByUser()`**: Retrieves sent/received contact requests with filtering
-   **`UpdateContactRequestStatus()`**: Status transitions (pending → accepted/declined)
-   **`GetPendingContactRequestsCount()`**: Efficient counting for UI badges and notifications

### 4.3. Friend Request Management (Auto-Generated Only)
-   **`CreateFriendRequest()`**: Creates auto-generated friend requests from expired bubbles
-   **`GetFriendRequestsByUser()`**: Retrieves friend requests with source bubble tracking
-   **`UpdateFriendRequestStatus()`**: Handles acceptance/decline with relationship updates
-   **Constraint Enforcement**: All friend requests must have `source_bubble_id` and `auto_generated = true`

### 4.4. Bubble Request Management
-   **`GetBubbleRequestsByUser()`**: Comprehensive bubble request retrieval (invite, join, kick, start)
-   **`GetPendingBubbleRequestsCount()`**: Real-time pending request counts
-   **Request Type Support**:
     - **Start**: Creates new bubbles (bubble_id initially NULL)
     - **Invite**: Existing bubble members invite new users
     - **Join**: Users request to join existing bubbles
     - **Kick**: Consensus-based member removal

### 4.5. User Relationship Management
-   **`CreateUserRelationship()`**: Manages relationship lifecycle with automatic expiration of previous relationships
-   **`GetUserRelationships()`**: Retrieves active relationships by type with efficient filtering
-   **Bidirectional Management**: Automatically creates symmetric relationship records
-   **Single Active Relationship Rule**: Database-enforced constraint preventing relationship conflicts

## 5. Relationship System Architecture

### 5.1. Relationship Lifecycle Flow

The Hopen platform implements a sophisticated relationship system with the following progression:

```
Unknown Users → 'none' → 'contact' → 'bubbler' → 'maybefriend' → 'friend'/'contact'
                   ↓                                                      ↑
                'block' ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

**Detailed Flow:**
1. **Unknown**: No relationship record exists
2. **None**: First interaction creates 'none' relationship (active)
3. **Contact**: Manual contact request accepted → 'contact' relationship (active)
4. **Bubbler**: Users join same bubble → 'bubbler' relationship (active)
5. **Maybe Friend**: Bubble expires → 'maybefriend' relationship (active) + auto-generated friend requests
6. **Final State**:
   - Both accept friend request → 'friend' relationship (active)
   - One declines friend request → 'contact' relationship (active)
7. **Block**: Any user can block another → 'block' relationship (active)

### 5.2. Critical Business Rules

**Single Active Relationship Constraint:**
- Only ONE active relationship type allowed between any two users
- Enforced by unique index: `idx_unique_active_relationship`
- Uses `LEAST(from_user_id, to_user_id), GREATEST(from_user_id, to_user_id)` for bidirectional uniqueness
- Previous relationships automatically set to 'expired' status when new relationship created

**Request System Separation:**
- **Contact Requests**: Manual, user-initiated, no source tracking required
- **Friend Requests**: Auto-generated ONLY, always include `source_bubble_id`, `auto_generated = true`
- **Bubble Requests**: Support all bubble operations with nullable `bubble_id` for start requests

### 5.3. Data Models

The implementation defines clean, focused data models:

**Core Models:**
- **`User`**: Clean user model without denormalized request tracking
- **`ContactRequest`**: Manual contact requests (no message field)
- **`FriendRequest`**: Auto-generated friend requests with source bubble tracking
- **`BubbleRequest`**: All bubble operations (invite, join, kick, start)
- **`RequestVote`**: Voting system for consensus-based bubble decisions

**Anti-Pattern Elimination:**
- Removed denormalized pending request arrays from User struct
- Eliminated caching of request states in user records
- All request data retrieved from authoritative source tables
- Prevents data inconsistency and stale cache issues

## 6. Performance Optimization Strategy

### 6.1. Connection Pool Tuning
- **pgx/v5 Connection Pool**: Enterprise-grade connection management
- **Configurable Parameters**: MaxConns, MinConns, MaxConnLifetime, MaxConnIdleTime
- **Health Check Monitoring**: Periodic connection validation
- **Resource Management**: Automatic connection cleanup and recycling

### 6.2. Query Optimization
- **Prepared Statement Benefits**: pgx automatically prepares frequently used queries
- **Single Round-Trip Operations**: Extensive use of `RETURNING` clauses
- **Efficient Scanning**: Direct struct mapping without reflection overhead
- **Batch Operations**: Support for bulk operations where applicable

### 6.3. Indexing Strategy
- **Covering Indexes**: Reduce table lookups for common queries
- **Partial Indexes**: Index only relevant subset of data (e.g., active relationships)
- **Expression Indexes**: Support complex query patterns (bidirectional relationships)
- **Composite Indexes**: Optimized for multi-column filtering

### 6.4. Denormalization Patterns
- **Bubble Member Counts**: Maintained via triggers for fast bubble listing
- **Relationship Status Caching**: Active relationships indexed for quick lookups
- **Request Counting**: Efficient badge count queries without full table scans

## 7. Operational Considerations

### 7.1. Schema Migration Strategy
- **Idempotent Initialization**: Safe to run on every application start
- **Atomic Transactions**: All-or-nothing schema updates
- **Backward Compatibility**: Careful handling of schema changes
- **Version Control**: Schema changes tracked in application code

### 7.2. Monitoring and Observability
- **Connection Pool Metrics**: Available via `Stats()` method
- **Query Performance**: Structured logging with zap
- **Error Tracking**: Comprehensive error wrapping and context
- **Health Checks**: Database connectivity validation

### 7.3. Data Integrity Guarantees
- **Foreign Key Constraints**: Automatic referential integrity
- **Check Constraints**: Business rule enforcement at database level
- **Unique Constraints**: Prevent duplicate data scenarios
- **Trigger-based Validation**: Complex business logic enforcement

## 8. Error Handling

The data access methods demonstrate a robust error handling strategy:

1.  **Contextual Wrapping**: All errors wrapped with `fmt.Errorf("...: %w", err)` for debugging context
2.  **Error Translation**: `HandlePgxError` utility translates database errors to application errors
3.  **Constraint Violation Handling**: Specific handling for unique violations, foreign key errors
4.  **Transaction Safety**: Proper rollback handling in multi-statement operations
5.  **Resource Cleanup**: Automatic connection and result set cleanup via defer statements

## 9. Security Considerations

### 9.1. SQL Injection Prevention
- **Parameterized Queries**: All user input properly parameterized
- **No Dynamic SQL**: Query structure defined at compile time
- **Input Validation**: Type-safe parameter binding

### 9.2. Access Control
- **Connection Security**: Secure connection string management
- **Principle of Least Privilege**: Database user permissions
- **Audit Trails**: Comprehensive logging of data modifications

### 9.3. Data Protection
- **Soft Deletes**: User data preservation for recovery
- **Encryption at Rest**: Database-level encryption support
- **Backup Strategy**: Point-in-time recovery capabilities

## 10. Best Practices and Recommendations

### 10.1. Development Guidelines

**Query Patterns:**
- Always use parameterized queries for user input
- Leverage `RETURNING` clauses for single round-trip operations
- Use transactions for multi-table operations
- Implement proper error handling with context

**Schema Evolution:**
- Add new columns as nullable initially
- Use feature flags for gradual rollouts
- Maintain backward compatibility during transitions
- Document all constraint changes

**Performance:**
- Monitor query execution plans regularly
- Use `EXPLAIN ANALYZE` for query optimization
- Consider read replicas for heavy read workloads
- Implement connection pool monitoring

### 10.2. Architectural Improvements

**Separation of Concerns:**
- Consider moving data models to dedicated `pkg/models` package
- Implement repository pattern for better testability
- Add service layer for complex business logic
- Use dependency injection for better modularity

**Scalability Considerations:**
- Implement database sharding strategy for user data
- Consider read replicas for relationship queries
- Add caching layer for frequently accessed data
- Monitor and optimize slow queries

**Testing Strategy:**
- Unit tests for all DAL methods
- Integration tests with test database
- Performance benchmarks for critical queries
- Database migration testing

### 10.3. Production Readiness

**Monitoring:**
- Database connection pool metrics
- Query performance monitoring
- Error rate tracking
- Resource utilization alerts

**Backup and Recovery:**
- Automated daily backups
- Point-in-time recovery testing
- Disaster recovery procedures
- Data retention policies

**Security:**
- Regular security audits
- Access control reviews
- Encryption key rotation
- Vulnerability assessments

## 11. Conclusion

The PostgreSQL database layer represents a well-architected, enterprise-grade data access solution for the Hopen social platform. Key strengths include:

- **Robust Relationship Management**: Sophisticated relationship lifecycle with database-enforced constraints
- **Comprehensive Request System**: Separate handling for contact, friend, and bubble requests
- **Performance Optimization**: Advanced indexing and query optimization strategies
- **Data Integrity**: Multi-layered constraint enforcement and validation
- **Operational Excellence**: Monitoring, error handling, and maintenance capabilities

## 8. Notification System Architecture

### 8.1. Notification Type Strategy
The notification system implements a sophisticated dual-strategy approach with 51 total notification types:

**Dialog-Based Events (12 types)** - Trigger immediate dialogs, stored for analytics:
- Contact/Friend: `contactRequestReceived`, `contactRequestAccepted`, `friendRequestReceived`, `friendRequestAccepted`
- Bubble Interactions: `bubbleInviteRequestReceived`, `bubbleJoinRequestReceived`, `bubbleJoinRequestAccepted`, `bubbleMemberJoined`
- Bubble Management: `bubbleProposeRequestReceived`, `bubbleKickoutRequestReceived`, `bubbleVotekickInitiated`, `bubbleStartRequestReceived`

**Notification List Events (39 types)** - Appear in notification feed:
- Response notifications: `contactRequestDeclined`, `bubbleJoinRequestRejected`, `bubbleInviteRequestRejected`, `bubbleVotekickPassed`
- Communication: `bubbleChatMessageReceived`, `bubbleVoiceMessageReceived`, `bubbleVideoMessageReceived`, `bubbleAudioCallIncoming`, etc.
- Lifecycle: `bubblePopReminder60Days`, `bubblePopReminder30Days`, `bubblePopReminder20Days`, etc.
- System: `statusUpdates`, `securityAlerts`, `appUpdates`, `bubbleExpired`, `bubbleExpiringSoon`, `bubbleIsFull`

### 8.2. Database Schema
```sql
-- Type-safe notification enum
CREATE TYPE notification_type AS ENUM (
    'contactRequestReceived', 'contactRequestAccepted', 'contactRequestDeclined',
    'friendRequestReceived', 'friendRequestAccepted',
    'bubbleInviteRequestReceived', 'bubbleJoinRequestReceived', 'bubbleJoinRequestAccepted',
    'bubbleJoinRequestRejected', 'bubbleInviteRequestRejected', 'bubbleMemberJoined',
    'bubbleProposeRequestReceived', 'bubbleKickoutRequestReceived', 'bubbleVotekickInitiated',
    'bubbleVotekickPassed', 'bubbleStartRequestReceived', 'bubbleExpired',
    -- ... (51 total types)
);

-- Optimized notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Performance-optimized composite index
CREATE INDEX idx_notifications_user_id_is_read_created_at
ON notifications (user_id, is_read, created_at DESC);
```

### 8.3. Key Benefits
- **Type Safety**: ENUM constraints prevent invalid notification types
- **Performance**: Composite index optimized for notification feed queries
- **Data Integrity**: CASCADE deletion maintains referential integrity
- **User Experience**: Dual strategy prevents notification spam
- **Analytics**: Complete audit trail for all user interactions

The implementation successfully balances performance, maintainability, and scalability while providing a solid foundation for the social features of the Hopen platform.

**Next Steps:**
1. Implement comprehensive test suite
2. Add performance monitoring and alerting
3. Consider architectural improvements for better separation of concerns
4. Plan for horizontal scaling as user base grows
