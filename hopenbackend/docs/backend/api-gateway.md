# API Gateway Documentation

## Overview

The API Gateway is a core component of the Hopen backend that provides intelligent routing, service discovery, load balancing, and health monitoring for microservices. It acts as a single entry point for all client requests and routes them to the appropriate microservices.

## Architecture

### Key Components

1. **Service Registry**: In-memory registry for tracking service instances
2. **Health Checker**: Asynchronous health monitoring of registered services
3. **Load Balancer**: Intelligent service selection based on health and load
4. **Reverse Proxy**: HTTP request routing using Go's `httputil.NewSingleHostReverseProxy`
5. **Configuration-Driven Routing**: Dynamic routing based on YAML configuration

### Design Principles

- **Separation of Concerns**: Routing logic is separated from service discovery
- **Configuration as Code**: Routes are defined in configuration files, not hardcoded
- **Fault Tolerance**: Health checks ensure only healthy services receive traffic
- **Extensibility**: Interfaces allow for different load balancing and registry implementations

## Configuration

### Gateway Configuration

```yaml
enterprise:
  gateway:
    enabled: true
    port: 8080
    service_discovery: true
    load_balancing: true
    routes:
      - path: "/api/v1/auth"
        service_name: "auth-service"
        strip_prefix: true
        timeout_seconds: 30
```

### Route Configuration

Each route defines:
- `path`: URL path prefix to match
- `service_name`: Target service name
- `strip_prefix`: Whether to remove the path prefix when forwarding
- `timeout_seconds`: Request timeout for this route

## Features

### 1. Configuration-Based Routing

**Before (Hardcoded):**
```go
switch {
case strings.HasPrefix(path, "/api/v1/auth"):
    serviceName = "auth-service"
case strings.HasPrefix(path, "/api/v1/users"):
    serviceName = "user-service"
// ... more cases
}
```

**After (Configuration-Driven):**
```yaml
routes:
  - path: "/api/v1/auth"
    service_name: "auth-service"
  - path: "/api/v1/users"
    service_name: "user-service"
```

**Benefits:**
- No code changes required for new routes
- Dynamic configuration updates
- Environment-specific routing
- Better maintainability

### 2. Improved Concurrency Safety

**Before (Race Condition Risk):**
```go
for _, service := range gw.services {
    go gw.checkServiceHealth(service) // Captures service by reference
}
```

**After (Thread-Safe):**
```go
// Copy services to avoid race conditions
services := make([]*ServiceInstance, 0, len(gw.services))
for _, service := range gw.services {
    services = append(services, service)
}

// Use channels for result collection
resultChan := make(chan healthCheckResult, len(services))
for _, service := range services {
    go gw.checkServiceHealth(service, resultChan)
}
```

**Benefits:**
- Eliminates data races
- Better resource management
- Predictable behavior under load

### 3. Health Checking

The gateway performs asynchronous health checks every 30 seconds:

```go
func (gw *APIGateway) HealthCheck(ctx context.Context) {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            gw.performHealthChecks()
        }
    }
}
```

**Health Check Process:**
1. Copy current services to avoid race conditions
2. Spawn goroutines for concurrent health checks
3. Collect results via channels
4. Update service health status atomically

### 4. Load Balancing

The gateway supports multiple load balancing strategies:

```go
type LoadBalancer interface {
    SelectService(services []*ServiceInstance) *ServiceInstance
}
```

**Available Strategies:**
- **Round Robin**: Simple rotation through healthy services
- **Weighted Round Robin**: Service selection based on configured weights
- **Health-Based**: Only routes to healthy services

### 5. Service Discovery

Services can be registered programmatically:

```go
service := &ServiceInstance{
    ID:       "auth-1",
    Name:     "auth-service",
    Host:     "localhost",
    Port:     8001,
    Health:   "healthy",
    Metadata: map[string]string{"version": "1.0.0"},
}

gateway.RegisterService(service)
```

## Usage Examples

### 1. Basic Service Registration

```go
// Register a new service instance
authService := &gateway.ServiceInstance{
    ID:       "auth-1",
    Name:     "auth-service",
    Host:     "auth-service.local",
    Port:     8001,
    Health:   "healthy",
    Metadata: map[string]string{
        "version": "1.0.0",
        "region":  "us-west-1",
    },
}

apiGateway.RegisterService(authService)
```

### 2. Health Monitoring

```go
// Start health checking in background
ctx, cancel := context.WithCancel(context.Background())
defer cancel()

go apiGateway.HealthCheck(ctx)
```

### 3. Service Statistics

```go
// Get service statistics for monitoring
stats := apiGateway.GetServiceStats()
fmt.Printf("Total services: %d\n", stats["total_services"])
fmt.Printf("Healthy services: %d\n", stats["total_healthy"])
fmt.Printf("Health ratio: %.2f%%\n", stats["health_ratio"].(float64)*100)
```

### 4. Custom Load Balancer

```go
// Implement custom load balancing strategy
type CustomBalancer struct{}

func (cb *CustomBalancer) SelectService(services []*ServiceInstance) *ServiceInstance {
    // Custom selection logic
    return services[0]
}
```

## Monitoring and Observability

### Metrics

The gateway provides several metrics:

- **Service Count**: Total registered services
- **Health Ratio**: Percentage of healthy services
- **Response Times**: Per-service response time tracking
- **Error Rates**: Failed requests per service

### Logging

Comprehensive logging for debugging:

```go
gw.logger.Info("Service registered",
    zap.String("service_id", service.ID),
    zap.String("service_name", service.Name),
    zap.String("host", service.Host),
    zap.Int("port", service.Port))

gw.logger.Warn("Service health check failed",
    zap.String("service_id", service.ID),
    zap.String("service_name", service.Name),
    zap.Error(err))
```

### Health Endpoints

The gateway exposes health endpoints:

- `/health`: Gateway health status
- `/services`: List all registered services
- `/stats`: Service statistics and metrics

## Best Practices

### 1. Service Registration

- Register services with unique IDs
- Include metadata for load balancing decisions
- Implement graceful shutdown to deregister services

### 2. Health Checks

- Configure appropriate health check intervals
- Use lightweight health endpoints
- Handle health check failures gracefully

### 3. Load Balancing

- Choose appropriate load balancing strategy
- Monitor service performance
- Implement circuit breakers for failing services

### 4. Configuration

- Use environment-specific configurations
- Validate route configurations
- Document service dependencies

## Future Enhancements

### Planned Features

1. **Service Mesh Integration**: Support for Istio, Linkerd, or Consul
2. **Advanced Load Balancing**: Least connections, response time-based
3. **Rate Limiting**: Per-service rate limiting
4. **Authentication**: JWT validation and service-to-service auth
5. **Caching**: Response caching for frequently accessed endpoints
6. **Metrics Export**: Prometheus metrics export
7. **Distributed Tracing**: OpenTelemetry integration

### Scalability Considerations

- **Horizontal Scaling**: Multiple gateway instances
- **Service Discovery**: Integration with external registries (Consul, etcd)
- **Load Balancing**: External load balancers (HAProxy, nginx)
- **Caching**: Redis-based caching layer
- **Monitoring**: Centralized monitoring and alerting

## Troubleshooting

### Common Issues

1. **Service Not Found**
   - Check service registration
   - Verify health check configuration
   - Review route configuration

2. **Health Check Failures**
   - Verify service health endpoint
   - Check network connectivity
   - Review timeout settings

3. **Routing Issues**
   - Validate route configuration
   - Check path matching logic
   - Review service names

### Debug Commands

```bash
# Check gateway health
curl http://localhost:8080/health

# List registered services
curl http://localhost:8080/services

# Get service statistics
curl http://localhost:8080/stats
```

## Conclusion

The improved API Gateway provides a robust, scalable foundation for microservices communication. Its configuration-driven approach, improved concurrency safety, and comprehensive monitoring make it suitable for production deployments.

The gateway successfully addresses the architectural concerns identified in the original implementation while maintaining simplicity and extensibility for future enhancements. 