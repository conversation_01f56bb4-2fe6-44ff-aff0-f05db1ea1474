# gRPC-Web Proxy Setup Guide

This document explains how to set up and use the gRPC-Web proxy for the Hopen backend.

## Overview

The gRPC-Web proxy enables web browsers and other HTTP/1.1 clients to communicate with gRPC services. Since browsers cannot directly make gRPC calls, we use Envoy as a proxy to transcode HTTP/1.1 requests to gRPC calls.

## Architecture

```
Web Client (Browser) → Envoy Proxy → Hopen Backend (gRPC)
         ↓                ↓               ↓
    HTTP/1.1        gRPC-Web Transcoding   gRPC
```

## Configuration

The Envoy configuration is defined in `hopenbackend/envoy.yaml`. Key components include:

1. **Listeners**: Handle incoming HTTP/1.1 requests on port 8080
2. **Routes**: Map gRPC service prefixes to backend clusters
3. **Clusters**: Define connections to the Hopen backend services
4. **Filters**: Handle gRPC-Web transcoding and CORS

## Docker Integration

The proxy is integrated into the Docker Compose setup as the `envoy` service:

```yaml
envoy:
  image: envoyproxy/envoy:v1.28-latest
  container_name: hopen_envoy
  depends_on:
    backend:
      condition: service_healthy
  ports:
    - "80:8080"      # HTTP port for web clients
    - "9901:9901"    # Admin interface
  volumes:
    - ./envoy.yaml:/etc/envoy/envoy.yaml:ro
  command: ["envoy", "-c", "/etc/envoy/envoy.yaml", "--service-cluster", "hopen"]
```

## Service Mapping

The proxy routes requests based on gRPC service prefixes:

- `/hopen.user.v1.UserService/` → User Service
- `/hopen.bubble.v1.BubbleService/` → Bubble Service
- `/hopen.contact.v1.ContactService/` → Contact Service
- `/hopen.sync.v1.SyncService/` → Sync Service
- `/hopen.social_analytics.v1.SocialAnalyticsService/` → Social Analytics Service
- `/hopen.notification.v1.NotificationService/` → Notification Service

## CORS Configuration

Cross-Origin Resource Sharing is enabled to allow web browsers to make requests:

```yaml
cors:
  allow_origin_string_match:
  - prefix: "*"
  allow_methods: "GET,PUT,DELETE,POST,OPTIONS"
  allow_headers: "keep-alive,user-agent,cache-control,content-type,content-transfer-encoding,custom-header-1,x-accept-content-transfer-encoding,x-accept-response-streaming,x-user-agent,x-grpc-web,grpc-timeout"
  expose_headers: "custom-header-1,grpc-status,grpc-message"
  max_age: "1728000"
```

## Testing the Proxy

To test the proxy setup:

1. Start the services:
   ```bash
   cd hopenbackend
   docker-compose up -d
   ```

2. Check that services are running:
   ```bash
   docker-compose ps
   ```

3. Test the health endpoints:
   ```bash
   curl http://localhost/health
   curl http://localhost/ready
   ```

4. Access the Envoy admin interface:
   ```bash
   curl http://localhost:9901/ready
   ```

## Client-Side Usage

Web clients can now make gRPC-Web calls using the standard gRPC-Web client library:

```javascript
// Import the gRPC-Web client
const { grpc } = require('@improbable-eng/grpc-web');

// Create a client
const client = new UserServiceClient('http://localhost');

// Make a call
const request = new GetUserRequest();
request.setUserId('user123');

client.getUser(request, {}, (err, response) => {
  if (err) {
    console.error('Error:', err);
  } else {
    console.log('User:', response.toObject());
  }
});
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure the CORS configuration matches your client's origin
2. **Connection Refused**: Check that the backend service is running and healthy
3. **Timeout Errors**: Increase timeout values in the route configuration

### Debugging

1. Check Envoy logs:
   ```bash
   docker logs hopen_envoy
   ```

2. Access the Envoy admin interface at `http://localhost:9901` for detailed metrics and configuration

3. Check backend logs:
   ```bash
   docker logs hopen_backend
   ```

## Production Considerations

For production deployments:

1. **TLS Termination**: Add TLS termination to the Envoy configuration
2. **Rate Limiting**: Implement rate limiting at the proxy level
3. **Monitoring**: Add Prometheus metrics and tracing
4. **Load Balancing**: Configure multiple backend instances for high availability
5. **Security**: Add authentication and authorization at the proxy level

## Next Steps

1. Implement end-to-end testing with web clients
2. Set up performance benchmarking
3. Configure production-ready TLS termination
4. Add advanced monitoring and tracing
