# Security Middleware Usage Guide

## Overview

The Security Middleware now includes integrated HTML sanitization capabilities using the `bluemonday` library. This provides a centralized security layer that handles both traditional security headers and HTML content sanitization.

## Configuration

### SecurityConfig Structure

```go
type SecurityConfig struct {
    CSPPolicy      string   `yaml:"csp_policy"`
    HSTSMaxAge     int      `yaml:"hsts_max_age"`
    MaxRequestSize int64    `yaml:"max_request_size"`
    TrustedOrigins []string `yaml:"trusted_origins"`
    TrustedProxies []string `yaml:"trusted_proxies"`
    // HTML Sanitization settings
    EnableHTMLSanitization  bool   `yaml:"enable_html_sanitization"`
    DefaultSanitizationMode string `yaml:"default_sanitization_mode"` // "strict", "relaxed", "text_only"
}
```

### Example Configuration

```yaml
security:
  csp_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'"
  hsts_max_age: 31536000
  max_request_size: 10485760  # 10MB
  trusted_origins:
    - "https://hopen.com"
    - "https://app.hopen.com"
  trusted_proxies:
    - "10.0.0.0/8"
    - "**********/12"
  # HTML Sanitization settings
  enable_html_sanitization: true
  default_sanitization_mode: "relaxed"  # "strict", "relaxed", "text_only"
```

## Usage Examples

### 1. Basic Security Middleware Setup

```go
package main

import (
    "hopenbackend/pkg/middleware"
    "github.com/gin-gonic/gin"
)

func main() {
    r := gin.Default()
    
    // Create security configuration
    securityConfig := &middleware.SecurityConfig{
        CSPPolicy:              "default-src 'self'; script-src 'self'",
        HSTSMaxAge:             31536000,
        MaxRequestSize:         10485760,
        EnableHTMLSanitization: true,
        DefaultSanitizationMode: "relaxed",
    }
    
    // Create security middleware
    securityMiddleware := middleware.NewSecurityMiddleware(securityConfig)
    
    // Apply security middleware to all routes
    r.Use(securityMiddleware.SecurityHandler())
    
    // Apply HTML sanitization to specific routes
    r.Use("/api/chat", securityMiddleware.HTMLSanitizationHandler())
    r.Use("/api/comments", securityMiddleware.HTMLSanitizationHandler())
    
    r.Run(":8080")
}
```

### 2. Manual HTML Sanitization in Handlers

```go
func (h *ChatHandler) SendMessage(c *gin.Context) {
    var req struct {
        Message string `json:"message"`
        UserID  string `json:"user_id"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get security middleware from context or dependency injection
    securityMiddleware := getSecurityMiddleware(c)
    
    // Sanitize the message content
    sanitizedMessage := securityMiddleware.SanitizeRelaxed(req.Message)
    
    // Store sanitized message
    message := &ChatMessage{
        ID:      uuid.New().String(),
        UserID:  req.UserID,
        Content: sanitizedMessage,
        Time:    time.Now(),
    }
    
    // Save to database...
    c.JSON(http.StatusOK, message)
}
```

### 3. Different Sanitization Levels

```go
func (h *UserHandler) UpdateProfile(c *gin.Context) {
    var profile UserProfile
    if err := c.ShouldBindJSON(&profile); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    securityMiddleware := getSecurityMiddleware(c)
    
    // Strict sanitization for display name (no HTML allowed)
    profile.DisplayName = securityMiddleware.SanitizeStrict(profile.DisplayName)
    
    // Relaxed sanitization for bio (allows basic formatting)
    profile.Bio = securityMiddleware.SanitizeRelaxed(profile.Bio)
    
    // Text-only conversion for search indexing
    searchText := securityMiddleware.SanitizeTextOnly(profile.Bio)
    
    // Update in database...
    c.JSON(http.StatusOK, profile)
}
```

### 4. Automatic JSON Body Sanitization

The `HTMLSanitizationHandler` automatically sanitizes JSON request bodies:

```go
// This route will automatically sanitize all string values in JSON requests
r.POST("/api/chat/message", chatHandler.SendMessage)
r.Use("/api/chat", securityMiddleware.HTMLSanitizationHandler())
```

Example request:
```json
{
    "message": "<script>alert('xss')</script>Hello <strong>World</strong>",
    "user_id": "user123"
}
```

Will be automatically sanitized to:
```json
{
    "message": "Hello <strong>World</strong>",
    "user_id": "user123"
}
```

## Sanitization Levels

### 1. Strict Sanitization (`SanitizeStrict`)
- Removes ALL HTML tags and attributes
- Leaves only plain text
- Use for: usernames, passwords, email addresses

```go
input := "<script>alert('xss')</script>Hello <strong>World</strong>"
result := securityMiddleware.SanitizeStrict(input)
// Result: "Hello World"
```

### 2. Relaxed Sanitization (`SanitizeRelaxed`)
- Allows safe HTML tags and attributes
- Blocks dangerous content
- Use for: chat messages, comments, user profiles

```go
input := "<script>alert('xss')</script>Hello <strong>World</strong>"
result := securityMiddleware.SanitizeRelaxed(input)
// Result: "Hello <strong>World</strong>"
```

### 3. Text-Only Conversion (`SanitizeTextOnly`)
- Converts HTML to plain text while preserving structure
- Use for: search indexing, plain text exports

```go
input := "<p>Hello <strong>World</strong></p>"
result := securityMiddleware.SanitizeTextOnly(input)
// Result: "<p>Hello <strong>World</strong></p>" (with attributes removed)
```

## Metrics and Monitoring

The security middleware provides comprehensive metrics:

```go
// Metrics available:
// - hopen_security_blocked_requests_total{reason="size_limit"}
// - hopen_security_blocked_requests_total{reason="invalid_content"}
// - hopen_security_violations_total{type="malicious_content"}
// - hopen_security_processing_duration_seconds
// - hopen_request_size_bytes
// - hopen_html_sanitized_total{mode="strict"}
// - hopen_html_sanitized_total{mode="relaxed"}
// - hopen_html_sanitized_total{mode="text_only"}
```

### Example Prometheus Query

```promql
# Monitor HTML sanitization usage
rate(hopen_html_sanitized_total[5m])

# Monitor blocked requests
rate(hopen_security_blocked_requests_total[5m])

# Monitor processing duration
histogram_quantile(0.95, rate(hopen_security_processing_duration_seconds_bucket[5m]))
```

## Security Headers

The middleware automatically sets comprehensive security headers:

- **Content-Security-Policy**: Prevents XSS attacks
- **Strict-Transport-Security**: Enforces HTTPS
- **X-Content-Type-Options**: Prevents MIME type sniffing
- **X-Frame-Options**: Prevents clickjacking
- **X-XSS-Protection**: Additional XSS protection
- **Referrer-Policy**: Controls referrer information
- **Permissions-Policy**: Controls browser features
- **Alt-Svc**: Advertises HTTP/3 support

## Best Practices

### 1. Always Use Appropriate Sanitization Level

```go
// For usernames - strict sanitization
username := securityMiddleware.SanitizeStrict(userInput)

// For chat messages - relaxed sanitization
message := securityMiddleware.SanitizeRelaxed(userInput)

// For search - text only
searchText := securityMiddleware.SanitizeTextOnly(userInput)
```

### 2. Apply Middleware to Appropriate Routes

```go
// Apply to all routes for basic security
r.Use(securityMiddleware.SecurityHandler())

// Apply HTML sanitization only to routes that accept user content
r.Use("/api/chat", securityMiddleware.HTMLSanitizationHandler())
r.Use("/api/comments", securityMiddleware.HTMLSanitizationHandler())
r.Use("/api/profiles", securityMiddleware.HTMLSanitizationHandler())
```

### 3. Monitor and Alert

```go
// Set up alerts for security violations
// Alert if blocked requests exceed threshold
// Alert if HTML sanitization usage is unusually high
// Monitor processing duration for performance issues
```

### 4. Test Sanitization

```go
func TestHTMLSanitization(t *testing.T) {
    config := &middleware.SecurityConfig{
        EnableHTMLSanitization: true,
        DefaultSanitizationMode: "relaxed",
    }
    
    sm := middleware.NewSecurityMiddleware(config)
    
    tests := []struct {
        name     string
        input    string
        expected string
        mode     string
    }{
        {
            name:     "Remove script tags",
            input:    "<script>alert('xss')</script>Hello",
            expected: "Hello",
            mode:     "strict",
        },
        {
            name:     "Allow safe HTML",
            input:    "<strong>Bold</strong> text",
            expected: "<strong>Bold</strong> text",
            mode:     "relaxed",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            var result string
            switch tt.mode {
            case "strict":
                result = sm.SanitizeStrict(tt.input)
            case "relaxed":
                result = sm.SanitizeRelaxed(tt.input)
            default:
                result = sm.SanitizeTextOnly(tt.input)
            }
            
            if result != tt.expected {
                t.Errorf("got %q, want %q", result, tt.expected)
            }
        })
    }
}
```

## Migration from Old Pattern Matching

### Before (Ineffective)
```go
// ❌ Old approach - easily bypassed
func oldSanitize(input string) string {
    patterns := []string{"<script", "javascript:", "onload="}
    for _, pattern := range patterns {
        input = strings.ReplaceAll(input, pattern, "")
    }
    return input
}
```

### After (Secure)
```go
// ✅ New approach - uses proven library
securityMiddleware := middleware.NewSecurityMiddleware(config)
sanitized := securityMiddleware.SanitizeRelaxed(input)
```

## Conclusion

The integrated security middleware provides:

1. **Comprehensive Security**: Headers, validation, and sanitization
2. **Proven HTML Sanitization**: Uses the industry-standard bluemonday library
3. **Flexible Configuration**: Different sanitization levels for different use cases
4. **Automatic Processing**: JSON body sanitization for API endpoints
5. **Monitoring**: Comprehensive metrics for security operations
6. **Performance**: Efficient processing with minimal overhead

Always use the appropriate sanitization level for your use case and monitor the metrics to ensure the security layer is working correctly. 