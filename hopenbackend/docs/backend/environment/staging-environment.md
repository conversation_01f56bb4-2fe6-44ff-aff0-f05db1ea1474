# 🚀 Hopen Backend — Staging Environment

The **staging** environment replicates production architecture on separate sub-domains/resources for final QA and pre-release validation.

---

## 1. Access URLs

| Service | URL | Notes |
|---------|-----|-------|
| API Gateway (HTTP/3) | `https://staging-api.hopenapp.com` (QUIC 443/UDP, fallback 443/TCP) | Cloud-LB terminates TLS, forwards to Encore cluster |
| WebSocket | `wss://staging-ws.hopenapp.com` | Same host, HTTP/2 fallback |
| Ory <PERSON>s Public | `https://auth.staging.hopenapp.com` | Public identity flows |
| Ory Hydra Public | `https://oauth.staging.hopenapp.com` | OAuth2 / OIDC |
| MinIO (S3) | `https://staging-storage.hopenapp.com` | Versioning & lifecycle policies ON |
| EMQX (MQTT 5) | `staging-mqtt.hopenapp.com:8883` (TLS) | Enforces username=JW<PERSON>, cert-pinned |
| Coturn | `turn:turn.staging.hopenapp.com:3478` / `turns:turn.staging.hopenapp.com:443` | Credentials from secret store |

---

## 2. Deployment Pipeline

1. **GitHub Actions** — branch `release/*` merges into `develop` trigger staging deploy.
2. Build Docker images, run `go test ./...`, `flutter test`, Trivy scan.
3. Push to GHCR → ArgoCD (OVH K8s) pulls, rolls out canary -> full.
4. Databases migrate automatically via Encore.

---

## 3. Secrets & Config

| Secret | Store | Description |
|--------|-------|-------------|
| `POSTGRES_PASSWORD` | OVH Vault | Staging DB |
| `MINIO_ROOT_*` | Vault | S3 access |
| `TURN_USERNAME/PASSWORD` | Vault | Coturn auth |
| `GOOGLE_OAUTH_CLIENT_IDS` | Vault | Mobile OAuth |
| TLS certificates | Let's Encrypt (auto-renew via cert-manager) | *.staging.hopenapp.com |

All secrets are mounted into pods as environment variables; CI never stores them.

---

## 4. Monitoring & Alerts

* Prometheus + Grafana stack (separate namespace `staging-monitor`).
* Alertmanager routes only to Slack `#hopen-staging`.
* Extra SLO dashboards for WebRTC packet-loss, HTTP latency p95.

---

## 5. Data & Policies

* PostgreSQL replica size: 2 vCPU / 4 GB, daily `pgBackRest` backups (7 days retention).
* MinIO bucket versioning **enabled**; automatic purge of non-current versions after 14 days.
* Rate-limiting: 500 req/min per IP, 50 req/min per authenticated user.

---

## 6. Release Promotion Checklist

1. All staging automated & manual tests green (CI, Playwright, Flutter driver).
2. Error budget (4xx/5xx) within SLO for 24 h.
3. Manual sanity: login, call setup, push-notifications.
4. Tag `release/vX.Y.Z` → promotion to production.

---

© 2025 Hopen Inc.  Staging environment — internal only. 