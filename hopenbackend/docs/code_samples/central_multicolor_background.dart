import 'package:flutter/material.dart';

class AnimatedG<PERSON>ientBackground extends StatefulWidget {
  const AnimatedGradientBackground({super.key, required this.child});

  final Widget child;

  @override
  State<AnimatedGradientBackground> createState() => _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<AnimatedGradientBackground>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 20))
          ..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color _pastel(double hue) {
    return HSVColor.fromAHSV(1.0, hue % 360, 0.25, 1.0).toColor();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final baseHue = _controller.value * 360;
        final colors = [
          _pastel(baseHue),
          _pastel(baseHue + 90),
          _pastel(baseHue + 180),
          _pastel(baseHue + 270),
        ];
        return Container(
          constraints: const BoxConstraints.expand(),
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: const Alignment(0.0, -0.2),
              radius: 1.2,
              colors: colors,
              stops: const [0.0, 0.5, 0.8, 1.0],
            ),
          ),
          child: child,
        );
      },
      child: widget.child,
    );
  }
}
