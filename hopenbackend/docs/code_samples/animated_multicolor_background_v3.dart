import 'package:flutter/material.dart';

class AnimatedG<PERSON>ientBackground extends StatefulWidget {
  const AnimatedGradientBackground({super.key, required this.child});

  final Widget child;

  @override
  State<AnimatedGradientBackground> createState() => _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<AnimatedGradientBackground>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 10))
          ..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color _hsvToColor(double hue, double saturation, double value) {
    return HSVColor.fromAHSV(1.0, hue % 360, saturation, value).toColor();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final hue = _controller.value * 360;
        return Container(
          constraints: const BoxConstraints.expand(),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _hsvToColor(hue, 0.8, 0.35),
                _hsvToColor(hue + 60, 0.8, 0.35),
                _hsvToColor(hue + 120, 0.8, 0.35),
                _hsvToColor(hue + 180, 0.8, 0.35),
              ],
            ),
          ),
          child: child,
        );
      },
      child: widget.child,
    );
  }
} 