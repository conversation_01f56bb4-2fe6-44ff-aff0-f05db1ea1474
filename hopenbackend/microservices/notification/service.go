package notification

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/api/option"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	notificationv1 "hopenbackend/protos/gen/notification"
)

// Service handles notification operations using gRPC and implements NotificationServiceServer
type Service struct {
	notificationv1.UnimplementedNotificationServiceServer
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	repository  *PostgreSQLRepository
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
	mqttClient  mqtt.Client
	fcmClient   *messaging.Client
}

// NotificationEvent represents a notification-related event for NATS
type NotificationEvent struct {
	UserID    string    `json:"user_id"`
	Type      string    `json:"type"`
	Action    string    `json:"action"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the notification service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
	MQTTClient  mqtt.Client
}

// NewService creates a new notification service instance
func NewService(deps *Dependencies) *Service {
	// Initialize repository
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	service := &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		repository:  repository,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
		mqttClient:  deps.MQTTClient,
	}

	// Initialize FCM client
	service.initializeFCM()

	// Set up NATS event handlers
	service.setupNATSHandlers()

	return service
}

// NewNotificationServiceServer creates a new gRPC server for the notification service
func NewNotificationServiceServer(service *Service) notificationv1.NotificationServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// initializeFCM initializes the Firebase Cloud Messaging client
func (s *Service) initializeFCM() {
	if s.config.Firebase.ServiceAccountPath == "" {
		s.logger.Warn("Firebase service account path not configured, FCM will be disabled")
		return
	}

	opt := option.WithCredentialsFile(s.config.Firebase.ServiceAccountPath)
	app, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		s.logger.Error("Failed to initialize Firebase app", zap.Error(err))
		return
	}

	fcmClient, err := app.Messaging(context.Background())
	if err != nil {
		s.logger.Error("Failed to initialize FCM client", zap.Error(err))
		return
	}

	s.fcmClient = fcmClient
	s.logger.Info("FCM client initialized successfully")
}

// setupNATSHandlers sets up NATS event handlers for contact and friendship events
func (s *Service) setupNATSHandlers() {
	if s.natsConn == nil {
		s.logger.Warn("NATS connection not available, skipping event handlers setup")
		return
	}

	// Subscribe to contact events
	_, err := s.natsConn.Subscribe("contact.request.*", s.handleContactEvent)
	if err != nil {
		s.logger.Error("Failed to subscribe to contact events", zap.Error(err))
	}

	// Subscribe to friendship events
	_, err = s.natsConn.Subscribe("friendship.request.*", s.handleFriendshipEvent)
	if err != nil {
		s.logger.Error("Failed to subscribe to friendship events", zap.Error(err))
	}

	s.logger.Info("NATS event handlers set up successfully")
}

// publishNotificationEvent publishes notification events via NATS
func (s *Service) publishNotificationEvent(userID, notificationType, action string, data any) {
	if s.natsConn == nil {
		return
	}

	event := NotificationEvent{
		UserID:    userID,
		Type:      notificationType,
		Action:    action,
		Data:      data,
		Timestamp: time.Now(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal notification event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("notification.%s", action)
	if err := s.natsConn.Publish(subject, eventData); err != nil {
		s.logger.Error("Failed to publish notification event", zap.Error(err))
	}
}

// publishMQTTNotification publishes real-time notifications via MQTT
func (s *Service) publishMQTTNotification(userID, eventType string, data map[string]interface{}) {
	if s.mqttClient == nil || !s.mqttClient.IsConnected() {
		return
	}

	notification := map[string]interface{}{
		"type":      eventType,
		"timestamp": time.Now().Unix(),
		"data":      data,
	}

	payload, err := json.Marshal(notification)
	if err != nil {
		s.logger.Error("Failed to marshal MQTT notification", zap.Error(err))
		return
	}

	topic := fmt.Sprintf("user/%s/notifications", userID)
	token := s.mqttClient.Publish(topic, 1, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish MQTT notification", zap.Error(token.Error()))
	}
}

// ContactEvent represents a contact-related event from the contact service
type ContactEvent struct {
	UserID    string    `json:"user_id"`
	ContactID string    `json:"contact_id"`
	Action    string    `json:"action"`
	Timestamp time.Time `json:"timestamp"`
}

// handleContactEvent handles contact-related NATS events
func (s *Service) handleContactEvent(msg *nats.Msg) {
	var event ContactEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		s.logger.Error("Failed to unmarshal contact event", zap.Error(err))
		return
	}

	// For contact events, ContactID is actually the target user ID
	targetUserID := event.ContactID

	// The notification service now orchestrates ALL notification types.
	var title, body string
	var notificationType commonv1.NotificationType
	var mqttEventType string

	switch event.Action {
	case "sent":
		title = "New Contact Request"
		body = fmt.Sprintf("You have a new contact request from user %s.", event.UserID) // In a real app, you'd look up the user's name
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_CONTACT_REQUEST
		mqttEventType = "contact_request_received"

		// 1. Send real-time MQTT notification
		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"from_user_id": event.UserID,
			"message":      body,
			"sent_at":      time.Now().Unix(),
		})

		// 2. Send a push notification via FCM
		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"from_user_id": event.UserID, "action": "sent"},
		})

	case "accepted":
		title = "Contact Request Accepted"
		body = fmt.Sprintf("User %s accepted your contact request.", event.UserID)
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_CONTACT_REQUEST
		mqttEventType = "contact_request_accepted"

		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"accepted_by": event.UserID,
			"message":     body,
			"sent_at":     time.Now().Unix(),
		})

		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"accepted_by": event.UserID, "action": "accepted"},
		})

	case "rejected":
		title = "Contact Request Rejected"
		body = fmt.Sprintf("User %s rejected your contact request.", event.UserID)
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_CONTACT_REQUEST
		mqttEventType = "contact_request_rejected"

		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"rejected_by": event.UserID,
			"message":     body,
			"sent_at":     time.Now().Unix(),
		})

		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"rejected_by": event.UserID, "action": "rejected"},
		})

	case "cancelled":
		title = "Contact Request Cancelled"
		body = fmt.Sprintf("User %s cancelled their contact request.", event.UserID)
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_CONTACT_REQUEST
		mqttEventType = "contact_request_cancelled"

		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"cancelled_by": event.UserID,
			"message":      body,
			"sent_at":      time.Now().Unix(),
		})

		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"cancelled_by": event.UserID, "action": "cancelled"},
		})

	case "removed":
		title = "Contact Removed"
		body = "You have been removed from someone's contacts."
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_CONTACT_REQUEST
		mqttEventType = "contact_removed"

		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"removed_by": event.UserID,
			"message":    body,
			"sent_at":    time.Now().Unix(),
		})

		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"removed_by": event.UserID, "action": "removed"},
		})

	default:
		s.logger.Warn("Unknown contact event action", zap.String("action", event.Action))
	}
}

// FriendshipEvent represents a friendship-related event from the friendship service
type FriendshipEvent struct {
	UserID    string    `json:"user_id"`
	FriendID  string    `json:"friend_id"`
	Action    string    `json:"action"`
	Timestamp time.Time `json:"timestamp"`
}

// handleFriendshipEvent handles friendship-related NATS events
func (s *Service) handleFriendshipEvent(msg *nats.Msg) {
	var event FriendshipEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		s.logger.Error("Failed to unmarshal friendship event", zap.Error(err))
		return
	}

	// For friendship events, FriendID is the target user ID
	targetUserID := event.FriendID

	// The notification service orchestrates ALL notification types for friendship events.
	var title, body string
	var notificationType commonv1.NotificationType
	var mqttEventType string

	switch event.Action {
	case "friend_request_created":
		title = "New Friend Request"
		body = fmt.Sprintf("You have a new friend request from user %s.", event.UserID)
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_FRIEND_REQUEST
		mqttEventType = "friend_request_received"

		// 1. Send real-time MQTT notification
		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"from_user_id": event.UserID,
			"message":      body,
			"sent_at":      time.Now().Unix(),
		})

		// 2. Send a push notification via FCM
		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"from_user_id": event.UserID, "action": "friend_request_created"},
		})

	case "friend_request_accepted":
		title = "Friend Request Accepted"
		body = fmt.Sprintf("User %s accepted your friend request.", event.UserID)
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_FRIEND_REQUEST
		mqttEventType = "friend_request_accepted"

		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"accepted_by": event.UserID,
			"message":     body,
			"sent_at":     time.Now().Unix(),
		})

		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"accepted_by": event.UserID, "action": "friend_request_accepted"},
		})

	case "declined":
		title = "Friend Request Declined"
		body = fmt.Sprintf("User %s declined your friend request.", event.UserID)
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_FRIEND_REQUEST
		mqttEventType = "friend_request_declined"

		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"declined_by": event.UserID,
			"message":     body,
			"sent_at":     time.Now().Unix(),
		})

		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"declined_by": event.UserID, "action": "declined"},
		})

	case "removed":
		title = "Friend Removed"
		body = "You have been removed from someone's friends."
		notificationType = commonv1.NotificationType_NOTIFICATION_TYPE_FRIEND_REQUEST
		mqttEventType = "friend_removed"

		s.publishMQTTNotification(targetUserID, mqttEventType, map[string]interface{}{
			"removed_by": event.UserID,
			"message":    body,
			"sent_at":    time.Now().Unix(),
		})

		s.SendPushNotification(context.Background(), &notificationv1.SendPushNotificationRequest{
			UserId: targetUserID,
			Title:  title,
			Body:   body,
			Type:   notificationType,
			Data:   map[string]string{"removed_by": event.UserID, "action": "removed"},
		})

	case "blocked":
		// Usually no notification is sent when someone blocks you
		s.logger.Info("User blocked", zap.String("blocker", event.UserID), zap.String("blocked", targetUserID))
	case "unblocked":
		// Usually no notification is sent when someone unblocks you
		s.logger.Info("User unblocked", zap.String("unblocker", event.UserID), zap.String("unblocked", targetUserID))
	default:
		s.logger.Warn("Unknown friendship event action", zap.String("action", event.Action))
	}
}

// RegisterFCMToken implements the RegisterFCMToken gRPC method
func (s *Service) RegisterFCMToken(ctx context.Context, req *notificationv1.RegisterFCMTokenRequest) (*notificationv1.RegisterFCMTokenResponse, error) {
	s.logger.Info("RegisterFCMToken called",
		zap.String("user_id", req.UserId),
		zap.String("platform", req.Platform))

	if req.UserId == "" || req.FcmToken == "" {
		return &notificationv1.RegisterFCMTokenResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and FCM token are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and FCM token are required")
	}

	// Store FCM token in database
	fcmToken := &FCMToken{
		ID:        fmt.Sprintf("fcm_%d", time.Now().UnixNano()),
		UserID:    req.UserId,
		Token:     req.FcmToken,
		DeviceID:  req.DeviceId,
		Platform:  req.Platform,
		IsActive:  true,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := s.repository.CreateFCMToken(ctx, fcmToken)
	if err != nil {
		s.logger.Error("Failed to store FCM token", zap.Error(err))
		return &notificationv1.RegisterFCMTokenResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to register FCM token"),
		}, status.Errorf(codes.Internal, "failed to register FCM token: %v", err)
	}

	// Publish FCM token registered event via NATS
	s.publishNotificationEvent(req.UserId, "fcm_token", "registered", map[string]interface{}{
		"fcm_token": req.FcmToken,
		"platform":  req.Platform,
		"device_id": req.DeviceId,
	})

	return &notificationv1.RegisterFCMTokenResponse{
		ApiResponse: createSuccessResponse("FCM token registered successfully"),
	}, nil
}

// UnregisterFCMToken implements the UnregisterFCMToken gRPC method
func (s *Service) UnregisterFCMToken(ctx context.Context, req *notificationv1.UnregisterFCMTokenRequest) (*notificationv1.UnregisterFCMTokenResponse, error) {
	s.logger.Info("UnregisterFCMToken called",
		zap.String("user_id", req.UserId),
		zap.String("fcm_token", req.FcmToken))

	if req.UserId == "" || req.FcmToken == "" {
		return &notificationv1.UnregisterFCMTokenResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and FCM token are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and FCM token are required")
	}

	// Remove FCM token from database
	err := s.repository.DeleteFCMToken(ctx, req.UserId, req.FcmToken)
	if err != nil {
		s.logger.Error("Failed to remove FCM token", zap.Error(err))
		return &notificationv1.UnregisterFCMTokenResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to unregister FCM token"),
		}, status.Errorf(codes.Internal, "failed to unregister FCM token: %v", err)
	}

	// Publish FCM token unregistered event via NATS
	s.publishNotificationEvent(req.UserId, "fcm_token", "unregistered", map[string]interface{}{
		"fcm_token": req.FcmToken,
	})

	return &notificationv1.UnregisterFCMTokenResponse{
		ApiResponse: createSuccessResponse("FCM token unregistered successfully"),
	}, nil
}

// SendPushNotification implements the SendPushNotification gRPC method
func (s *Service) SendPushNotification(ctx context.Context, req *notificationv1.SendPushNotificationRequest) (*notificationv1.SendPushNotificationResponse, error) {
	s.logger.Info("SendPushNotification called",
		zap.String("user_id", req.UserId),
		zap.String("title", req.Title))

	if req.UserId == "" || req.Title == "" {
		return &notificationv1.SendPushNotificationResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and title are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and title are required")
	}

	// Send FCM notification if client is available
	if s.fcmClient != nil {
		// Get user's FCM tokens from database
		fcmTokens, err := s.repository.GetUserFCMTokens(ctx, req.UserId)
		if err != nil {
			s.logger.Error("Failed to get user FCM tokens", zap.Error(err))
		} else {
			// Send notification to all user's devices
			for _, token := range fcmTokens {
				message := &messaging.Message{
					Token: token.Token,
					Notification: &messaging.Notification{
						Title: req.Title,
						Body:  req.Body,
					},
					Data: req.Data,
				}

				response, err := s.fcmClient.Send(ctx, message)
				if err != nil {
					s.logger.Error("Failed to send FCM notification",
						zap.Error(err),
						zap.String("token", token.Token),
						zap.String("user_id", req.UserId))
				} else {
					s.logger.Info("FCM notification sent successfully",
						zap.String("message_id", response),
						zap.String("user_id", req.UserId),
						zap.String("device_id", token.DeviceID))
				}
			}
		}
	}

	// Store notification in database
	notification := &Notification{
		ID:        fmt.Sprintf("notif_%d", time.Now().UnixNano()),
		UserID:    req.UserId,
		Type:      req.Type.String(),
		Title:     req.Title,
		Body:      req.Body,
		Data:      fmt.Sprintf("%v", req.Data), // Convert map to JSON string
		IsRead:    false,
		CreatedAt: time.Now(),
	}

	err := s.repository.CreateNotification(ctx, notification)
	if err != nil {
		s.logger.Error("Failed to store notification", zap.Error(err))
		// Don't fail the request since FCM might have succeeded
	}

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(req.UserId, "push_notification", map[string]interface{}{
		"title":   req.Title,
		"body":    req.Body,
		"data":    req.Data,
		"sent_at": time.Now().Unix(),
	})

	// Publish notification sent event via NATS
	s.publishNotificationEvent(req.UserId, "push_notification", "sent", map[string]interface{}{
		"title": req.Title,
		"body":  req.Body,
		"data":  req.Data,
	})

	return &notificationv1.SendPushNotificationResponse{
		ApiResponse: createSuccessResponse("Push notification sent successfully"),
	}, nil
}

// SendPushNotificationToTopic implements the SendPushNotificationToTopic gRPC method
func (s *Service) SendPushNotificationToTopic(ctx context.Context, req *notificationv1.SendPushNotificationToTopicRequest) (*notificationv1.SendPushNotificationToTopicResponse, error) {
	s.logger.Info("SendPushNotificationToTopic called",
		zap.String("topic", req.Topic),
		zap.String("title", req.Title))

	if req.Topic == "" || req.Title == "" {
		return &notificationv1.SendPushNotificationToTopicResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Topic and title are required"),
		}, status.Errorf(codes.InvalidArgument, "topic and title are required")
	}

	// Send FCM topic notification if client is available
	if s.fcmClient != nil {
		message := &messaging.Message{
			Topic: req.Topic,
			Notification: &messaging.Notification{
				Title: req.Title,
				Body:  req.Body,
			},
			Data: req.Data,
		}

		response, err := s.fcmClient.Send(ctx, message)
		if err != nil {
			s.logger.Error("Failed to send FCM topic notification", zap.Error(err))
			return &notificationv1.SendPushNotificationToTopicResponse{
				ApiResponse: createErrorResponse("FCM_ERROR", "Failed to send FCM notification"),
			}, status.Errorf(codes.Internal, "failed to send FCM notification: %v", err)
		}

		s.logger.Info("FCM topic notification sent", zap.String("message_id", response))
	}

	// Publish topic notification event via NATS
	s.publishNotificationEvent("", "topic_notification", "sent", map[string]interface{}{
		"topic": req.Topic,
		"title": req.Title,
		"body":  req.Body,
		"data":  req.Data,
	})

	return &notificationv1.SendPushNotificationToTopicResponse{
		ApiResponse: createSuccessResponse("Topic notification sent successfully"),
	}, nil
}

// GetUserNotifications implements the GetUserNotifications gRPC method
func (s *Service) GetUserNotifications(ctx context.Context, req *notificationv1.GetUserNotificationsRequest) (*notificationv1.GetUserNotificationsResponse, error) {
	s.logger.Info("GetUserNotifications called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &notificationv1.GetUserNotificationsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get notifications from database
	limit := int(req.PageSize)
	if limit <= 0 {
		limit = 20 // Default page size
	}
	offset := int((req.Page - 1) * req.PageSize)

	notifications, err := s.repository.GetUserNotifications(ctx, req.UserId, limit, offset)
	if err != nil {
		s.logger.Error("Failed to get notifications", zap.Error(err))
		return &notificationv1.GetUserNotificationsResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve notifications"),
		}, status.Errorf(codes.Internal, "failed to retrieve notifications: %v", err)
	}

	// Convert to proto format
	var protoNotifications []*commonv1.Notification
	for _, notif := range notifications {
		// Parse notification type
		var notifType commonv1.NotificationType
		switch notif.Type {
		case "FRIEND_REQUEST":
			notifType = commonv1.NotificationType_NOTIFICATION_TYPE_FRIEND_REQUEST
		case "BUBBLE_INVITE":
			notifType = commonv1.NotificationType_NOTIFICATION_TYPE_BUBBLE_INVITE
		case "MESSAGE":
			notifType = commonv1.NotificationType_NOTIFICATION_TYPE_MESSAGE
		default:
			notifType = commonv1.NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
		}

		protoNotif := &commonv1.Notification{
			Id:        notif.ID,
			UserId:    notif.UserID,
			Type:      notifType,
			Title:     notif.Title,
			Body:      notif.Body,
			Data:      make(map[string]string), // Initialize empty map for now
			IsRead:    notif.IsRead,
			CreatedAt: timestamppb.New(notif.CreatedAt),
		}
		if notif.ReadAt != nil {
			protoNotif.ReadAt = timestamppb.New(*notif.ReadAt)
		}
		protoNotifications = append(protoNotifications, protoNotif)
	}

	return &notificationv1.GetUserNotificationsResponse{
		Notifications: protoNotifications,
		Pagination:    &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(protoNotifications))},
		ApiResponse:   createSuccessResponse("Notifications retrieved successfully"),
	}, nil
}

// MarkNotificationAsRead implements the MarkNotificationAsRead gRPC method
func (s *Service) MarkNotificationAsRead(ctx context.Context, req *notificationv1.MarkNotificationAsReadRequest) (*notificationv1.MarkNotificationAsReadResponse, error) {
	s.logger.Info("MarkNotificationAsRead called",
		zap.String("user_id", req.UserId),
		zap.String("notification_id", req.NotificationId))

	if req.UserId == "" || req.NotificationId == "" {
		return &notificationv1.MarkNotificationAsReadResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and notification ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and notification ID are required")
	}

	// Mark notification as read in database
	err := s.repository.MarkNotificationAsRead(ctx, req.UserId, req.NotificationId)
	if err != nil {
		s.logger.Error("Failed to mark notification as read", zap.Error(err))
		return &notificationv1.MarkNotificationAsReadResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to mark notification as read"),
		}, status.Errorf(codes.Internal, "failed to mark notification as read: %v", err)
	}

	// Publish notification read event via NATS
	s.publishNotificationEvent(req.UserId, "notification", "read", map[string]interface{}{
		"notification_id": req.NotificationId,
	})

	return &notificationv1.MarkNotificationAsReadResponse{
		ApiResponse: createSuccessResponse("Notification marked as read successfully"),
	}, nil
}

// MarkAllNotificationsAsRead implements the MarkAllNotificationsAsRead gRPC method
func (s *Service) MarkAllNotificationsAsRead(ctx context.Context, req *notificationv1.MarkAllNotificationsAsReadRequest) (*notificationv1.MarkAllNotificationsAsReadResponse, error) {
	s.logger.Info("MarkAllNotificationsAsRead called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &notificationv1.MarkAllNotificationsAsReadResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Mark all notifications as read in database
	err := s.repository.MarkAllNotificationsAsRead(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to mark all notifications as read", zap.Error(err))
		return &notificationv1.MarkAllNotificationsAsReadResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to mark all notifications as read"),
		}, status.Errorf(codes.Internal, "failed to mark all notifications as read: %v", err)
	}

	// Publish all notifications read event via NATS
	s.publishNotificationEvent(req.UserId, "notification", "all_read", map[string]interface{}{
		"marked_at": time.Now().Unix(),
	})

	return &notificationv1.MarkAllNotificationsAsReadResponse{
		ApiResponse: createSuccessResponse("All notifications marked as read successfully"),
	}, nil
}

// DeleteNotification implements the DeleteNotification gRPC method
func (s *Service) DeleteNotification(ctx context.Context, req *notificationv1.DeleteNotificationRequest) (*notificationv1.DeleteNotificationResponse, error) {
	s.logger.Info("DeleteNotification called",
		zap.String("user_id", req.UserId),
		zap.String("notification_id", req.NotificationId))

	if req.UserId == "" || req.NotificationId == "" {
		return &notificationv1.DeleteNotificationResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and notification ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and notification ID are required")
	}

	// Delete notification from database
	err := s.repository.DeleteNotification(ctx, req.UserId, req.NotificationId)
	if err != nil {
		s.logger.Error("Failed to delete notification", zap.Error(err))
		return &notificationv1.DeleteNotificationResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to delete notification"),
		}, status.Errorf(codes.Internal, "failed to delete notification: %v", err)
	}

	// Publish notification deleted event via NATS
	s.publishNotificationEvent(req.UserId, "notification", "deleted", map[string]interface{}{
		"notification_id": req.NotificationId,
	})

	return &notificationv1.DeleteNotificationResponse{
		ApiResponse: createSuccessResponse("Notification deleted successfully"),
	}, nil
}

// GetNotificationSettings implements the GetNotificationSettings gRPC method
func (s *Service) GetNotificationSettings(ctx context.Context, req *notificationv1.GetNotificationSettingsRequest) (*notificationv1.GetNotificationSettingsResponse, error) {
	s.logger.Info("GetNotificationSettings called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &notificationv1.GetNotificationSettingsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get notification settings from database
	settings, err := s.repository.GetNotificationSettings(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get notification settings", zap.Error(err))
		return &notificationv1.GetNotificationSettingsResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve notification settings"),
		}, status.Errorf(codes.Internal, "failed to retrieve notification settings: %v", err)
	}

	return &notificationv1.GetNotificationSettingsResponse{
		Settings: &notificationv1.NotificationSettings{
			UserId:                 settings.UserID,
			PushEnabled:            settings.PushEnabled,
			ContactRequestsEnabled: settings.ContactRequestsEnabled,
			BubbleInvitesEnabled:   settings.BubbleInvitesEnabled,
			FriendRequestsEnabled:  settings.FriendRequestsEnabled,
			UpdatedAt:              timestamppb.New(settings.UpdatedAt),
		},
		ApiResponse: createSuccessResponse("Notification settings retrieved successfully"),
	}, nil
}

// UpdateNotificationSettings implements the UpdateNotificationSettings gRPC method
func (s *Service) UpdateNotificationSettings(ctx context.Context, req *notificationv1.UpdateNotificationSettingsRequest) (*notificationv1.UpdateNotificationSettingsResponse, error) {
	s.logger.Info("UpdateNotificationSettings called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &notificationv1.UpdateNotificationSettingsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Update notification settings in database
	settings := &NotificationSettings{
		UserID:                 req.UserId,
		PushEnabled:            req.PushEnabled != nil && *req.PushEnabled,
		EmailEnabled:           true, // Default since not in proto
		ContactRequestsEnabled: req.ContactRequestsEnabled != nil && *req.ContactRequestsEnabled,
		BubbleInvitesEnabled:   req.BubbleInvitesEnabled != nil && *req.BubbleInvitesEnabled,
		FriendRequestsEnabled:  req.FriendRequestsEnabled != nil && *req.FriendRequestsEnabled,
		ChatMessagesEnabled:    true, // Default since not in proto
		UpdatedAt:              time.Now(),
	}

	err := s.repository.UpdateNotificationSettings(ctx, settings)
	if err != nil {
		s.logger.Error("Failed to update notification settings", zap.Error(err))
		return &notificationv1.UpdateNotificationSettingsResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to update notification settings"),
		}, status.Errorf(codes.Internal, "failed to update notification settings: %v", err)
	}

	// Publish notification settings updated event via NATS
	s.publishNotificationEvent(req.UserId, "notification_settings", "updated", map[string]interface{}{
		"push_enabled":             req.PushEnabled,
		"contact_requests_enabled": req.ContactRequestsEnabled,
		"friend_requests_enabled":  req.FriendRequestsEnabled,
	})

	return &notificationv1.UpdateNotificationSettingsResponse{
		ApiResponse: createSuccessResponse("Notification settings updated successfully"),
	}, nil
}

// GetUnreadNotificationCount implements the GetUnreadNotificationCount gRPC method
func (s *Service) GetUnreadNotificationCount(ctx context.Context, req *notificationv1.GetUnreadNotificationCountRequest) (*notificationv1.GetUnreadNotificationCountResponse, error) {
	s.logger.Info("GetUnreadNotificationCount called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &notificationv1.GetUnreadNotificationCountResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get unread count from database
	count, err := s.repository.GetUnreadNotificationCount(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get unread notification count", zap.Error(err))
		return &notificationv1.GetUnreadNotificationCountResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve unread notification count"),
		}, status.Errorf(codes.Internal, "failed to retrieve unread notification count: %v", err)
	}

	return &notificationv1.GetUnreadNotificationCountResponse{
		Count:       count,
		ApiResponse: createSuccessResponse("Unread notification count retrieved successfully"),
	}, nil
}

// SendBubbleExpiryNotification implements the bubble service interface
func (s *Service) SendBubbleExpiryNotification(ctx context.Context, userID, bubbleID string, daysLeft int) error {
	s.logger.Info("SendBubbleExpiryNotification called",
		zap.String("user_id", userID),
		zap.String("bubble_id", bubbleID),
		zap.Int("days_left", daysLeft))

	message := fmt.Sprintf("Your bubble will expire in %d days", daysLeft)
	if daysLeft <= 0 {
		message = "Your bubble has expired"
	}

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(userID, "bubble_expiry", map[string]interface{}{
		"bubble_id": bubbleID,
		"days_left": daysLeft,
		"message":   message,
		"sent_at":   time.Now().Unix(),
	})

	// Publish notification event via NATS
	s.publishNotificationEvent(userID, "bubble_expiry", "sent", map[string]interface{}{
		"bubble_id": bubbleID,
		"days_left": daysLeft,
	})

	return nil
}

// SendBubbleInviteNotification implements the bubble service interface
func (s *Service) SendBubbleInviteNotification(ctx context.Context, recipientID, senderID, bubbleID string) error {
	s.logger.Info("SendBubbleInviteNotification called",
		zap.String("recipient_id", recipientID),
		zap.String("sender_id", senderID),
		zap.String("bubble_id", bubbleID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(recipientID, "bubble_invite", map[string]interface{}{
		"sender_id": senderID,
		"bubble_id": bubbleID,
		"message":   "You have been invited to join a bubble",
		"sent_at":   time.Now().Unix(),
	})

	// Publish notification event via NATS
	s.publishNotificationEvent(recipientID, "bubble_invite", "sent", map[string]interface{}{
		"sender_id": senderID,
		"bubble_id": bubbleID,
	})

	return nil
}

// SendBubbleJoinRequestNotification implements the bubble service interface
func (s *Service) SendBubbleJoinRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID string) error {
	s.logger.Info("SendBubbleJoinRequestNotification called",
		zap.String("recipient_id", recipientID),
		zap.String("requester_id", requesterID),
		zap.String("bubble_id", bubbleID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(recipientID, "bubble_join_request", map[string]interface{}{
		"requester_id": requesterID,
		"bubble_id":    bubbleID,
		"message":      "Someone wants to join your bubble",
		"sent_at":      time.Now().Unix(),
	})

	// Publish notification event via NATS
	s.publishNotificationEvent(recipientID, "bubble_join_request", "sent", map[string]interface{}{
		"requester_id": requesterID,
		"bubble_id":    bubbleID,
	})

	return nil
}

// SendBubbleJoinRequestAcceptedNotification implements the bubble service interface
func (s *Service) SendBubbleJoinRequestAcceptedNotification(ctx context.Context, requesterID, acceptedByUserID, bubbleID string) error {
	s.logger.Info("SendBubbleJoinRequestAcceptedNotification called",
		zap.String("requester_id", requesterID),
		zap.String("accepted_by_user_id", acceptedByUserID),
		zap.String("bubble_id", bubbleID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(requesterID, "bubble_join_request_accepted", map[string]interface{}{
		"accepted_by_user_id": acceptedByUserID,
		"bubble_id":           bubbleID,
		"message":             "Your bubble join request was accepted",
		"sent_at":             time.Now().Unix(),
	})

	// Publish notification event via NATS
	s.publishNotificationEvent(requesterID, "bubble_join_request_accepted", "sent", map[string]interface{}{
		"accepted_by_user_id": acceptedByUserID,
		"bubble_id":           bubbleID,
	})

	return nil
}

// SendBubbleJoinRequestRejectedNotification implements the bubble service interface
func (s *Service) SendBubbleJoinRequestRejectedNotification(ctx context.Context, requesterID, rejectedByUserID, bubbleID string) error {
	s.logger.Info("SendBubbleJoinRequestRejectedNotification called",
		zap.String("requester_id", requesterID),
		zap.String("rejected_by_user_id", rejectedByUserID),
		zap.String("bubble_id", bubbleID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(requesterID, "bubble_join_request_rejected", map[string]interface{}{
		"rejected_by_user_id": rejectedByUserID,
		"bubble_id":           bubbleID,
		"message":             "Your bubble join request was rejected",
		"sent_at":             time.Now().Unix(),
	})

	// Publish notification event via NATS
	s.publishNotificationEvent(requesterID, "bubble_join_request_rejected", "sent", map[string]interface{}{
		"rejected_by_user_id": rejectedByUserID,
		"bubble_id":           bubbleID,
	})

	return nil
}

// SendBubbleMemberJoinedNotification implements the bubble service interface
func (s *Service) SendBubbleMemberJoinedNotification(ctx context.Context, memberID, newMemberID, bubbleID string) error {
	s.logger.Info("SendBubbleMemberJoinedNotification called",
		zap.String("member_id", memberID),
		zap.String("new_member_id", newMemberID),
		zap.String("bubble_id", bubbleID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(memberID, "bubble_member_joined", map[string]interface{}{
		"new_member_id": newMemberID,
		"bubble_id":     bubbleID,
		"message":       "A new member joined your bubble",
		"sent_at":       time.Now().Unix(),
	})

	// Publish notification event via NATS
	s.publishNotificationEvent(memberID, "bubble_member_joined", "sent", map[string]interface{}{
		"new_member_id": newMemberID,
		"bubble_id":     bubbleID,
	})

	return nil
}

// SendBubbleProposeRequestNotification implements the bubble service interface
func (s *Service) SendBubbleProposeRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, proposedUserID string) error {
	s.logger.Info("SendBubbleProposeRequestNotification called",
		zap.String("recipient_id", recipientID),
		zap.String("requester_id", requesterID),
		zap.String("bubble_id", bubbleID),
		zap.String("proposed_user_id", proposedUserID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(recipientID, "bubble_propose_request", map[string]interface{}{
		"requester_id":     requesterID,
		"bubble_id":        bubbleID,
		"proposed_user_id": proposedUserID,
		"message":          "Someone wants to propose a user for your bubble",
		"sent_at":          time.Now().Unix(),
	})

	return nil
}

// SendBubbleKickoutRequestNotification implements the bubble service interface
func (s *Service) SendBubbleKickoutRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, targetUserID string) error {
	s.logger.Info("SendBubbleKickoutRequestNotification called",
		zap.String("recipient_id", recipientID),
		zap.String("requester_id", requesterID),
		zap.String("bubble_id", bubbleID),
		zap.String("target_user_id", targetUserID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(recipientID, "bubble_kickout_request", map[string]interface{}{
		"requester_id":   requesterID,
		"bubble_id":      bubbleID,
		"target_user_id": targetUserID,
		"message":        "Someone wants to kick out a user from your bubble",
		"sent_at":        time.Now().Unix(),
	})

	return nil
}

// SendBubbleVotekickInitiatedNotification implements the bubble service interface
func (s *Service) SendBubbleVotekickInitiatedNotification(ctx context.Context, memberID, initiatorID, targetUserID, bubbleID string) error {
	s.logger.Info("SendBubbleVotekickInitiatedNotification called",
		zap.String("member_id", memberID),
		zap.String("initiator_id", initiatorID),
		zap.String("target_user_id", targetUserID),
		zap.String("bubble_id", bubbleID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(memberID, "bubble_votekick_initiated", map[string]interface{}{
		"initiator_id":   initiatorID,
		"target_user_id": targetUserID,
		"bubble_id":      bubbleID,
		"message":        "A vote to kick out a member has been initiated",
		"sent_at":        time.Now().Unix(),
	})

	return nil
}

// SendBubbleVotekickPassedNotification implements the bubble service interface
func (s *Service) SendBubbleVotekickPassedNotification(ctx context.Context, memberID, targetUserID, bubbleID string) error {
	s.logger.Info("SendBubbleVotekickPassedNotification called",
		zap.String("member_id", memberID),
		zap.String("target_user_id", targetUserID),
		zap.String("bubble_id", bubbleID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(memberID, "bubble_votekick_passed", map[string]interface{}{
		"target_user_id": targetUserID,
		"bubble_id":      bubbleID,
		"message":        "A member has been voted out of the bubble",
		"sent_at":        time.Now().Unix(),
	})

	return nil
}

// SendBubbleStartRequestNotification implements the bubble service interface
func (s *Service) SendBubbleStartRequestNotification(ctx context.Context, recipientID, requesterID string) error {
	s.logger.Info("SendBubbleStartRequestNotification called",
		zap.String("recipient_id", recipientID),
		zap.String("requester_id", requesterID))

	// Publish notification via MQTT for real-time delivery
	s.publishMQTTNotification(recipientID, "bubble_start_request", map[string]interface{}{
		"requester_id": requesterID,
		"message":      "Someone wants to start a bubble with you",
		"sent_at":      time.Now().Unix(),
	})

	return nil
}
