package notification

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// FCMToken represents an FCM token in the database
type FCMToken struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	DeviceID  string    `json:"device_id"`
	Platform  string    `json:"platform"` // ios, android, web
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Notification represents a notification in the database
type Notification struct {
	ID        string     `json:"id"`
	UserID    string     `json:"user_id"`
	Type      string     `json:"type"`
	Title     string     `json:"title"`
	Body      string     `json:"body"`
	Data      string     `json:"data"` // JSON string
	IsRead    bool       `json:"is_read"`
	CreatedAt time.Time  `json:"created_at"`
	ReadAt    *time.Time `json:"read_at"`
}

// NotificationSettings represents user notification preferences
type NotificationSettings struct {
	UserID                 string    `json:"user_id"`
	PushEnabled            bool      `json:"push_enabled"`
	EmailEnabled           bool      `json:"email_enabled"`
	ContactRequestsEnabled bool      `json:"contact_requests_enabled"`
	BubbleInvitesEnabled   bool      `json:"bubble_invites_enabled"`
	FriendRequestsEnabled  bool      `json:"friend_requests_enabled"`
	ChatMessagesEnabled    bool      `json:"chat_messages_enabled"`
	UpdatedAt              time.Time `json:"updated_at"`
}

// PostgreSQLRepository handles notification database operations
type PostgreSQLRepository struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for notifications
func NewPostgreSQLRepository(pool *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		pool:   pool,
		logger: logger,
	}
}

// CreateFCMToken creates or updates an FCM token
func (r *PostgreSQLRepository) CreateFCMToken(ctx context.Context, token *FCMToken) error {
	query := `
		INSERT INTO fcm_tokens (id, user_id, token, device_id, platform, is_active, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		ON CONFLICT (user_id, device_id) 
		DO UPDATE SET token = $3, is_active = $6, updated_at = $8`

	_, err := r.pool.Exec(ctx, query,
		token.ID, token.UserID, token.Token, token.DeviceID,
		token.Platform, token.IsActive, token.CreatedAt, token.UpdatedAt)

	if err != nil {
		r.logger.Error("Failed to create FCM token", zap.Error(err))
		return fmt.Errorf("failed to create FCM token: %w", err)
	}

	return nil
}

// GetUserFCMTokens retrieves active FCM tokens for a user
func (r *PostgreSQLRepository) GetUserFCMTokens(ctx context.Context, userID string) ([]*FCMToken, error) {
	query := `
		SELECT id, user_id, token, device_id, platform, is_active, created_at, updated_at
		FROM fcm_tokens
		WHERE user_id = $1 AND is_active = true
		ORDER BY updated_at DESC`

	rows, err := r.pool.Query(ctx, query, userID)
	if err != nil {
		r.logger.Error("Failed to get user FCM tokens", zap.Error(err))
		return nil, fmt.Errorf("failed to get user FCM tokens: %w", err)
	}
	defer rows.Close()

	var tokens []*FCMToken
	for rows.Next() {
		var token FCMToken
		err := rows.Scan(
			&token.ID, &token.UserID, &token.Token, &token.DeviceID,
			&token.Platform, &token.IsActive, &token.CreatedAt, &token.UpdatedAt)
		if err != nil {
			r.logger.Error("Failed to scan FCM token", zap.Error(err))
			continue
		}
		tokens = append(tokens, &token)
	}

	return tokens, nil
}

// DeleteFCMToken removes an FCM token
func (r *PostgreSQLRepository) DeleteFCMToken(ctx context.Context, userID, token string) error {
	query := `DELETE FROM fcm_tokens WHERE user_id = $1 AND token = $2`

	result, err := r.pool.Exec(ctx, query, userID, token)
	if err != nil {
		r.logger.Error("Failed to delete FCM token", zap.Error(err))
		return fmt.Errorf("failed to delete FCM token: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("FCM token not found")
	}

	return nil
}

// CreateNotification creates a new notification
func (r *PostgreSQLRepository) CreateNotification(ctx context.Context, notification *Notification) error {
	query := `
		INSERT INTO notifications (id, user_id, type, title, body, data, is_read, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

	_, err := r.pool.Exec(ctx, query,
		notification.ID, notification.UserID, notification.Type,
		notification.Title, notification.Body, notification.Data,
		notification.IsRead, notification.CreatedAt)

	if err != nil {
		r.logger.Error("Failed to create notification", zap.Error(err))
		return fmt.Errorf("failed to create notification: %w", err)
	}

	return nil
}

// GetUserNotifications retrieves notifications for a user with pagination
func (r *PostgreSQLRepository) GetUserNotifications(ctx context.Context, userID string, limit, offset int) ([]*Notification, error) {
	query := `
		SELECT id, user_id, type, title, body, data, is_read, created_at, read_at
		FROM notifications
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.pool.Query(ctx, query, userID, limit, offset)
	if err != nil {
		r.logger.Error("Failed to get user notifications", zap.Error(err))
		return nil, fmt.Errorf("failed to get user notifications: %w", err)
	}
	defer rows.Close()

	var notifications []*Notification
	for rows.Next() {
		var notification Notification
		err := rows.Scan(
			&notification.ID, &notification.UserID, &notification.Type,
			&notification.Title, &notification.Body, &notification.Data,
			&notification.IsRead, &notification.CreatedAt, &notification.ReadAt)
		if err != nil {
			r.logger.Error("Failed to scan notification", zap.Error(err))
			continue
		}
		notifications = append(notifications, &notification)
	}

	return notifications, nil
}

// MarkNotificationAsRead marks a notification as read
func (r *PostgreSQLRepository) MarkNotificationAsRead(ctx context.Context, userID, notificationID string) error {
	query := `
		UPDATE notifications 
		SET is_read = true, read_at = $1 
		WHERE id = $2 AND user_id = $3`

	result, err := r.pool.Exec(ctx, query, time.Now(), notificationID, userID)
	if err != nil {
		r.logger.Error("Failed to mark notification as read", zap.Error(err))
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("notification not found")
	}

	return nil
}

// MarkAllNotificationsAsRead marks all notifications as read for a user
func (r *PostgreSQLRepository) MarkAllNotificationsAsRead(ctx context.Context, userID string) error {
	query := `
		UPDATE notifications 
		SET is_read = true, read_at = $1 
		WHERE user_id = $2 AND is_read = false`

	_, err := r.pool.Exec(ctx, query, time.Now(), userID)
	if err != nil {
		r.logger.Error("Failed to mark all notifications as read", zap.Error(err))
		return fmt.Errorf("failed to mark all notifications as read: %w", err)
	}

	return nil
}

// DeleteNotification deletes a notification
func (r *PostgreSQLRepository) DeleteNotification(ctx context.Context, userID, notificationID string) error {
	query := `DELETE FROM notifications WHERE id = $1 AND user_id = $2`

	result, err := r.pool.Exec(ctx, query, notificationID, userID)
	if err != nil {
		r.logger.Error("Failed to delete notification", zap.Error(err))
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("notification not found")
	}

	return nil
}

// GetUnreadNotificationCount gets the count of unread notifications for a user
func (r *PostgreSQLRepository) GetUnreadNotificationCount(ctx context.Context, userID string) (int32, error) {
	query := `SELECT COUNT(*) FROM notifications WHERE user_id = $1 AND is_read = false`

	var count int32
	err := r.pool.QueryRow(ctx, query, userID).Scan(&count)
	if err != nil {
		r.logger.Error("Failed to get unread notification count", zap.Error(err))
		return 0, fmt.Errorf("failed to get unread notification count: %w", err)
	}

	return count, nil
}

// GetNotificationSettings retrieves notification settings for a user
func (r *PostgreSQLRepository) GetNotificationSettings(ctx context.Context, userID string) (*NotificationSettings, error) {
	query := `
		SELECT user_id, push_enabled, email_enabled, contact_requests_enabled,
			   bubble_invites_enabled, friend_requests_enabled, chat_messages_enabled, updated_at
		FROM notification_settings
		WHERE user_id = $1`

	var settings NotificationSettings
	err := r.pool.QueryRow(ctx, query, userID).Scan(
		&settings.UserID, &settings.PushEnabled, &settings.EmailEnabled,
		&settings.ContactRequestsEnabled, &settings.BubbleInvitesEnabled,
		&settings.FriendRequestsEnabled, &settings.ChatMessagesEnabled,
		&settings.UpdatedAt)

	if err != nil {
		if err == pgx.ErrNoRows {
			// Return default settings if none exist
			return &NotificationSettings{
				UserID:                 userID,
				PushEnabled:            true,
				EmailEnabled:           true,
				ContactRequestsEnabled: true,
				BubbleInvitesEnabled:   true,
				FriendRequestsEnabled:  true,
				ChatMessagesEnabled:    true,
				UpdatedAt:              time.Now(),
			}, nil
		}
		r.logger.Error("Failed to get notification settings", zap.Error(err))
		return nil, fmt.Errorf("failed to get notification settings: %w", err)
	}

	return &settings, nil
}

// UpdateNotificationSettings updates notification settings for a user
func (r *PostgreSQLRepository) UpdateNotificationSettings(ctx context.Context, settings *NotificationSettings) error {
	query := `
		INSERT INTO notification_settings (
			user_id, push_enabled, email_enabled, contact_requests_enabled,
			bubble_invites_enabled, friend_requests_enabled, chat_messages_enabled, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		ON CONFLICT (user_id) 
		DO UPDATE SET 
			push_enabled = $2, email_enabled = $3, contact_requests_enabled = $4,
			bubble_invites_enabled = $5, friend_requests_enabled = $6, 
			chat_messages_enabled = $7, updated_at = $8`

	_, err := r.pool.Exec(ctx, query,
		settings.UserID, settings.PushEnabled, settings.EmailEnabled,
		settings.ContactRequestsEnabled, settings.BubbleInvitesEnabled,
		settings.FriendRequestsEnabled, settings.ChatMessagesEnabled,
		settings.UpdatedAt)

	if err != nil {
		r.logger.Error("Failed to update notification settings", zap.Error(err))
		return fmt.Errorf("failed to update notification settings: %w", err)
	}

	return nil
}
