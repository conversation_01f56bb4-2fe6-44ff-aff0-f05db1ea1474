package realtime

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gocql/gocql"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/auth"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/pagination"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	realtimev1 "hopenbackend/protos/gen/realtime"
	syncv1 "hopenbackend/protos/gen/sync"

	"google.golang.org/grpc/status"
)

// Service handles realtime operations using gRPC and implements RealtimeServiceServer
type Service struct {
	realtimev1.UnimplementedRealtimeServiceServer
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	scylladb    *database.ScyllaDBClient
	repository  *ScyllaDBRepository
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
	mqttClient  mqtt.Client
}

// RealtimeEvent represents a realtime-related event for NATS
type RealtimeEvent struct {
	UserID    string    `json:"user_id"`
	BubbleID  string    `json:"bubble_id"`
	Type      string    `json:"type"`
	Action    string    `json:"action"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the realtime service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	ScyllaDB    *database.ScyllaDBClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
	MQTTClient  mqtt.Client
}

// NewService creates a new realtime service instance
func NewService(deps *Dependencies) *Service {
	// Initialize ScyllaDB repository for messages
	repository := NewScyllaDBRepository(deps.ScyllaDB, deps.Logger)

	return &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		scylladb:    deps.ScyllaDB,
		repository:  repository,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
		mqttClient:  deps.MQTTClient,
	}
}

// NewRealtimeServiceServer creates a new gRPC server for the realtime service
func NewRealtimeServiceServer(service *Service) realtimev1.RealtimeServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishRealtimeEvent publishes realtime events via NATS
func (s *Service) publishRealtimeEvent(userID, bubbleID, eventType, action string, data any) {
	if s.natsConn == nil {
		return
	}

	event := RealtimeEvent{
		UserID:    userID,
		BubbleID:  bubbleID,
		Type:      eventType,
		Action:    action,
		Data:      data,
		Timestamp: time.Now(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal realtime event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("realtime.%s", action)
	if err := s.natsConn.Publish(subject, eventData); err != nil {
		s.logger.Error("Failed to publish realtime event", zap.Error(err))
	}
}

// publishMQTTMessage publishes real-time messages via MQTT
func (s *Service) publishMQTTMessage(bubbleID, eventType string, data map[string]interface{}) {
	if s.mqttClient == nil || !s.mqttClient.IsConnected() {
		return
	}

	message := map[string]interface{}{
		"type":      eventType,
		"timestamp": time.Now().Unix(),
		"data":      data,
	}

	payload, err := json.Marshal(message)
	if err != nil {
		s.logger.Error("Failed to marshal MQTT message", zap.Error(err))
		return
	}

	// Publish to bubble-specific topic
	bubbleTopic := fmt.Sprintf("bubble/%s/messages", bubbleID)
	token := s.mqttClient.Publish(bubbleTopic, 1, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish MQTT message", zap.Error(token.Error()))
	}

	// Also publish to global realtime topic
	globalTopic := "realtime/messages"
	token = s.mqttClient.Publish(globalTopic, 1, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish MQTT global message", zap.Error(token.Error()))
	}
}

// calculateUnreadCount calculates the number of unread messages in a conversation for a user
func (s *Service) calculateUnreadCount(ctx context.Context, conversationID gocql.UUID, userID string) int32 {
	// Get conversation messages and count unread ones
	// Using a reasonable limit to avoid performance issues
	messages, _, err := s.scylladb.GetConversationMessages(ctx, conversationID, 100, nil)
	if err != nil {
		s.logger.Error("Failed to get conversation messages for unread count", zap.Error(err))
		return 0
	}

	userUUID, err := gocql.ParseUUID(userID)
	if err != nil {
		s.logger.Error("Invalid user ID for unread count", zap.Error(err))
		return 0
	}

	var unreadCount int32
	for _, msg := range messages {
		// Count messages that are not from this user and not read
		if msg.SenderID != userUUID && !msg.IsRead {
			unreadCount++
		}
	}

	s.logger.Debug("Calculated unread count",
		zap.String("conversation_id", conversationID.String()),
		zap.String("user_id", userID),
		zap.Int32("unread_count", unreadCount))
	return unreadCount
}

// SendMessage implements the SendMessage gRPC method
func (s *Service) SendMessage(ctx context.Context, req *realtimev1.SendMessageRequest) (*realtimev1.SendMessageResponse, error) {
	s.logger.Info("SendMessage called",
		zap.String("bubble_id", req.BubbleId),
		zap.String("content", req.Content))

	if req.BubbleId == "" || req.Content == "" {
		return &realtimev1.SendMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Bubble ID and content are required"),
		}, status.Errorf(codes.InvalidArgument, "bubble ID and content are required")
	}

	// Get sender ID from authentication context
	senderID, err := auth.GetUserID(ctx)
	if err != nil {
		return &realtimev1.SendMessageResponse{
			ApiResponse: createErrorResponse("AUTHENTICATION_ERROR", "Failed to authenticate user"),
		}, status.Errorf(codes.Unauthenticated, "missing user authentication")
	}

	// Parse UUIDs
	bubbleUUID, err := gocql.ParseUUID(req.BubbleId)
	if err != nil {
		s.logger.Error("Invalid bubble ID", zap.Error(err))
		return &realtimev1.SendMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid bubble ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid bubble ID format")
	}

	senderUUID, err := gocql.ParseUUID(senderID)
	if err != nil {
		s.logger.Error("Invalid sender ID", zap.Error(err))
		return &realtimev1.SendMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid sender ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid sender ID format")
	}

	// Save message to database
	messageID := gocql.TimeUUID()
	dbMessage := &database.Message{
		BubbleID:    bubbleUUID,
		MessageID:   messageID,
		SenderID:    senderUUID,
		Content:     req.Content,
		MessageType: "text",
		MediaURL:    nil,
		ReplyToID:   nil,
		IsEdited:    false,
		IsDeleted:   false,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.repository.CreateMessage(ctx, dbMessage)
	if err != nil {
		s.logger.Error("Failed to save message", zap.Error(err))
		return &realtimev1.SendMessageResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to save message"),
		}, status.Errorf(codes.Internal, "failed to save message: %v", err)
	}

	// Create proto message object
	protoMessage := &realtimev1.Message{
		MessageId: messageID.String(),
		BubbleId:  req.BubbleId,
		SenderId:  senderID,
		Content:   req.Content,
		CreatedAt: timestamppb.Now(),
		UpdatedAt: timestamppb.Now(),
	}

	// Publish message sent event via NATS
	s.publishRealtimeEvent(senderID, req.BubbleId, "message", "sent", map[string]interface{}{
		"message_id": messageID,
		"content":    req.Content,
	})

	// Publish real-time message via MQTT
	s.publishMQTTMessage(req.BubbleId, "message_sent", map[string]interface{}{
		"message_id": messageID,
		"sender_id":  senderID,
		"content":    req.Content,
		"sent_at":    time.Now().Unix(),
	})

	return &realtimev1.SendMessageResponse{
		Message:     protoMessage,
		ApiResponse: createSuccessResponse("Message sent successfully"),
	}, nil
}

// GetChatMessages implements the GetChatMessages gRPC method
func (s *Service) GetChatMessages(ctx context.Context, req *realtimev1.GetChatMessagesRequest) (*realtimev1.GetChatMessagesResponse, error) {
	s.logger.Info("GetChatMessages called", zap.String("bubble_id", req.BubbleId))

	if req.BubbleId == "" {
		return &realtimev1.GetChatMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Bubble ID is required"),
		}, status.Errorf(codes.InvalidArgument, "bubble ID is required")
	}

	// Get messages from database using ScyllaDB pagination
	limit := int(req.Limit)
	if limit <= 0 {
		limit = 50 // Default limit
	}

	// Parse page state for ScyllaDB pagination
	paginationOpts := pagination.NewPaginationOptions(int(req.Limit), req.PageState)
	if err := paginationOpts.Validate(); err != nil {
		return &realtimev1.GetChatMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid pagination parameters"),
		}, status.Errorf(codes.InvalidArgument, "invalid pagination parameters: %v", err)
	}
	pageState := paginationOpts.GetPageState()

	bubbleUUID, err := gocql.ParseUUID(req.BubbleId)
	if err != nil {
		s.logger.Error("Invalid bubble ID", zap.Error(err))
		return &realtimev1.GetChatMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid bubble ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid bubble ID format")
	}

	dbMessages, nextPageState, err := s.scylladb.GetMessages(ctx, bubbleUUID, limit, pageState)
	if err != nil {
		s.logger.Error("Failed to get messages", zap.Error(err))
		return &realtimev1.GetChatMessagesResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve messages"),
		}, status.Errorf(codes.Internal, "failed to retrieve messages: %v", err)
	}

	// Convert to proto format
	var protoMessages []*realtimev1.Message
	for _, dbMsg := range dbMessages {
		protoMsg := &realtimev1.Message{
			MessageId:   dbMsg.MessageID.String(),
			BubbleId:    dbMsg.BubbleID.String(),
			SenderId:    dbMsg.SenderID.String(),
			Content:     dbMsg.Content,
			MessageType: dbMsg.MessageType,
			IsEdited:    dbMsg.IsEdited,
			IsDeleted:   dbMsg.IsDeleted,
			CreatedAt:   timestamppb.New(dbMsg.CreatedAt),
			UpdatedAt:   timestamppb.New(dbMsg.UpdatedAt),
		}

		// Set optional fields
		if dbMsg.MediaURL != nil {
			protoMsg.MediaUrl = dbMsg.MediaURL
		}
		if dbMsg.ReplyToID != nil {
			replyToStr := dbMsg.ReplyToID.String()
			protoMsg.ReplyToId = &replyToStr
		}

		protoMessages = append(protoMessages, protoMsg)
	}

	// Prepare next page state
	var nextPageStateStr *string
	if len(nextPageState) > 0 {
		encoded, err := pagination.EncodePageState(nextPageState, paginationOpts.Limit)
		if err != nil {
			s.logger.Error("Failed to encode page state", zap.Error(err))
		} else {
			nextPageStateStr = &encoded
		}
	}

	return &realtimev1.GetChatMessagesResponse{
		Messages:      protoMessages,
		Count:         int32(len(protoMessages)),
		NextPageState: nextPageStateStr,
		ApiResponse:   createSuccessResponse("Chat messages retrieved successfully"),
	}, nil
}

// SendTypingIndicator implements the SendTypingIndicator gRPC method
func (s *Service) SendTypingIndicator(ctx context.Context, req *realtimev1.SendTypingIndicatorRequest) (*realtimev1.SendTypingIndicatorResponse, error) {
	s.logger.Info("SendTypingIndicator called",
		zap.String("bubble_id", req.BubbleId),
		zap.Bool("is_typing", req.IsTyping))

	if req.BubbleId == "" {
		return &realtimev1.SendTypingIndicatorResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Bubble ID is required"),
		}, status.Errorf(codes.InvalidArgument, "bubble ID is required")
	}

	// Get user ID from authentication context
	userID, err := auth.GetUserID(ctx)
	if err != nil {
		return &realtimev1.SendTypingIndicatorResponse{
			ApiResponse: createErrorResponse("AUTHENTICATION_ERROR", "Failed to authenticate user"),
		}, status.Errorf(codes.Unauthenticated, "missing user authentication")
	}

	// Publish typing indicator via MQTT for real-time delivery
	s.publishMQTTMessage(req.BubbleId, "typing_indicator", map[string]interface{}{
		"user_id":   userID,
		"is_typing": req.IsTyping,
		"timestamp": time.Now().Unix(),
	})

	// Publish typing event via NATS
	action := "started"
	if !req.IsTyping {
		action = "stopped"
	}
	s.publishRealtimeEvent(userID, req.BubbleId, "typing", action, map[string]interface{}{
		"is_typing": req.IsTyping,
	})

	return &realtimev1.SendTypingIndicatorResponse{
		ApiResponse: createSuccessResponse("Typing indicator sent successfully"),
	}, nil
}

// EditMessage implements the EditMessage gRPC method
func (s *Service) EditMessage(ctx context.Context, req *realtimev1.EditMessageRequest) (*realtimev1.EditMessageResponse, error) {
	s.logger.Info("EditMessage called",
		zap.String("message_id", req.MessageId),
		zap.String("content", req.Content))

	if req.MessageId == "" || req.Content == "" {
		return &realtimev1.EditMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Message ID and content are required"),
		}, status.Errorf(codes.InvalidArgument, "message ID and content are required")
	}

	// Edit message in database
	err := s.repository.UpdateMessage(ctx, req.MessageId, req.Content)
	if err != nil {
		s.logger.Error("Failed to edit message", zap.Error(err))
		return &realtimev1.EditMessageResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to edit message"),
		}, status.Errorf(codes.Internal, "failed to edit message: %v", err)
	}

	// Publish message edited event via NATS
	s.publishRealtimeEvent("", "", "message", "edited", map[string]interface{}{
		"message_id": req.MessageId,
		"content":    req.Content,
	})

	return &realtimev1.EditMessageResponse{
		ApiResponse: createSuccessResponse("Message edited successfully"),
	}, nil
}

// DeleteMessage implements the DeleteMessage gRPC method
func (s *Service) DeleteMessage(ctx context.Context, req *realtimev1.DeleteMessageRequest) (*realtimev1.DeleteMessageResponse, error) {
	s.logger.Info("DeleteMessage called", zap.String("message_id", req.MessageId))

	if req.MessageId == "" {
		return &realtimev1.DeleteMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Message ID is required"),
		}, status.Errorf(codes.InvalidArgument, "message ID is required")
	}

	// Delete message in database
	err := s.repository.DeleteMessage(ctx, req.MessageId)
	if err != nil {
		s.logger.Error("Failed to delete message", zap.Error(err))
		return &realtimev1.DeleteMessageResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to delete message"),
		}, status.Errorf(codes.Internal, "failed to delete message: %v", err)
	}

	// Publish message deleted event via NATS
	s.publishRealtimeEvent("", "", "message", "deleted", map[string]interface{}{
		"message_id": req.MessageId,
	})

	return &realtimev1.DeleteMessageResponse{
		ApiResponse: createSuccessResponse("Message deleted successfully"),
	}, nil
}

// GetConversations implements the GetConversations gRPC method
func (s *Service) GetConversations(ctx context.Context, req *realtimev1.GetConversationsRequest) (*realtimev1.GetConversationsResponse, error) {
	s.logger.Info("GetConversations called", zap.Int32("limit", req.Limit))

	limit := int(req.Limit)
	if limit <= 0 {
		limit = 20 // Default limit
	}

	// Get user ID from authentication context
	userID, err := auth.GetUserID(ctx)
	if err != nil {
		return &realtimev1.GetConversationsResponse{
			ApiResponse: createErrorResponse("AUTHENTICATION_ERROR", "Failed to authenticate user"),
		}, status.Errorf(codes.Unauthenticated, "missing user authentication")
	}
	userUUID, err := gocql.ParseUUID(userID)
	if err != nil {
		s.logger.Error("Invalid user ID", zap.Error(err))
		return &realtimev1.GetConversationsResponse{
			ApiResponse: createErrorResponse("AUTHENTICATION_ERROR", "Invalid user context"),
		}, status.Errorf(codes.Unauthenticated, "invalid user context")
	}

	// Get conversations from ScyllaDB
	conversations, err := s.scylladb.GetUserConversations(ctx, userUUID, limit)
	if err != nil {
		s.logger.Error("Failed to get conversations", zap.Error(err))
		return &realtimev1.GetConversationsResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve conversations"),
		}, status.Errorf(codes.Internal, "failed to retrieve conversations: %v", err)
	}

	// Convert to proto format
	var protoConversations []*syncv1.Conversation
	for _, conv := range conversations {
		protoConv := &syncv1.Conversation{
			Id:             conv.ConversationID.String(),
			Name:           "Direct Message", // Default name for DM conversations
			ParticipantIds: []string{conv.Participant1ID.String(), conv.Participant2ID.String()},
			UnreadCount:    s.calculateUnreadCount(ctx, conv.ConversationID, userID), // Calculate actual unread count
			CreatedAt:      timestamppb.New(conv.CreatedAt),
			UpdatedAt:      timestamppb.New(conv.LastMessageAt),
		}

		// Set last message if available
		if conv.LastMessageID != nil {
			protoConv.LastMessage = &syncv1.Message{
				Id:        conv.LastMessageID.String(),
				SenderId:  conv.Participant1ID.String(), // Simplified - would need actual sender
				Content:   "Last message content",       // Simplified - would need actual content
				Type:      syncv1.MessageType_MESSAGE_TYPE_TEXT,
				IsRead:    false,
				CreatedAt: timestamppb.New(conv.LastMessageAt),
				UpdatedAt: timestamppb.New(conv.LastMessageAt),
			}
		}

		protoConversations = append(protoConversations, protoConv)
	}

	return &realtimev1.GetConversationsResponse{
		Conversations: protoConversations,
		Count:         int32(len(protoConversations)),
		ApiResponse:   createSuccessResponse("Conversations retrieved successfully"),
	}, nil
}

// SendDirectMessage implements the SendDirectMessage gRPC method
func (s *Service) SendDirectMessage(ctx context.Context, req *realtimev1.SendDirectMessageRequest) (*realtimev1.SendDirectMessageResponse, error) {
	s.logger.Info("SendDirectMessage called", zap.String("conversation_id", req.ConversationId))

	if req.ConversationId == "" || req.Content == "" {
		return &realtimev1.SendDirectMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Conversation ID and content are required"),
		}, status.Errorf(codes.InvalidArgument, "conversation ID and content are required")
	}

	// Get sender ID from authentication context
	senderID, err := auth.GetUserID(ctx)
	if err != nil {
		return &realtimev1.SendDirectMessageResponse{
			ApiResponse: createErrorResponse("AUTHENTICATION_ERROR", "Failed to authenticate user"),
		}, status.Errorf(codes.Unauthenticated, "missing user authentication")
	}

	// Parse conversation ID
	conversationUUID, err := gocql.ParseUUID(req.ConversationId)
	if err != nil {
		s.logger.Error("Invalid conversation ID", zap.Error(err))
		return &realtimev1.SendDirectMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid conversation ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid conversation ID format")
	}

	// Get conversation participants to determine the recipient
	participant1ID, participant2ID, err := s.scylladb.GetConversationParticipants(ctx, conversationUUID)
	if err != nil {
		s.logger.Error("Failed to get conversation participants", zap.Error(err))
		return &realtimev1.SendDirectMessageResponse{
			ApiResponse: createErrorResponse("NOT_FOUND", "Conversation not found"),
		}, status.Errorf(codes.NotFound, "conversation not found: %v", err)
	}

	// Parse sender UUID
	senderUUID, err := gocql.ParseUUID(senderID)
	if err != nil {
		s.logger.Error("Invalid sender ID", zap.Error(err))
		return &realtimev1.SendDirectMessageResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid sender ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid sender ID format")
	}

	// Determine the recipient (the other participant in the conversation)
	var recipientUUID gocql.UUID
	if participant1ID == senderUUID {
		recipientUUID = participant2ID
	} else if participant2ID == senderUUID {
		recipientUUID = participant1ID
	} else {
		return &realtimev1.SendDirectMessageResponse{
			ApiResponse: createErrorResponse("PERMISSION_DENIED", "User is not a participant in this conversation"),
		}, status.Errorf(codes.PermissionDenied, "user is not a participant in this conversation")
	}

	// Create conversation message in ScyllaDB
	messageID := gocql.TimeUUID()

	convMessage := &database.ConversationMessage{
		ConversationID: conversationUUID,
		MessageID:      messageID,
		SenderID:       senderUUID,
		RecipientID:    recipientUUID, // Actual recipient from conversation
		Content:        req.Content,
		MessageType:    req.MessageType,
		IsEdited:       false,
		IsDeleted:      false,
		IsRead:         false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Set optional fields
	if req.MediaUrl != nil {
		convMessage.MediaURL = req.MediaUrl
	}
	if req.ReplyToId != nil {
		replyUUID, err := gocql.ParseUUID(*req.ReplyToId)
		if err == nil {
			convMessage.ReplyToID = &replyUUID
		}
	}

	err = s.scylladb.CreateConversationMessage(ctx, convMessage)
	if err != nil {
		s.logger.Error("Failed to create conversation message", zap.Error(err))
		return &realtimev1.SendDirectMessageResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to send message"),
		}, status.Errorf(codes.Internal, "failed to send message: %v", err)
	}

	// Return the created message
	return &realtimev1.SendDirectMessageResponse{
		Message: &syncv1.Message{
			Id:             messageID.String(),
			ConversationId: req.ConversationId,
			SenderId:       senderID,
			Content:        req.Content,
			Type:           syncv1.MessageType_MESSAGE_TYPE_TEXT,
			IsRead:         false,
			CreatedAt:      timestamppb.Now(),
			UpdatedAt:      timestamppb.Now(),
		},
		ApiResponse: createSuccessResponse("Direct message sent successfully"),
	}, nil
}

// GetConversationMessages implements the GetConversationMessages gRPC method
func (s *Service) GetConversationMessages(ctx context.Context, req *realtimev1.GetConversationMessagesRequest) (*realtimev1.GetConversationMessagesResponse, error) {
	s.logger.Info("GetConversationMessages called", zap.String("conversation_id", req.ConversationId))

	if req.ConversationId == "" {
		return &realtimev1.GetConversationMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Conversation ID is required"),
		}, status.Errorf(codes.InvalidArgument, "conversation ID is required")
	}

	// Parse conversation ID
	conversationUUID, err := gocql.ParseUUID(req.ConversationId)
	if err != nil {
		s.logger.Error("Invalid conversation ID", zap.Error(err))
		return &realtimev1.GetConversationMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid conversation ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid conversation ID format")
	}

	limit := int(req.Limit)
	if limit <= 0 {
		limit = 50 // Default limit
	}

	// Parse page state for ScyllaDB pagination
	pageStateStr := ""
	if req.PageState != nil {
		pageStateStr = *req.PageState
	}
	paginationOpts := pagination.NewPaginationOptions(limit, pageStateStr)
	if err := paginationOpts.Validate(); err != nil {
		return &realtimev1.GetConversationMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid pagination parameters"),
		}, status.Errorf(codes.InvalidArgument, "invalid pagination parameters: %v", err)
	}
	pageState := paginationOpts.GetPageState()

	// Get conversation messages from ScyllaDB
	convMessages, nextPageState, err := s.scylladb.GetConversationMessages(ctx, conversationUUID, limit, pageState)
	if err != nil {
		s.logger.Error("Failed to get conversation messages", zap.Error(err))
		return &realtimev1.GetConversationMessagesResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve messages"),
		}, status.Errorf(codes.Internal, "failed to retrieve messages: %v", err)
	}

	// Convert to proto format
	var protoMessages []*syncv1.Message
	for _, convMsg := range convMessages {
		protoMsg := &syncv1.Message{
			Id:             convMsg.MessageID.String(),
			ConversationId: convMsg.ConversationID.String(),
			SenderId:       convMsg.SenderID.String(),
			Content:        convMsg.Content,
			Type:           syncv1.MessageType_MESSAGE_TYPE_TEXT, // Default to text
			IsRead:         convMsg.IsRead,
			CreatedAt:      timestamppb.New(convMsg.CreatedAt),
			UpdatedAt:      timestamppb.New(convMsg.UpdatedAt),
		}

		// Set message type based on content
		switch convMsg.MessageType {
		case "image":
			protoMsg.Type = syncv1.MessageType_MESSAGE_TYPE_IMAGE
		case "video":
			protoMsg.Type = syncv1.MessageType_MESSAGE_TYPE_VIDEO
		case "audio":
			protoMsg.Type = syncv1.MessageType_MESSAGE_TYPE_AUDIO
		case "file":
			protoMsg.Type = syncv1.MessageType_MESSAGE_TYPE_FILE
		default:
			protoMsg.Type = syncv1.MessageType_MESSAGE_TYPE_TEXT
		}

		protoMessages = append(protoMessages, protoMsg)
	}

	// Prepare next page state
	var nextPageStateStr *string
	if len(nextPageState) > 0 {
		encoded, err := pagination.EncodePageState(nextPageState, paginationOpts.Limit)
		if err != nil {
			s.logger.Error("Failed to encode page state", zap.Error(err))
		} else {
			nextPageStateStr = &encoded
		}
	}

	return &realtimev1.GetConversationMessagesResponse{
		Messages:      protoMessages,
		Count:         int32(len(protoMessages)),
		NextPageState: nextPageStateStr,
		ApiResponse:   createSuccessResponse("Conversation messages retrieved successfully"),
	}, nil
}

// SearchChatMessages implements the SearchChatMessages gRPC method
func (s *Service) SearchChatMessages(ctx context.Context, req *realtimev1.SearchChatMessagesRequest) (*realtimev1.SearchChatMessagesResponse, error) {
	s.logger.Info("SearchChatMessages called",
		zap.String("conversation_id", req.ConversationId),
		zap.String("query", req.Query))

	if req.ConversationId == "" || req.Query == "" {
		return &realtimev1.SearchChatMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Conversation ID and search query are required"),
		}, status.Errorf(codes.InvalidArgument, "conversation ID and search query are required")
	}

	// Parse conversation ID
	conversationUUID, err := gocql.ParseUUID(req.ConversationId)
	if err != nil {
		s.logger.Error("Invalid conversation ID", zap.Error(err))
		return &realtimev1.SearchChatMessagesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid conversation ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid conversation ID format")
	}

	// Perform search using basic ScyllaDB LIKE operator
	// Note: For production, use an external search engine like Elasticsearch
	limit := int(req.Limit)
	if limit <= 0 {
		limit = 50 // Default limit
	}

	messages, err := s.scylladb.SearchConversationMessages(ctx, conversationUUID, req.Query, limit)
	if err != nil {
		s.logger.Error("Failed to search conversation messages", zap.Error(err))
		return &realtimev1.SearchChatMessagesResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to search messages"),
		}, status.Errorf(codes.Internal, "failed to search messages: %v", err)
	}

	// Convert to protobuf messages
	var protoMessages []*realtimev1.Message
	for _, msg := range messages {
		protoMsg := &realtimev1.Message{
			MessageId:   msg.MessageID.String(),
			BubbleId:    "", // Conversation messages don't have bubble_id, leave empty
			SenderId:    msg.SenderID.String(),
			Content:     msg.Content,
			MessageType: msg.MessageType,
			IsEdited:    msg.IsEdited,
			IsDeleted:   msg.IsDeleted,
			CreatedAt:   timestamppb.New(msg.CreatedAt),
			UpdatedAt:   timestamppb.New(msg.UpdatedAt),
		}

		if msg.MediaURL != nil {
			protoMsg.MediaUrl = msg.MediaURL
		}

		if msg.ReplyToID != nil {
			replyToStr := msg.ReplyToID.String()
			protoMsg.ReplyToId = &replyToStr
		}

		protoMessages = append(protoMessages, protoMsg)
	}

	return &realtimev1.SearchChatMessagesResponse{
		Messages:    protoMessages,
		Count:       int32(len(protoMessages)),
		ApiResponse: createSuccessResponse("Search completed successfully"),
	}, nil
}

// MarkMessageAsRead implements the MarkMessageAsRead gRPC method
func (s *Service) MarkMessageAsRead(ctx context.Context, req *realtimev1.MarkMessageAsReadRequest) (*realtimev1.MarkMessageAsReadResponse, error) {
	s.logger.Info("MarkMessageAsRead called", zap.String("message_id", req.MessageId))

	if req.MessageId == "" {
		return &realtimev1.MarkMessageAsReadResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Message ID is required"),
		}, status.Errorf(codes.InvalidArgument, "message ID is required")
	}

	// Get user ID from authentication context
	userID, err := auth.GetUserID(ctx)
	if err != nil {
		return &realtimev1.MarkMessageAsReadResponse{
			ApiResponse: createErrorResponse("AUTHENTICATION_ERROR", "Failed to authenticate user"),
		}, status.Errorf(codes.Unauthenticated, "missing user authentication")
	}

	// Parse message ID
	messageUUID, err := gocql.ParseUUID(req.MessageId)
	if err != nil {
		s.logger.Error("Invalid message ID", zap.Error(err))
		return &realtimev1.MarkMessageAsReadResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid message ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid message ID format")
	}

	// Parse user ID
	userUUID, err := gocql.ParseUUID(userID)
	if err != nil {
		s.logger.Error("Invalid user ID", zap.Error(err))
		return &realtimev1.MarkMessageAsReadResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Invalid user ID format"),
		}, status.Errorf(codes.InvalidArgument, "invalid user ID format")
	}

	// Get the conversation message to obtain conversation_id and created_at
	message, err := s.scylladb.GetConversationMessageByID(ctx, messageUUID)
	if err != nil {
		s.logger.Error("Failed to get conversation message", zap.Error(err))
		return &realtimev1.MarkMessageAsReadResponse{
			ApiResponse: createErrorResponse("NOT_FOUND", "Message not found"),
		}, status.Errorf(codes.NotFound, "message not found: %v", err)
	}

	// Mark the message as read
	err = s.scylladb.MarkMessageAsRead(ctx, message.ConversationID, message.CreatedAt, messageUUID, userUUID)
	if err != nil {
		s.logger.Error("Failed to mark message as read", zap.Error(err))
		return &realtimev1.MarkMessageAsReadResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to mark message as read"),
		}, status.Errorf(codes.Internal, "failed to mark message as read: %v", err)
	}

	s.logger.Info("Message marked as read successfully",
		zap.String("message_id", req.MessageId),
		zap.String("user_id", userID),
		zap.String("conversation_id", message.ConversationID.String()))

	return &realtimev1.MarkMessageAsReadResponse{
		ApiResponse: createSuccessResponse("Message marked as read successfully"),
	}, nil
}

// These methods require careful analysis of the proto structures to implement correctly
