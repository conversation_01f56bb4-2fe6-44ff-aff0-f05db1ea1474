package realtime

import (
	"context"
	"fmt"
	"time"

	"hopenbackend/pkg/database"

	"github.com/gocql/gocql"
	"go.uber.org/zap"
)

// ScyllaDBRepository handles realtime database operations using ScyllaDB
type ScyllaDBRepository struct {
	client *database.ScyllaDBClient
	logger *zap.Logger
}

// NewScyllaDBRepository creates a new ScyllaDB repository for realtime
func NewScyllaDBRepository(client *database.ScyllaDBClient, logger *zap.Logger) *ScyllaDBRepository {
	return &ScyllaDBRepository{
		client: client,
		logger: logger,
	}
}

// CreateMessage creates a new message in a bubble using the existing ScyllaDB client
func (r *ScyllaDBRepository) CreateMessage(ctx context.Context, message *database.Message) error {
	return r.client.CreateMessage(ctx, message)
}

// GetBubbleMessages retrieves messages for a bubble with pagination
func (r *ScyllaDBRepository) GetBubbleMessages(ctx context.Context, bubbleID string, limit, offset int) ([]*database.Message, error) {
	bubbleUUID, err := gocql.ParseUUID(bubbleID)
	if err != nil {
		return nil, fmt.Errorf("invalid bubble ID: %w", err)
	}

	messages, _, err := r.client.GetMessages(ctx, bubbleUUID, limit, nil)
	return messages, err
}

// UpdateMessage updates a message (for editing)
func (r *ScyllaDBRepository) UpdateMessage(ctx context.Context, messageID, newContent string) error {
	// For simplicity, we'll need to get the message first to get the full primary key
	// TODO : In a real implementation, you'd pass the full primary key from the client
	r.logger.Warn("UpdateMessage requires full primary key - simplified implementation")
	return fmt.Errorf("update message requires bubble_id and created_at - not implemented in simplified version")
}

// DeleteMessage soft deletes a message
func (r *ScyllaDBRepository) DeleteMessage(ctx context.Context, messageID string) error {
	// For simplicity, we'll need to get the message first to get the full primary key
	// TODO : In a real implementation, you'd pass the full primary key from the client
	r.logger.Warn("DeleteMessage requires full primary key - simplified implementation")
	return fmt.Errorf("delete message requires bubble_id and created_at - not implemented in simplified version")
}

// SearchMessages searches for messages containing the query text
// Note: This is a basic implementation using LIKE operator. For production,
// use an external search engine like Elasticsearch for better performance and features.
func (r *ScyllaDBRepository) SearchMessages(ctx context.Context, bubbleID, query string, limit, offset int) ([]*database.Message, error) {
	// Parse bubble ID
	bubbleUUID, err := gocql.ParseUUID(bubbleID)
	if err != nil {
		return nil, fmt.Errorf("invalid bubble ID: %w", err)
	}

	// Basic search using LIKE operator (not efficient for large datasets)
	// In production, you should use an external search engine
	searchQuery := `
		SELECT bubble_id, message_id, sender_id, content, message_type, media_url,
		       reply_to_id, is_edited, is_deleted, created_at, updated_at
		FROM messages
		WHERE bubble_id = ? AND content LIKE ?
		ALLOW FILTERING
		LIMIT ?`

	// Add wildcards for partial matching
	searchTerm := "%" + query + "%"

	iter := r.client.Session.Query(searchQuery, bubbleUUID, searchTerm, limit).WithContext(ctx).Iter()
	defer iter.Close()

	var messages []*database.Message
	var message database.Message

	for iter.Scan(&message.BubbleID, &message.MessageID, &message.SenderID, &message.Content,
		&message.MessageType, &message.MediaURL, &message.ReplyToID, &message.IsEdited,
		&message.IsDeleted, &message.CreatedAt, &message.UpdatedAt) {

		// Skip deleted messages in search results
		if !message.IsDeleted {
			messages = append(messages, &message)
		}
	}

	if err := iter.Close(); err != nil {
		return nil, fmt.Errorf("failed to search messages: %w", err)
	}

	r.logger.Info("Message search completed",
		zap.String("bubble_id", bubbleID),
		zap.String("query", query),
		zap.Int("results", len(messages)),
		zap.Int("limit", limit))

	return messages, nil
}

// CreateConversation creates a new direct message conversation
func (r *ScyllaDBRepository) CreateConversation(ctx context.Context, conversation *database.Conversation) error {
	return r.client.CreateConversation(ctx, conversation)
}

// GetUserConversations retrieves conversations for a user
func (r *ScyllaDBRepository) GetUserConversations(ctx context.Context, userID string, limit, offset int) ([]*database.Conversation, error) {
	userUUID, err := gocql.ParseUUID(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	conversations, err := r.client.GetUserConversations(ctx, userUUID, limit)
	return conversations, err
}

// GetConversationMessages retrieves messages for a conversation
func (r *ScyllaDBRepository) GetConversationMessages(ctx context.Context, conversationID string, limit, offset int) ([]*database.Message, error) {
	conversationUUID, err := gocql.ParseUUID(conversationID)
	if err != nil {
		return nil, fmt.Errorf("invalid conversation ID: %w", err)
	}

	convMessages, _, err := r.client.GetConversationMessages(ctx, conversationUUID, limit, nil)
	if err != nil {
		return nil, err
	}

	// Convert ConversationMessage to Message
	var messages []*database.Message
	for _, convMsg := range convMessages {
		msg := &database.Message{
			BubbleID:    gocql.UUID{}, // Empty for conversation messages
			MessageID:   convMsg.MessageID,
			SenderID:    convMsg.SenderID,
			Content:     convMsg.Content,
			MessageType: convMsg.MessageType,
			MediaURL:    convMsg.MediaURL,
			ReplyToID:   convMsg.ReplyToID,
			IsEdited:    convMsg.IsEdited,
			IsDeleted:   convMsg.IsDeleted,
			CreatedAt:   convMsg.CreatedAt,
			UpdatedAt:   convMsg.UpdatedAt,
		}
		messages = append(messages, msg)
	}

	return messages, nil
}

// SendDirectMessage creates a direct message between two users
func (r *ScyllaDBRepository) SendDirectMessage(ctx context.Context, senderID, recipientID, content string) (*database.ConversationMessage, error) {
	// Parse UUIDs
	senderUUID, err := gocql.ParseUUID(senderID)
	if err != nil {
		return nil, fmt.Errorf("invalid sender ID: %w", err)
	}

	recipientUUID, err := gocql.ParseUUID(recipientID)
	if err != nil {
		return nil, fmt.Errorf("invalid recipient ID: %w", err)
	}

	// Generate conversation ID deterministically from user IDs
	conversationID := r.generateConversationID(senderID, recipientID)
	conversationUUID, err := gocql.ParseUUID(conversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to parse conversation ID: %w", err)
	}

	// Create conversation message
	messageID := gocql.TimeUUID()
	now := time.Now()

	conversationMessage := &database.ConversationMessage{
		ConversationID: conversationUUID,
		MessageID:      messageID,
		SenderID:       senderUUID,
		RecipientID:    recipientUUID,
		Content:        content,
		MessageType:    "text",
		IsEdited:       false,
		IsDeleted:      false,
		IsRead:         false,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	// Store the message using the ScyllaDB client
	err = r.client.CreateConversationMessage(ctx, conversationMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to create conversation message: %w", err)
	}

	r.logger.Info("Direct message sent successfully",
		zap.String("message_id", messageID.String()),
		zap.String("conversation_id", conversationID),
		zap.String("sender_id", senderID),
		zap.String("recipient_id", recipientID))

	return conversationMessage, nil
}

// generateConversationID creates a deterministic conversation ID from two user IDs
func (r *ScyllaDBRepository) generateConversationID(userID1, userID2 string) string {
	// Sort user IDs to ensure consistent conversation ID regardless of order
	if userID1 > userID2 {
		userID1, userID2 = userID2, userID1
	}
	return fmt.Sprintf("conv_%s_%s", userID1, userID2)
}

// MarkMessageAsRead marks a message as read by a user
func (r *ScyllaDBRepository) MarkMessageAsRead(ctx context.Context, messageID, userID string) error {
	// Simplified implementation - requires full primary key in real implementation
	r.logger.Info("MarkMessageAsRead called - simplified implementation")
	return nil
}
