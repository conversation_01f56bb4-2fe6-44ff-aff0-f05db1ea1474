package call

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	callv1 "hopenbackend/protos/gen/call"
	commonv1 "hopenbackend/protos/gen/common"
	userv1 "hopenbackend/protos/gen/user"

	"github.com/google/uuid"
)

// UserService interface for dependency injection
type UserService interface {
	GetUser(ctx context.Context, req *userv1.GetUserRequest) (*userv1.GetUserResponse, error)
}

// BubbleService interface for dependency injection
type BubbleService interface {
	CheckBubbleMembership(ctx context.Context, bubbleID, userID string) (bool, error)
}

// BubbleServiceWrapper wraps database access to implement bubble membership checking
type BubbleServiceWrapper struct {
	DB *database.PostgreSQLClient
}

func (w *BubbleServiceWrapper) CheckBubbleMembership(ctx context.Context, bubbleID, userID string) (bool, error) {
	if w.DB == nil {
		return false, fmt.Errorf("database connection not available")
	}

	query := `SELECT EXISTS(SELECT 1 FROM bubble_members WHERE bubble_id = $1 AND user_id = $2 AND status = 'active')`
	var exists bool
	err := w.DB.Pool.QueryRow(ctx, query, bubbleID, userID).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check bubble membership: %w", err)
	}

	return exists, nil
}

// Service handles call operations using gRPC and implements CallServiceServer
type Service struct {
	callv1.UnimplementedCallServiceServer
	logger        *zap.Logger
	db            *database.PostgreSQLClient
	livekitRepo   *LiveKitRepository
	config        *config.Config
	rateLimiter   *ratelimit.RateLimiter
	oryClient     *ory.Client
	natsConn      *nats.Conn
	userService   UserService
	bubbleService BubbleService
}

// CallEvent represents a call-related event for NATS
type CallEvent struct {
	UserID    string    `json:"user_id"`
	CallID    string    `json:"call_id"`
	Type      string    `json:"type"`
	Action    string    `json:"action"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the call service
type Dependencies struct {
	Logger        *zap.Logger
	DB            *database.PostgreSQLClient
	Config        *config.Config
	RateLimiter   *ratelimit.RateLimiter
	OryClient     *ory.Client
	NATSConn      *nats.Conn
	UserService   UserService
	BubbleService BubbleService
}

// NewService creates a new call service instance
func NewService(deps *Dependencies) *Service {
	// Initialize LiveKit repository
	var livekitRepo *LiveKitRepository
	if deps.Config.LiveKit.APIKey != "" && deps.Config.LiveKit.APISecret != "" {
		livekitRepo = NewLiveKitRepository(
			deps.Config.LiveKit.APIKey,
			deps.Config.LiveKit.APISecret,
			deps.Config.LiveKit.ServerURL,
			deps.Logger,
		)
		deps.Logger.Info("LiveKit repository initialized successfully")
	} else {
		deps.Logger.Warn("LiveKit credentials not configured, call service will use fallback implementation")
	}

	return &Service{
		logger:        deps.Logger,
		db:            deps.DB,
		livekitRepo:   livekitRepo,
		config:        deps.Config,
		rateLimiter:   deps.RateLimiter,
		oryClient:     deps.OryClient,
		natsConn:      deps.NATSConn,
		userService:   deps.UserService,
		bubbleService: deps.BubbleService,
	}
}

// NewCallServiceServer creates a new gRPC server for the call service
func NewCallServiceServer(service *Service) callv1.CallServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishCallEvent publishes call events via NATS
func (s *Service) publishCallEvent(userID, callID, eventType, action string, data any) {
	if s.natsConn == nil {
		return
	}

	event := CallEvent{
		UserID:    userID,
		CallID:    callID,
		Type:      eventType,
		Action:    action,
		Data:      data,
		Timestamp: time.Now(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal call event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("call.%s", action)
	if err := s.natsConn.Publish(subject, eventData); err != nil {
		s.logger.Error("Failed to publish call event", zap.Error(err))
	}
}

// StartCall implements the StartCall gRPC method
func (s *Service) StartCall(ctx context.Context, req *callv1.StartCallRequest) (*callv1.StartCallResponse, error) {
	s.logger.Info("StartCall called",
		zap.String("user_id", req.UserId),
		zap.String("bubble_id", req.BubbleId))

	if req.UserId == "" || req.BubbleId == "" {
		return &callv1.StartCallResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and bubble ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and bubble ID are required")
	}

	// Create call in database and start LiveKit room
	callId := uuid.NewString()

	// Create call object
	call := &Call{
		CallID:      callId,
		BubbleID:    req.BubbleId,
		InitiatorID: req.UserId,
		CallType:    req.CallType.String(),
		Status:      "initiated",
		StartedAt:   time.Now(),
	}

	// Create LiveKit room if repository is available
	if s.livekitRepo != nil {
		createdCall, err := s.livekitRepo.CreateCall(ctx, call)
		if err != nil {
			s.logger.Error("Failed to create LiveKit room", zap.Error(err))
			return &callv1.StartCallResponse{
				ApiResponse: createErrorResponse("LIVEKIT_ERROR", "Failed to create call room"),
			}, status.Errorf(codes.Internal, "failed to create call room: %v", err)
		}
		call = createdCall
	}

	// Generate access token for the initiator
	var accessToken string
	if s.livekitRepo != nil {
		token, err := s.livekitRepo.GenerateAccessToken(callId, req.UserId, "User") // Would get actual username
		if err != nil {
			s.logger.Error("Failed to generate access token", zap.Error(err))
		} else {
			accessToken = token
		}
	}

	callInfo := &callv1.CallInfo{
		CallId:      call.CallID,
		BubbleId:    call.BubbleID,
		InitiatorId: call.InitiatorID,
		CallType:    req.CallType,
		Status:      callv1.CallStatus_CALL_STATUS_CONNECTED,
		StartedAt:   timestamppb.New(call.StartedAt),
		Participants: []*callv1.CallParticipant{
			{
				UserId:   req.UserId,
				JoinedAt: timestamppb.New(call.StartedAt),
				IsMuted:  false,
				HasVideo: req.WithVideo,
				HasAudio: true,
			},
		},
	}

	// Publish call started event via NATS
	s.publishCallEvent(req.UserId, callId, "call", "started", map[string]interface{}{
		"bubble_id":  req.BubbleId,
		"call_type":  req.CallType,
		"with_video": req.WithVideo,
	})

	// Log access token for debugging (in production, this would be returned securely)
	if accessToken != "" {
		s.logger.Debug("Generated LiveKit access token for call initiator",
			zap.String("call_id", callId),
			zap.String("user_id", req.UserId))
	}

	return &callv1.StartCallResponse{
		CallId:      callId,
		CallInfo:    callInfo,
		ApiResponse: createSuccessResponse("Call started successfully"),
	}, nil
}

// JoinCall implements the gRPC method to authorize a user to join a call.
// It returns a LiveKit access token that the client will use to connect.
func (s *Service) JoinCall(ctx context.Context, req *callv1.JoinCallRequest) (*callv1.JoinCallResponse, error) {
	s.logger.Info("JoinCall called",
		zap.String("user_id", req.UserId),
		zap.String("call_id", req.CallId))

	if req.UserId == "" || req.CallId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "User ID and Call ID are required")
	}

	if s.livekitRepo == nil {
		s.logger.Error("LiveKit repository not configured")
		return nil, status.Errorf(codes.FailedPrecondition, "Call functionality is not available")
	}

	// 1. Verify the call exists in LiveKit.
	call, err := s.livekitRepo.GetCall(ctx, req.CallId)
	if err != nil {
		s.logger.Error("Failed to get call info for joining", zap.Error(err))
		return nil, status.Errorf(codes.NotFound, "Call not found or has already ended")
	}

	// 2. Authorization: Check if user is a member of the bubble
	if s.bubbleService != nil && call.BubbleID != "" {
		isMember, err := s.bubbleService.CheckBubbleMembership(ctx, call.BubbleID, req.UserId)
		if err != nil {
			s.logger.Error("Failed to check bubble membership for call authorization",
				zap.String("user_id", req.UserId),
				zap.String("bubble_id", call.BubbleID),
				zap.Error(err))
			return nil, status.Errorf(codes.Internal, "Failed to verify authorization")
		}

		if !isMember {
			s.logger.Warn("User attempted to join call without bubble membership",
				zap.String("user_id", req.UserId),
				zap.String("bubble_id", call.BubbleID),
				zap.String("call_id", req.CallId))
			return nil, status.Errorf(codes.PermissionDenied, "User not authorized to join this call")
		}

		s.logger.Info("User authorized to join call",
			zap.String("user_id", req.UserId),
			zap.String("bubble_id", call.BubbleID),
			zap.String("call_id", req.CallId))
	}

	// 3. Get user information for display name
	userName := "User " + req.UserId // Default fallback
	if s.userService != nil {
		userResp, err := s.userService.GetUser(ctx, &userv1.GetUserRequest{UserId: req.UserId})
		if err == nil && userResp.User != nil {
			// Use display name if available, otherwise use username
			if userResp.User.FirstName != "" && userResp.User.LastName != "" {
				userName = userResp.User.FirstName + " " + userResp.User.LastName
			} else if userResp.User.Username != "" {
				userName = userResp.User.Username
			}
		} else {
			s.logger.Warn("Failed to get user information for call",
				zap.String("user_id", req.UserId),
				zap.Error(err))
		}
	}

	// 4. Generate a unique access token for this user and this call
	accessToken, err := s.livekitRepo.GenerateAccessToken(req.CallId, req.UserId, userName)
	if err != nil {
		s.logger.Error("Failed to generate access token", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Could not authorize user for the call")
	}

	s.logger.Info("Successfully generated access token for user to join call",
		zap.String("user_id", req.UserId),
		zap.String("call_id", req.CallId))

	// Publish call joined event via NATS.
	s.publishCallEvent(req.UserId, req.CallId, "call", "joined", map[string]interface{}{
		"user_id": req.UserId,
	})

	// 5. Return the call info with access token embedded in the response message.
	// Note: The access token should be added to the protobuf definition.
	// For now, we'll include it in the API response message.

	// Convert participant IDs to CallParticipant objects
	var participants []*callv1.CallParticipant
	for _, participantID := range call.Participants {
		participants = append(participants, &callv1.CallParticipant{
			UserId:   participantID,
			JoinedAt: timestamppb.New(call.StartedAt), // Simplified - would get actual join time
			IsMuted:  false,
			HasVideo: req.WithVideo,
			HasAudio: req.WithAudio,
		})
	}

	callInfo := &callv1.CallInfo{
		CallId:       call.CallID,
		BubbleId:     call.BubbleID,
		InitiatorId:  call.InitiatorID,
		CallType:     callv1.CallType_CALL_TYPE_VIDEO, // Default to video
		Status:       callv1.CallStatus_CALL_STATUS_CONNECTED,
		StartedAt:    timestamppb.New(call.StartedAt),
		Participants: participants,
	}

	return &callv1.JoinCallResponse{
		CallInfo:    callInfo,
		AccessToken: accessToken,
		LivekitUrl:  s.config.LiveKit.ServerURL,
		ApiResponse: createSuccessResponse("Authorization successful. Use the access token to connect."),
	}, nil
}

// LeaveCall implements the LeaveCall gRPC method
func (s *Service) LeaveCall(ctx context.Context, req *callv1.LeaveCallRequest) (*callv1.LeaveCallResponse, error) {
	s.logger.Info("LeaveCall called",
		zap.String("user_id", req.UserId),
		zap.String("call_id", req.CallId))

	if req.UserId == "" || req.CallId == "" {
		return &callv1.LeaveCallResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and call ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and call ID are required")
	}

	// Leave call in LiveKit room and update database
	if s.livekitRepo != nil {
		err := s.livekitRepo.LeaveCall(ctx, req.CallId, req.UserId)
		if err != nil {
			s.logger.Error("Failed to leave LiveKit call", zap.Error(err))
			// Don't return error, just log it and continue with cleanup
		}
	}

	// Publish call left event via NATS
	s.publishCallEvent(req.UserId, req.CallId, "call", "left", map[string]interface{}{
		"user_id": req.UserId,
	})

	return &callv1.LeaveCallResponse{
		Success:     true,
		ApiResponse: createSuccessResponse("Left call successfully"),
	}, nil
}

// EndCall implements the EndCall gRPC method
func (s *Service) EndCall(ctx context.Context, req *callv1.EndCallRequest) (*callv1.EndCallResponse, error) {
	s.logger.Info("EndCall called",
		zap.String("user_id", req.UserId),
		zap.String("call_id", req.CallId))

	if req.UserId == "" || req.CallId == "" {
		return &callv1.EndCallResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and call ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and call ID are required")
	}

	// End call in LiveKit room and update database
	if s.livekitRepo != nil {
		err := s.livekitRepo.EndCall(ctx, req.CallId)
		if err != nil {
			s.logger.Error("Failed to end LiveKit call", zap.Error(err))
			return &callv1.EndCallResponse{
				ApiResponse: createErrorResponse("LIVEKIT_ERROR", "Failed to end call"),
			}, status.Errorf(codes.Internal, "failed to end call: %v", err)
		}
	}

	// Publish call ended event via NATS
	s.publishCallEvent(req.UserId, req.CallId, "call", "ended", map[string]interface{}{
		"user_id": req.UserId,
	})

	return &callv1.EndCallResponse{
		Success:     true,
		ApiResponse: createSuccessResponse("Call ended successfully"),
	}, nil
}

// GetCall implements the GetCall gRPC method
func (s *Service) GetCall(ctx context.Context, req *callv1.GetCallRequest) (*callv1.GetCallResponse, error) {
	s.logger.Info("GetCall called", zap.String("call_id", req.CallId))

	if req.CallId == "" {
		return &callv1.GetCallResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Call ID is required"),
		}, status.Errorf(codes.InvalidArgument, "call ID is required")
	}

	// Get call from LiveKit and database
	var callInfo *callv1.CallInfo

	if s.livekitRepo != nil {
		call, err := s.livekitRepo.GetCall(ctx, req.CallId)
		if err != nil {
			s.logger.Error("Failed to get call info", zap.Error(err))
			return &callv1.GetCallResponse{
				ApiResponse: createErrorResponse("CALL_NOT_FOUND", "Call not found"),
			}, status.Errorf(codes.NotFound, "call not found: %v", err)
		}

		// Convert participants to proto format using detailed participant information
		var participants []*callv1.CallParticipant
		for _, participant := range call.DetailedParticipants {
			// Extract actual audio/video status from tracks
			hasAudio := false
			hasVideo := false
			isMuted := true // Default to muted if no audio track found

			for _, track := range participant.Tracks {
				switch track.Type {
				case "audio":
					hasAudio = true
					isMuted = track.Muted
				case "video":
					hasVideo = true
				}
			}

			// Use actual join time from LiveKit
			joinedAt := time.Unix(participant.JoinedAt, 0)

			participants = append(participants, &callv1.CallParticipant{
				UserId:   participant.Identity,
				JoinedAt: timestamppb.New(joinedAt),
				IsMuted:  isMuted,
				HasVideo: hasVideo,
				HasAudio: hasAudio,
			})
		}

		callInfo = &callv1.CallInfo{
			CallId:       call.CallID,
			BubbleId:     call.BubbleID,
			InitiatorId:  call.InitiatorID,
			CallType:     callv1.CallType_CALL_TYPE_VIDEO, // Default to video
			Status:       callv1.CallStatus_CALL_STATUS_CONNECTED,
			StartedAt:    timestamppb.New(call.StartedAt),
			Participants: participants,
		}
	} else {
		// Fallback implementation
		callInfo = &callv1.CallInfo{
			CallId:       req.CallId,
			BubbleId:     "unknown_bubble",
			InitiatorId:  "unknown_initiator",
			CallType:     callv1.CallType_CALL_TYPE_VIDEO,
			Status:       callv1.CallStatus_CALL_STATUS_CONNECTED,
			StartedAt:    timestamppb.Now(),
			Participants: []*callv1.CallParticipant{},
		}
	}

	return &callv1.GetCallResponse{
		CallInfo:    callInfo,
		ApiResponse: createSuccessResponse("Call retrieved successfully"),
	}, nil
}
