package call

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// Call represents a call in the database
type Call struct {
	CallID               string               `json:"call_id"`
	BubbleID             string               `json:"bubble_id"`
	InitiatorID          string               `json:"initiator_id"`
	CallType             string               `json:"call_type"` // audio, video, screen_share
	Status               string               `json:"status"`    // initiated, ringing, connected, ended
	LiveKitRoom          string               `json:"livekit_room"`
	Participants         []string             `json:"participants"`          // For backward compatibility
	DetailedParticipants []LiveKitParticipant `json:"detailed_participants"` // Detailed participant info
	StartedAt            time.Time            `json:"started_at"`
	EndedAt              *time.Time           `json:"ended_at"`
	Duration             int32                `json:"duration"` // in seconds
	CreatedAt            time.Time            `json:"created_at"`
	UpdatedAt            time.Time            `json:"updated_at"`
}

// CallParticipant represents a participant in a call
type CallParticipant struct {
	CallID     string     `json:"call_id"`
	UserID     string     `json:"user_id"`
	JoinedAt   time.Time  `json:"joined_at"`
	LeftAt     *time.Time `json:"left_at"`
	Status     string     `json:"status"` // joining, connected, disconnected
	AudioMuted bool       `json:"audio_muted"`
	VideoMuted bool       `json:"video_muted"`
}

// LiveKitRoom represents a LiveKit room response
type LiveKitRoom struct {
	Name            string `json:"name"`
	Sid             string `json:"sid"`
	CreationTime    int64  `json:"creation_time"`
	Metadata        string `json:"metadata"`
	NumParticipants int32  `json:"num_participants"`
}

// LiveKitTrack represents an audio or video track
type LiveKitTrack struct {
	Sid    string `json:"sid"`
	Type   string `json:"type"`   // audio, video
	Source string `json:"source"` // camera, microphone, screen_share
	Muted  bool   `json:"muted"`
	Width  int32  `json:"width"`  // for video tracks
	Height int32  `json:"height"` // for video tracks
}

// LiveKitParticipant represents a participant in a LiveKit room
type LiveKitParticipant struct {
	Identity    string                 `json:"identity"`
	Name        string                 `json:"name"`
	Metadata    string                 `json:"metadata"`
	JoinedAt    int64                  `json:"joined_at"`
	State       string                 `json:"state"`      // JOINING, JOINED, DISCONNECTED
	Tracks      []LiveKitTrack         `json:"tracks"`     // Audio/video tracks
	Permission  map[string]interface{} `json:"permission"` // Participant permissions
	IsPublisher bool                   `json:"is_publisher"`
}

// LiveKitRepository handles call operations using LiveKit HTTP API
type LiveKitRepository struct {
	apiKey     string
	apiSecret  string
	serverURL  string
	httpClient *http.Client
	logger     *zap.Logger
}

// NewLiveKitRepository creates a new LiveKit repository
func NewLiveKitRepository(apiKey, apiSecret, serverURL string, logger *zap.Logger) *LiveKitRepository {
	return &LiveKitRepository{
		apiKey:     apiKey,
		apiSecret:  apiSecret,
		serverURL:  serverURL,
		httpClient: &http.Client{Timeout: 30 * time.Second},
		logger:     logger,
	}
}

// generateJWT generates a JWT token for LiveKit API authentication
func (r *LiveKitRepository) generateJWT(grant map[string]interface{}) (string, error) {
	header := map[string]interface{}{
		"alg": "HS256",
		"typ": "JWT",
	}

	now := time.Now()
	payload := map[string]interface{}{
		"iss": r.apiKey,
		"exp": now.Add(time.Hour).Unix(),
		"nbf": now.Unix(),
		"iat": now.Unix(),
	}

	// Merge grant into payload
	for k, v := range grant {
		payload[k] = v
	}

	// Encode header and payload
	headerBytes, _ := json.Marshal(header)
	payloadBytes, _ := json.Marshal(payload)

	headerB64 := base64.RawURLEncoding.EncodeToString(headerBytes)
	payloadB64 := base64.RawURLEncoding.EncodeToString(payloadBytes)

	// Create signature
	message := headerB64 + "." + payloadB64
	h := hmac.New(sha256.New, []byte(r.apiSecret))
	h.Write([]byte(message))
	signature := base64.RawURLEncoding.EncodeToString(h.Sum(nil))

	return message + "." + signature, nil
}

// makeAPIRequest makes an authenticated request to LiveKit API
func (r *LiveKitRepository) makeAPIRequest(ctx context.Context, method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewReader(jsonBody)
	}

	url := r.serverURL + endpoint
	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Generate JWT for authentication
	grant := map[string]interface{}{
		"video": map[string]interface{}{
			"roomAdmin":  true,
			"roomList":   true,
			"roomCreate": true,
		},
	}

	token, err := r.generateJWT(grant)
	if err != nil {
		return nil, fmt.Errorf("failed to generate JWT: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	return r.httpClient.Do(req)
}

// CreateCall creates a new call and LiveKit room
func (r *LiveKitRepository) CreateCall(ctx context.Context, call *Call) (*Call, error) {
	// Generate unique and deterministic room name based on the Call ID
	roomName := fmt.Sprintf("call-%s", call.CallID)
	call.LiveKitRoom = roomName

	// Create LiveKit room via HTTP API
	roomData := map[string]interface{}{
		"name":             roomName,
		"empty_timeout":    300, // 5 minutes
		"max_participants": 50,  // Adjust based on your needs
		"metadata":         fmt.Sprintf(`{"call_id":"%s","bubble_id":"%s","call_type":"%s"}`, call.CallID, call.BubbleID, call.CallType),
	}

	resp, err := r.makeAPIRequest(ctx, "POST", "/twirp/livekit.RoomService/CreateRoom", roomData)
	if err != nil {
		r.logger.Error("Failed to create LiveKit room", zap.Error(err))
		return nil, fmt.Errorf("failed to create LiveKit room: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		r.logger.Error("LiveKit room creation failed", zap.Int("status", resp.StatusCode), zap.String("body", string(body)))
		return nil, fmt.Errorf("failed to create LiveKit room: status %d", resp.StatusCode)
	}

	r.logger.Info("LiveKit room created successfully",
		zap.String("room_name", roomName),
		zap.String("call_id", call.CallID))

	call.Status = "initiated"
	call.CreatedAt = time.Now()
	call.UpdatedAt = time.Now()

	return call, nil
}

// GenerateAccessToken generates a LiveKit access token for a user to join a call
func (r *LiveKitRepository) GenerateAccessToken(callID, userID, userName string) (string, error) {
	// Use the same deterministic room name format
	roomName := fmt.Sprintf("call-%s", callID)

	// Create access token grant with permissions for camera, microphone, and screen sharing
	grant := map[string]interface{}{
		"video": map[string]interface{}{
			"roomJoin":     true,
			"room":         roomName,
			"canPublish":   true,
			"canSubscribe": true,
			// Explicitly define what sources can be published
			"canPublishSources": []string{"camera", "microphone", "screen"},
		},
		"sub":      userID,
		"name":     userName,
		"metadata": fmt.Sprintf(`{"user_id":"%s"}`, userID), // Example of passing metadata
	}

	token, err := r.generateJWT(grant)
	if err != nil {
		r.logger.Error("Failed to generate LiveKit access token", zap.Error(err))
		return "", fmt.Errorf("failed to generate access token: %w", err)
	}

	r.logger.Debug("Generated LiveKit access token",
		zap.String("user_id", userID),
		zap.String("room_name", roomName))

	return token, nil
}

// LeaveCall removes a participant from a call
func (r *LiveKitRepository) LeaveCall(ctx context.Context, callID, userID string) error {
	// Use the same deterministic room name format
	roomName := fmt.Sprintf("call-%s", callID)

	// Remove participant from LiveKit room via HTTP API
	removeData := map[string]interface{}{
		"room":     roomName,
		"identity": userID,
	}

	resp, err := r.makeAPIRequest(ctx, "POST", "/twirp/livekit.RoomService/RemoveParticipant", removeData)
	if err != nil {
		r.logger.Error("Failed to remove participant from LiveKit room", zap.Error(err))
		return fmt.Errorf("failed to remove participant: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		r.logger.Error("Failed to remove participant", zap.Int("status", resp.StatusCode), zap.String("body", string(body)))
		return fmt.Errorf("failed to remove participant: status %d", resp.StatusCode)
	}

	r.logger.Info("User left call",
		zap.String("call_id", callID),
		zap.String("user_id", userID))

	return nil
}

// EndCall ends a call and closes the LiveKit room
func (r *LiveKitRepository) EndCall(ctx context.Context, callID string) error {
	// Use the same deterministic room name format
	roomName := fmt.Sprintf("call-%s", callID)

	// Delete LiveKit room via HTTP API
	deleteData := map[string]interface{}{
		"room": roomName,
	}

	resp, err := r.makeAPIRequest(ctx, "POST", "/twirp/livekit.RoomService/DeleteRoom", deleteData)
	if err != nil {
		r.logger.Error("Failed to delete LiveKit room", zap.Error(err))
		return fmt.Errorf("failed to delete LiveKit room: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		r.logger.Error("Failed to delete room", zap.Int("status", resp.StatusCode), zap.String("body", string(body)))
		return fmt.Errorf("failed to delete LiveKit room: status %d", resp.StatusCode)
	}

	r.logger.Info("Call ended and LiveKit room deleted",
		zap.String("call_id", callID),
		zap.String("room_name", roomName))

	return nil
}

// GetCall retrieves call information
func (r *LiveKitRepository) GetCall(ctx context.Context, callID string) (*Call, error) {
	// Use the same deterministic room name format
	roomName := fmt.Sprintf("call-%s", callID)

	// List rooms to find our call room via HTTP API
	listData := map[string]interface{}{
		"names": []string{roomName},
	}

	resp, err := r.makeAPIRequest(ctx, "POST", "/twirp/livekit.RoomService/ListRooms", listData)
	if err != nil {
		r.logger.Error("Failed to get LiveKit room info", zap.Error(err))
		return nil, fmt.Errorf("failed to get room info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("call not found")
	}

	var response struct {
		Rooms []LiveKitRoom `json:"rooms"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if len(response.Rooms) == 0 {
		return nil, fmt.Errorf("call not found")
	}

	room := response.Rooms[0]

	// Parse metadata to get call info
	call := &Call{
		CallID:      callID,
		LiveKitRoom: room.Name,
		Status:      "connected", // Simplified status
		StartedAt:   time.Unix(room.CreationTime, 0),
		UpdatedAt:   time.Now(),
	}

	// Get participants with detailed information
	participantsData := map[string]interface{}{
		"room": roomName,
	}

	participantsResp, err := r.makeAPIRequest(ctx, "POST", "/twirp/livekit.RoomService/ListParticipants", participantsData)
	if err == nil {
		defer participantsResp.Body.Close()

		var participantsResponse struct {
			Participants []LiveKitParticipant `json:"participants"`
		}

		if json.NewDecoder(participantsResp.Body).Decode(&participantsResponse) == nil {
			// Store detailed participant information
			call.DetailedParticipants = participantsResponse.Participants

			// Also maintain backward compatibility with simple participant list
			for _, p := range participantsResponse.Participants {
				call.Participants = append(call.Participants, p.Identity)
			}
		}
	}

	return call, nil
}
