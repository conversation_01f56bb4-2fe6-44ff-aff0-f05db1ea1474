package presence

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	presencev1 "hopenbackend/protos/gen/presence"
)

// Service handles presence operations using gRPC and implements PresenceServiceServer
type Service struct {
	presencev1.UnimplementedPresenceServiceServer
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	repository  *PostgreSQLRepository
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
	mqttClient  mqtt.Client
}

// PresenceEvent represents a presence-related event for NATS
type PresenceEvent struct {
	UserID    string    `json:"user_id"`
	Status    string    `json:"status"`
	Action    string    `json:"action"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the presence service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
	MQTTClient  mqtt.Client
}

// NewService creates a new presence service instance
func NewService(deps *Dependencies) *Service {
	// Initialize repository
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	return &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		repository:  repository,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
		mqttClient:  deps.MQTTClient,
	}
}

// NewPresenceServiceServer creates a new gRPC server for the presence service
func NewPresenceServiceServer(service *Service) presencev1.PresenceServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishPresenceEvent publishes presence events via NATS
func (s *Service) publishPresenceEvent(userID, presenceStatus, action string, data any) {
	if s.natsConn == nil {
		return
	}

	event := PresenceEvent{
		UserID:    userID,
		Status:    presenceStatus,
		Action:    action,
		Data:      data,
		Timestamp: time.Now(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal presence event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("presence.%s", action)
	if err := s.natsConn.Publish(subject, eventData); err != nil {
		s.logger.Error("Failed to publish presence event", zap.Error(err))
	}
}

// publishMQTTPresenceUpdate publishes real-time presence updates via MQTT
func (s *Service) publishMQTTPresenceUpdate(userID, status string, data map[string]interface{}) {
	if s.mqttClient == nil || !s.mqttClient.IsConnected() {
		return
	}

	presenceUpdate := map[string]interface{}{
		"user_id":   userID,
		"status":    status,
		"timestamp": time.Now().Unix(),
		"data":      data,
	}

	payload, err := json.Marshal(presenceUpdate)
	if err != nil {
		s.logger.Error("Failed to marshal MQTT presence update", zap.Error(err))
		return
	}

	// Publish to user-specific presence topic
	userTopic := fmt.Sprintf("user/%s/presence", userID)
	token := s.mqttClient.Publish(userTopic, 1, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish MQTT presence update", zap.Error(token.Error()))
	}

	// Also publish to global presence topic for subscribers
	globalTopic := "presence/updates"
	token = s.mqttClient.Publish(globalTopic, 1, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish MQTT global presence update", zap.Error(token.Error()))
	}
}

// GetUserPresence implements the GetUserPresence gRPC method
func (s *Service) GetUserPresence(ctx context.Context, req *presencev1.GetUserPresenceRequest) (*presencev1.GetUserPresenceResponse, error) {
	s.logger.Info("GetUserPresence called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &presencev1.GetUserPresenceResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get presence from database
	userPresence, err := s.repository.GetUserPresence(ctx, req.UserId)
	if err != nil {
		// If user not found, return default offline presence
		s.logger.Info("User presence not found, returning default", zap.String("user_id", req.UserId))
		userPresence = &UserPresence{
			UserID:    req.UserId,
			Status:    "offline",
			LastSeen:  time.Now().Add(-24 * time.Hour),
			UpdatedAt: time.Now(),
		}
	}

	// Convert status to proto enum
	var protoStatus presencev1.PresenceStatus
	switch userPresence.Status {
	case "online":
		protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_ONLINE
	case "away":
		protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_AWAY
	case "busy":
		protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_BUSY
	default:
		protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_OFFLINE
	}

	presence := &presencev1.UserPresence{
		UserId:    userPresence.UserID,
		Status:    protoStatus,
		LastSeen:  timestamppb.New(userPresence.LastSeen),
		UpdatedAt: timestamppb.New(userPresence.UpdatedAt),
	}

	// Custom status field not available in current proto definition

	return &presencev1.GetUserPresenceResponse{
		Presence:    presence,
		ApiResponse: createSuccessResponse("User presence retrieved successfully"),
	}, nil
}

// UpdateUserPresence implements the UpdateUserPresence gRPC method
func (s *Service) UpdateUserPresence(ctx context.Context, req *presencev1.UpdateUserPresenceRequest) (*presencev1.UpdateUserPresenceResponse, error) {
	s.logger.Info("UpdateUserPresence called",
		zap.String("user_id", req.UserId),
		zap.String("status", req.Status.String()))

	if req.UserId == "" {
		return &presencev1.UpdateUserPresenceResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Convert proto status to string
	var statusString string
	switch req.Status {
	case presencev1.PresenceStatus_PRESENCE_STATUS_ONLINE:
		statusString = "online"
	case presencev1.PresenceStatus_PRESENCE_STATUS_AWAY:
		statusString = "away"
	case presencev1.PresenceStatus_PRESENCE_STATUS_BUSY:
		statusString = "busy"
	default:
		statusString = "offline"
	}

	// Update presence in database
	userPresence := &UserPresence{
		UserID:     req.UserId,
		Status:     statusString,
		LastSeen:   time.Now(),
		DeviceType: "unknown", // Could be passed in request
		UpdatedAt:  time.Now(),
	}

	err := s.repository.UpdateUserPresence(ctx, userPresence)
	if err != nil {
		s.logger.Error("Failed to update user presence", zap.Error(err))
		return &presencev1.UpdateUserPresenceResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to update presence"),
		}, status.Errorf(codes.Internal, "failed to update presence: %v", err)
	}

	// Publish presence updated event via NATS
	s.publishPresenceEvent(req.UserId, req.Status.String(), "updated", map[string]interface{}{
		"status":     req.Status.String(),
		"updated_at": time.Now().Unix(),
	})

	// Publish real-time presence update via MQTT
	s.publishMQTTPresenceUpdate(req.UserId, req.Status.String(), map[string]interface{}{
		"updated_at": time.Now().Unix(),
	})

	presence := &presencev1.UserPresence{
		UserId:    req.UserId,
		Status:    req.Status,
		LastSeen:  timestamppb.Now(),
		UpdatedAt: timestamppb.Now(),
	}

	return &presencev1.UpdateUserPresenceResponse{
		Presence:    presence,
		ApiResponse: createSuccessResponse("User presence updated successfully"),
	}, nil
}

// GetBatchPresence implements the GetBatchPresence gRPC method
func (s *Service) GetBatchPresence(ctx context.Context, req *presencev1.GetBatchPresenceRequest) (*presencev1.GetBatchPresenceResponse, error) {
	s.logger.Info("GetBatchPresence called", zap.Int("user_count", len(req.UserIds)))

	if len(req.UserIds) == 0 {
		return &presencev1.GetBatchPresenceResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User IDs are required"),
		}, status.Errorf(codes.InvalidArgument, "user IDs are required")
	}

	// Get batch presence from the now-corrected repository
	foundPresences, err := s.repository.GetBatchUserPresence(ctx, req.UserIds)
	if err != nil {
		s.logger.Error("Failed to get batch user presence", zap.Error(err))
		return &presencev1.GetBatchPresenceResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve user presences"),
		}, status.Errorf(codes.Internal, "failed to retrieve user presences: %v", err)
	}

	// Business logic now lives in the service.
	// Create the final map and fill in defaults for users not found by the repository.
	allPresences := make(map[string]*presencev1.UserPresence)

	for _, userID := range req.UserIds {
		if found, ok := foundPresences[userID]; ok {
			// User was found, convert their presence to proto format
			var protoStatus presencev1.PresenceStatus
			switch found.Status {
			case "online":
				protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_ONLINE
			case "away":
				protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_AWAY
			case "busy":
				protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_BUSY
			default:
				protoStatus = presencev1.PresenceStatus_PRESENCE_STATUS_OFFLINE
			}
			allPresences[userID] = &presencev1.UserPresence{
				UserId:    found.UserID,
				Status:    protoStatus,
				LastSeen:  timestamppb.New(found.LastSeen),
				UpdatedAt: timestamppb.New(found.UpdatedAt),
			}
		} else {
			// User was not found, create the default offline presence here.
			allPresences[userID] = &presencev1.UserPresence{
				UserId:    userID,
				Status:    presencev1.PresenceStatus_PRESENCE_STATUS_OFFLINE,
				LastSeen:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
				UpdatedAt: timestamppb.Now(),
			}
		}
	}

	return &presencev1.GetBatchPresenceResponse{
		Presences:   allPresences, // Use the new complete map
		ApiResponse: createSuccessResponse("Batch presence retrieved successfully"),
	}, nil
}
