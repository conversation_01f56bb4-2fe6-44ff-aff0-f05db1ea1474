package presence

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// UserPresence represents user presence in the database
type UserPresence struct {
	UserID       string    `json:"user_id"`
	Status       string    `json:"status"` // online, offline, away, busy
	LastSeen     time.Time `json:"last_seen"`
	DeviceType   string    `json:"device_type"` // mobile, web, desktop
	CustomStatus *string   `json:"custom_status"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// PostgreSQLRepository handles presence database operations
type PostgreSQLRepository struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for presence
func NewPostgreSQLRepository(pool *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		pool:   pool,
		logger: logger,
	}
}

// UpdateUserPresence updates or creates user presence
func (r *PostgreSQLRepository) UpdateUserPresence(ctx context.Context, presence *UserPresence) error {
	query := `
		INSERT INTO user_presence (user_id, status, last_seen, device_type, custom_status, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (user_id) 
		DO UPDATE SET 
			status = $2, 
			last_seen = $3, 
			device_type = $4, 
			custom_status = $5, 
			updated_at = $6`

	_, err := r.pool.Exec(ctx, query,
		presence.UserID, presence.Status, presence.LastSeen,
		presence.DeviceType, presence.CustomStatus, presence.UpdatedAt)

	if err != nil {
		r.logger.Error("Failed to update user presence", zap.Error(err))
		return fmt.Errorf("failed to update user presence: %w", err)
	}

	return nil
}

// GetUserPresence retrieves presence for a single user
func (r *PostgreSQLRepository) GetUserPresence(ctx context.Context, userID string) (*UserPresence, error) {
	query := `
		SELECT user_id, status, last_seen, device_type, custom_status, updated_at
		FROM user_presence
		WHERE user_id = $1`

	var presence UserPresence
	err := r.pool.QueryRow(ctx, query, userID).Scan(
		&presence.UserID, &presence.Status, &presence.LastSeen,
		&presence.DeviceType, &presence.CustomStatus, &presence.UpdatedAt)

	if err != nil {
		r.logger.Error("Failed to get user presence", zap.Error(err))
		return nil, fmt.Errorf("failed to get user presence: %w", err)
	}

	return &presence, nil
}

// GetBatchUserPresence retrieves presence for multiple users
func (r *PostgreSQLRepository) GetBatchUserPresence(ctx context.Context, userIDs []string) (map[string]*UserPresence, error) {
	if len(userIDs) == 0 {
		return make(map[string]*UserPresence), nil
	}

	query := `
		SELECT user_id, status, last_seen, device_type, custom_status, updated_at
		FROM user_presence
		WHERE user_id = ANY($1)`

	rows, err := r.pool.Query(ctx, query, userIDs)
	if err != nil {
		r.logger.Error("Failed to get batch user presence", zap.Error(err))
		return nil, fmt.Errorf("failed to get batch user presence: %w", err)
	}
	defer rows.Close()

	presences := make(map[string]*UserPresence)
	for rows.Next() {
		var presence UserPresence
		err := rows.Scan(
			&presence.UserID, &presence.Status, &presence.LastSeen,
			&presence.DeviceType, &presence.CustomStatus, &presence.UpdatedAt)
		if err != nil {
			r.logger.Error("Failed to scan user presence", zap.Error(err))
			continue
		}
		presences[presence.UserID] = &presence
	}

	// The repository's job is done. It has reported what is in the database.
	// Business logic for creating default objects should be in the service layer.
	return presences, nil
}

// GetOnlineUsers retrieves all currently online users
func (r *PostgreSQLRepository) GetOnlineUsers(ctx context.Context) ([]*UserPresence, error) {
	query := `
		SELECT user_id, status, last_seen, device_type, custom_status, updated_at
		FROM user_presence
		WHERE status = 'online' AND last_seen > NOW() - INTERVAL '5 minutes'
		ORDER BY last_seen DESC`

	rows, err := r.pool.Query(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get online users", zap.Error(err))
		return nil, fmt.Errorf("failed to get online users: %w", err)
	}
	defer rows.Close()

	var presences []*UserPresence
	for rows.Next() {
		var presence UserPresence
		err := rows.Scan(
			&presence.UserID, &presence.Status, &presence.LastSeen,
			&presence.DeviceType, &presence.CustomStatus, &presence.UpdatedAt)
		if err != nil {
			r.logger.Error("Failed to scan online user presence", zap.Error(err))
			continue
		}
		presences = append(presences, &presence)
	}

	return presences, nil
}

// CleanupStalePresence marks users as offline if they haven't been seen recently
func (r *PostgreSQLRepository) CleanupStalePresence(ctx context.Context, staleThreshold time.Duration) error {
	query := `
		UPDATE user_presence 
		SET status = 'offline', updated_at = NOW()
		WHERE status != 'offline' AND last_seen < NOW() - $1`

	result, err := r.pool.Exec(ctx, query, staleThreshold)
	if err != nil {
		r.logger.Error("Failed to cleanup stale presence", zap.Error(err))
		return fmt.Errorf("failed to cleanup stale presence: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected > 0 {
		r.logger.Info("Cleaned up stale presence", zap.Int64("users_marked_offline", rowsAffected))
	}

	return nil
}

// GetUserLastSeen retrieves when a user was last seen
func (r *PostgreSQLRepository) GetUserLastSeen(ctx context.Context, userID string) (*time.Time, error) {
	query := `SELECT last_seen FROM user_presence WHERE user_id = $1`

	var lastSeen time.Time
	err := r.pool.QueryRow(ctx, query, userID).Scan(&lastSeen)
	if err != nil {
		r.logger.Error("Failed to get user last seen", zap.Error(err))
		return nil, fmt.Errorf("failed to get user last seen: %w", err)
	}

	return &lastSeen, nil
}

// UpdateUserLastSeen updates only the last seen timestamp for a user
func (r *PostgreSQLRepository) UpdateUserLastSeen(ctx context.Context, userID string) error {
	query := `
		UPDATE user_presence 
		SET last_seen = NOW(), updated_at = NOW()
		WHERE user_id = $1`

	_, err := r.pool.Exec(ctx, query, userID)
	if err != nil {
		r.logger.Error("Failed to update user last seen", zap.Error(err))
		return fmt.Errorf("failed to update user last seen: %w", err)
	}

	return nil
}

// GetPresenceStats retrieves presence statistics
func (r *PostgreSQLRepository) GetPresenceStats(ctx context.Context) (map[string]int32, error) {
	query := `
		SELECT status, COUNT(*) as count
		FROM user_presence
		WHERE last_seen > NOW() - INTERVAL '24 hours'
		GROUP BY status`

	rows, err := r.pool.Query(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get presence stats", zap.Error(err))
		return nil, fmt.Errorf("failed to get presence stats: %w", err)
	}
	defer rows.Close()

	stats := make(map[string]int32)
	for rows.Next() {
		var status string
		var count int32
		err := rows.Scan(&status, &count)
		if err != nil {
			r.logger.Error("Failed to scan presence stats", zap.Error(err))
			continue
		}
		stats[status] = count
	}

	return stats, nil
}
