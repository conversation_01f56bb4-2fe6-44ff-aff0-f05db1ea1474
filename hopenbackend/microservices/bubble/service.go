package bubble

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	bubblev1 "hopenbackend/protos/gen/bubble"
	commonv1 "hopenbackend/protos/gen/common"
)

// NotificationService interface for sending notifications
type NotificationService interface {
	SendBubbleInviteNotification(ctx context.Context, recipientID, senderID, bubbleID string) error
	SendBubbleJoinRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID string) error
	SendBubbleJoinRequestAcceptedNotification(ctx context.Context, requesterID, acceptedByUserID, bubbleID string) error
	SendBubbleJoinRequestRejectedNotification(ctx context.Context, requesterID, rejectedByUserID, bubbleID string) error
	SendBubbleMemberJoinedNotification(ctx context.Context, memberID, newMemberID, bubbleID string) error
	SendBubbleProposeRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, proposedUserID string) error
	SendBubbleKickoutRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, targetUserID string) error
	SendBubbleVotekickInitiatedNotification(ctx context.Context, memberID, initiatorID, targetUserID, bubbleID string) error
	SendBubbleVotekickPassedNotification(ctx context.Context, memberID, targetUserID, bubbleID string) error
	SendBubbleStartRequestNotification(ctx context.Context, recipientID, requesterID string) error
	SendBubbleExpiryNotification(ctx context.Context, userID, bubbleID string, daysLeft int) error
}

// Service represents the bubble gRPC service
type Service struct {
	bubblev1.UnimplementedBubbleServiceServer
	db                  *database.PostgreSQLClient
	repository          *PostgreSQLRepository
	oryClient           *ory.Client
	natsConn            *nats.Conn
	js                  nats.JetStreamContext
	logger              *zap.Logger
	notificationService NotificationService
}

// Dependencies holds the dependencies for the bubble service
type Dependencies struct {
	DB                  *database.PostgreSQLClient
	OryClient           *ory.Client
	NATSConn            *nats.Conn
	Logger              *zap.Logger
	NotificationService NotificationService
}

// NewBubbleServiceServer creates a new gRPC server for the bubble service
func NewBubbleServiceServer(service *Service) bubblev1.BubbleServiceServer {
	return service
}

// NewService creates a new bubble service instance
func NewService(deps *Dependencies) *Service {
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	service := &Service{
		db:                  deps.DB,
		repository:          repository,
		oryClient:           deps.OryClient,
		natsConn:            deps.NATSConn,
		logger:              deps.Logger,
		notificationService: deps.NotificationService,
	}

	// Initialize JetStream if NATS is available
	if deps.NATSConn != nil {
		if err := service.initializeJetStream(); err != nil {
			deps.Logger.Error("Failed to initialize JetStream", zap.Error(err))
		}
	}

	// Subscribe to bubble expiration events
	service.subscribeToBubbleExpirationEvents()

	return service
}

// Event types for NATS
type BubbleMemberJoinedEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	UserID    string `json:"user_id"`
	MemberID  string `json:"member_id"`
	Timestamp int64  `json:"timestamp"`
}

type BubbleMemberLeftEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	UserID    string `json:"user_id"`
	Reason    string `json:"reason"`
	Timestamp int64  `json:"timestamp"`
}

type BubbleExpireEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	Timestamp int64  `json:"timestamp"`
}

type BubbleExpiredEvent struct {
	BubbleID  string   `json:"bubble_id"`
	Members   []string `json:"members"`
	ExpiredAt int64    `json:"expired_at"`
}

type BubbleExpiryReminderEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	DaysLeft  int    `json:"days_left"`
	Timestamp int64  `json:"timestamp"`
}

type NotificationEvent struct {
	Type      string                 `json:"type"`
	Title     string                 `json:"title"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data"`
	Timestamp int64                  `json:"timestamp"`
}

// gRPC Service Implementation

// CreateBubble implements the gRPC CreateBubble method
func (s *Service) CreateBubble(ctx context.Context, req *bubblev1.CreateBubbleRequest) (*bubblev1.CreateBubbleResponse, error) {
	// Validate request
	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble name is required")
	}
	if req.CreatorId == "" {
		return nil, status.Error(codes.InvalidArgument, "creator_id is required")
	}

	// Create bubble
	bubble := &Bubble{
		ID:              uuid.New().String(),
		Name:            req.Name,
		MaxMembers:      5, // Default max members
		CurrentMembers:  1,
		LifecycleStatus: "active",
		CreatedBy:       req.CreatorId,
		ExpiresAt:       time.Now().AddDate(0, 0, 90),
	}

	if req.ExpiresAt != nil {
		bubble.ExpiresAt = req.ExpiresAt.AsTime()
	}

	err := s.db.WithTransaction(ctx, func(tx pgx.Tx) error {
		if err := s.repository.CreateBubble(ctx, tx, bubble); err != nil {
			return err
		}

		// Add creator as first member
		member := &BubbleMember{
			BubbleID: bubble.ID,
			UserID:   req.CreatorId,
			Status:   "active",
		}

		if err := s.repository.AddBubbleMember(ctx, tx, member); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		s.logger.Error("Failed to create bubble", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to create bubble")
	}

	// Get the created bubble for response
	createdBubble, err := s.repository.GetBubbleByID(ctx, bubble.ID)
	if err != nil {
		s.logger.Error("Failed to get created bubble", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to get created bubble")
	}

	// Publish member joined event for creator
	if err := s.publishMemberJoinedEvent(createdBubble.ID, req.CreatorId, ""); err != nil {
		s.logger.Error("Failed to publish member joined event for creator", zap.Error(err))
	}

	// Schedule expiry event using NATS Cron
	if err := s.scheduleExpiryEvent(createdBubble.ID, createdBubble.ExpiresAt); err != nil {
		s.logger.Error("Failed to schedule bubble expiry", zap.Error(err))
	}

	// Schedule expiry reminder notifications
	if err := s.scheduleExpiryReminders(createdBubble.ID, createdBubble.ExpiresAt); err != nil {
		s.logger.Error("Failed to schedule bubble expiry reminders", zap.Error(err))
	}

	// Convert to protobuf response
	protoBubble := &commonv1.Bubble{
		Id:          createdBubble.ID,
		Name:        createdBubble.Name,
		CreatorId:   createdBubble.CreatedBy,
		ExpiresAt:   timestamppb.New(createdBubble.ExpiresAt),
		CreatedAt:   timestamppb.New(createdBubble.CreatedAt),
		UpdatedAt:   timestamppb.New(createdBubble.UpdatedAt),
		Status:      commonv1.BubbleStatus_BUBBLE_STATUS_ACTIVE,
		MemberCount: int32(createdBubble.CurrentMembers),
	}

	return &bubblev1.CreateBubbleResponse{
		Bubble: protoBubble,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Bubble created successfully",
		},
	}, nil
}

// GetBubble implements the gRPC GetBubble method
func (s *Service) GetBubble(ctx context.Context, req *bubblev1.GetBubbleRequest) (*bubblev1.GetBubbleResponse, error) {
	if req.BubbleId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id is required")
	}

	bubble, err := s.repository.GetBubbleByID(ctx, req.BubbleId)
	if err != nil {
		s.logger.Error("Failed to get bubble", zap.Error(err))
		return nil, status.Error(codes.NotFound, "bubble not found")
	}

	protoBubble := &commonv1.Bubble{
		Id:          bubble.ID,
		Name:        bubble.Name,
		CreatorId:   bubble.CreatedBy,
		ExpiresAt:   timestamppb.New(bubble.ExpiresAt),
		CreatedAt:   timestamppb.New(bubble.CreatedAt),
		UpdatedAt:   timestamppb.New(bubble.UpdatedAt),
		Status:      commonv1.BubbleStatus_BUBBLE_STATUS_ACTIVE,
		MemberCount: int32(bubble.CurrentMembers),
	}

	return &bubblev1.GetBubbleResponse{
		Bubble: protoBubble,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Bubble retrieved successfully",
		},
	}, nil
}

// UpdateBubble implements the gRPC UpdateBubble method
func (s *Service) UpdateBubble(ctx context.Context, req *bubblev1.UpdateBubbleRequest) (*bubblev1.UpdateBubbleResponse, error) {
	if req.BubbleId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id is required")
	}

	var name *string
	if req.Name != nil {
		name = req.Name
	}

	err := s.repository.UpdateBubble(ctx, req.BubbleId, name)
	if err != nil {
		s.logger.Error("Failed to update bubble", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to update bubble")
	}

	// Get updated bubble
	bubble, err := s.repository.GetBubbleByID(ctx, req.BubbleId)
	if err != nil {
		s.logger.Error("Failed to get updated bubble", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to get updated bubble")
	}

	protoBubble := &commonv1.Bubble{
		Id:          bubble.ID,
		Name:        bubble.Name,
		CreatorId:   bubble.CreatedBy,
		ExpiresAt:   timestamppb.New(bubble.ExpiresAt),
		CreatedAt:   timestamppb.New(bubble.CreatedAt),
		UpdatedAt:   timestamppb.New(bubble.UpdatedAt),
		Status:      commonv1.BubbleStatus_BUBBLE_STATUS_ACTIVE,
		MemberCount: int32(bubble.CurrentMembers),
	}

	return &bubblev1.UpdateBubbleResponse{
		Bubble: protoBubble,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Bubble updated successfully",
		},
	}, nil
}

// DeleteBubble implements the gRPC DeleteBubble method
func (s *Service) DeleteBubble(ctx context.Context, req *bubblev1.DeleteBubbleRequest) (*bubblev1.DeleteBubbleResponse, error) {
	if req.BubbleId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id is required")
	}

	err := s.repository.DeleteBubble(ctx, req.BubbleId)
	if err != nil {
		s.logger.Error("Failed to delete bubble", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to delete bubble")
	}

	return &bubblev1.DeleteBubbleResponse{
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Bubble deleted successfully",
		},
	}, nil
}

// JoinBubble implements the gRPC JoinBubble method
func (s *Service) JoinBubble(ctx context.Context, req *bubblev1.JoinBubbleRequest) (*bubblev1.JoinBubbleResponse, error) {
	if req.BubbleId == "" || req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id and user_id are required")
	}

	// Check if bubble exists and is active
	bubble, err := s.repository.GetBubbleByID(ctx, req.BubbleId)
	if err != nil {
		s.logger.Error("Bubble not found", zap.Error(err))
		return nil, status.Error(codes.NotFound, "bubble not found or not active")
	}

	if bubble.LifecycleStatus != "active" {
		return nil, status.Error(codes.FailedPrecondition, "bubble is not active")
	}

	// Check if user is already a member
	var existingMember bool
	err = s.db.Pool.QueryRow(ctx, `
		SELECT EXISTS(SELECT 1 FROM bubble_members WHERE bubble_id = $1 AND user_id = $2 AND status = 'active')`,
		req.BubbleId, req.UserId).Scan(&existingMember)

	if err != nil {
		s.logger.Error("Failed to check existing membership", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to check membership")
	}

	if existingMember {
		return nil, status.Error(codes.AlreadyExists, "user is already a member of this bubble")
	}

	// Check if bubble has capacity
	if bubble.CurrentMembers >= bubble.MaxMembers {
		return nil, status.Error(codes.ResourceExhausted, "bubble is at full capacity")
	}

	// Create join request
	requestID := uuid.New().String()
	_, err = s.db.Pool.Exec(ctx, `
		INSERT INTO bubble_requests (id, bubble_id, requester_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
		requestID, req.BubbleId, req.UserId, "join", "pending", "",
		time.Now(), time.Now(), time.Now().Add(7*24*time.Hour))

	if err != nil {
		s.logger.Error("Failed to create join request", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to create join request")
	}

	// Send notification event
	notificationEvent := NotificationEvent{
		Type:      "bubble_join_request_received",
		Title:     "Bubble Join Request",
		Message:   "Someone wants to join your bubble!",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"requester_id":        req.UserId,
			"bubble_id":           req.BubbleId,
			"request_id":          requestID,
			"broadcast_to_bubble": req.BubbleId,
		},
	}

	if err := s.publishNotificationEvent(notificationEvent); err != nil {
		s.logger.Error("Failed to publish join request notification event", zap.Error(err))
	}

	return &bubblev1.JoinBubbleResponse{
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Join request created successfully",
		},
	}, nil
}

// LeaveBubble implements the gRPC LeaveBubble method
func (s *Service) LeaveBubble(ctx context.Context, req *bubblev1.LeaveBubbleRequest) (*bubblev1.LeaveBubbleResponse, error) {
	if req.BubbleId == "" || req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id and user_id are required")
	}

	err := s.db.WithTransaction(ctx, func(tx pgx.Tx) error {
		// Get current bubble to check member count
		bubble, err := s.repository.GetBubbleByID(ctx, req.BubbleId)
		if err != nil {
			s.logger.Error("Failed to get bubble for leaving", zap.String("bubble_id", req.BubbleId), zap.Error(err))
			return fmt.Errorf("failed to find bubble: %w", err)
		}

		// Update membership status to removed
		if err := s.repository.RemoveBubbleMember(ctx, req.BubbleId, req.UserId); err != nil {
			return fmt.Errorf("failed to remove member: %w", err)
		}

		// Decrement the bubble's member count
		if err := s.repository.UpdateBubbleMemberCount(ctx, tx, req.BubbleId, bubble.CurrentMembers-1); err != nil {
			return fmt.Errorf("failed to update member count: %w", err)
		}

		// Check if bubble should be dissolved (less than 2 active members)
		if bubble.CurrentMembers-1 < 2 {
			if err := s.db.CheckAndDissolveBubble(ctx, req.BubbleId); err != nil {
				s.logger.Error("Failed to check bubble for dissolution", zap.Error(err))
				// Decide if this should rollback the transaction. For now, we log and continue.
			}
		}

		return nil
	})

	if err != nil {
		s.logger.Error("Failed to leave bubble", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to leave bubble")
	}

	// Publish member left event after successful transaction
	if err := s.publishMemberLeftEvent(req.BubbleId, req.UserId, "left"); err != nil {
		s.logger.Error("Failed to publish member left event", zap.Error(err))
	}

	return &bubblev1.LeaveBubbleResponse{
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Left bubble successfully",
		},
	}, nil
}

// KickMember implements the gRPC KickMember method
func (s *Service) KickMember(ctx context.Context, req *bubblev1.KickMemberRequest) (*bubblev1.KickMemberResponse, error) {
	if req.BubbleId == "" || req.UserId == "" || req.MemberId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id, user_id, and member_id are required")
	}

	// Check if bubble has only 2 active members - kickout should be disabled
	var activeCount int
	err := s.db.Pool.QueryRow(ctx, `
		SELECT COUNT(*)
		FROM bubble_members
		WHERE bubble_id = $1 AND status = 'active'`, req.BubbleId).Scan(&activeCount)

	if err != nil {
		s.logger.Error("Failed to count active members", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to check bubble status")
	}

	if activeCount <= 2 {
		return nil, status.Error(codes.FailedPrecondition, "kickout is not allowed in bubbles with 2 or fewer members")
	}

	// Create kickout request
	requestID := uuid.New().String()
	_, err = s.db.Pool.Exec(ctx, `
		INSERT INTO bubble_requests (id, bubble_id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
		requestID, req.BubbleId, req.UserId, req.MemberId, "kickout", "pending", "",
		time.Now(), time.Now(), time.Now().Add(7*24*time.Hour))

	if err != nil {
		s.logger.Error("Failed to create kickout request", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to create kickout request")
	}

	// Send votekick initiated notification event
	notificationEvent := NotificationEvent{
		Type:      "bubble_votekick_initiated_notification",
		Title:     "Votekick Initiated",
		Message:   "A votekick has been initiated in your bubble!",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"initiator_id":               req.UserId,
			"target_user_id":             req.MemberId,
			"bubble_id":                  req.BubbleId,
			"request_id":                 requestID,
			"broadcast_to_bubble_except": []string{req.UserId, req.MemberId},
		},
	}

	if err := s.publishNotificationEvent(notificationEvent); err != nil {
		s.logger.Error("Failed to publish votekick initiated notification event", zap.Error(err))
	}

	return &bubblev1.KickMemberResponse{
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Kickout request created successfully",
		},
	}, nil
}

// GetBubbleMembers implements the gRPC GetBubbleMembers method
func (s *Service) GetBubbleMembers(ctx context.Context, req *bubblev1.GetBubbleMembersRequest) (*bubblev1.GetBubbleMembersResponse, error) {
	if req.BubbleId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id is required")
	}

	members, err := s.repository.GetBubbleMembers(ctx, req.BubbleId)
	if err != nil {
		s.logger.Error("Failed to get bubble members", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to get members")
	}

	protoMembers := make([]*bubblev1.BubbleMember, 0, len(members))
	for _, member := range members {
		protoMember := &bubblev1.BubbleMember{
			BubbleId: req.BubbleId, // Use the request bubble ID
			UserId:   member.UserID,
			IsActive: member.Status == "active",
		}
		if member.JoinedAt != nil {
			protoMember.JoinedAt = timestamppb.New(*member.JoinedAt)
		}
		// LeftAt field doesn't exist in BubbleMemberInfo, skip it
		protoMembers = append(protoMembers, protoMember)
	}

	return &bubblev1.GetBubbleMembersResponse{
		Members: protoMembers,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Bubble members retrieved successfully",
		},
	}, nil
}

// GetUserBubbles implements the gRPC GetUserBubbles method
func (s *Service) GetUserBubbles(ctx context.Context, req *bubblev1.GetUserBubblesRequest) (*bubblev1.GetUserBubblesResponse, error) {
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	bubbles, err := s.repository.GetBubblesByUser(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user bubbles", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to get bubbles")
	}

	protoBubbles := make([]*commonv1.Bubble, 0, len(bubbles))
	for _, bubble := range bubbles {
		// Convert status string to BubbleStatus enum
		var status commonv1.BubbleStatus
		switch bubble.LifecycleStatus {
		case "active":
			status = commonv1.BubbleStatus_BUBBLE_STATUS_ACTIVE
		case "expired":
			status = commonv1.BubbleStatus_BUBBLE_STATUS_EXPIRED
		case "deleted":
			status = commonv1.BubbleStatus_BUBBLE_STATUS_DELETED
		default:
			status = commonv1.BubbleStatus_BUBBLE_STATUS_UNSPECIFIED
		}

		protoBubble := &commonv1.Bubble{
			Id:          bubble.ID,
			Name:        bubble.Name,
			CreatorId:   bubble.CreatedBy,
			Status:      status,
			MemberCount: int32(bubble.CurrentMembers),
			ExpiresAt:   timestamppb.New(bubble.ExpiresAt),
			CreatedAt:   timestamppb.New(bubble.CreatedAt),
			UpdatedAt:   timestamppb.New(bubble.UpdatedAt),
		}
		protoBubbles = append(protoBubbles, protoBubble)
	}

	return &bubblev1.GetUserBubblesResponse{
		Bubbles: protoBubbles,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "User bubbles retrieved successfully",
		},
	}, nil
}

// GetBubbleRequests implements the gRPC GetBubbleRequests method
func (s *Service) GetBubbleRequests(ctx context.Context, req *bubblev1.GetBubbleRequestsRequest) (*bubblev1.GetBubbleRequestsResponse, error) {
	if req.BubbleId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id is required")
	}

	requests, err := s.repository.GetBubbleRequests(ctx, req.BubbleId)
	if err != nil {
		s.logger.Error("Failed to get bubble requests", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to get requests")
	}

	protoRequests := make([]*bubblev1.BubbleRequest, 0, len(requests))
	for _, request := range requests {
		// Convert request type string to enum
		var requestType bubblev1.BubbleRequestType
		switch request.RequestType {
		case "join":
			requestType = bubblev1.BubbleRequestType_BUBBLE_REQUEST_TYPE_JOIN
		case "invite":
			requestType = bubblev1.BubbleRequestType_BUBBLE_REQUEST_TYPE_INVITE
		default:
			requestType = bubblev1.BubbleRequestType_BUBBLE_REQUEST_TYPE_UNSPECIFIED
		}

		// Convert status string to enum
		var requestStatus bubblev1.BubbleRequestStatus
		switch request.Status {
		case "pending":
			requestStatus = bubblev1.BubbleRequestStatus_BUBBLE_REQUEST_STATUS_PENDING
		case "accepted":
			requestStatus = bubblev1.BubbleRequestStatus_BUBBLE_REQUEST_STATUS_ACCEPTED
		case "rejected":
			requestStatus = bubblev1.BubbleRequestStatus_BUBBLE_REQUEST_STATUS_REJECTED
		case "expired", "cancelled":
			requestStatus = bubblev1.BubbleRequestStatus_BUBBLE_REQUEST_STATUS_CANCELLED
		default:
			requestStatus = bubblev1.BubbleRequestStatus_BUBBLE_REQUEST_STATUS_UNSPECIFIED
		}

		protoRequest := &bubblev1.BubbleRequest{
			Id:          request.ID,
			BubbleId:    request.BubbleID,
			UserId:      request.RecipientID, // Use RecipientID as UserId
			RequesterId: request.RequesterID,
			Type:        requestType,
			Status:      requestStatus,
			CreatedAt:   timestamppb.New(request.CreatedAt),
			UpdatedAt:   timestamppb.New(request.UpdatedAt),
		}
		protoRequests = append(protoRequests, protoRequest)
	}

	return &bubblev1.GetBubbleRequestsResponse{
		Requests: protoRequests,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Bubble requests retrieved successfully",
		},
	}, nil
}

// AcceptBubbleRequest implements the gRPC AcceptBubbleRequest method
func (s *Service) AcceptBubbleRequest(ctx context.Context, req *bubblev1.AcceptBubbleRequestRequest) (*bubblev1.AcceptBubbleRequestResponse, error) {
	if req.RequestId == "" || req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "request_id and user_id are required")
	}

	// Begin transaction
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		s.logger.Error("Failed to begin transaction", zap.Error(err))
		return nil, status.Error(codes.Internal, "internal server error")
	}
	defer tx.Rollback(ctx)

	// Get request details
	var bubbleID, requesterID string
	err = tx.QueryRow(ctx,
		"SELECT bubble_id, requester_id FROM bubble_requests WHERE id = $1 AND status = 'pending'",
		req.RequestId).Scan(&bubbleID, &requesterID)

	if err != nil {
		return nil, status.Error(codes.NotFound, "request not found or not pending")
	}

	// Get bubble info for expiry extension logic
	var bubble Bubble
	err = tx.QueryRow(ctx, `
		SELECT id, name, capacity, current_members, status, expires_at, created_at
		FROM bubbles WHERE id = $1 AND status = 'active'`, bubbleID).Scan(
		&bubble.ID, &bubble.Name, &bubble.MaxMembers, &bubble.CurrentMembers,
		&bubble.LifecycleStatus, &bubble.ExpiresAt, &bubble.CreatedAt)

	if err != nil {
		s.logger.Error("Failed to get bubble info", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to get bubble info")
	}

	// Add user to bubble
	memberID := uuid.New().String()
	_, err = tx.Exec(ctx, `
		INSERT INTO bubble_members (id, bubble_id, user_id, status, joined_at)
		VALUES ($1, $2, $3, 'active', $4)`,
		memberID, bubbleID, requesterID, time.Now())

	if err != nil {
		s.logger.Error("Failed to add bubble member", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to accept request")
	}

	// Update member count and calculate expiry extension
	newMemberCount := bubble.CurrentMembers + 1

	// Expiry extension logic: +30 days per new member (capped at 90 days from creation)
	maxExpiryDate := bubble.CreatedAt.AddDate(0, 0, 90) // 90 days from creation
	currentExpiry := bubble.ExpiresAt
	extendedExpiry := currentExpiry.AddDate(0, 0, 30) // +30 days

	// Use the earlier of extended expiry or max expiry
	var newExpiryDate time.Time
	if extendedExpiry.Before(maxExpiryDate) {
		newExpiryDate = extendedExpiry
	} else {
		newExpiryDate = maxExpiryDate
	}

	// Update bubble with new member count and expiry
	_, err = tx.Exec(ctx, `
		UPDATE bubbles SET current_members = $1, expires_at = $2, updated_at = $3
		WHERE id = $4`,
		newMemberCount, newExpiryDate, time.Now(), bubbleID)

	if err != nil {
		s.logger.Error("Failed to update bubble", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to update bubble")
	}

	// Update request status
	_, err = tx.Exec(ctx,
		"UPDATE bubble_requests SET status = 'accepted', updated_at = $1 WHERE id = $2",
		time.Now(), req.RequestId)

	if err != nil {
		s.logger.Error("Failed to update request status", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to accept request")
	}

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		s.logger.Error("Failed to commit transaction", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to accept request")
	}

	// Publish member joined event after successful transaction
	if err := s.publishMemberJoinedEvent(bubbleID, requesterID, memberID); err != nil {
		s.logger.Error("Failed to publish member joined event", zap.Error(err))
	}

	return &bubblev1.AcceptBubbleRequestResponse{
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Request accepted successfully",
		},
	}, nil
}

// RejectBubbleRequest implements the gRPC RejectBubbleRequest method
func (s *Service) RejectBubbleRequest(ctx context.Context, req *bubblev1.RejectBubbleRequestRequest) (*bubblev1.RejectBubbleRequestResponse, error) {
	if req.RequestId == "" || req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "request_id and user_id are required")
	}

	// Update request status
	_, err := s.db.Pool.Exec(ctx,
		"UPDATE bubble_requests SET status = 'rejected', updated_at = $1 WHERE id = $2",
		time.Now(), req.RequestId)

	if err != nil {
		s.logger.Error("Failed to reject request", zap.Error(err))
		return nil, status.Error(codes.Internal, "failed to reject request")
	}

	return &bubblev1.RejectBubbleRequestResponse{
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Request rejected successfully",
		},
	}, nil
}

// SendBubbleInvite implements the gRPC SendBubbleInvite method
func (s *Service) SendBubbleInvite(ctx context.Context, req *bubblev1.SendBubbleInviteRequest) (*bubblev1.SendBubbleInviteResponse, error) {
	if req.BubbleId == "" || req.InviterId == "" || len(req.InviteeIds) == 0 {
		return nil, status.Error(codes.InvalidArgument, "bubble_id, inviter_id, and invitee_ids are required")
	}

	requestIDs := make([]string, 0, len(req.InviteeIds))

	for _, inviteeID := range req.InviteeIds {
		// Create invite request
		requestID := uuid.New().String()
		_, err := s.db.Pool.Exec(ctx, `
			INSERT INTO bubble_requests (id, bubble_id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
			requestID, req.BubbleId, req.InviterId, inviteeID, "invite", "pending", req.Message,
			time.Now(), time.Now(), time.Now().Add(7*24*time.Hour))

		if err != nil {
			s.logger.Error("Failed to create invite request", zap.Error(err))
			continue
		}

		requestIDs = append(requestIDs, requestID)

		// Send notification event
		notificationEvent := NotificationEvent{
			Type:      "bubble_invite_notification",
			Title:     "Bubble Invitation",
			Message:   "You've been invited to join a bubble!",
			Timestamp: time.Now().Unix(),
			Data: map[string]interface{}{
				"recipient_id": inviteeID,
				"sender_id":    req.InviterId,
				"bubble_id":    req.BubbleId,
				"request_id":   requestID,
				"message":      req.Message,
			},
		}

		if err := s.publishNotificationEvent(notificationEvent); err != nil {
			s.logger.Error("Failed to publish invite notification event", zap.Error(err))
		}
	}

	return &bubblev1.SendBubbleInviteResponse{
		RequestIds: requestIDs,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Invite requests created successfully",
		},
	}, nil
}

// GetBubbleAnalytics implements the gRPC GetBubbleAnalytics method
func (s *Service) GetBubbleAnalytics(ctx context.Context, req *bubblev1.GetBubbleAnalyticsRequest) (*bubblev1.GetBubbleAnalyticsResponse, error) {
	if req.BubbleId == "" {
		return nil, status.Error(codes.InvalidArgument, "bubble_id is required")
	}

	// Get bubble info
	bubble, err := s.repository.GetBubbleByID(ctx, req.BubbleId)
	if err != nil {
		s.logger.Error("Failed to get bubble for analytics", zap.Error(err))
		return nil, status.Error(codes.NotFound, "bubble not found")
	}

	// Get member activity (simplified for now)
	memberActivity := make(map[string]int32)
	members, err := s.repository.GetBubbleMembers(ctx, req.BubbleId)
	if err == nil {
		for _, member := range members {
			if member.Status == "active" {
				memberActivity[member.UserID] = 1 // Simplified activity metric
			}
		}
	}

	analytics := &bubblev1.BubbleAnalytics{
		BubbleId:       req.BubbleId,
		TotalMembers:   int32(bubble.MaxMembers),
		ActiveMembers:  int32(bubble.CurrentMembers),
		TotalMessages:  s.getBubbleMessageCount(ctx, req.BubbleId),
		TotalCalls:     s.getBubbleCallCount(ctx, req.BubbleId),
		CreatedAt:      timestamppb.New(bubble.CreatedAt),
		LastActivityAt: timestamppb.New(bubble.UpdatedAt),
		MemberActivity: memberActivity,
	}

	return &bubblev1.GetBubbleAnalyticsResponse{
		Analytics: analytics,
		ApiResponse: &commonv1.ApiResponse{
			Success: true,
			Message: "Bubble analytics retrieved successfully",
		},
	}, nil
}

// Helper methods for NATS event publishing

func (s *Service) publishMemberJoinedEvent(bubbleID, userID, memberID string) error {
	if s.js == nil {
		s.logger.Warn("JetStream not available, skipping event publishing")
		return nil
	}

	event := BubbleMemberJoinedEvent{
		EventType: "bubble.member_joined",
		BubbleID:  bubbleID,
		UserID:    userID,
		MemberID:  memberID,
		Timestamp: time.Now().Unix(),
	}

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal member joined event: %w", err)
	}

	_, err = s.js.Publish("events.bubble.member_joined", data)
	if err != nil {
		return fmt.Errorf("failed to publish member joined event: %w", err)
	}

	s.logger.Info("Published member joined event",
		zap.String("bubble_id", bubbleID),
		zap.String("user_id", userID),
		zap.String("member_id", memberID))

	return nil
}

func (s *Service) publishMemberLeftEvent(bubbleID, userID, reason string) error {
	if s.js == nil {
		s.logger.Warn("JetStream not available, skipping event publishing")
		return nil
	}

	event := BubbleMemberLeftEvent{
		EventType: "bubble.member_left",
		BubbleID:  bubbleID,
		UserID:    userID,
		Reason:    reason,
		Timestamp: time.Now().Unix(),
	}

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal member left event: %w", err)
	}

	_, err = s.js.Publish("events.bubble.member_left", data)
	if err != nil {
		return fmt.Errorf("failed to publish member left event: %w", err)
	}

	s.logger.Info("Published member left event",
		zap.String("bubble_id", bubbleID),
		zap.String("user_id", userID),
		zap.String("reason", reason))

	return nil
}

func (s *Service) publishNotificationEvent(event NotificationEvent) error {
	if s.natsConn == nil {
		s.logger.Warn("NATS connection not available, skipping notification event publishing")
		return nil
	}

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal notification event: %w", err)
	}

	err = s.natsConn.Publish("events.notifications", data)
	if err != nil {
		return fmt.Errorf("failed to publish notification event: %w", err)
	}

	s.logger.Info("Published notification event",
		zap.String("type", event.Type),
		zap.String("title", event.Title),
		zap.Any("data", event.Data))

	return nil
}

func (s *Service) scheduleExpiryEvent(bubbleID string, expiresAt time.Time) error {
	if s.js == nil {
		return nil // JetStream not configured
	}

	event := BubbleExpireEvent{
		EventType: "bubble.expire",
		BubbleID:  bubbleID,
		Timestamp: expiresAt.Unix(),
	}
	data, err := json.Marshal(event)
	if err != nil {
		return err
	}

	hdr := nats.Header{}
	hdr.Set("Nats-Not-Before", expiresAt.UTC().Format(time.RFC3339))

	msg := &nats.Msg{
		Subject: "events.bubble.expire",
		Header:  hdr,
		Data:    data,
	}

	_, err = s.js.PublishMsg(msg)
	return err
}

func (s *Service) scheduleExpiryReminders(bubbleID string, expiresAt time.Time) error {
	if s.js == nil {
		return nil // JetStream not configured
	}

	// Schedule reminders for 60, 30, 20, 10, 7, 3 days, and 24 hours before expiry
	reminderDays := []int{60, 30, 20, 10, 7, 3, 1}

	for _, days := range reminderDays {
		reminderTime := expiresAt.AddDate(0, 0, -days)

		// Only schedule if reminder time is in the future
		if reminderTime.After(time.Now()) {
			event := BubbleExpiryReminderEvent{
				EventType: "bubble.expiry.reminder",
				BubbleID:  bubbleID,
				DaysLeft:  days,
				Timestamp: reminderTime.Unix(),
			}

			data, err := json.Marshal(event)
			if err != nil {
				s.logger.Error("Failed to marshal expiry reminder event", zap.Error(err))
				continue
			}

			hdr := nats.Header{}
			hdr.Set("Nats-Not-Before", reminderTime.UTC().Format(time.RFC3339))

			msg := &nats.Msg{
				Subject: "events.bubble.expiry.reminder",
				Header:  hdr,
				Data:    data,
			}

			if _, err := s.js.PublishMsg(msg); err != nil {
				s.logger.Error("Failed to schedule expiry reminder",
					zap.String("bubble_id", bubbleID),
					zap.Int("days_left", days),
					zap.Error(err))
			} else {
				s.logger.Info("Scheduled expiry reminder",
					zap.String("bubble_id", bubbleID),
					zap.Int("days_left", days),
					zap.Time("reminder_time", reminderTime))
			}
		}
	}

	return nil
}

func (s *Service) initializeJetStream() error {
	if s.natsConn == nil {
		s.logger.Warn("NATS connection not available, skipping JetStream initialization")
		return nil
	}

	js, err := s.natsConn.JetStream()
	if err != nil {
		return fmt.Errorf("failed to create JetStream context: %w", err)
	}
	s.js = js

	// Create or update the bubble events stream
	streamConfig := &nats.StreamConfig{
		Name:        "BUBBLE_EVENTS",
		Description: "Stream for bubble membership events",
		Subjects:    []string{"events.bubble.>"},
		Retention:   nats.WorkQueuePolicy,
		MaxAge:      24 * time.Hour, // Keep events for 24 hours
		Storage:     nats.FileStorage,
		Replicas:    1,
	}

	_, err = js.AddStream(streamConfig)
	if err != nil && err != nats.ErrStreamNameAlreadyInUse {
		return fmt.Errorf("failed to create bubble events stream: %w", err)
	}

	s.logger.Info("JetStream initialized successfully for bubble service")
	return nil
}

// subscribeToBubbleExpirationEvents subscribes to bubble expiration events via NATS
func (s *Service) subscribeToBubbleExpirationEvents() {
	if s.natsConn == nil {
		s.logger.Warn("NATS connection not available, skipping bubble expiration event subscription")
		return
	}

	// Subscribe to bubble expiration events
	_, err := s.natsConn.Subscribe("events.bubble.expire", func(msg *nats.Msg) {
		s.handleBubbleExpirationEvent(msg.Data)
	})

	if err != nil {
		s.logger.Error("Failed to subscribe to bubble expiration events", zap.Error(err))
		return
	}

	s.logger.Info("Successfully subscribed to bubble expiration events")
}

// handleBubbleExpirationEvent processes bubble expiration events
func (s *Service) handleBubbleExpirationEvent(data []byte) {
	var event BubbleExpireEvent
	if err := json.Unmarshal(data, &event); err != nil {
		s.logger.Error("Failed to unmarshal bubble expiration event", zap.Error(err))
		return
	}

	s.logger.Info("Processing bubble expiration event", zap.String("bubble_id", event.BubbleID))

	ctx := context.Background()

	// Start transaction to expire the bubble
	err := s.db.WithTransaction(ctx, func(tx pgx.Tx) error {
		// Get bubble members before expiring
		members, err := s.repository.GetBubbleMembers(ctx, event.BubbleID)
		if err != nil {
			s.logger.Error("Failed to get bubble members for expiration", zap.Error(err))
			return err
		}

		// Update bubble status to expired
		_, err = tx.Exec(ctx, `
			UPDATE bubbles
			SET lifecycle_status = 'expired', updated_at = $1
			WHERE id = $2 AND lifecycle_status = 'active'`,
			time.Now(), event.BubbleID)

		if err != nil {
			s.logger.Error("Failed to expire bubble", zap.Error(err))
			return err
		}

		// Update all active memberships to expired
		_, err = tx.Exec(ctx, `
			UPDATE bubble_members
			SET status = 'expired', updated_at = $1
			WHERE bubble_id = $2 AND status = 'active'`,
			time.Now(), event.BubbleID)

		if err != nil {
			s.logger.Error("Failed to expire bubble memberships", zap.Error(err))
			return err
		}

		// Prepare member IDs for the expired event
		var memberIDs []string
		for _, member := range members {
			memberIDs = append(memberIDs, member.UserID)
		}

		// Publish bubble expired event for friendship service
		expiredEvent := BubbleExpiredEvent{
			BubbleID:  event.BubbleID,
			Members:   memberIDs,
			ExpiredAt: time.Now().Unix(),
		}

		expiredEventData, err := json.Marshal(expiredEvent)
		if err != nil {
			s.logger.Error("Failed to marshal bubble expired event", zap.Error(err))
			return err
		}

		// Publish to NATS for friendship service to consume
		err = s.natsConn.Publish("events.bubble.expired", expiredEventData)
		if err != nil {
			s.logger.Error("Failed to publish bubble expired event", zap.Error(err))
			return err
		}

		s.logger.Info("Successfully expired bubble and published event",
			zap.String("bubble_id", event.BubbleID),
			zap.Strings("member_ids", memberIDs))

		return nil
	})

	if err != nil {
		s.logger.Error("Failed to process bubble expiration",
			zap.String("bubble_id", event.BubbleID),
			zap.Error(err))
	}
}

// getBubbleMessageCount gets the total number of messages in a bubble
func (s *Service) getBubbleMessageCount(ctx context.Context, bubbleID string) int32 {
	// Query messages table for count
	query := `SELECT COUNT(*) FROM messages WHERE bubble_id = $1 AND is_deleted = false`

	var count int32
	err := s.db.Pool.QueryRow(ctx, query, bubbleID).Scan(&count)
	if err != nil {
		s.logger.Error("Failed to get bubble message count",
			zap.String("bubble_id", bubbleID),
			zap.Error(err))
		return 0
	}

	return count
}

// getBubbleCallCount gets the total number of calls in a bubble
func (s *Service) getBubbleCallCount(ctx context.Context, bubbleID string) int32 {
	// Query calls table for count
	query := `SELECT COUNT(*) FROM calls WHERE bubble_id = $1`

	var count int32
	err := s.db.Pool.QueryRow(ctx, query, bubbleID).Scan(&count)
	if err != nil {
		s.logger.Error("Failed to get bubble call count",
			zap.String("bubble_id", bubbleID),
			zap.Error(err))
		return 0
	}

	return count
}
