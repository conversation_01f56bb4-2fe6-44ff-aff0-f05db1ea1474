package bubble

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/database"
)

// PostgreSQLRepository implements bubble operations using PostgreSQL
type PostgreSQLRepository struct {
	db     *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for bubbles
func NewPostgreSQLRepository(db *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		db:     db,
		logger: logger,
	}
}

// Bubble represents a bubble entity in PostgreSQL
type Bubble struct {
	ID              string    `json:"id"`
	Name            string    `json:"name"`
	MaxMembers      int       `json:"capacity"`
	CurrentMembers  int       `json:"current_members"`
	LifecycleStatus string    `json:"status"`
	CreatedBy       string    `json:"creator_id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	ExpiresAt       time.Time `json:"expires_at"`
}

// BubbleMember represents a bubble member in PostgreSQL
type BubbleMember struct {
	ID        string     `json:"id"`
	BubbleID  string     `json:"bubble_id"`
	UserID    string     `json:"user_id"`
	Status    string     `json:"status"`
	JoinedAt  *time.Time `json:"joined_at,omitempty"`
	LeftAt    *time.Time `json:"left_at,omitempty"`
	InvitedBy *string    `json:"invited_by,omitempty"`
}

// BubbleRequest represents a bubble request in PostgreSQL
type BubbleRequest struct {
	ID          string    `json:"id"`
	BubbleID    string    `json:"bubble_id"`
	RequesterID string    `json:"requester_id"`
	RecipientID string    `json:"recipient_id"`
	RequestType string    `json:"request_type"`
	Status      string    `json:"status"`
	Message     string    `json:"message"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	Username    string    `json:"username,omitempty"`
	FirstName   string    `json:"first_name,omitempty"`
	LastName    string    `json:"last_name,omitempty"`
}

// BubbleMemberInfo represents detailed bubble member information for API responses.
type BubbleMemberInfo struct {
	ID        string     `json:"id"`
	UserID    string     `json:"user_id"`
	Status    string     `json:"status"`
	JoinedAt  *time.Time `json:"joined_at"`
	Username  string     `json:"username"`
	FirstName *string    `json:"first_name"`
	LastName  *string    `json:"last_name"`
}

// GetBubblesByUser retrieves bubbles for a user
func (r *PostgreSQLRepository) GetBubblesByUser(ctx context.Context, userID string) ([]*Bubble, error) {
	query := `
		SELECT b.id, b.name, b.capacity, b.current_members,
		       b.status, b.creator_id, b.created_at, b.updated_at, b.expires_at
		FROM bubbles b
		JOIN bubble_members bm ON b.id = bm.bubble_id
		WHERE bm.user_id = $1 AND bm.status = 'active'
		ORDER BY b.created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query bubbles: %w", err)
	}
	defer rows.Close()

	var bubbles []*Bubble
	for rows.Next() {
		var bubble Bubble
		err := rows.Scan(
			&bubble.ID, &bubble.Name, &bubble.MaxMembers,
			&bubble.CurrentMembers, &bubble.LifecycleStatus, &bubble.CreatedBy,
			&bubble.CreatedAt, &bubble.UpdatedAt, &bubble.ExpiresAt)
		if err != nil {
			r.logger.Error("Failed to scan bubble", zap.Error(err))
			continue
		}
		bubbles = append(bubbles, &bubble)
	}

	return bubbles, nil
}

// CreateBubble creates a new bubble
func (r *PostgreSQLRepository) CreateBubble(ctx context.Context, q database.Querier, bubble *Bubble) error {
	bubble.ID = uuid.New().String()
	bubble.CreatedAt = time.Now()
	bubble.UpdatedAt = time.Now()

	query := `
		INSERT INTO bubbles (id, name, capacity, current_members,
		                   status, creator_id, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`

	_, err := q.Exec(ctx, query,
		bubble.ID, bubble.Name, bubble.MaxMembers, bubble.CurrentMembers,
		bubble.LifecycleStatus, bubble.CreatedBy, bubble.CreatedAt, bubble.UpdatedAt, bubble.ExpiresAt)

	if err != nil {
		r.logger.Error("Failed to create bubble", zap.Error(err))
		return database.HandlePgxError(err, "bubbles")
	}

	return nil
}

// CreateBubbleWithPool creates a new bubble using the pool (for backward compatibility)
func (r *PostgreSQLRepository) CreateBubbleWithPool(ctx context.Context, bubble *Bubble) error {
	return r.CreateBubble(ctx, r.db, bubble)
}

// GetBubbleByID retrieves a bubble by ID
func (r *PostgreSQLRepository) GetBubbleByID(ctx context.Context, bubbleID string) (*Bubble, error) {
	query := `
		SELECT id, name, capacity, current_members,
		       status, creator_id, created_at, updated_at, expires_at
		FROM bubbles WHERE id = $1`

	var bubble Bubble
	err := r.db.QueryRow(ctx, query, bubbleID).Scan(
		&bubble.ID, &bubble.Name, &bubble.MaxMembers,
		&bubble.CurrentMembers, &bubble.LifecycleStatus, &bubble.CreatedBy,
		&bubble.CreatedAt, &bubble.UpdatedAt, &bubble.ExpiresAt)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("bubble not found")
		}
		return nil, fmt.Errorf("failed to get bubble: %w", err)
	}

	return &bubble, nil
}

// UpdateBubble updates a bubble
func (r *PostgreSQLRepository) UpdateBubble(ctx context.Context, bubbleID string, name *string) error {
	query := `
		UPDATE bubbles SET name = COALESCE($1, name),
		                 updated_at = $2 WHERE id = $3`

	result, err := r.db.Exec(ctx, query, name, time.Now(), bubbleID)
	if err != nil {
		return fmt.Errorf("failed to update bubble: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("bubble not found")
	}

	return nil
}

// DeleteBubble soft deletes a bubble
func (r *PostgreSQLRepository) DeleteBubble(ctx context.Context, bubbleID string) error {
	query := `UPDATE bubbles SET status = 'archived', updated_at = $1 WHERE id = $2`

	result, err := r.db.Exec(ctx, query, time.Now(), bubbleID)
	if err != nil {
		return fmt.Errorf("failed to delete bubble: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("bubble not found")
	}

	return nil
}

// GetBubbleMembers retrieves active members for a bubble
func (r *PostgreSQLRepository) GetBubbleMembers(ctx context.Context, bubbleID string) ([]*BubbleMemberInfo, error) {
	query := `
		SELECT bm.id, bm.user_id, bm.status, bm.joined_at,
		       u.username, u.first_name, u.last_name
		FROM bubble_members bm
		JOIN users u ON bm.user_id = u.id
		WHERE bm.bubble_id = $1 AND bm.status = 'active'
		ORDER BY bm.joined_at ASC`

	rows, err := r.db.Query(ctx, query, bubbleID)
	if err != nil {
		return nil, fmt.Errorf("failed to query bubble members: %w", err)
	}
	defer rows.Close()

	var members []*BubbleMemberInfo
	for rows.Next() {
		var member BubbleMemberInfo
		err := rows.Scan(&member.ID, &member.UserID, &member.Status, &member.JoinedAt,
			&member.Username, &member.FirstName, &member.LastName)
		if err != nil {
			r.logger.Error("Failed to scan bubble member", zap.Error(err))
			continue
		}
		members = append(members, &member)
	}

	return members, nil
}

// AddBubbleMember adds a user to a bubble
func (r *PostgreSQLRepository) AddBubbleMember(ctx context.Context, q database.Querier, member *BubbleMember) error {
	member.ID = uuid.New().String()
	now := time.Now()
	member.JoinedAt = &now

	query := `
		INSERT INTO bubble_members (id, bubble_id, user_id, status, joined_at, invited_by)
		VALUES ($1, $2, $3, $4, $5, $6)`

	_, err := q.Exec(ctx, query,
		member.ID, member.BubbleID, member.UserID, member.Status, member.JoinedAt, member.InvitedBy)

	if err != nil {
		r.logger.Error("Failed to add bubble member", zap.Error(err))
		return database.HandlePgxError(err, "bubble_members")
	}

	return nil
}

// AddBubbleMemberWithPool adds a user to a bubble using the pool (for backward compatibility)
func (r *PostgreSQLRepository) AddBubbleMemberWithPool(ctx context.Context, member *BubbleMember) error {
	return r.AddBubbleMember(ctx, r.db, member)
}

// RemoveBubbleMember removes a user from a bubble
func (r *PostgreSQLRepository) RemoveBubbleMember(ctx context.Context, bubbleID, userID string) error {
	query := `
		UPDATE bubble_members SET status = 'removed', left_at = $1, updated_at = $2
		WHERE bubble_id = $3 AND user_id = $4 AND status = 'active'`

	result, err := r.db.Exec(ctx, query, time.Now(), time.Now(), bubbleID, userID)
	if err != nil {
		return fmt.Errorf("failed to remove bubble member: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("bubble member not found")
	}

	return nil
}

// UpdateBubbleMemberCount updates the member count for a bubble
func (r *PostgreSQLRepository) UpdateBubbleMemberCount(ctx context.Context, q database.Querier, bubbleID string, memberCount int) error {
	query := `UPDATE bubbles SET current_members = $1, updated_at = $2 WHERE id = $3`

	_, err := q.Exec(ctx, query, memberCount, time.Now(), bubbleID)
	if err != nil {
		return fmt.Errorf("failed to update bubble member count: %w", err)
	}

	return nil
}

// UpdateBubbleExpiry updates the expiry date for a bubble
func (r *PostgreSQLRepository) UpdateBubbleExpiry(ctx context.Context, q database.Querier, bubbleID string, expiresAt time.Time) error {
	query := `UPDATE bubbles SET expires_at = $1, updated_at = $2 WHERE id = $3`

	_, err := q.Exec(ctx, query, expiresAt, time.Now(), bubbleID)
	if err != nil {
		return fmt.Errorf("failed to update bubble expiry: %w", err)
	}

	return nil
}

// CheckBubbleMembership checks if a user is a member of a bubble
func (r *PostgreSQLRepository) CheckBubbleMembership(ctx context.Context, bubbleID, userID string) (bool, error) {
	query := `
		SELECT EXISTS(SELECT 1 FROM bubble_members 
		WHERE bubble_id = $1 AND user_id = $2 AND status = 'active')`

	var exists bool
	err := r.db.QueryRow(ctx, query, bubbleID, userID).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check bubble membership: %w", err)
	}

	return exists, nil
}

// GetBubbleActiveMemberCount gets the count of active members in a bubble
func (r *PostgreSQLRepository) GetBubbleActiveMemberCount(ctx context.Context, bubbleID string) (int, error) {
	query := `SELECT COUNT(*) FROM bubble_members WHERE bubble_id = $1 AND status = 'active'`

	var count int
	err := r.db.QueryRow(ctx, query, bubbleID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get bubble member count: %w", err)
	}

	return count, nil
}

// CreateBubbleRequest creates a new bubble request
func (r *PostgreSQLRepository) CreateBubbleRequest(ctx context.Context, request *BubbleRequest) error {
	request.ID = uuid.New().String()
	request.CreatedAt = time.Now()
	request.UpdatedAt = time.Now()

	query := `
		INSERT INTO bubble_requests (id, bubble_id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`

	_, err := r.db.Exec(ctx, query,
		request.ID, request.BubbleID, request.RequesterID, request.RecipientID,
		request.RequestType, request.Status, request.Message,
		request.CreatedAt, request.UpdatedAt, request.ExpiresAt)

	if err != nil {
		r.logger.Error("Failed to create bubble request", zap.Error(err))
		return database.HandlePgxError(err, "bubble_requests")
	}

	return nil
}

// GetBubbleRequests retrieves pending requests for a bubble
func (r *PostgreSQLRepository) GetBubbleRequests(ctx context.Context, bubbleID string) ([]*BubbleRequest, error) {
	query := `
		SELECT br.id, br.requester_id, br.request_type, br.status, br.message, br.created_at,
		       u.username, u.first_name, u.last_name
		FROM bubble_requests br
		JOIN users u ON br.requester_id = u.id
		WHERE br.bubble_id = $1 AND br.status = 'pending'
		ORDER BY br.created_at ASC`

	rows, err := r.db.Query(ctx, query, bubbleID)
	if err != nil {
		return nil, fmt.Errorf("failed to query bubble requests: %w", err)
	}
	defer rows.Close()

	var requests []*BubbleRequest
	for rows.Next() {
		var req BubbleRequest
		var firstName, lastName *string
		err := rows.Scan(&req.ID, &req.RequesterID, &req.RequestType, &req.Status,
			&req.Message, &req.CreatedAt, &req.Username, &firstName, &lastName)
		if err != nil {
			r.logger.Error("Failed to scan bubble request", zap.Error(err))
			continue
		}

		if firstName != nil {
			req.FirstName = *firstName
		}
		if lastName != nil {
			req.LastName = *lastName
		}

		requests = append(requests, &req)
	}

	return requests, nil
}

// GetBubbleRequestByID retrieves a bubble request by ID
func (r *PostgreSQLRepository) GetBubbleRequestByID(ctx context.Context, q database.Querier, requestID string) (*BubbleRequest, error) {
	query := `
		SELECT id, bubble_id, requester_id, target_user_id, request_type, status, message, created_at, updated_at, expires_at
		FROM bubble_requests WHERE id = $1 AND status = 'pending'`

	var request BubbleRequest
	err := q.QueryRow(ctx, query, requestID).Scan(
		&request.ID, &request.BubbleID, &request.RequesterID, &request.RecipientID,
		&request.RequestType, &request.Status, &request.Message,
		&request.CreatedAt, &request.UpdatedAt, &request.ExpiresAt)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("request not found or not pending")
		}
		return nil, fmt.Errorf("failed to get bubble request: %w", err)
	}

	return &request, nil
}

// GetBubbleRequestByIDWithPool retrieves a bubble request by ID using the pool (for backward compatibility)
func (r *PostgreSQLRepository) GetBubbleRequestByIDWithPool(ctx context.Context, requestID string) (*BubbleRequest, error) {
	return r.GetBubbleRequestByID(ctx, r.db, requestID)
}

// UpdateBubbleRequestStatus updates the status of a bubble request
func (r *PostgreSQLRepository) UpdateBubbleRequestStatus(ctx context.Context, q database.Querier, requestID, status string) error {
	query := `UPDATE bubble_requests SET status = $1, updated_at = $2 WHERE id = $3`

	result, err := q.Exec(ctx, query, status, time.Now(), requestID)
	if err != nil {
		return fmt.Errorf("failed to update bubble request status: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("bubble request not found")
	}

	return nil
}

// UpdateBubbleRequestStatusWithPool updates the status of a bubble request using the pool (for backward compatibility)
func (r *PostgreSQLRepository) UpdateBubbleRequestStatusWithPool(ctx context.Context, requestID, status string) error {
	return r.UpdateBubbleRequestStatus(ctx, r.db, requestID, status)
}

// GetUserPendingRequests retrieves all pending requests for a user
func (r *PostgreSQLRepository) GetUserPendingRequests(ctx context.Context, userID string) ([]*BubbleRequest, error) {
	query := `
		SELECT br.id, br.bubble_id, br.requester_id, br.target_user_id, br.request_type,
		       br.status, br.message, br.created_at, br.updated_at, br.expires_at,
		       u.username, u.first_name, u.last_name,
		       b.name as bubble_name
		FROM bubble_requests br
		JOIN users u ON br.requester_id = u.id
		LEFT JOIN bubbles b ON br.bubble_id = b.id
		WHERE (br.target_user_id = $1 OR
		       (br.request_type = 'join' AND br.bubble_id IN (
		           SELECT bubble_id FROM bubble_members WHERE user_id = $1 AND status = 'active'
		       )))
		AND br.status = 'pending'
		AND br.expires_at > NOW()
		ORDER BY br.created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user pending requests: %w", err)
	}
	defer rows.Close()

	var requests []*BubbleRequest
	for rows.Next() {
		var req BubbleRequest
		var firstName, lastName *string
		var bubbleName *string
		var targetUserID *string
		err := rows.Scan(&req.ID, &req.BubbleID, &req.RequesterID, &targetUserID, &req.RequestType,
			&req.Status, &req.Message, &req.CreatedAt, &req.UpdatedAt, &req.ExpiresAt,
			&req.Username, &firstName, &lastName, &bubbleName)
		if err != nil {
			r.logger.Error("Failed to scan bubble request", zap.Error(err))
			continue
		}

		if firstName != nil {
			req.FirstName = *firstName
		}
		if lastName != nil {
			req.LastName = *lastName
		}
		if targetUserID != nil {
			req.RecipientID = *targetUserID
		}

		requests = append(requests, &req)
	}

	return requests, nil
}

// CheckExistingStartRequest checks if a pending start request already exists between users
func (r *PostgreSQLRepository) CheckExistingStartRequest(ctx context.Context, q database.Querier, requesterID, targetUserID string) (string, error) {
	query := `
		SELECT id FROM bubble_requests
		WHERE requester_id = $1 AND target_user_id = $2 AND request_type = 'start' AND status = 'pending'
		ORDER BY created_at DESC LIMIT 1`

	var requestID string
	err := q.QueryRow(ctx, query, requesterID, targetUserID).Scan(&requestID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return "", nil // No existing request
		}
		return "", fmt.Errorf("failed to check existing start request: %w", err)
	}

	return requestID, nil
}

// GetBubbleMembersForNotification retrieves active member IDs for notifications
func (r *PostgreSQLRepository) GetBubbleMembersForNotification(ctx context.Context, bubbleID string) ([]string, error) {
	query := `SELECT user_id FROM bubble_members WHERE bubble_id = $1 AND status = 'active'`

	rows, err := r.db.Query(ctx, query, bubbleID)
	if err != nil {
		return nil, fmt.Errorf("failed to query bubble members for notification: %w", err)
	}
	defer rows.Close()

	var memberIDs []string
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			continue
		}
		memberIDs = append(memberIDs, memberID)
	}

	return memberIDs, nil
}

// GetBubbleMembersForNotificationExcluding retrieves active member IDs excluding specific users
func (r *PostgreSQLRepository) GetBubbleMembersForNotificationExcluding(ctx context.Context, bubbleID string, excludeUserIDs ...string) ([]string, error) {
	query := `SELECT user_id FROM bubble_members WHERE bubble_id = $1 AND status = 'active'`

	// Add exclusion conditions
	if len(excludeUserIDs) > 0 {
		query += " AND user_id NOT IN ("
		for i := range excludeUserIDs {
			if i > 0 {
				query += ","
			}
			query += fmt.Sprintf("$%d", i+2)
		}
		query += ")"
	}

	args := []interface{}{bubbleID}
	for _, userID := range excludeUserIDs {
		args = append(args, userID)
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query bubble members for notification: %w", err)
	}
	defer rows.Close()

	var memberIDs []string
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			continue
		}
		memberIDs = append(memberIDs, memberID)
	}

	return memberIDs, nil
}

// ExpireBubble marks a bubble as expired
func (r *PostgreSQLRepository) ExpireBubble(ctx context.Context, bubbleID string) error {
	query := `UPDATE bubbles SET status='expired', updated_at=$1 WHERE id=$2 AND status='active'`

	result, err := r.db.Exec(ctx, query, time.Now(), bubbleID)
	if err != nil {
		return fmt.Errorf("failed to expire bubble: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("bubble not found or already expired")
	}

	return nil
}

// CheckBubbleExists checks if a bubble exists and is active
func (r *PostgreSQLRepository) CheckBubbleExists(ctx context.Context, bubbleID string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM bubbles WHERE id = $1 AND status = 'active')`

	var exists bool
	err := r.db.QueryRow(ctx, query, bubbleID).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check bubble existence: %w", err)
	}

	return exists, nil
}

// GetBubbleName retrieves the name of a bubble
func (r *PostgreSQLRepository) GetBubbleName(ctx context.Context, bubbleID string) (string, error) {
	query := `SELECT name FROM bubbles WHERE id = $1`

	var name string
	err := r.db.QueryRow(ctx, query, bubbleID).Scan(&name)
	if err != nil {
		if err == pgx.ErrNoRows {
			return "Unknown Bubble", nil // Default fallback
		}
		return "", fmt.Errorf("failed to get bubble name: %w", err)
	}

	return name, nil
}
