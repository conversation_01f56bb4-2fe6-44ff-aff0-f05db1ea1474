package media

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"github.com/minio/minio-go/v7"
	"go.uber.org/zap"
)

// MinIORepository handles basic MinIO storage operations
// This repository only contains low-level MinIO interactions.
// All business logic and orchestration should be in the service layer.
type MinIORepository struct {
	client     *minio.Client
	bucketName string
	logger     *zap.Logger
}

// NewMinIORepository creates a new MinIO repository
func NewMinIORepository(client *minio.Client, bucketName string, logger *zap.Logger) *MinIORepository {
	return &MinIORepository{
		client:     client,
		bucketName: bucketName,
		logger:     logger,
	}
}

// PutObject uploads an object to MinIO
func (r *MinIORepository) PutObject(ctx context.Context, objectName string, data []byte, contentType string) (*minio.UploadInfo, error) {
	reader := bytes.NewReader(data)
	uploadInfo, err := r.client.PutObject(ctx, r.bucketName, objectName, reader, int64(len(data)), minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		r.logger.Error("Failed to upload object to MinIO", zap.Error(err))
		return nil, fmt.Errorf("failed to upload object: %w", err)
	}

	r.logger.Debug("Object uploaded successfully", zap.String("object_name", objectName))
	return &uploadInfo, nil
}

// PresignedGetObject generates a presigned URL for object access
func (r *MinIORepository) PresignedGetObject(ctx context.Context, objectName string, expiry time.Duration) (string, error) {
	presignedURL, err := r.client.PresignedGetObject(ctx, r.bucketName, objectName, expiry, nil)
	if err != nil {
		r.logger.Error("Failed to generate presigned URL", zap.Error(err))
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	r.logger.Debug("Generated presigned URL", zap.String("object_name", objectName))
	return presignedURL.String(), nil
}

// RemoveObject deletes an object from MinIO
func (r *MinIORepository) RemoveObject(ctx context.Context, objectName string) error {
	err := r.client.RemoveObject(ctx, r.bucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		r.logger.Error("Failed to delete object from MinIO", zap.Error(err))
		return fmt.Errorf("failed to delete object: %w", err)
	}

	r.logger.Debug("Object deleted successfully", zap.String("object_name", objectName))
	return nil
}

// GetFileInfo gets file information from MinIO
func (r *MinIORepository) GetFileInfo(ctx context.Context, fileID string) (*minio.ObjectInfo, error) {
	objInfo, err := r.client.StatObject(ctx, r.bucketName, fileID, minio.StatObjectOptions{})
	if err != nil {
		r.logger.Error("Failed to get file info from MinIO", zap.Error(err))
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	return &objInfo, nil
}

// GetBucketStats gets storage statistics for the bucket
func (r *MinIORepository) GetBucketStats(ctx context.Context) (int64, int64, error) {
	var totalFiles int64
	var totalSize int64

	// List all objects in bucket
	objectCh := r.client.ListObjects(ctx, r.bucketName, minio.ListObjectsOptions{
		Recursive: true,
	})

	for object := range objectCh {
		if object.Err != nil {
			r.logger.Error("Error listing objects", zap.Error(object.Err))
			continue
		}
		totalFiles++
		totalSize += object.Size
	}

	r.logger.Debug("Retrieved bucket stats",
		zap.Int64("total_files", totalFiles),
		zap.Int64("total_size", totalSize))

	return totalFiles, totalSize, nil
}

// EnsureBucketExists ensures the bucket exists, creating it if necessary
func (r *MinIORepository) EnsureBucketExists(ctx context.Context) error {
	exists, err := r.client.BucketExists(ctx, r.bucketName)
	if err != nil {
		r.logger.Error("Failed to check if bucket exists", zap.Error(err))
		return fmt.Errorf("failed to check bucket existence: %w", err)
	}

	if !exists {
		err = r.client.MakeBucket(ctx, r.bucketName, minio.MakeBucketOptions{})
		if err != nil {
			r.logger.Error("Failed to create bucket", zap.Error(err))
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		r.logger.Info("Bucket created successfully", zap.String("bucket", r.bucketName))
	}

	return nil
}

// GetDetailedBucketStats gets detailed storage statistics including file type breakdown
func (r *MinIORepository) GetDetailedBucketStats(ctx context.Context) (map[string]int64, map[string]int64, error) {
	filesByType := make(map[string]int64)
	sizeByType := make(map[string]int64)

	// List all objects in bucket
	objectCh := r.client.ListObjects(ctx, r.bucketName, minio.ListObjectsOptions{
		Recursive: true,
	})

	for object := range objectCh {
		if object.Err != nil {
			r.logger.Error("Error listing objects", zap.Error(object.Err))
			continue
		}

		// Determine file type from object name/path
		fileType := r.determineFileTypeFromPath(object.Key)

		filesByType[fileType]++
		sizeByType[fileType] += object.Size
	}

	r.logger.Debug("Retrieved detailed bucket stats",
		zap.Any("files_by_type", filesByType),
		zap.Any("size_by_type", sizeByType))

	return filesByType, sizeByType, nil
}

// determineFileTypeFromPath determines file type from object path/name
func (r *MinIORepository) determineFileTypeFromPath(objectKey string) string {
	// Extract file extension
	lastDot := -1
	for i := len(objectKey) - 1; i >= 0; i-- {
		if objectKey[i] == '.' {
			lastDot = i
			break
		}
		if objectKey[i] == '/' {
			break
		}
	}

	if lastDot == -1 {
		return "other"
	}

	ext := objectKey[lastDot+1:]

	// Categorize by extension
	switch ext {
	case "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg":
		return "image"
	case "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv":
		return "video"
	case "mp3", "wav", "flac", "aac", "ogg", "m4a":
		return "audio"
	case "pdf", "doc", "docx", "txt", "rtf", "odt":
		return "document"
	default:
		return "other"
	}
}
