package media

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// MediaFile represents a media file in the database
type MediaFile struct {
	ID           string    `json:"id"`
	UserID       string    `json:"user_id"`
	FileName     string    `json:"file_name"`
	FileSize     int64     `json:"file_size"`
	MimeType     string    `json:"mime_type"`
	FileType     string    `json:"file_type"` // image, video, audio, document
	StoragePath  string    `json:"storage_path"`
	URL          string    `json:"url"`
	ThumbnailURL *string   `json:"thumbnail_url"`
	Width        *int32    `json:"width"`
	Height       *int32    `json:"height"`
	Duration     *int32    `json:"duration"` // for video/audio in seconds
	IsPublic     bool      `json:"is_public"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// MediaStats represents media statistics for a user
type MediaStats struct {
	UserID         string           `json:"user_id"`
	TotalFiles     int32            `json:"total_files"`
	TotalSizeBytes int64            `json:"total_size_bytes"`
	FilesByType    map[string]int32 `json:"files_by_type"`
	LastUploadAt   *time.Time       `json:"last_upload_at"`
}

// PostgreSQLRepository handles media database operations
type PostgreSQLRepository struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for media
func NewPostgreSQLRepository(pool *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		pool:   pool,
		logger: logger,
	}
}

// CreateMediaFile creates a new media file record
func (r *PostgreSQLRepository) CreateMediaFile(ctx context.Context, tx pgx.Tx, file *MediaFile) error {
	query := `
		INSERT INTO media_files (
			id, user_id, file_name, file_size, mime_type, file_type,
			storage_path, url, thumbnail_url, width, height, duration,
			is_public, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`

	if tx != nil {
		_, err := tx.Exec(ctx, query,
			file.ID, file.UserID, file.FileName, file.FileSize, file.MimeType,
			file.FileType, file.StoragePath, file.URL, file.ThumbnailURL,
			file.Width, file.Height, file.Duration, file.IsPublic,
			file.CreatedAt, file.UpdatedAt)

		if err != nil {
			r.logger.Error("Failed to create media file", zap.Error(err))
			return fmt.Errorf("failed to create media file: %w", err)
		}
	} else {
		_, err := r.pool.Exec(ctx, query,
			file.ID, file.UserID, file.FileName, file.FileSize, file.MimeType,
			file.FileType, file.StoragePath, file.URL, file.ThumbnailURL,
			file.Width, file.Height, file.Duration, file.IsPublic,
			file.CreatedAt, file.UpdatedAt)

		if err != nil {
			r.logger.Error("Failed to create media file", zap.Error(err))
			return fmt.Errorf("failed to create media file: %w", err)
		}
	}

	return nil
}

// GetMediaFileByID retrieves a media file by ID
func (r *PostgreSQLRepository) GetMediaFileByID(ctx context.Context, fileID string) (*MediaFile, error) {
	query := `
		SELECT id, user_id, file_name, file_size, mime_type, file_type,
			   storage_path, url, thumbnail_url, width, height, duration,
			   is_public, created_at, updated_at
		FROM media_files
		WHERE id = $1`

	var file MediaFile
	err := r.pool.QueryRow(ctx, query, fileID).Scan(
		&file.ID, &file.UserID, &file.FileName, &file.FileSize,
		&file.MimeType, &file.FileType, &file.StoragePath, &file.URL,
		&file.ThumbnailURL, &file.Width, &file.Height, &file.Duration,
		&file.IsPublic, &file.CreatedAt, &file.UpdatedAt)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("media file not found")
		}
		r.logger.Error("Failed to get media file", zap.Error(err))
		return nil, fmt.Errorf("failed to get media file: %w", err)
	}

	return &file, nil
}

// GetUserMediaFiles retrieves media files for a user with pagination
func (r *PostgreSQLRepository) GetUserMediaFiles(ctx context.Context, userID string, limit, offset int) ([]*MediaFile, error) {
	query := `
		SELECT id, user_id, file_name, file_size, mime_type, file_type,
			   storage_path, url, thumbnail_url, width, height, duration,
			   is_public, created_at, updated_at
		FROM media_files
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.pool.Query(ctx, query, userID, limit, offset)
	if err != nil {
		r.logger.Error("Failed to get user media files", zap.Error(err))
		return nil, fmt.Errorf("failed to get user media files: %w", err)
	}
	defer rows.Close()

	var files []*MediaFile
	for rows.Next() {
		var file MediaFile
		err := rows.Scan(
			&file.ID, &file.UserID, &file.FileName, &file.FileSize,
			&file.MimeType, &file.FileType, &file.StoragePath, &file.URL,
			&file.ThumbnailURL, &file.Width, &file.Height, &file.Duration,
			&file.IsPublic, &file.CreatedAt, &file.UpdatedAt)
		if err != nil {
			r.logger.Error("Failed to scan media file", zap.Error(err))
			continue
		}
		files = append(files, &file)
	}

	return files, nil
}

// DeleteMediaFile deletes a media file record
func (r *PostgreSQLRepository) DeleteMediaFile(ctx context.Context, tx pgx.Tx, fileID string) error {
	query := `DELETE FROM media_files WHERE id = $1`

	var result pgconn.CommandTag
	var err error

	if tx != nil {
		result, err = tx.Exec(ctx, query, fileID)
	} else {
		result, err = r.pool.Exec(ctx, query, fileID)
	}

	if err != nil {
		r.logger.Error("Failed to delete media file", zap.Error(err))
		return fmt.Errorf("failed to delete media file: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("media file not found")
	}

	return nil
}

// GetMediaStats retrieves media statistics for a user
func (r *PostgreSQLRepository) GetMediaStats(ctx context.Context, userID string) (*MediaStats, error) {
	// Get total files and size
	totalQuery := `
		SELECT COUNT(*), COALESCE(SUM(file_size), 0), MAX(created_at)
		FROM media_files
		WHERE user_id = $1`

	var totalFiles int32
	var totalSize int64
	var lastUpload sql.NullTime

	err := r.pool.QueryRow(ctx, totalQuery, userID).Scan(&totalFiles, &totalSize, &lastUpload)
	if err != nil {
		r.logger.Error("Failed to get media stats totals", zap.Error(err))
		return nil, fmt.Errorf("failed to get media stats: %w", err)
	}

	// Get files by type
	typeQuery := `
		SELECT file_type, COUNT(*)
		FROM media_files
		WHERE user_id = $1
		GROUP BY file_type`

	rows, err := r.pool.Query(ctx, typeQuery, userID)
	if err != nil {
		r.logger.Error("Failed to get media stats by type", zap.Error(err))
		return nil, fmt.Errorf("failed to get media stats by type: %w", err)
	}
	defer rows.Close()

	filesByType := make(map[string]int32)
	for rows.Next() {
		var fileType string
		var count int32
		if err := rows.Scan(&fileType, &count); err != nil {
			r.logger.Error("Failed to scan file type stats", zap.Error(err))
			continue
		}
		filesByType[fileType] = count
	}

	stats := &MediaStats{
		UserID:         userID,
		TotalFiles:     totalFiles,
		TotalSizeBytes: totalSize,
		FilesByType:    filesByType,
	}

	if lastUpload.Valid {
		stats.LastUploadAt = &lastUpload.Time
	}

	return stats, nil
}

// UpdateProfilePicture updates user's profile picture URL
func (r *PostgreSQLRepository) UpdateProfilePicture(ctx context.Context, tx pgx.Tx, userID, avatarURL string) error {
	query := `UPDATE users SET avatar_url = $1, updated_at = $2 WHERE id = $3`

	if tx != nil {
		_, err := tx.Exec(ctx, query, avatarURL, time.Now(), userID)
		if err != nil {
			r.logger.Error("Failed to update profile picture", zap.Error(err))
			return fmt.Errorf("failed to update profile picture: %w", err)
		}
	} else {
		_, err := r.pool.Exec(ctx, query, avatarURL, time.Now(), userID)
		if err != nil {
			r.logger.Error("Failed to update profile picture", zap.Error(err))
			return fmt.Errorf("failed to update profile picture: %w", err)
		}
	}

	return nil
}

// UpdateThumbnailURL updates the thumbnail URL for a media file
func (r *PostgreSQLRepository) UpdateThumbnailURL(ctx context.Context, fileID, thumbnailURL string) error {
	query := `UPDATE media_files SET thumbnail_url = $1, updated_at = $2 WHERE id = $3`

	_, err := r.pool.Exec(ctx, query, thumbnailURL, time.Now(), fileID)
	if err != nil {
		r.logger.Error("Failed to update thumbnail URL", zap.Error(err))
		return fmt.Errorf("failed to update thumbnail URL: %w", err)
	}

	return nil
}
