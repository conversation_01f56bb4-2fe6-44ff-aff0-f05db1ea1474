package media

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"mime"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/nats-io/nats.go"
	"github.com/nfnt/resize"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	"hopenbackend/pkg/retry"
	commonv1 "hopenbackend/protos/gen/common"
	mediav1 "hopenbackend/protos/gen/media"
)

// Service handles media operations using gRPC and implements MediaServiceServer
type Service struct {
	mediav1.UnimplementedMediaServiceServer
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	repository  *PostgreSQLRepository
	minioClient *minio.Client
	minioRepo   *MinIORepository
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
	retriers    *retry.ServiceRetriers
}

// MediaEvent represents a media-related event for NATS
type MediaEvent struct {
	UserID    string    `json:"user_id"`
	FileID    string    `json:"file_id"`
	Type      string    `json:"type"`
	Action    string    `json:"action"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the media service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
}

// NewService creates a new media service instance
func NewService(deps *Dependencies) *Service {
	// Initialize repository
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	// Initialize MinIO client
	var minioClient *minio.Client
	if deps.Config.MinIO.Endpoint != "" {
		var err error
		minioClient, err = minio.New(deps.Config.MinIO.Endpoint, &minio.Options{
			Creds:  credentials.NewStaticV4(deps.Config.MinIO.AccessKey, deps.Config.MinIO.SecretKey, ""),
			Secure: deps.Config.MinIO.UseSSL,
		})
		if err != nil {
			deps.Logger.Error("Failed to initialize MinIO client", zap.Error(err))
		} else {
			deps.Logger.Info("MinIO client initialized successfully")
		}
	}

	// Initialize MinIO repository if MinIO client is available
	var minioRepo *MinIORepository
	if minioClient != nil {
		minioRepo = NewMinIORepository(minioClient, deps.Config.MinIO.BucketName, deps.Logger)

		// Ensure bucket exists
		ctx := context.Background()
		if err := minioRepo.EnsureBucketExists(ctx); err != nil {
			deps.Logger.Error("Failed to ensure MinIO bucket exists", zap.Error(err))
		}
	}

	// Initialize retry mechanisms for external services
	retriers := retry.NewServiceRetriers(deps.Logger)

	service := &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		repository:  repository,
		minioClient: minioClient,
		minioRepo:   minioRepo,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
		retriers:    retriers,
	}

	return service
}

// NewMediaServiceServer creates a new gRPC server for the media service
func NewMediaServiceServer(service *Service) mediav1.MediaServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishMediaEvent publishes media events via NATS
func (s *Service) publishMediaEvent(userID, fileID, mediaType, action string, data any) {
	if s.natsConn == nil {
		return
	}

	event := MediaEvent{
		UserID:    userID,
		FileID:    fileID,
		Type:      mediaType,
		Action:    action,
		Data:      data,
		Timestamp: time.Now(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal media event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("media.%s", action)
	if err := s.natsConn.Publish(subject, eventData); err != nil {
		s.logger.Error("Failed to publish media event", zap.Error(err))
	}
}

// calculateSizeByType calculates storage size by file type
func (s *Service) calculateSizeByType(ctx context.Context, mediaStats *MediaStats) map[string]int64 {
	sizeByType := make(map[string]int64)

	// If we have MinIO repository, get actual size statistics
	if s.minioRepo != nil {
		// Try to get detailed statistics from MinIO first
		minioFilesByType, minioSizeByType, err := s.minioRepo.GetDetailedBucketStats(ctx)
		if err != nil {
			s.logger.Error("Failed to get detailed MinIO bucket stats", zap.Error(err))

			// Fallback to basic bucket stats
			totalFiles, totalSize, err := s.minioRepo.GetBucketStats(ctx)
			if err != nil {
				s.logger.Error("Failed to get basic MinIO bucket stats", zap.Error(err))
				// Use database statistics as final fallback
				totalFiles = int64(mediaStats.TotalFiles)
				totalSize = mediaStats.TotalSizeBytes
			}

			if totalFiles > 0 {
				avgSize := totalSize / totalFiles
				// Distribute size proportionally based on file counts from database
				for fileType, count := range mediaStats.FilesByType {
					sizeByType[fileType] = int64(count) * avgSize
				}
			}
		} else {
			// Use actual MinIO statistics
			s.logger.Debug("Using detailed MinIO bucket stats",
				zap.Any("files_by_type", minioFilesByType),
				zap.Any("size_by_type", minioSizeByType))

			// Use MinIO data directly
			for fileType, size := range minioSizeByType {
				sizeByType[fileType] = size
			}
		}
	} else {
		// Fallback: use database statistics with estimated sizes
		for fileType, count := range mediaStats.FilesByType {
			// Estimate average sizes by file type
			var avgSize int64
			switch fileType {
			case "image":
				avgSize = 2 * 1024 * 1024 // 2MB average for images
			case "video":
				avgSize = 50 * 1024 * 1024 // 50MB average for videos
			case "audio":
				avgSize = 5 * 1024 * 1024 // 5MB average for audio
			case "document":
				avgSize = 1 * 1024 * 1024 // 1MB average for documents
			default:
				avgSize = 1 * 1024 * 1024 // 1MB default
			}
			sizeByType[fileType] = int64(count) * avgSize
		}
	}

	return sizeByType
}

// UploadFile implements the UploadFile gRPC method
func (s *Service) UploadFile(ctx context.Context, req *mediav1.UploadFileRequest) (*mediav1.UploadFileResponse, error) {
	s.logger.Info("UploadFile called",
		zap.String("user_id", req.UserId),
		zap.String("file_name", req.FileName),
		zap.String("file_type", req.FileType),
		zap.Int64("file_size", req.FileSize))

	if req.UserId == "" || req.FileName == "" || len(req.FileData) == 0 {
		return &mediav1.UploadFileResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID, file name, and file data are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID, file name, and file data are required")
	}

	if s.minioClient == nil {
		return &mediav1.UploadFileResponse{
			ApiResponse: createErrorResponse("SERVICE_UNAVAILABLE", "File storage service is not available"),
		}, status.Errorf(codes.Unavailable, "file storage service is not available")
	}

	// Upload file to MinIO
	uploadResult, err := s.uploadFileToMinIO(ctx, req.UserId, req.FileName, []byte(req.FileData), req.FileType)
	if err != nil {
		s.logger.Error("Failed to upload file to MinIO", zap.Error(err))
		return &mediav1.UploadFileResponse{
			ApiResponse: createErrorResponse("UPLOAD_ERROR", "Failed to upload file"),
		}, status.Errorf(codes.Internal, "failed to upload file: %v", err)
	}

	// Save file metadata to database
	mediaFile := &MediaFile{
		ID:          uploadResult.FileID,
		UserID:      req.UserId,
		FileName:    uploadResult.FileName,
		FileSize:    uploadResult.FileSize,
		MimeType:    uploadResult.MimeType,
		FileType:    uploadResult.FileType,
		StoragePath: uploadResult.StoragePath,
		URL:         uploadResult.URL,
		IsPublic:    false,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.repository.CreateMediaFile(ctx, nil, mediaFile)
	if err != nil {
		s.logger.Error("Failed to save file metadata", zap.Error(err))
		// Try to clean up the uploaded file
		_ = s.deleteFileFromMinIO(ctx, uploadResult.StoragePath)
		return &mediav1.UploadFileResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to save file metadata"),
		}, status.Errorf(codes.Internal, "failed to save file metadata: %v", err)
	}

	// Convert to proto format
	fileInfo := &mediav1.FileInfo{
		FileId:    mediaFile.ID,
		UserId:    mediaFile.UserID,
		FileName:  mediaFile.FileName,
		FileType:  mediaFile.FileType,
		FileSize:  mediaFile.FileSize,
		FileUrl:   mediaFile.URL,
		BubbleId:  req.BubbleId,
		MessageId: req.MessageId,
		CreatedAt: timestamppb.New(mediaFile.CreatedAt),
		UpdatedAt: timestamppb.New(mediaFile.UpdatedAt),
	}

	// Publish file uploaded event via NATS
	s.publishMediaEvent(req.UserId, mediaFile.ID, "file", "uploaded", map[string]interface{}{
		"file_id":   mediaFile.ID,
		"file_name": mediaFile.FileName,
		"file_type": mediaFile.FileType,
		"file_size": mediaFile.FileSize,
		"bubble_id": req.BubbleId,
	})

	return &mediav1.UploadFileResponse{
		FileId:      mediaFile.ID,
		FileInfo:    fileInfo,
		UploadUrl:   mediaFile.URL,
		ApiResponse: createSuccessResponse("File uploaded successfully"),
	}, nil
}

// GetFileInfo implements the GetFileInfo gRPC method
func (s *Service) GetFileInfo(ctx context.Context, req *mediav1.GetFileInfoRequest) (*mediav1.GetFileInfoResponse, error) {
	s.logger.Info("GetFileInfo called", zap.String("file_id", req.FileId))

	if req.FileId == "" {
		return &mediav1.GetFileInfoResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "File ID is required"),
		}, status.Errorf(codes.InvalidArgument, "file ID is required")
	}

	// Get file info from database
	mediaFile, err := s.repository.GetMediaFileByID(ctx, req.FileId)
	if err != nil {
		s.logger.Error("Failed to get file info", zap.Error(err))
		return &mediav1.GetFileInfoResponse{
			ApiResponse: createErrorResponse("NOT_FOUND", "File not found"),
		}, status.Errorf(codes.NotFound, "file not found: %v", err)
	}

	// Convert to proto format
	fileInfo := &mediav1.FileInfo{
		FileId:    mediaFile.ID,
		UserId:    mediaFile.UserID,
		FileName:  mediaFile.FileName,
		FileType:  mediaFile.FileType,
		FileSize:  mediaFile.FileSize,
		FileUrl:   mediaFile.URL,
		CreatedAt: timestamppb.New(mediaFile.CreatedAt),
		UpdatedAt: timestamppb.New(mediaFile.UpdatedAt),
	}

	return &mediav1.GetFileInfoResponse{
		FileInfo:    fileInfo,
		ApiResponse: createSuccessResponse("File info retrieved successfully"),
	}, nil
}

// DeleteFile implements the DeleteFile gRPC method
func (s *Service) DeleteFile(ctx context.Context, req *mediav1.DeleteFileRequest) (*mediav1.DeleteFileResponse, error) {
	s.logger.Info("DeleteFile called", zap.String("file_id", req.FileId))

	if req.FileId == "" {
		return &mediav1.DeleteFileResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "File ID is required"),
		}, status.Errorf(codes.InvalidArgument, "file ID is required")
	}

	// Get file info first to get storage path
	mediaFile, err := s.repository.GetMediaFileByID(ctx, req.FileId)
	if err != nil {
		s.logger.Error("Failed to get file for deletion", zap.Error(err))
		return &mediav1.DeleteFileResponse{
			ApiResponse: createErrorResponse("NOT_FOUND", "File not found"),
		}, status.Errorf(codes.NotFound, "file not found: %v", err)
	}

	// Delete from database first
	err = s.repository.DeleteMediaFile(ctx, nil, req.FileId)
	if err != nil {
		s.logger.Error("Failed to delete file from database", zap.Error(err))
		return &mediav1.DeleteFileResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to delete file"),
		}, status.Errorf(codes.Internal, "failed to delete file: %v", err)
	}

	// Delete from MinIO
	if s.minioClient != nil {
		err = s.deleteFileFromMinIO(ctx, mediaFile.StoragePath)
		if err != nil {
			s.logger.Error("Failed to delete file from MinIO", zap.Error(err))
			// Don't fail the request since database deletion succeeded
		}
	}

	// Publish file deleted event via NATS
	s.publishMediaEvent(mediaFile.UserID, req.FileId, "file", "deleted", map[string]interface{}{
		"file_id":   req.FileId,
		"file_name": mediaFile.FileName,
		"user_id":   mediaFile.UserID,
	})

	return &mediav1.DeleteFileResponse{
		ApiResponse: createSuccessResponse("File deleted successfully"),
	}, nil
}

// GetFileUrl implements the GetFileUrl gRPC method
func (s *Service) GetFileUrl(ctx context.Context, req *mediav1.GetFileUrlRequest) (*mediav1.GetFileUrlResponse, error) {
	s.logger.Info("GetFileUrl called", zap.String("file_id", req.FileId))

	if req.FileId == "" {
		return &mediav1.GetFileUrlResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "File ID is required"),
		}, status.Errorf(codes.InvalidArgument, "file ID is required")
	}

	// First get the file metadata to get the storage path
	mediaFile, err := s.repository.GetMediaFileByID(ctx, req.FileId)
	if err != nil {
		s.logger.Error("Failed to get media file", zap.Error(err))
		return &mediav1.GetFileUrlResponse{
			ApiResponse: createErrorResponse("NOT_FOUND", "File not found"),
		}, status.Errorf(codes.NotFound, "file not found")
	}

	// Generate presigned URL from MinIO using the storage path
	var fileURL string
	if s.minioRepo != nil {
		// Generate presigned URL with 1 hour expiry
		presignedURL, err := s.minioRepo.PresignedGetObject(ctx, mediaFile.StoragePath, time.Hour)
		if err != nil {
			s.logger.Error("Failed to generate presigned URL", zap.Error(err))
			return &mediav1.GetFileUrlResponse{
				ApiResponse: createErrorResponse("STORAGE_ERROR", "Failed to generate file URL"),
			}, status.Errorf(codes.Internal, "failed to generate file URL: %v", err)
		}
		fileURL = presignedURL
	} else {
		// Fallback to direct URL if MinIO is not configured
		fileURL = fmt.Sprintf("https://%s/%s/%s", s.config.MinIO.Endpoint, s.config.MinIO.BucketName, mediaFile.StoragePath)
	}

	return &mediav1.GetFileUrlResponse{
		FileUrl:     fileURL,
		ExpiresAt:   timestamppb.New(time.Now().Add(time.Hour)),
		ApiResponse: createSuccessResponse("File URL generated successfully"),
	}, nil
}

// UploadProfilePicture implements the UploadProfilePicture gRPC method
func (s *Service) UploadProfilePicture(ctx context.Context, req *mediav1.UploadProfilePictureRequest) (*mediav1.UploadProfilePictureResponse, error) {
	s.logger.Info("UploadProfilePicture called", zap.String("user_id", req.UserId))

	if req.UserId == "" || len(req.ImageData) == 0 {
		return &mediav1.UploadProfilePictureResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and image data are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and image data are required")
	}

	if s.minioClient == nil {
		return nil, status.Errorf(codes.Unavailable, "File storage service is not available")
	}

	// 1. Use the centralized upload logic. We'll give it a generic filename.
	fileName := fmt.Sprintf("profile-%s.jpg", req.UserId)
	uploadResult, err := s.uploadFileToMinIO(ctx, req.UserId, fileName, req.ImageData, "image/jpeg")
	if err != nil {
		s.logger.Error("Failed to upload profile picture to MinIO", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Failed to upload profile picture: %v", err)
	}

	// 2. Save metadata for the profile picture to the media_files table (optional but good practice).
	profileMediaFile := &MediaFile{
		ID:          uploadResult.FileID,
		UserID:      req.UserId,
		FileName:    uploadResult.FileName,
		FileSize:    uploadResult.FileSize,
		MimeType:    uploadResult.MimeType,
		FileType:    "image",
		StoragePath: uploadResult.StoragePath,
		URL:         uploadResult.URL,
		IsPublic:    true, // Profile pictures are typically public
	}

	// Use a transaction to ensure both tables are updated
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		s.logger.Error("Failed to begin transaction for profile picture update", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Failed to update profile picture")
	}
	defer tx.Rollback(ctx)

	if err := s.repository.CreateMediaFile(ctx, tx, profileMediaFile); err != nil {
		s.logger.Error("Failed to save profile picture metadata", zap.Error(err))
		// We can still continue, the most important part is updating the user's avatar_url
	}

	// 3. CRITICAL: Update the user's avatar_url in the users table.
	if err := s.repository.UpdateProfilePicture(ctx, tx, req.UserId, uploadResult.URL); err != nil {
		s.logger.Error("Failed to update user's avatar_url", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Failed to link profile picture to user")
	}

	if err := tx.Commit(ctx); err != nil {
		s.logger.Error("Failed to commit transaction for profile picture update", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Failed to save profile picture update")
	}

	// Publish event
	s.publishMediaEvent(req.UserId, uploadResult.FileID, "profile_picture", "uploaded", map[string]interface{}{
		"user_id": req.UserId,
		"url":     uploadResult.URL,
	})

	return &mediav1.UploadProfilePictureResponse{
		FileId:       uploadResult.FileID,
		ThumbnailUrl: uploadResult.URL, // For a profile pic, the main URL is fine as the thumbnail
		ApiResponse:  createSuccessResponse("Profile picture uploaded successfully"),
	}, nil
}

// GetUserMedia implements the GetUserMedia gRPC method
func (s *Service) GetUserMedia(ctx context.Context, req *mediav1.GetUserMediaRequest) (*mediav1.GetUserMediaResponse, error) {
	s.logger.Info("GetUserMedia called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &mediav1.GetUserMediaResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get user media from database
	limit := int(req.PageSize)
	if limit <= 0 {
		limit = 20 // Default page size
	}
	offset := int((req.Page - 1) * req.PageSize)

	mediaFiles, err := s.repository.GetUserMediaFiles(ctx, req.UserId, limit, offset)
	if err != nil {
		s.logger.Error("Failed to get user media", zap.Error(err))
		return &mediav1.GetUserMediaResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve user media"),
		}, status.Errorf(codes.Internal, "failed to retrieve user media: %v", err)
	}

	// Convert to proto format
	var protoFiles []*mediav1.FileInfo
	for _, file := range mediaFiles {
		protoFile := &mediav1.FileInfo{
			FileId:    file.ID,
			UserId:    file.UserID,
			FileName:  file.FileName,
			FileType:  file.FileType,
			FileSize:  file.FileSize,
			FileUrl:   file.URL,
			CreatedAt: timestamppb.New(file.CreatedAt),
			UpdatedAt: timestamppb.New(file.UpdatedAt),
		}
		protoFiles = append(protoFiles, protoFile)
	}

	return &mediav1.GetUserMediaResponse{
		MediaFiles:  protoFiles,
		Pagination:  &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(protoFiles))},
		ApiResponse: createSuccessResponse("User media retrieved successfully"),
	}, nil
}

// GenerateThumbnail implements the GenerateThumbnail gRPC method
func (s *Service) GenerateThumbnail(ctx context.Context, req *mediav1.GenerateThumbnailRequest) (*mediav1.GenerateThumbnailResponse, error) {
	s.logger.Info("GenerateThumbnail called", zap.String("file_id", req.FileId))

	if req.FileId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "File ID is required")
	}
	if s.minioClient == nil {
		return nil, status.Errorf(codes.Unavailable, "File storage service is not available")
	}

	// 1. Get the original file's metadata from the database
	originalFile, err := s.repository.GetMediaFileByID(ctx, req.FileId)
	if err != nil {
		s.logger.Error("Failed to get original file for thumbnail generation", zap.Error(err))
		return nil, status.Errorf(codes.NotFound, "Original file not found")
	}

	// 2. Download the original file from MinIO
	object, err := s.minioClient.GetObject(ctx, s.config.MinIO.BucketName, originalFile.StoragePath, minio.GetObjectOptions{})
	if err != nil {
		s.logger.Error("Failed to get original file object from MinIO", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Could not retrieve original file")
	}
	defer object.Close()

	// 3. Read file data
	data, err := io.ReadAll(object)
	if err != nil {
		s.logger.Error("Failed to read original file data", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Failed to read file data")
	}

	// 4. Decode the image for resizing
	img, format, err := image.Decode(bytes.NewReader(data))
	if err != nil {
		s.logger.Error("Failed to decode image for thumbnail generation", zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "Invalid image format")
	}

	// 5. Set default dimensions if not provided
	width := uint(200)  // Default width
	height := uint(200) // Default height
	if req.Width > 0 {
		width = uint(req.Width)
	}
	if req.Height > 0 {
		height = uint(req.Height)
	}

	// 6. Resize the image to create thumbnail
	// Use Lanczos resampling for high quality thumbnails
	thumbnailImg := resize.Resize(width, height, img, resize.Lanczos3)

	// 7. Encode the thumbnail back to bytes
	var thumbnailBuffer bytes.Buffer
	switch format {
	case "jpeg":
		err = jpeg.Encode(&thumbnailBuffer, thumbnailImg, &jpeg.Options{Quality: 85})
	case "png":
		err = png.Encode(&thumbnailBuffer, thumbnailImg)
	default:
		// Default to JPEG for other formats
		err = jpeg.Encode(&thumbnailBuffer, thumbnailImg, &jpeg.Options{Quality: 85})
	}

	if err != nil {
		s.logger.Error("Failed to encode thumbnail image", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Failed to encode thumbnail")
	}

	thumbnailData := thumbnailBuffer.Bytes()

	// 8. Upload the thumbnail to MinIO
	thumbFileName := fmt.Sprintf("thumb-%s", originalFile.FileName)
	thumbnailUploadResult, err := s.uploadFileToMinIO(ctx, originalFile.UserID, thumbFileName, thumbnailData, "image/jpeg")
	if err != nil {
		s.logger.Error("Failed to upload thumbnail to MinIO", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "Failed to store thumbnail")
	}

	// 9. CRITICAL: Update the original media file's record with the new thumbnail URL
	err = s.repository.UpdateThumbnailURL(ctx, originalFile.ID, thumbnailUploadResult.URL)
	if err != nil {
		s.logger.Error("Failed to link thumbnail to original file", zap.Error(err))
		// Attempt to clean up the orphaned thumbnail
		_ = s.deleteFileFromMinIO(ctx, thumbnailUploadResult.StoragePath)
		return nil, status.Errorf(codes.Internal, "Failed to update file metadata with thumbnail")
	}

	s.logger.Info("Thumbnail generated and linked successfully",
		zap.String("original_file_id", originalFile.ID),
		zap.String("thumbnail_url", thumbnailUploadResult.URL))

	return &mediav1.GenerateThumbnailResponse{
		ThumbnailUrl: thumbnailUploadResult.URL,
		ApiResponse:  createSuccessResponse("Thumbnail generated successfully"),
	}, nil
}

// GetMediaStats implements the GetMediaStats gRPC method
func (s *Service) GetMediaStats(ctx context.Context, req *mediav1.GetMediaStatsRequest) (*mediav1.GetMediaStatsResponse, error) {
	s.logger.Info("GetMediaStats called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &mediav1.GetMediaStatsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get media statistics from database
	mediaStats, err := s.repository.GetMediaStats(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get media stats", zap.Error(err))
		return &mediav1.GetMediaStatsResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve media statistics"),
		}, status.Errorf(codes.Internal, "failed to retrieve media statistics: %v", err)
	}

	// Convert to proto format
	stats := &mediav1.MediaStats{
		TotalFiles:     mediaStats.TotalFiles,
		TotalSizeBytes: mediaStats.TotalSizeBytes,
		FilesByType:    mediaStats.FilesByType,
		SizeByType:     s.calculateSizeByType(ctx, mediaStats), // Calculate size by type from stats
	}

	return &mediav1.GetMediaStatsResponse{
		Stats:       stats,
		ApiResponse: createSuccessResponse("Media stats retrieved successfully"),
	}, nil
}

// FileUploadResult contains the result of a file upload
type FileUploadResult struct {
	FileID      string
	FileName    string
	FileSize    int64
	MimeType    string
	FileType    string
	StoragePath string
	URL         string
}

// uploadFileToMinIO uploads a file to MinIO and returns upload result
func (s *Service) uploadFileToMinIO(ctx context.Context, userID, fileName string, fileData []byte, contentType string) (*FileUploadResult, error) {
	// Generate unique file ID
	fileID := uuid.New().String()

	// Determine file extension
	ext := filepath.Ext(fileName)
	if ext == "" {
		// Try to determine extension from content type
		exts, err := mime.ExtensionsByType(contentType)
		if err == nil && len(exts) > 0 {
			ext = exts[0]
		}
	}

	// Create storage path: users/{userID}/files/{fileID}{ext}
	storagePath := fmt.Sprintf("users/%s/files/%s%s", userID, fileID, ext)

	// Determine file type category
	fileType := s.determineFileType(contentType)

	// Upload file to MinIO with retry logic
	var uploadInfo minio.UploadInfo
	err := s.retriers.ExecuteWithMinIORetry(ctx, func() error {
		reader := bytes.NewReader(fileData)
		info, uploadErr := s.minioClient.PutObject(ctx, s.config.MinIO.BucketName, storagePath, reader, int64(len(fileData)), minio.PutObjectOptions{
			ContentType: contentType,
		})
		if uploadErr != nil {
			s.logger.Warn("MinIO upload attempt failed, will retry if retryable",
				zap.Error(uploadErr),
				zap.String("storage_path", storagePath))
			return uploadErr
		}
		uploadInfo = info
		return nil
	})
	if err != nil {
		s.logger.Error("Failed to upload file to MinIO after retries", zap.Error(err))
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	s.logger.Info("File uploaded to MinIO",
		zap.String("file_id", fileID),
		zap.String("storage_path", storagePath),
		zap.Int64("size", uploadInfo.Size))

	// Generate file URL
	fileURL := s.generateFileURL(storagePath)

	return &FileUploadResult{
		FileID:      fileID,
		FileName:    fileName,
		FileSize:    int64(len(fileData)),
		MimeType:    contentType,
		FileType:    fileType,
		StoragePath: storagePath,
		URL:         fileURL,
	}, nil
}

// deleteFileFromMinIO deletes a file from MinIO
func (s *Service) deleteFileFromMinIO(ctx context.Context, storagePath string) error {
	err := s.minioClient.RemoveObject(ctx, s.config.MinIO.BucketName, storagePath, minio.RemoveObjectOptions{})
	if err != nil {
		s.logger.Error("Failed to delete file from MinIO", zap.Error(err))
		return fmt.Errorf("failed to delete file: %w", err)
	}

	s.logger.Info("File deleted from MinIO", zap.String("storage_path", storagePath))
	return nil
}

// generateFileURL generates the public URL for a file
func (s *Service) generateFileURL(storagePath string) string {
	if s.config.MinIO.UseSSL {
		return fmt.Sprintf("https://%s/%s/%s", s.config.MinIO.Endpoint, s.config.MinIO.BucketName, storagePath)
	}
	return fmt.Sprintf("http://%s/%s/%s", s.config.MinIO.Endpoint, s.config.MinIO.BucketName, storagePath)
}

// determineFileType determines the file type category from MIME type
func (s *Service) determineFileType(mimeType string) string {
	switch {
	case strings.HasPrefix(mimeType, "image/"):
		return "image"
	case strings.HasPrefix(mimeType, "video/"):
		return "video"
	case strings.HasPrefix(mimeType, "audio/"):
		return "audio"
	case strings.HasPrefix(mimeType, "application/pdf"):
		return "document"
	case strings.Contains(mimeType, "document") || strings.Contains(mimeType, "text"):
		return "document"
	default:
		return "other"
	}
}
