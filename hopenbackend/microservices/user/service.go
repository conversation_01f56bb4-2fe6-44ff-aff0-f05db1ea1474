package user

import (
	"context"
	"time"

	"github.com/gocql/gocql"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	userv1 "hopenbackend/protos/gen/user"
)

// Service handles user operations and implements the gRPC UserServiceServer interface
type Service struct {
	userv1.UnimplementedUserServiceServer
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	scylladb    *database.ScyllaDBClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
}

// Dependencies holds the dependencies for the user service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	ScyllaDB    *database.ScyllaDBClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
}

// NewService creates a new user service instance
func NewService(deps *Dependencies) *Service {
	return &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		scylladb:    deps.ScyllaDB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
	}
}

// NewUserServiceServer creates a new gRPC server for the user service
func NewUserServiceServer(service *Service) userv1.UserServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to convert database user to protobuf user
func (s *Service) convertDBUserToProtoUser(dbUser *database.User) *commonv1.User {
	protoUser := &commonv1.User{
		Id:        dbUser.ID,
		Email:     dbUser.Email,
		IsOnline:  dbUser.IsPresent,
		CreatedAt: timestamppb.New(dbUser.CreatedAt),
		UpdatedAt: timestamppb.New(dbUser.UpdatedAt),
	}

	if dbUser.Username != nil {
		protoUser.Username = *dbUser.Username
	}
	if dbUser.FirstName != nil {
		protoUser.FirstName = *dbUser.FirstName
	}
	if dbUser.LastName != nil {
		protoUser.LastName = *dbUser.LastName
	}
	if dbUser.AvatarURL != nil {
		protoUser.AvatarUrl = dbUser.AvatarURL
	}

	return protoUser
}

// GetUser implements the GetUser gRPC method
func (s *Service) GetUser(ctx context.Context, req *userv1.GetUserRequest) (*userv1.GetUserResponse, error) {
	s.logger.Info("GetUser called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &userv1.GetUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get user from database
	dbUser, err := s.db.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user by ID",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		return &userv1.GetUserResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Convert to protobuf user
	protoUser := s.convertDBUserToProtoUser(dbUser)

	return &userv1.GetUserResponse{
		User:        protoUser,
		ApiResponse: createSuccessResponse("User retrieved successfully"),
	}, nil
}

// UpdateUser implements the UpdateUser gRPC method
func (s *Service) UpdateUser(ctx context.Context, req *userv1.UpdateUserRequest) (*userv1.UpdateUserResponse, error) {
	s.logger.Info("UpdateUser called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &userv1.UpdateUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get current user
	dbUser, err := s.db.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user for update",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		return &userv1.UpdateUserResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Update fields if provided
	if req.Username != nil {
		dbUser.Username = req.Username
	}
	if req.FirstName != nil {
		dbUser.FirstName = req.FirstName
	}
	if req.LastName != nil {
		dbUser.LastName = req.LastName
	}
	if req.AvatarUrl != nil {
		dbUser.AvatarURL = req.AvatarUrl
	}

	// Update timestamp
	dbUser.UpdatedAt = time.Now()

	// Save to database
	if err := s.db.UpdateUser(ctx, dbUser); err != nil {
		s.logger.Error("Failed to update user in database",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		return &userv1.UpdateUserResponse{
			ApiResponse: createErrorResponse("UPDATE_ERROR", "Failed to update user"),
		}, status.Errorf(codes.Internal, "failed to update user: %v", err)
	}

	// Update Ory identity traits if name/username fields were updated
	if req.FirstName != nil || req.LastName != nil || req.Username != nil {
		traits := map[string]interface{}{
			"email": dbUser.Email,
		}

		if dbUser.Username != nil {
			traits["username"] = *dbUser.Username
		}
		if dbUser.FirstName != nil {
			traits["first_name"] = *dbUser.FirstName
		}
		if dbUser.LastName != nil {
			traits["last_name"] = *dbUser.LastName
		}

		_, err := s.oryClient.UpdateIdentity(ctx, req.UserId, traits)
		if err != nil {
			s.logger.Error("Failed to update Ory identity traits",
				zap.String("user_id", req.UserId),
				zap.Error(err))
			// Don't fail the request if Ory update fails
		}
	}

	// Convert to protobuf user
	protoUser := s.convertDBUserToProtoUser(dbUser)

	return &userv1.UpdateUserResponse{
		User:        protoUser,
		ApiResponse: createSuccessResponse("User updated successfully"),
	}, nil
}

// SearchUsers implements the SearchUsers gRPC method
func (s *Service) SearchUsers(ctx context.Context, req *userv1.SearchUsersRequest) (*userv1.SearchUsersResponse, error) {
	s.logger.Info("SearchUsers called", zap.String("query", req.Query))

	if req.Query == "" {
		return &userv1.SearchUsersResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Search query is required"),
		}, status.Errorf(codes.InvalidArgument, "search query is required")
	}

	// Set default pagination
	pageSize := req.PageSize
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// Search users with privacy filtering
	users, err := s.db.SearchUsers(ctx, req.Query, int(pageSize))
	if err != nil {
		s.logger.Error("Failed to search users", zap.Error(err))
		return &userv1.SearchUsersResponse{
			ApiResponse: createErrorResponse("SEARCH_ERROR", "Failed to search users"),
		}, status.Errorf(codes.Internal, "failed to search users: %v", err)
	}

	// Convert database users to protobuf users
	protoUsers := make([]*commonv1.User, len(users))
	for i, dbUser := range users {
		protoUsers[i] = s.convertDBUserToProtoUser(dbUser)
	}

	// Create pagination info
	pagination := &commonv1.Pagination{
		Page:       req.Page,
		PageSize:   pageSize,
		TotalCount: int32(len(protoUsers)),
		TotalPages: 1, // Simple implementation
		HasNext:    false,
		HasPrev:    false,
	}

	return &userv1.SearchUsersResponse{
		Users:       protoUsers,
		Pagination:  pagination,
		ApiResponse: createSuccessResponse("Users searched successfully"),
	}, nil
}

// CheckUsernameAvailability implements the CheckUsernameAvailability gRPC method
func (s *Service) CheckUsernameAvailability(ctx context.Context, req *userv1.CheckUsernameAvailabilityRequest) (*userv1.CheckUsernameAvailabilityResponse, error) {
	s.logger.Info("CheckUsernameAvailability called", zap.String("username", req.Username))

	if req.Username == "" {
		return &userv1.CheckUsernameAvailabilityResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Username is required"),
		}, status.Errorf(codes.InvalidArgument, "username is required")
	}

	// Check if username exists
	query := `SELECT EXISTS(SELECT 1 FROM users WHERE LOWER(username) = LOWER($1))`
	var exists bool
	if err := s.db.Pool.QueryRow(ctx, query, req.Username).Scan(&exists); err != nil {
		s.logger.Error("Failed to check username availability", zap.Error(err))
		return &userv1.CheckUsernameAvailabilityResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to check username availability"),
		}, status.Errorf(codes.Internal, "failed to check username availability: %v", err)
	}

	return &userv1.CheckUsernameAvailabilityResponse{
		Available:   !exists,
		ApiResponse: createSuccessResponse("Username availability checked successfully"),
	}, nil
}

// CheckEmailAvailability implements the CheckEmailAvailability gRPC method
func (s *Service) CheckEmailAvailability(ctx context.Context, req *userv1.CheckEmailAvailabilityRequest) (*userv1.CheckEmailAvailabilityResponse, error) {
	s.logger.Info("CheckEmailAvailability called", zap.String("email", req.Email))

	if req.Email == "" {
		return &userv1.CheckEmailAvailabilityResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Email is required"),
		}, status.Errorf(codes.InvalidArgument, "email is required")
	}

	// Check if email exists
	query := `SELECT EXISTS(SELECT 1 FROM users WHERE LOWER(email) = LOWER($1))`
	var exists bool
	if err := s.db.Pool.QueryRow(ctx, query, req.Email).Scan(&exists); err != nil {
		s.logger.Error("Failed to check email availability", zap.Error(err))
		return &userv1.CheckEmailAvailabilityResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to check email availability"),
		}, status.Errorf(codes.Internal, "failed to check email availability: %v", err)
	}

	return &userv1.CheckEmailAvailabilityResponse{
		Available:   !exists,
		ApiResponse: createSuccessResponse("Email availability checked successfully"),
	}, nil
}

// GetUserPrivacySettings implements the GetUserPrivacySettings gRPC method
func (s *Service) GetUserPrivacySettings(ctx context.Context, req *userv1.GetUserPrivacySettingsRequest) (*userv1.GetUserPrivacySettingsResponse, error) {
	s.logger.Info("GetUserPrivacySettings called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &userv1.GetUserPrivacySettingsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get user from database to check privacy settings
	dbUser, err := s.db.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user for privacy settings",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		return &userv1.GetUserPrivacySettingsResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Create privacy settings response
	settings := &userv1.UserPrivacySettings{
		UserId:                req.UserId,
		IsPrivate:             dbUser.IsPrivate,
		AllowSearchByEmail:    true, // Default values - these could be stored in DB
		AllowSearchByUsername: true,
		ShowOnlineStatus:      true,
		ShowBubbleMembership:  true,
		UpdatedAt:             timestamppb.New(dbUser.UpdatedAt),
	}

	return &userv1.GetUserPrivacySettingsResponse{
		Settings:    settings,
		ApiResponse: createSuccessResponse("Privacy settings retrieved successfully"),
	}, nil
}

// UpdateUserPrivacySettings implements the UpdateUserPrivacySettings gRPC method
func (s *Service) UpdateUserPrivacySettings(ctx context.Context, req *userv1.UpdateUserPrivacySettingsRequest) (*userv1.UpdateUserPrivacySettingsResponse, error) {
	s.logger.Info("UpdateUserPrivacySettings called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &userv1.UpdateUserPrivacySettingsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get current user
	dbUser, err := s.db.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user for privacy update",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		return &userv1.UpdateUserPrivacySettingsResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Update privacy settings if provided
	if req.IsPrivate != nil {
		dbUser.IsPrivate = *req.IsPrivate
	}

	// Update timestamp
	dbUser.UpdatedAt = time.Now()

	// Save to database
	if err := s.db.UpdateUser(ctx, dbUser); err != nil {
		s.logger.Error("Failed to update user privacy settings",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		return &userv1.UpdateUserPrivacySettingsResponse{
			ApiResponse: createErrorResponse("UPDATE_ERROR", "Failed to update privacy settings"),
		}, status.Errorf(codes.Internal, "failed to update privacy settings: %v", err)
	}

	// Create updated privacy settings response
	settings := &userv1.UserPrivacySettings{
		UserId:                req.UserId,
		IsPrivate:             dbUser.IsPrivate,
		AllowSearchByEmail:    true, // Default values
		AllowSearchByUsername: true,
		ShowOnlineStatus:      true,
		ShowBubbleMembership:  true,
		UpdatedAt:             timestamppb.New(dbUser.UpdatedAt),
	}

	return &userv1.UpdateUserPrivacySettingsResponse{
		Settings:    settings,
		ApiResponse: createSuccessResponse("Privacy settings updated successfully"),
	}, nil
}

// DeleteUser implements the DeleteUser gRPC method
func (s *Service) DeleteUser(ctx context.Context, req *userv1.DeleteUserRequest) (*userv1.DeleteUserResponse, error) {
	s.logger.Info("DeleteUser called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &userv1.DeleteUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Soft delete user by setting is_active to false
	if err := s.db.SoftDeleteUser(ctx, req.UserId); err != nil {
		s.logger.Error("Failed to delete user", zap.Error(err))
		return &userv1.DeleteUserResponse{
			ApiResponse: createErrorResponse("DELETE_ERROR", "Failed to delete user"),
		}, status.Errorf(codes.Internal, "failed to delete user: %v", err)
	}

	s.logger.Info("User deleted successfully", zap.String("user_id", req.UserId))

	return &userv1.DeleteUserResponse{
		ApiResponse: createSuccessResponse("User deleted successfully"),
	}, nil
}

// GetUserStats implements the GetUserStats gRPC method
func (s *Service) GetUserStats(ctx context.Context, req *userv1.GetUserStatsRequest) (*userv1.GetUserStatsResponse, error) {
	s.logger.Info("GetUserStats called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &userv1.GetUserStatsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get user from database
	dbUser, err := s.db.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user for stats",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		return &userv1.GetUserStatsResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Get user statistics from PostgreSQL
	pgStats, err := s.db.GetUserStatistics(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user statistics from PostgreSQL",
			zap.String("user_id", req.UserId),
			zap.Error(err))
		// Continue with zero values if stats query fails
		pgStats = &database.UserStatistics{}
	}

	// Get message statistics from ScyllaDB
	var messagesSent, messagesReceived int64
	if s.scylladb != nil {
		userUUID, err := gocql.ParseUUID(req.UserId)
		if err != nil {
			s.logger.Error("Invalid user ID format for ScyllaDB query", zap.Error(err))
		} else {
			messagesSent, messagesReceived, err = s.scylladb.GetUserMessageStatistics(ctx, userUUID)
			if err != nil {
				s.logger.Error("Failed to get message statistics from ScyllaDB",
					zap.String("user_id", req.UserId),
					zap.Error(err))
				// Continue with zero values if ScyllaDB query fails
				messagesSent, messagesReceived = 0, 0
			}
		}
	}

	// Create comprehensive stats
	stats := &userv1.UserStats{
		UserId:                req.UserId,
		TotalBubblesCreated:   int32(pgStats.BubblesCreated),
		TotalBubblesJoined:    int32(pgStats.BubblesJoined),
		TotalContacts:         int32(pgStats.TotalContacts),
		TotalFriends:          int32(pgStats.TotalFriends),
		TotalMessagesSent:     int32(messagesSent),
		TotalMessagesReceived: int32(messagesReceived),
		LastActiveAt:          timestamppb.New(dbUser.UpdatedAt),
		CreatedAt:             timestamppb.New(dbUser.CreatedAt),
	}

	return &userv1.GetUserStatsResponse{
		Stats:       stats,
		ApiResponse: createSuccessResponse("User stats retrieved successfully"),
	}, nil
}

// Helper function to create a string pointer
func stringPtr(s string) *string {
	return &s
}

// BlockUser implements the BlockUser gRPC method
func (s *Service) BlockUser(ctx context.Context, req *userv1.BlockUserRequest) (*userv1.BlockUserResponse, error) {
	s.logger.Info("BlockUser called",
		zap.String("user_id", req.UserId),
		zap.String("blocked_user_id", req.BlockedUserId))

	if req.UserId == "" || req.BlockedUserId == "" {
		return &userv1.BlockUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and blocked user ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and blocked user ID are required")
	}

	if req.UserId == req.BlockedUserId {
		return &userv1.BlockUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Cannot block yourself"),
		}, status.Errorf(codes.InvalidArgument, "cannot block yourself")
	}

	// Create block relationship in database
	err := s.db.CreateUserRelationship(ctx, &database.UserRelationship{
		FromUserID:       req.UserId,
		ToUserID:         req.BlockedUserId,
		RelationshipType: "block",
		Status:           "active",
		CreatedBy:        &req.UserId,
		Reason:           &req.Reason,
	})
	if err != nil {
		s.logger.Error("Failed to create block relationship", zap.Error(err))
		return &userv1.BlockUserResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to block user"),
		}, status.Errorf(codes.Internal, "failed to block user: %v", err)
	}

	s.logger.Info("User blocked successfully",
		zap.String("user_id", req.UserId),
		zap.String("blocked_user_id", req.BlockedUserId))

	return &userv1.BlockUserResponse{
		ApiResponse: createSuccessResponse("User blocked successfully"),
	}, nil
}

// UnblockUser implements the UnblockUser gRPC method
func (s *Service) UnblockUser(ctx context.Context, req *userv1.UnblockUserRequest) (*userv1.UnblockUserResponse, error) {
	s.logger.Info("UnblockUser called",
		zap.String("user_id", req.UserId),
		zap.String("blocked_user_id", req.BlockedUserId))

	if req.UserId == "" || req.BlockedUserId == "" {
		return &userv1.UnblockUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and blocked user ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and blocked user ID are required")
	}

	// Remove block relationship from database
	err := s.db.RemoveUserRelationship(ctx, req.UserId, req.BlockedUserId, "block")
	if err != nil {
		s.logger.Error("Failed to remove block relationship", zap.Error(err))
		return &userv1.UnblockUserResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to unblock user"),
		}, status.Errorf(codes.Internal, "failed to unblock user: %v", err)
	}

	s.logger.Info("User unblocked successfully",
		zap.String("user_id", req.UserId),
		zap.String("blocked_user_id", req.BlockedUserId))

	return &userv1.UnblockUserResponse{
		ApiResponse: createSuccessResponse("User unblocked successfully"),
	}, nil
}

// GetBlockedUsers implements the GetBlockedUsers gRPC method
func (s *Service) GetBlockedUsers(ctx context.Context, req *userv1.GetBlockedUsersRequest) (*userv1.GetBlockedUsersResponse, error) {
	s.logger.Info("GetBlockedUsers called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &userv1.GetBlockedUsersResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get blocked user relationships
	relationships, err := s.db.GetUserRelationships(ctx, req.UserId, stringPtr("block"))
	if err != nil {
		s.logger.Error("Failed to get blocked users", zap.Error(err))
		return &userv1.GetBlockedUsersResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve blocked users"),
		}, status.Errorf(codes.Internal, "failed to retrieve blocked users: %v", err)
	}

	// Get blocked user IDs
	blockedUserIDs := relationships["block"]
	if len(blockedUserIDs) == 0 {
		return &userv1.GetBlockedUsersResponse{
			BlockedUsers: []*commonv1.User{},
			Pagination:   &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: 0},
			ApiResponse:  createSuccessResponse("Blocked users retrieved successfully"),
		}, nil
	}

	// Apply pagination
	pageSize := int(req.PageSize)
	if pageSize <= 0 {
		pageSize = 20 // Default page size
	}
	page := int(req.Page)
	if page <= 0 {
		page = 1
	}

	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= len(blockedUserIDs) {
		return &userv1.GetBlockedUsersResponse{
			BlockedUsers: []*commonv1.User{},
			Pagination:   &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(blockedUserIDs))},
			ApiResponse:  createSuccessResponse("Blocked users retrieved successfully"),
		}, nil
	}
	if end > len(blockedUserIDs) {
		end = len(blockedUserIDs)
	}

	paginatedUserIDs := blockedUserIDs[start:end]

	// Get user details for blocked users
	var blockedUsers []*commonv1.User
	for _, userID := range paginatedUserIDs {
		user, err := s.db.GetUserByID(ctx, userID)
		if err != nil {
			s.logger.Error("Failed to get blocked user details", zap.String("user_id", userID), zap.Error(err))
			continue
		}

		blockedUsers = append(blockedUsers, s.convertDBUserToProtoUser(user))
	}

	return &userv1.GetBlockedUsersResponse{
		BlockedUsers: blockedUsers,
		Pagination:   &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(blockedUserIDs))},
		ApiResponse:  createSuccessResponse("Blocked users retrieved successfully"),
	}, nil
}
