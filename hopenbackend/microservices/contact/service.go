package contact

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	contactv1 "hopenbackend/protos/gen/contact"
)

// Constants for contact statuses to avoid magic strings
const (
	StatusPending  = "pending"
	StatusAccepted = "accepted"
	StatusDeclined = "declined"
)

// Service handles contact operations using PostgreSQL and implements gRPC ContactServiceServer
type Service struct {
	contactv1.UnimplementedContactServiceServer
	logger      *zap.Logger
	repository  *PostgreSQLRepository
	db          *database.PostgreSQLClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
}

// ContactEvent represents a contact-related event for NATS
type ContactEvent struct {
	UserID    string    `json:"user_id"`
	ContactID string    `json:"contact_id"`
	Action    string    `json:"action"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the contact service
type Dependencies struct {
	Logger      *zap.Logger
	Repository  *PostgreSQLRepository
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
}

// NewService creates a new contact service instance
func NewService(deps *Dependencies) *Service {
	return &Service{
		logger:      deps.Logger,
		repository:  deps.Repository,
		db:          deps.DB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
	}
}

// NewContactServiceServer creates a new gRPC server for the contact service
func NewContactServiceServer(service *Service) contactv1.ContactServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishContactRequestEvent publishes contact events via NATS
func (s *Service) publishContactRequestEvent(userID, contactID, action string) {
	if s.natsConn == nil {
		return
	}

	event := ContactEvent{
		UserID:    userID,
		ContactID: contactID,
		Action:    action,
		Timestamp: time.Now(),
	}

	data, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal contact event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("contact.%s", action)
	if err := s.natsConn.Publish(subject, data); err != nil {
		s.logger.Error("Failed to publish contact event", zap.Error(err))
	}
}

// SendContactRequest implements the SendContactRequest gRPC method
func (s *Service) SendContactRequest(ctx context.Context, req *contactv1.SendContactRequestRequest) (*contactv1.SendContactRequestResponse, error) {
	s.logger.Info("SendContactRequest called",
		zap.String("from_user_id", req.FromUserId),
		zap.String("to_user_id", req.ToUserId))

	if req.FromUserId == "" || req.ToUserId == "" {
		return &contactv1.SendContactRequestResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "From User ID and To User ID are required"),
		}, status.Errorf(codes.InvalidArgument, "from user ID and to user ID are required")
	}

	if req.FromUserId == req.ToUserId {
		return &contactv1.SendContactRequestResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Cannot send contact request to yourself"),
		}, status.Errorf(codes.InvalidArgument, "cannot send contact request to yourself")
	}

	// Check if contact relationship already exists
	exists, err := s.repository.ContactRelationshipExists(ctx, req.FromUserId, req.ToUserId)
	if err != nil {
		s.logger.Error("Failed to check contact relationship existence", zap.Error(err))
		return &contactv1.SendContactRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to check existing relationship"),
		}, status.Errorf(codes.Internal, "failed to check existing relationship")
	}

	if exists {
		return &contactv1.SendContactRequestResponse{
			ApiResponse: createErrorResponse("ALREADY_EXISTS", "Contact request or relationship already exists"),
		}, status.Errorf(codes.AlreadyExists, "contact request or relationship already exists")
	}

	// Create contact request
	contact := &Contact{
		RequesterID: req.FromUserId,
		RecipientID: req.ToUserId,
		Status:      StatusPending,
		Message:     &req.Message,
	}

	err = s.repository.CreateContact(ctx, contact)
	if err != nil {
		s.logger.Error("Failed to create contact request", zap.Error(err))
		return &contactv1.SendContactRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to create contact request"),
		}, status.Errorf(codes.Internal, "failed to create contact request")
	}

	// Publish contact request event via NATS
	s.publishContactRequestEvent(req.FromUserId, req.ToUserId, "sent")

	return &contactv1.SendContactRequestResponse{
		RequestId:   contact.ID,
		ApiResponse: createSuccessResponse("Contact request sent successfully"),
	}, nil
}

// getAndValidateRequest is a helper to fetch a contact request and perform common validation.
func (s *Service) getAndValidateRequest(ctx context.Context, requestID, userID, role string) (*Contact, error) {
	if userID == "" || requestID == "" {
		return nil, status.Errorf(codes.InvalidArgument, "user ID and request ID are required")
	}

	contact, err := s.repository.GetContactByID(ctx, requestID)
	if err != nil {
		if err.Error() == "contact not found" {
			s.logger.Warn("Contact request not found", zap.String("request_id", requestID))
			return nil, status.Errorf(codes.NotFound, "contact request not found")
		}
		s.logger.Error("Failed to get contact request", zap.Error(err), zap.String("request_id", requestID))
		return nil, status.Errorf(codes.Internal, "failed to retrieve contact request")
	}

	switch role {
	case "recipient":
		if contact.RecipientID != userID {
			return nil, status.Errorf(codes.PermissionDenied, "you can only accept/reject requests sent to you")
		}
	case "requester":
		if contact.RequesterID != userID {
			return nil, status.Errorf(codes.PermissionDenied, "you can only cancel requests you sent")
		}
	default:
		return nil, status.Errorf(codes.Internal, "invalid role specified for validation")
	}

	if contact.Status != StatusPending {
		return nil, status.Errorf(codes.FailedPrecondition, "contact request is no longer pending")
	}

	return contact, nil
}

// AcceptContactRequest implements the AcceptContactRequest gRPC method
func (s *Service) AcceptContactRequest(ctx context.Context, req *contactv1.AcceptContactRequestRequest) (*contactv1.AcceptContactRequestResponse, error) {
	s.logger.Info("AcceptContactRequest called",
		zap.String("user_id", req.UserId),
		zap.String("request_id", req.RequestId))

	contact, err := s.getAndValidateRequest(ctx, req.RequestId, req.UserId, "recipient")
	if err != nil {
		// The gRPC status error is already created by the helper.
		// We return nil for the response, as is standard gRPC practice for errors.
		return nil, err
	}

	// Update the contact request status to accepted
	err = s.repository.UpdateContactStatus(ctx, req.RequestId, StatusAccepted)
	if err != nil {
		s.logger.Error("Failed to update contact request status", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to accept contact request")
	}

	// Publish contact request accepted event via NATS
	s.publishContactRequestEvent(req.UserId, contact.RequesterID, "accepted")

	return &contactv1.AcceptContactRequestResponse{
		ApiResponse: createSuccessResponse("Contact request accepted successfully"),
	}, nil
}

// RejectContactRequest implements the RejectContactRequest gRPC method
func (s *Service) RejectContactRequest(ctx context.Context, req *contactv1.RejectContactRequestRequest) (*contactv1.RejectContactRequestResponse, error) {
	s.logger.Info("RejectContactRequest called",
		zap.String("user_id", req.UserId),
		zap.String("request_id", req.RequestId))

	contact, err := s.getAndValidateRequest(ctx, req.RequestId, req.UserId, "recipient")
	if err != nil {
		return nil, err
	}

	// Update the contact request status to declined
	err = s.repository.UpdateContactStatus(ctx, req.RequestId, StatusDeclined)
	if err != nil {
		s.logger.Error("Failed to update contact request status", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to reject contact request")
	}

	// Publish contact rejected event via NATS
	s.publishContactRequestEvent(req.UserId, contact.RequesterID, "rejected")

	return &contactv1.RejectContactRequestResponse{
		ApiResponse: createSuccessResponse("Contact request rejected successfully"),
	}, nil
}

// CancelContactRequest implements the CancelContactRequest gRPC method
func (s *Service) CancelContactRequest(ctx context.Context, req *contactv1.CancelContactRequestRequest) (*contactv1.CancelContactRequestResponse, error) {
	s.logger.Info("CancelContactRequest called",
		zap.String("user_id", req.UserId),
		zap.String("request_id", req.RequestId))

	// Use the validation helper for consistency and cleaner code
	contact, err := s.getAndValidateRequest(ctx, req.RequestId, req.UserId, "requester")
	if err != nil {
		// The gRPC status error is already created by the helper
		return nil, err
	}

	// Delete the contact request
	err = s.repository.DeleteContact(ctx, req.RequestId)
	if err != nil {
		s.logger.Error("Failed to delete contact request", zap.Error(err))
		return &contactv1.CancelContactRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to cancel contact request"),
		}, status.Errorf(codes.Internal, "failed to cancel contact request")
	}

	// Publish contact cancelled event via NATS
	s.publishContactRequestEvent(req.UserId, contact.RecipientID, "cancelled")

	return &contactv1.CancelContactRequestResponse{
		ApiResponse: createSuccessResponse("Contact request cancelled successfully"),
	}, nil
}

// RemoveContact implements the RemoveContact gRPC method
func (s *Service) RemoveContact(ctx context.Context, req *contactv1.RemoveContactRequest) (*contactv1.RemoveContactResponse, error) {
	s.logger.Info("RemoveContact called",
		zap.String("user_id", req.UserId),
		zap.String("contact_id", req.ContactId))

	if req.UserId == "" || req.ContactId == "" {
		return &contactv1.RemoveContactResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and Contact ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and contact ID are required")
	}

	// Remove the contact relationship from the database
	err := s.repository.DeleteContactBetweenUsers(ctx, req.UserId, req.ContactId)
	if err != nil {
		s.logger.Error("Failed to remove contact relationship", zap.Error(err))
		return &contactv1.RemoveContactResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to remove contact"),
		}, status.Errorf(codes.Internal, "Failed to remove contact")
	}

	// Publish contact removed event via NATS
	s.publishContactRequestEvent(req.UserId, req.ContactId, "removed")

	return &contactv1.RemoveContactResponse{
		ApiResponse: createSuccessResponse("Contact removed successfully"),
	}, nil
}

// BlockContact implements the BlockContact gRPC method
// DEPRECATED: Block/unblock functionality has been moved to the User service
// This method now returns an error directing users to use the User service instead
func (s *Service) BlockContact(ctx context.Context, req *contactv1.BlockContactRequest) (*contactv1.BlockContactResponse, error) {
	s.logger.Warn("BlockContact called - this method is deprecated",
		zap.String("user_id", req.UserId),
		zap.String("contact_id", req.ContactId))

	return &contactv1.BlockContactResponse{
		ApiResponse: createErrorResponse("DEPRECATED", "Block functionality has been moved to User service. Please use BlockUser instead."),
	}, status.Errorf(codes.Unimplemented, "block functionality has been moved to User service")
}

// UnblockContact implements the UnblockContact gRPC method
// DEPRECATED: Block/unblock functionality has been moved to the User service
// This method now returns an error directing users to use the User service instead
func (s *Service) UnblockContact(ctx context.Context, req *contactv1.UnblockContactRequest) (*contactv1.UnblockContactResponse, error) {
	s.logger.Warn("UnblockContact called - this method is deprecated",
		zap.String("user_id", req.UserId),
		zap.String("contact_id", req.ContactId))

	return &contactv1.UnblockContactResponse{
		ApiResponse: createErrorResponse("DEPRECATED", "Unblock functionality has been moved to User service. Please use UnblockUser instead."),
	}, status.Errorf(codes.Unimplemented, "unblock functionality has been moved to User service")
}

// GetUserContacts implements the GetUserContacts gRPC method
func (s *Service) GetUserContacts(ctx context.Context, req *contactv1.GetUserContactsRequest) (*contactv1.GetUserContactsResponse, error) {
	s.logger.Info("GetUserContacts called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &contactv1.GetUserContactsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get accepted contacts for the user
	richContacts, err := s.repository.GetRichContactsByUser(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user contacts", zap.Error(err))
		return &contactv1.GetUserContactsResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve contacts"),
		}, status.Errorf(codes.Internal, "failed to retrieve contacts")
	}

	// Convert the database models to the protobuf message type
	protoContacts := make([]*contactv1.ContactWithUser, 0, len(richContacts))
	for _, richContact := range richContacts {
		// Create the protobuf message for the user part of the contact
		protoUser := &commonv1.User{
			Id:        richContact.OtherUserID,
			Username:  richContact.OtherUsername,
			FirstName: richContact.OtherFirstName,
			LastName:  richContact.OtherLastName,
		}
		if richContact.OtherAvatarURL != nil {
			protoUser.AvatarUrl = richContact.OtherAvatarURL
		}

		// Create the contact part
		protoContact := &commonv1.Contact{
			Id:            richContact.ContactID,
			UserId:        req.UserId,
			ContactUserId: richContact.OtherUserID,
			Status:        commonv1.ContactStatus_CONTACT_STATUS_ACCEPTED,
			CreatedAt:     timestamppb.New(richContact.CreatedAt),
		}
		if richContact.AcceptedAt != nil {
			protoContact.UpdatedAt = timestamppb.New(*richContact.AcceptedAt)
		}

		// Create the main ContactWithUser message
		protoContactWithUser := &contactv1.ContactWithUser{
			Contact: protoContact,
			User:    protoUser,
			// Note: mutual_bubbles and mutual_friends would need additional queries
			// For now, setting to 0 as placeholders
			MutualBubbles: 0,
			MutualFriends: 0,
		}
		protoContacts = append(protoContacts, protoContactWithUser)
	}

	return &contactv1.GetUserContactsResponse{
		Contacts:    protoContacts,
		Pagination:  &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(richContacts))},
		ApiResponse: createSuccessResponse("Contacts retrieved successfully"),
	}, nil
}

// GetContactRequests implements the GetContactRequests gRPC method
func (s *Service) GetContactRequests(ctx context.Context, req *contactv1.GetContactRequestsRequest) (*contactv1.GetContactRequestsResponse, error) {
	s.logger.Info("GetContactRequests called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &contactv1.GetContactRequestsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get received contact requests for the user
	richContacts, err := s.repository.GetRichReceivedContactRequests(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get contact requests", zap.Error(err))
		return &contactv1.GetContactRequestsResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve contact requests"),
		}, status.Errorf(codes.Internal, "failed to retrieve contact requests")
	}

	// Convert the database models to the protobuf message type
	protoRequests := make([]*contactv1.ContactRequestWithUser, 0, len(richContacts))
	for _, richContact := range richContacts {
		// Create the protobuf message for the user part (the requester)
		protoUser := &commonv1.User{
			Id:        richContact.OtherUserID,
			Username:  richContact.OtherUsername,
			FirstName: richContact.OtherFirstName,
			LastName:  richContact.OtherLastName,
		}
		if richContact.OtherAvatarURL != nil {
			protoUser.AvatarUrl = richContact.OtherAvatarURL
		}

		// Convert status string to protobuf enum
		var status commonv1.ContactStatus
		switch richContact.Status {
		case "pending":
			status = commonv1.ContactStatus_CONTACT_STATUS_PENDING
		case "accepted":
			status = commonv1.ContactStatus_CONTACT_STATUS_ACCEPTED
		case "declined":
			status = commonv1.ContactStatus_CONTACT_STATUS_REJECTED
		default:
			status = commonv1.ContactStatus_CONTACT_STATUS_UNSPECIFIED
		}

		// Create the main ContactRequestWithUser message
		protoRequest := &contactv1.ContactRequestWithUser{
			Id:         richContact.ContactID,
			FromUserId: richContact.OtherUserID,
			ToUserId:   req.UserId,
			FromUser:   protoUser,
			Status:     status,
			CreatedAt:  timestamppb.New(richContact.CreatedAt),
			UpdatedAt:  timestamppb.New(richContact.CreatedAt), // Use created_at as fallback
		}
		if richContact.Message != nil {
			protoRequest.Message = *richContact.Message
		}

		protoRequests = append(protoRequests, protoRequest)
	}

	return &contactv1.GetContactRequestsResponse{
		Requests:    protoRequests,
		Pagination:  &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(richContacts))},
		ApiResponse: createSuccessResponse("Contact requests retrieved successfully"),
	}, nil
}

// GetContact implements the GetContact gRPC method
func (s *Service) GetContact(ctx context.Context, req *contactv1.GetContactRequest) (*contactv1.GetContactResponse, error) {
	s.logger.Info("GetContact called",
		zap.String("user_id", req.UserId),
		zap.String("contact_id", req.ContactId))

	if req.UserId == "" || req.ContactId == "" {
		return &contactv1.GetContactResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and Contact ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and contact ID are required")
	}

	// Return empty response for now - repository implementation needed
	return &contactv1.GetContactResponse{
		Contact:     nil,
		ApiResponse: createErrorResponse("NOT_FOUND", "Contact not found"),
	}, status.Errorf(codes.NotFound, "contact not found")
}

// SearchContacts implements the SearchContacts gRPC method
func (s *Service) SearchContacts(ctx context.Context, req *contactv1.SearchContactsRequest) (*contactv1.SearchContactsResponse, error) {
	s.logger.Info("SearchContacts called",
		zap.String("user_id", req.UserId),
		zap.String("query", req.Query))

	if req.UserId == "" {
		return &contactv1.SearchContactsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Create search filter
	filter := ContactSearchFilter{
		RequesterUserID: req.UserId,
		Query:           req.Query,
		Limit:           int(req.PageSize),
		Offset:          int((req.Page - 1) * req.PageSize),
		IncludeBlocked:  false, // Don't include blocked users in search
	}

	// Perform contact search
	searchResults, err := s.repository.SearchContacts(ctx, filter)
	if err != nil {
		s.logger.Error("Failed to search contacts", zap.Error(err))
		return &contactv1.SearchContactsResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to search contacts"),
		}, status.Errorf(codes.Internal, "failed to search contacts")
	}

	// Convert the search results to the protobuf message type
	protoContacts := make([]*contactv1.ContactWithUser, 0, len(searchResults))
	for _, result := range searchResults {
		// Create the protobuf message for the user
		protoUser := &commonv1.User{
			Id:        result.ID,
			Username:  result.Username,
			FirstName: result.FirstName,
			LastName:  result.LastName,
			IsOnline:  result.IsOnline,
		}
		if result.AvatarURL != nil {
			protoUser.AvatarUrl = result.AvatarURL
		}

		// Convert relationship type to contact status
		var contactStatus commonv1.ContactStatus
		switch result.RelationshipType {
		case "contact":
			contactStatus = commonv1.ContactStatus_CONTACT_STATUS_ACCEPTED
		case "pending":
			contactStatus = commonv1.ContactStatus_CONTACT_STATUS_PENDING
		case "blocked":
			contactStatus = commonv1.ContactStatus_CONTACT_STATUS_BLOCKED
		default:
			contactStatus = commonv1.ContactStatus_CONTACT_STATUS_UNSPECIFIED
		}

		// Create a contact object (may be nil for non-contacts)
		var protoContact *commonv1.Contact
		if result.RelationshipType != "none" {
			protoContact = &commonv1.Contact{
				Id:            "", // Would need additional query to get contact ID
				UserId:        req.UserId,
				ContactUserId: result.ID,
				Status:        contactStatus,
				CreatedAt:     timestamppb.Now(), // Placeholder
			}
		}

		// Create the main ContactWithUser message
		protoContactWithUser := &contactv1.ContactWithUser{
			Contact:       protoContact,
			User:          protoUser,
			MutualBubbles: 0, // Would need additional query
			MutualFriends: 0, // Would need additional query
		}
		protoContacts = append(protoContacts, protoContactWithUser)
	}

	return &contactv1.SearchContactsResponse{
		Contacts:    protoContacts,
		Pagination:  &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(searchResults))},
		ApiResponse: createSuccessResponse("Contact search completed successfully"),
	}, nil
}

// GetContactSuggestions implements the GetContactSuggestions gRPC method
func (s *Service) GetContactSuggestions(ctx context.Context, req *contactv1.GetContactSuggestionsRequest) (*contactv1.GetContactSuggestionsResponse, error) {
	s.logger.Info("GetContactSuggestions called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &contactv1.GetContactSuggestionsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Return empty list for now - repository implementation needed
	return &contactv1.GetContactSuggestionsResponse{
		Suggestions: []*contactv1.ContactSuggestion{},
		ApiResponse: createSuccessResponse("Contact suggestions retrieved successfully"),
	}, nil
}
