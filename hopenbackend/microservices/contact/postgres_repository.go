package contact

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/database"

	"github.com/jackc/pgx/v5"
)

// PostgreSQLRepository implements contact operations using PostgreSQL
type PostgreSQLRepository struct {
	db     *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for contacts
func NewPostgreSQLRepository(db *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		db:     db,
		logger: logger,
	}
}

// Contact represents a contact relationship in PostgreSQL
type Contact struct {
	ID          string     `json:"id"`
	RequesterID string     `json:"requester_id"`
	RecipientID string     `json:"recipient_id"`
	Status      string     `json:"status"` // pending, accepted, declined
	Message     *string    `json:"message,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	AcceptedAt  *time.Time `json:"accepted_at,omitempty"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// ContactSearchFilter represents search criteria for contacts
type ContactSearchFilter struct {
	RequesterUserID   string   `json:"requester_user_id"`
	Query             string   `json:"query"`
	Limit             int      `json:"limit"`
	Offset            int      `json:"offset"`
	IncludeBlocked    bool     `json:"include_blocked"`
	BubbleStatuses    []string `json:"bubble_statuses"`
	RelationshipTypes []string `json:"relationship_types"`
}

// ContactSearchResult represents a user in search results
type ContactSearchResult struct {
	ID               string     `json:"id"`
	Username         string     `json:"username"`
	FirstName        string     `json:"first_name"`
	LastName         string     `json:"last_name"`
	DisplayName      string     `json:"display_name"`
	AvatarBucketName string     `json:"avatar_bucket_name"`
	AvatarObjectKey  string     `json:"avatar_object_key"`
	AvatarURL        *string    `json:"avatar_url"`
	IsPresent        bool       `json:"is_present"`
	IsOnline         bool       `json:"is_online"`
	RelationshipType string     `json:"relationship_type"` // "none", "contact", "friend", "blocked"
	BubbleStatus     string     `json:"bubble_status"`     // "no_bubble", "not_full_bubble", "full_bubble"
	LastActiveAt     *time.Time `json:"last_active_at"`
}

// CreateContact creates a new contact request
func (r *PostgreSQLRepository) CreateContact(ctx context.Context, contact *Contact) error {
	contact.ID = uuid.New().String()
	contact.CreatedAt = time.Now()
	contact.UpdatedAt = time.Now()

	query := `
		INSERT INTO contacts (id, requester_id, recipient_id, status, message, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := r.db.Exec(ctx, query,
		contact.ID,
		contact.RequesterID,
		contact.RecipientID,
		contact.Status,
		contact.Message,
		contact.CreatedAt,
		contact.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create contact", zap.Error(err))
		return database.HandlePgxError(err, "contacts")
	}

	return nil
}

// GetContactByID retrieves a contact by ID
func (r *PostgreSQLRepository) GetContactByID(ctx context.Context, contactID string) (*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE id = $1`

	var contact Contact
	var acceptedAt sql.NullTime

	err := r.db.QueryRow(ctx, query, contactID).Scan(
		&contact.ID,
		&contact.RequesterID,
		&contact.RecipientID,
		&contact.Status,
		&contact.Message,
		&contact.CreatedAt,
		&acceptedAt,
		&contact.UpdatedAt,
	)

	if err != nil {
		return nil, database.HandlePgxError(err, "contacts")
	}

	if acceptedAt.Valid {
		contact.AcceptedAt = &acceptedAt.Time
	}

	return &contact, nil
}

// UpdateContactStatus updates the status of a contact request
func (r *PostgreSQLRepository) UpdateContactStatus(ctx context.Context, contactID, status string) error {
	now := time.Now()
	var acceptedAt *time.Time
	if status == "accepted" {
		acceptedAt = &now
	}

	query := `
		UPDATE contacts 
		SET status = $1, accepted_at = $2, updated_at = $3
		WHERE id = $4`

	result, err := r.db.Exec(ctx, query, status, acceptedAt, now, contactID)
	if err != nil {
		return fmt.Errorf("failed to update contact status: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("contact not found")
	}

	return nil
}

// ContactRelationshipExists checks if a contact relationship already exists between two users
func (r *PostgreSQLRepository) ContactRelationshipExists(ctx context.Context, userID1, userID2 string) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM contacts
			WHERE ((requester_id = $1 AND recipient_id = $2) OR 
			       (requester_id = $2 AND recipient_id = $1))
			AND status IN ('pending', 'accepted')
		)`

	var exists bool
	err := r.db.QueryRow(ctx, query, userID1, userID2).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check contact relationship existence: %w", err)
	}

	return exists, nil
}

// GetContactsByUser retrieves all contact relationships for a user
func (r *PostgreSQLRepository) GetContactsByUser(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE (requester_id = $1 OR recipient_id = $1)
		AND status IN ('accepted', 'pending')
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query contacts: %w", err)
	}
	defer rows.Close()

	var contacts []*Contact
	for rows.Next() {
		var contact Contact
		var acceptedAt sql.NullTime

		err := rows.Scan(
			&contact.ID,
			&contact.RequesterID,
			&contact.RecipientID,
			&contact.Status,
			&contact.Message,
			&contact.CreatedAt,
			&acceptedAt,
			&contact.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan contact row", zap.Error(err))
			continue
		}

		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}

		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// RichContact represents a contact with full user details for the other party.
type RichContact struct {
	ContactID      string     `json:"contact_id"`
	Status         string     `json:"status"`
	Message        *string    `json:"message"`
	CreatedAt      time.Time  `json:"created_at"`
	AcceptedAt     *time.Time `json:"accepted_at"`
	OtherUserID    string     `json:"other_user_id"`
	OtherUsername  string     `json:"other_username"`
	OtherFirstName string     `json:"other_first_name"`
	OtherLastName  string     `json:"other_last_name"`
	OtherAvatarURL *string    `json:"other_avatar_url"`
}

// GetRichContactsByUser retrieves all accepted contacts for a user with full profile data in a single query.
func (r *PostgreSQLRepository) GetRichContactsByUser(ctx context.Context, userID string) ([]*RichContact, error) {
	query := `
		SELECT
			c.id, c.status, c.message, c.created_at, c.accepted_at,
			other_user.id, other_user.username, other_user.first_name, other_user.last_name, other_user.avatar_url
		FROM contacts c
		JOIN users other_user ON other_user.id = (CASE WHEN c.requester_id = $1 THEN c.recipient_id ELSE c.requester_id END)
		WHERE ($1 IN (c.requester_id, c.recipient_id))
		AND c.status = 'accepted'
		ORDER BY other_user.first_name, other_user.last_name;
	`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query rich contacts: %w", err)
	}
	defer rows.Close()

	var contacts []*RichContact
	for rows.Next() {
		var contact RichContact
		var acceptedAt sql.NullTime

		err := rows.Scan(
			&contact.ContactID, &contact.Status, &contact.Message, &contact.CreatedAt, &acceptedAt,
			&contact.OtherUserID, &contact.OtherUsername, &contact.OtherFirstName, &contact.OtherLastName, &contact.OtherAvatarURL,
		)
		if err != nil {
			r.logger.Error("Failed to scan rich contact row", zap.Error(err))
			continue
		}

		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}
		contacts = append(contacts, &contact)
	}
	return contacts, nil
}

// GetSentContactRequests retrieves pending contact requests sent by a user
func (r *PostgreSQLRepository) GetSentContactRequests(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE requester_id = $1 AND status = 'pending'
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query sent contact requests: %w", err)
	}
	defer rows.Close()

	var contacts []*Contact
	for rows.Next() {
		var contact Contact
		var acceptedAt sql.NullTime

		err := rows.Scan(
			&contact.ID,
			&contact.RequesterID,
			&contact.RecipientID,
			&contact.Status,
			&contact.Message,
			&contact.CreatedAt,
			&acceptedAt,
			&contact.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan contact row", zap.Error(err))
			continue
		}

		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}

		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// GetReceivedContactRequests retrieves contact requests received by a user
func (r *PostgreSQLRepository) GetReceivedContactRequests(ctx context.Context, userID string) ([]*Contact, error) {
	query := `
		SELECT id, requester_id, recipient_id, status, message, created_at, accepted_at, updated_at
		FROM contacts
		WHERE recipient_id = $1 AND status = 'pending'
		ORDER BY created_at DESC`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query received contact requests: %w", err)
	}
	defer rows.Close()

	var contacts []*Contact
	for rows.Next() {
		var contact Contact
		var acceptedAt sql.NullTime

		err := rows.Scan(
			&contact.ID,
			&contact.RequesterID,
			&contact.RecipientID,
			&contact.Status,
			&contact.Message,
			&contact.CreatedAt,
			&acceptedAt,
			&contact.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan contact row", zap.Error(err))
			continue
		}

		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}

		contacts = append(contacts, &contact)
	}

	return contacts, nil
}

// GetRichSentContactRequests retrieves pending contact requests sent by a user with recipient profile data.
func (r *PostgreSQLRepository) GetRichSentContactRequests(ctx context.Context, userID string) ([]*RichContact, error) {
	query := `
		SELECT
			c.id, c.status, c.message, c.created_at, c.accepted_at,
			recipient.id, recipient.username, recipient.first_name, recipient.last_name, recipient.avatar_url
		FROM contacts c
		JOIN users recipient ON c.recipient_id = recipient.id
		WHERE c.requester_id = $1 AND c.status = 'pending'
		ORDER BY c.created_at DESC;
	`
	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query rich sent requests: %w", err)
	}
	defer rows.Close()
	return r.scanRichContacts(rows)
}

// GetRichReceivedContactRequests retrieves pending contact requests received by a user with requester profile data.
func (r *PostgreSQLRepository) GetRichReceivedContactRequests(ctx context.Context, userID string) ([]*RichContact, error) {
	query := `
		SELECT
			c.id, c.status, c.message, c.created_at, c.accepted_at,
			requester.id, requester.username, requester.first_name, requester.last_name, requester.avatar_url
		FROM contacts c
		JOIN users requester ON c.requester_id = requester.id
		WHERE c.recipient_id = $1 AND c.status = 'pending'
		ORDER BY c.created_at DESC;
	`
	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query rich received requests: %w", err)
	}
	defer rows.Close()
	return r.scanRichContacts(rows)
}

// scanRichContacts is a helper to scan rows into a slice of RichContact, reducing code duplication.
func (r *PostgreSQLRepository) scanRichContacts(rows pgx.Rows) ([]*RichContact, error) {
	var contacts []*RichContact
	for rows.Next() {
		var contact RichContact
		var acceptedAt sql.NullTime
		err := rows.Scan(
			&contact.ContactID, &contact.Status, &contact.Message, &contact.CreatedAt, &acceptedAt,
			&contact.OtherUserID, &contact.OtherUsername, &contact.OtherFirstName, &contact.OtherLastName, &contact.OtherAvatarURL,
		)
		if err != nil {
			r.logger.Error("Failed to scan rich contact row", zap.Error(err))
			continue
		}
		if acceptedAt.Valid {
			contact.AcceptedAt = &acceptedAt.Time
		}
		contacts = append(contacts, &contact)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return contacts, nil
}

// GetMutualContacts retrieves mutual contacts between two users
func (r *PostgreSQLRepository) GetMutualContacts(ctx context.Context, userID1, userID2 string) ([]string, error) {
	query := `
		WITH user1_contacts AS (
			SELECT CASE
				WHEN requester_id = $1 THEN recipient_id
				ELSE requester_id
			END AS contact_id
			FROM contacts
			WHERE (requester_id = $1 OR recipient_id = $1) AND status = 'accepted'
		),
		user2_contacts AS (
			SELECT CASE
				WHEN requester_id = $2 THEN recipient_id
				ELSE requester_id
			END AS contact_id
			FROM contacts
			WHERE (requester_id = $2 OR recipient_id = $2) AND status = 'accepted'
		)
		SELECT u1.contact_id
		FROM user1_contacts u1
		INNER JOIN user2_contacts u2 ON u1.contact_id = u2.contact_id`

	rows, err := r.db.Query(ctx, query, userID1, userID2)
	if err != nil {
		return nil, fmt.Errorf("failed to query mutual contacts: %w", err)
	}
	defer rows.Close()

	var contactIDs []string
	for rows.Next() {
		var contactID string
		if err := rows.Scan(&contactID); err != nil {
			r.logger.Error("Failed to scan mutual contact row", zap.Error(err))
			continue
		}
		contactIDs = append(contactIDs, contactID)
	}

	return contactIDs, nil
}

// DeleteContact deletes a contact request (for cancellation)
func (r *PostgreSQLRepository) DeleteContact(ctx context.Context, contactID string) error {
	query := `DELETE FROM contacts WHERE id = $1`

	result, err := r.db.Exec(ctx, query, contactID)
	if err != nil {
		return fmt.Errorf("failed to delete contact: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("contact not found")
	}

	return nil
}

// DeleteContactBetweenUsers removes the contact relationship between two users
func (r *PostgreSQLRepository) DeleteContactBetweenUsers(ctx context.Context, userID1, userID2 string) error {
	// We don't hard-delete. We find the 'accepted' contact and set its status to 'declined'.
	// This preserves history while effectively removing the contact relationship.
	query := `
		UPDATE contacts
		SET status = 'declined', updated_at = NOW()
		WHERE status = 'accepted' AND
			((requester_id = $1 AND recipient_id = $2) OR (requester_id = $2 AND recipient_id = $1))`

	result, err := r.db.Exec(ctx, query, userID1, userID2)
	if err != nil {
		return fmt.Errorf("failed to remove contact: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("active contact not found between users")
	}

	return nil
}

// SearchContacts performs simplified contact search respecting microservice boundaries
// Note: This is a refactored version that only handles contact-related data.
// For complex cross-service queries (bubble status, etc.), use API Gateway aggregation.
func (r *PostgreSQLRepository) SearchContacts(ctx context.Context, filter ContactSearchFilter) ([]ContactSearchResult, error) {
	// Simplified query that only accesses contact-related data and basic user info
	// This respects microservice boundaries by not directly querying bubble or other service tables
	query := `
		WITH contact_relationships AS (
			SELECT
				CASE
					WHEN c.requester_id = $1 THEN c.recipient_id
					ELSE c.requester_id
				END as other_user_id,
				CASE
					WHEN c.status = 'accepted' THEN 'contact'
					WHEN c.status = 'pending' AND c.requester_id = $1 THEN 'pending_sent'
					WHEN c.status = 'pending' AND c.recipient_id = $1 THEN 'pending_received'
					WHEN c.status = 'blocked' THEN 'blocked'
					ELSE 'none'
				END as relationship_type
			FROM contacts c
			WHERE (c.requester_id = $1 OR c.recipient_id = $1)
			AND c.status IN ('accepted', 'pending', 'blocked')
		)
		SELECT
			u.id,
			u.username,
			u.first_name,
			u.last_name,
			u.display_name,
			u.avatar_bucket_name,
			u.avatar_object_key,
			u.avatar_url,
			u.is_present,
			u.last_active_at,
			COALESCE(cr.relationship_type, 'none') as relationship_type,
			CASE
				WHEN u.is_present = true
				AND u.last_active_at > NOW() - INTERVAL '5 minutes'
				THEN true
				ELSE false
			END as is_online
		FROM users u
		LEFT JOIN contact_relationships cr ON u.id = cr.other_user_id
		WHERE u.is_active = true
		AND u.is_banned = false
		AND u.id != $1  -- Exclude the requester
	`

	args := []interface{}{filter.RequesterUserID}
	argIndex := 2

	// Add text search filter if query provided
	if filter.Query != "" {
		query += fmt.Sprintf(` AND (
			u.first_name ILIKE $%d OR
			u.last_name ILIKE $%d OR
			u.username ILIKE $%d OR
			u.display_name ILIKE $%d OR
			(u.first_name || ' ' || u.last_name) ILIKE $%d
		)`, argIndex, argIndex, argIndex, argIndex, argIndex)
		searchTerm := "%" + filter.Query + "%"
		args = append(args, searchTerm)
		argIndex++
	}

	// Note: Bubble status filtering has been removed to respect microservice boundaries.
	// For bubble-related filtering, use API Gateway aggregation or a dedicated search service.

	// Add relationship type filter
	if len(filter.RelationshipTypes) > 0 {
		placeholders := make([]string, len(filter.RelationshipTypes))
		for i, relType := range filter.RelationshipTypes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, relType)
			argIndex++
		}
		query += fmt.Sprintf(" AND COALESCE(cr.relationship_type, 'none') IN (%s)", strings.Join(placeholders, ","))
	}

	// Exclude blocked users unless explicitly requested
	if !filter.IncludeBlocked {
		query += " AND COALESCE(cr.relationship_type, 'none') != 'blocked'"
	}

	// Add ordering for optimal user experience
	query += `
		ORDER BY
			CASE WHEN u.is_present = true THEN 0 ELSE 1 END,
			u.last_active_at DESC,
			u.created_at DESC
	`

	// Add pagination
	if filter.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, filter.Limit)
		argIndex++
	}

	if filter.Offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, filter.Offset)
	}

	r.logger.Debug("Executing contact search query",
		zap.String("query", query),
		zap.Any("args", args))

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute contact search query: %w", err)
	}
	defer rows.Close()

	var results []ContactSearchResult
	for rows.Next() {
		var result ContactSearchResult
		err := rows.Scan(
			&result.ID,
			&result.Username,
			&result.FirstName,
			&result.LastName,
			&result.DisplayName,
			&result.AvatarBucketName,
			&result.AvatarObjectKey,
			&result.AvatarURL,
			&result.IsPresent,
			&result.LastActiveAt,
			&result.RelationshipType,
			&result.IsOnline,
		)
		// Note: BubbleStatus is no longer populated to respect microservice boundaries
		// Use API Gateway aggregation to get bubble status from the bubble service
		result.BubbleStatus = "unknown" // Placeholder value
		if err != nil {
			return nil, fmt.Errorf("failed to scan contact search result: %w", err)
		}
		results = append(results, result)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating contact search results: %w", err)
	}

	r.logger.Info("Contact search completed",
		zap.Int("results_count", len(results)),
		zap.String("query", filter.Query))

	return results, nil
}

// ExpireOldRequests marks old pending contact requests as expired
func (r *PostgreSQLRepository) ExpireOldRequests(ctx context.Context, olderThan time.Time) (int, error) {
	query := `
		UPDATE contacts
		SET status = 'declined', updated_at = NOW()
		WHERE status = 'pending' AND created_at < $1`

	result, err := r.db.Exec(ctx, query, olderThan)
	if err != nil {
		return 0, fmt.Errorf("failed to expire old requests: %w", err)
	}

	return int(result.RowsAffected()), nil
}
