package sync

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// SyncStatus represents sync status in the database
type SyncStatus struct {
	UserID         string    `json:"user_id"`
	LastSyncAt     time.Time `json:"last_sync_at"`
	SyncVersion    int64     `json:"sync_version"`
	DeviceID       string    `json:"device_id"`
	SyncType       string    `json:"sync_type"` // full, incremental
	Status         string    `json:"status"`    // pending, in_progress, completed, failed
	ErrorMessage   *string   `json:"error_message"`
	DataChecksum   string    `json:"data_checksum"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// SyncData represents aggregated sync data for a user
type SyncData struct {
	UserID      string                 `json:"user_id"`
	User        map[string]interface{} `json:"user"`
	Contacts    []map[string]interface{} `json:"contacts"`
	Friends     []map[string]interface{} `json:"friends"`
	Bubbles     []map[string]interface{} `json:"bubbles"`
	Messages    []map[string]interface{} `json:"messages"`
	SyncVersion int64                  `json:"sync_version"`
	GeneratedAt time.Time              `json:"generated_at"`
}

// PostgreSQLRepository handles sync database operations
type PostgreSQLRepository struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for sync
func NewPostgreSQLRepository(pool *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		pool:   pool,
		logger: logger,
	}
}

// UpdateSyncStatus updates or creates sync status for a user
func (r *PostgreSQLRepository) UpdateSyncStatus(ctx context.Context, status *SyncStatus) error {
	query := `
		INSERT INTO sync_status (user_id, last_sync_at, sync_version, device_id, sync_type, status, error_message, data_checksum, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		ON CONFLICT (user_id, device_id) 
		DO UPDATE SET 
			last_sync_at = $2, 
			sync_version = $3, 
			sync_type = $5, 
			status = $6, 
			error_message = $7, 
			data_checksum = $8, 
			updated_at = $9`

	_, err := r.pool.Exec(ctx, query,
		status.UserID, status.LastSyncAt, status.SyncVersion, status.DeviceID,
		status.SyncType, status.Status, status.ErrorMessage, status.DataChecksum,
		status.UpdatedAt)

	if err != nil {
		r.logger.Error("Failed to update sync status", zap.Error(err))
		return fmt.Errorf("failed to update sync status: %w", err)
	}

	return nil
}

// GetSyncStatus retrieves sync status for a user
func (r *PostgreSQLRepository) GetSyncStatus(ctx context.Context, userID string) (*SyncStatus, error) {
	query := `
		SELECT user_id, last_sync_at, sync_version, device_id, sync_type, status, error_message, data_checksum, updated_at
		FROM sync_status
		WHERE user_id = $1
		ORDER BY updated_at DESC
		LIMIT 1`

	var status SyncStatus
	err := r.pool.QueryRow(ctx, query, userID).Scan(
		&status.UserID, &status.LastSyncAt, &status.SyncVersion, &status.DeviceID,
		&status.SyncType, &status.Status, &status.ErrorMessage, &status.DataChecksum,
		&status.UpdatedAt)

	if err != nil {
		r.logger.Error("Failed to get sync status", zap.Error(err))
		return nil, fmt.Errorf("failed to get sync status: %w", err)
	}

	return &status, nil
}

// GetCompleteSyncData retrieves all sync data for a user
func (r *PostgreSQLRepository) GetCompleteSyncData(ctx context.Context, userID string) (*SyncData, error) {
	syncData := &SyncData{
		UserID:      userID,
		SyncVersion: time.Now().Unix(),
		GeneratedAt: time.Now(),
	}

	// Get user data
	userQuery := `
		SELECT id, username, email, first_name, last_name, avatar_url, is_private, created_at, updated_at
		FROM users
		WHERE id = $1 AND is_active = true`

	var user map[string]interface{}
	row := r.pool.QueryRow(ctx, userQuery, userID)
	
	var id, email string
	var username, firstName, lastName, avatarURL *string
	var isPrivate bool
	var createdAt, updatedAt time.Time

	err := row.Scan(&id, &username, &email, &firstName, &lastName, &avatarURL, &isPrivate, &createdAt, &updatedAt)
	if err != nil {
		r.logger.Error("Failed to get user data for sync", zap.Error(err))
		return nil, fmt.Errorf("failed to get user data: %w", err)
	}

	user = map[string]interface{}{
		"id":         id,
		"email":      email,
		"is_private": isPrivate,
		"created_at": createdAt,
		"updated_at": updatedAt,
	}
	if username != nil {
		user["username"] = *username
	}
	if firstName != nil {
		user["first_name"] = *firstName
	}
	if lastName != nil {
		user["last_name"] = *lastName
	}
	if avatarURL != nil {
		user["avatar_url"] = *avatarURL
	}
	syncData.User = user

	// Get contacts
	contactsQuery := `
		SELECT c.id, c.contact_user_id, c.status, c.created_at, u.username, u.email, u.first_name, u.last_name, u.avatar_url
		FROM contacts c
		JOIN users u ON c.contact_user_id = u.id
		WHERE c.user_id = $1 AND c.status = 'accepted'`

	contactRows, err := r.pool.Query(ctx, contactsQuery, userID)
	if err != nil {
		r.logger.Error("Failed to get contacts for sync", zap.Error(err))
	} else {
		defer contactRows.Close()
		var contacts []map[string]interface{}
		for contactRows.Next() {
			var contactID, contactUserID, status, contactUsername, contactEmail, contactFirstName, contactLastName, contactAvatarURL string
			var contactCreatedAt time.Time
			
			err := contactRows.Scan(&contactID, &contactUserID, &status, &contactCreatedAt,
				&contactUsername, &contactEmail, &contactFirstName, &contactLastName, &contactAvatarURL)
			if err != nil {
				r.logger.Error("Failed to scan contact", zap.Error(err))
				continue
			}

			contact := map[string]interface{}{
				"id":              contactID,
				"contact_user_id": contactUserID,
				"status":          status,
				"created_at":      contactCreatedAt,
				"username":        contactUsername,
				"email":           contactEmail,
				"first_name":      contactFirstName,
				"last_name":       contactLastName,
				"avatar_url":      contactAvatarURL,
			}
			contacts = append(contacts, contact)
		}
		syncData.Contacts = contacts
	}

	// Get friends
	friendsQuery := `
		SELECT f.id, f.friend_user_id, f.status, f.created_at, u.username, u.email, u.first_name, u.last_name, u.avatar_url
		FROM friendships f
		JOIN users u ON f.friend_user_id = u.id
		WHERE f.user_id = $1 AND f.status = 'accepted'`

	friendRows, err := r.pool.Query(ctx, friendsQuery, userID)
	if err != nil {
		r.logger.Error("Failed to get friends for sync", zap.Error(err))
	} else {
		defer friendRows.Close()
		var friends []map[string]interface{}
		for friendRows.Next() {
			var friendID, friendUserID, status, friendUsername, friendEmail, friendFirstName, friendLastName, friendAvatarURL string
			var friendCreatedAt time.Time
			
			err := friendRows.Scan(&friendID, &friendUserID, &status, &friendCreatedAt,
				&friendUsername, &friendEmail, &friendFirstName, &friendLastName, &friendAvatarURL)
			if err != nil {
				r.logger.Error("Failed to scan friend", zap.Error(err))
				continue
			}

			friend := map[string]interface{}{
				"id":             friendID,
				"friend_user_id": friendUserID,
				"status":         status,
				"created_at":     friendCreatedAt,
				"username":       friendUsername,
				"email":          friendEmail,
				"first_name":     friendFirstName,
				"last_name":      friendLastName,
				"avatar_url":     friendAvatarURL,
			}
			friends = append(friends, friend)
		}
		syncData.Friends = friends
	}

	// Get bubbles (user is member of)
	bubblesQuery := `
		SELECT b.id, b.name, b.description, b.is_private, b.max_members, b.current_members, b.created_at, b.updated_at
		FROM bubbles b
		JOIN bubble_members bm ON b.id = bm.bubble_id
		WHERE bm.user_id = $1 AND bm.status = 'active'`

	bubbleRows, err := r.pool.Query(ctx, bubblesQuery, userID)
	if err != nil {
		r.logger.Error("Failed to get bubbles for sync", zap.Error(err))
	} else {
		defer bubbleRows.Close()
		var bubbles []map[string]interface{}
		for bubbleRows.Next() {
			var bubbleID, name, description string
			var isPrivate bool
			var maxMembers, currentMembers int32
			var bubbleCreatedAt, bubbleUpdatedAt time.Time
			
			err := bubbleRows.Scan(&bubbleID, &name, &description, &isPrivate,
				&maxMembers, &currentMembers, &bubbleCreatedAt, &bubbleUpdatedAt)
			if err != nil {
				r.logger.Error("Failed to scan bubble", zap.Error(err))
				continue
			}

			bubble := map[string]interface{}{
				"id":              bubbleID,
				"name":            name,
				"description":     description,
				"is_private":      isPrivate,
				"max_members":     maxMembers,
				"current_members": currentMembers,
				"created_at":      bubbleCreatedAt,
				"updated_at":      bubbleUpdatedAt,
			}
			bubbles = append(bubbles, bubble)
		}
		syncData.Bubbles = bubbles
	}

	// Get recent messages (last 100 messages from user's bubbles)
	messagesQuery := `
		SELECT m.id, m.bubble_id, m.sender_id, m.content, m.type, m.created_at
		FROM messages m
		JOIN bubble_members bm ON m.bubble_id = bm.bubble_id
		WHERE bm.user_id = $1 AND bm.status = 'active' AND m.is_deleted = false
		ORDER BY m.created_at DESC
		LIMIT 100`

	messageRows, err := r.pool.Query(ctx, messagesQuery, userID)
	if err != nil {
		r.logger.Error("Failed to get messages for sync", zap.Error(err))
	} else {
		defer messageRows.Close()
		var messages []map[string]interface{}
		for messageRows.Next() {
			var messageID, bubbleID, senderID, content, messageType string
			var messageCreatedAt time.Time
			
			err := messageRows.Scan(&messageID, &bubbleID, &senderID, &content, &messageType, &messageCreatedAt)
			if err != nil {
				r.logger.Error("Failed to scan message", zap.Error(err))
				continue
			}

			message := map[string]interface{}{
				"id":         messageID,
				"bubble_id":  bubbleID,
				"sender_id":  senderID,
				"content":    content,
				"type":       messageType,
				"created_at": messageCreatedAt,
			}
			messages = append(messages, message)
		}
		syncData.Messages = messages
	}

	return syncData, nil
}

// TriggerForceSync marks a sync as pending for force refresh
func (r *PostgreSQLRepository) TriggerForceSync(ctx context.Context, userID, deviceID string) error {
	status := &SyncStatus{
		UserID:      userID,
		LastSyncAt:  time.Now(),
		SyncVersion: time.Now().Unix(),
		DeviceID:    deviceID,
		SyncType:    "full",
		Status:      "pending",
		UpdatedAt:   time.Now(),
	}

	return r.UpdateSyncStatus(ctx, status)
}
