package sync

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	syncv1 "hopenbackend/protos/gen/sync"
)

// Service handles sync operations using gRPC and implements SyncServiceServer
type Service struct {
	syncv1.UnimplementedSyncServiceServer
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	repository  *PostgreSQLRepository
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
}

// SyncEvent represents a sync-related event for NATS
type SyncEvent struct {
	UserID    string    `json:"user_id"`
	Type      string    `json:"type"`
	Action    string    `json:"action"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the sync service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
}

// NewService creates a new sync service instance
func NewService(deps *Dependencies) *Service {
	// Initialize repository
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	return &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		repository:  repository,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
	}
}

// NewSyncServiceServer creates a new gRPC server for the sync service
func NewSyncServiceServer(service *Service) syncv1.SyncServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishSyncEvent publishes sync events via NATS
func (s *Service) publishSyncEvent(userID, eventType, action string, data any) {
	if s.natsConn == nil {
		return
	}

	event := SyncEvent{
		UserID:    userID,
		Type:      eventType,
		Action:    action,
		Data:      data,
		Timestamp: time.Now(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal sync event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("sync.%s", action)
	if err := s.natsConn.Publish(subject, eventData); err != nil {
		s.logger.Error("Failed to publish sync event", zap.Error(err))
	}
}

// SyncInitialState implements the SyncInitialState gRPC method
func (s *Service) SyncInitialState(ctx context.Context, req *syncv1.SyncInitialStateRequest) (*syncv1.SyncInitialStateResponse, error) {
	s.logger.Info("SyncInitialState called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &syncv1.SyncInitialStateResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get complete sync data from database
	dbSyncData, err := s.repository.GetCompleteSyncData(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get sync data", zap.Error(err))
		return &syncv1.SyncInitialStateResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve sync data"),
		}, status.Errorf(codes.Internal, "failed to retrieve sync data: %v", err)
	}

	// Create user data from database or fallback
	var user *commonv1.User
	if dbSyncData != nil && dbSyncData.User != nil {
		user = &commonv1.User{
			Id:        req.UserId,
			Email:     dbSyncData.User["email"].(string),
			CreatedAt: timestamppb.New(dbSyncData.User["created_at"].(time.Time)),
			UpdatedAt: timestamppb.New(dbSyncData.User["updated_at"].(time.Time)),
		}
		if username, ok := dbSyncData.User["username"].(string); ok {
			user.Username = username
		}
		if firstName, ok := dbSyncData.User["first_name"].(string); ok {
			user.FirstName = firstName
		}
		if lastName, ok := dbSyncData.User["last_name"].(string); ok {
			user.LastName = lastName
		}
	} else {
		user = &commonv1.User{
			Id:        req.UserId,
			Username:  "unknown_user",
			Email:     "<EMAIL>",
			FirstName: "Unknown",
			LastName:  "User",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		}
	}

	syncData := &syncv1.SyncData{
		User: user,
		UserSettings: &syncv1.UserSettings{
			UserId:               req.UserId,
			NotificationsEnabled: true,
			SoundEnabled:         true,
			VibrationEnabled:     true,
			Theme:                "light",
			Language:             "en",
			UpdatedAt:            timestamppb.Now(),
		},
		ActiveBubbles:   []*commonv1.Bubble{},
		Contacts:        []*commonv1.Contact{},
		Friendships:     []*commonv1.Friendship{},
		PendingRequests: &syncv1.PendingRequests{},
		Conversations:   []*syncv1.Conversation{},
		Notifications:   []*commonv1.Notification{},
		Metadata: &syncv1.SyncMetadata{
			SyncTimestamp: timestamppb.Now(),
			SyncVersion:   "1",
			HasMoreData:   false,
			TotalItems:    0,
		},
	}

	// Publish sync completed event via NATS
	s.publishSyncEvent(req.UserId, "sync", "initial_state", map[string]interface{}{
		"sync_version": syncData.Metadata.SyncVersion,
		"last_sync":    req.LastSyncAt,
	})

	return &syncv1.SyncInitialStateResponse{
		Data:        syncData,
		ApiResponse: createSuccessResponse("Initial state synchronized successfully"),
	}, nil
}

// GetSyncStatus implements the GetSyncStatus gRPC method
func (s *Service) GetSyncStatus(ctx context.Context, req *syncv1.GetSyncStatusRequest) (*syncv1.GetSyncStatusResponse, error) {
	s.logger.Info("GetSyncStatus called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &syncv1.GetSyncStatusResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get sync status from database
	dbSyncStatus, err := s.repository.GetSyncStatus(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get sync status", zap.Error(err))
		// Return default status if not found
		dbSyncStatus = &SyncStatus{
			UserID:      req.UserId,
			LastSyncAt:  time.Now().Add(-24 * time.Hour),
			SyncVersion: 0,
			Status:      "completed",
			UpdatedAt:   time.Now(),
		}
	}

	syncStatus := &syncv1.SyncStatus{
		UserId:         dbSyncStatus.UserID,
		LastSyncAt:     timestamppb.New(dbSyncStatus.LastSyncAt),
		SyncVersion:    fmt.Sprintf("%d", dbSyncStatus.SyncVersion),
		IsSyncing:      dbSyncStatus.Status == "in_progress",
		SyncStatus:     dbSyncStatus.Status,
		PendingChanges: 0, // Could be calculated from database
		NextSyncAt:     timestamppb.New(time.Now().Add(time.Hour)),
	}

	return &syncv1.GetSyncStatusResponse{
		Status:      syncStatus,
		ApiResponse: createSuccessResponse("Sync status retrieved successfully"),
	}, nil
}

// ForceSyncRefresh implements the ForceSyncRefresh gRPC method
func (s *Service) ForceSyncRefresh(ctx context.Context, req *syncv1.ForceSyncRefreshRequest) (*syncv1.ForceSyncRefreshResponse, error) {
	s.logger.Info("ForceSyncRefresh called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &syncv1.ForceSyncRefreshResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Trigger force sync refresh in database
	err := s.repository.TriggerForceSync(ctx, req.UserId, "default_device")
	if err != nil {
		s.logger.Error("Failed to trigger force sync", zap.Error(err))
		return &syncv1.ForceSyncRefreshResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to trigger force sync"),
		}, status.Errorf(codes.Internal, "failed to trigger force sync: %v", err)
	}

	// Publish force sync event via NATS
	s.publishSyncEvent(req.UserId, "sync", "force_refresh", map[string]interface{}{
		"requested_at": time.Now().Unix(),
	})

	return &syncv1.ForceSyncRefreshResponse{
		Status: &syncv1.SyncStatus{
			UserId:      req.UserId,
			IsSyncing:   true,
			SyncStatus:  "refreshing",
			LastSyncAt:  timestamppb.Now(),
			SyncVersion: "1",
		},
		ApiResponse: createSuccessResponse("Force sync refresh triggered successfully"),
	}, nil
}
