package social_analytics

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// EnhancedProfile represents enhanced user profile data
type EnhancedProfile struct {
	UserID              string    `json:"user_id"`
	Username            string    `json:"username"`
	Email               string    `json:"email"`
	FirstName           string    `json:"first_name"`
	LastName            string    `json:"last_name"`
	AvatarURL           *string   `json:"avatar_url"`
	Bio                 *string   `json:"bio"`
	Location            *string   `json:"location"`
	Website             *string   `json:"website"`
	IsPrivate           bool      `json:"is_private"`
	IsVerified          bool      `json:"is_verified"`
	FriendsCount        int32     `json:"friends_count"`
	ContactsCount       int32     `json:"contacts_count"`
	BubblesCount        int32     `json:"bubbles_count"`
	MessagesCount       int32     `json:"messages_count"`
	LastActiveAt        time.Time `json:"last_active_at"`
	JoinedAt            time.Time `json:"joined_at"`
	ProfileCompleteness float32   `json:"profile_completeness"`
}

// ProfileAnalytics represents user profile analytics
type ProfileAnalytics struct {
	UserID                string    `json:"user_id"`
	MutualFriends         []string  `json:"mutual_friends"`
	MutualContacts        []string  `json:"mutual_contacts"`
	CommonBubbles         []string  `json:"common_bubbles"`
	InteractionFrequency  float32   `json:"interaction_frequency"`
	LastInteractionAt     time.Time `json:"last_interaction_at"`
	ProfileViewsCount     int32     `json:"profile_views_count"`
	MessagesExchanged     int32     `json:"messages_exchanged"`
	SharedBubblesCount    int32     `json:"shared_bubbles_count"`
	ConnectionStrength    float32   `json:"connection_strength"`
}

// CommonBubble represents a bubble shared between users
type CommonBubble struct {
	BubbleID          string    `json:"bubble_id"`
	BubbleName        string    `json:"bubble_name"`
	BubbleDescription *string   `json:"bubble_description"`
	MemberCount       int32     `json:"member_count"`
	CreatedAt         time.Time `json:"created_at"`
	LastActivityAt    time.Time `json:"last_activity_at"`
}

// ConnectionStrength represents the strength of connection between users
type ConnectionStrength struct {
	UserID                string    `json:"user_id"`
	TargetUserID          string    `json:"target_user_id"`
	OverallStrength       float32   `json:"overall_strength"`
	MutualFriendsScore    float32   `json:"mutual_friends_score"`
	InteractionScore      float32   `json:"interaction_score"`
	SharedBubblesScore    float32   `json:"shared_bubbles_score"`
	TimeBasedScore        float32   `json:"time_based_score"`
	LastCalculatedAt      time.Time `json:"last_calculated_at"`
}

// PostgreSQLRepository handles social analytics database operations
type PostgreSQLRepository struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLRepository creates a new PostgreSQL repository for social analytics
func NewPostgreSQLRepository(pool *pgxpool.Pool, logger *zap.Logger) *PostgreSQLRepository {
	return &PostgreSQLRepository{
		pool:   pool,
		logger: logger,
	}
}

// GetEnhancedProfile retrieves enhanced profile data for a user
func (r *PostgreSQLRepository) GetEnhancedProfile(ctx context.Context, userID string) (*EnhancedProfile, error) {
	query := `
		SELECT 
			u.id, u.username, u.email, u.first_name, u.last_name, u.avatar_url, 
			u.bio, u.location, u.website, u.is_private, u.is_verified, u.created_at,
			COALESCE(f.friends_count, 0) as friends_count,
			COALESCE(c.contacts_count, 0) as contacts_count,
			COALESCE(b.bubbles_count, 0) as bubbles_count,
			COALESCE(up.last_seen, u.created_at) as last_active_at
		FROM users u
		LEFT JOIN (
			SELECT user_id, COUNT(*) as friends_count 
			FROM friendships 
			WHERE status = 'accepted' 
			GROUP BY user_id
		) f ON u.id = f.user_id
		LEFT JOIN (
			SELECT user_id, COUNT(*) as contacts_count 
			FROM contacts 
			WHERE status = 'accepted' 
			GROUP BY user_id
		) c ON u.id = c.user_id
		LEFT JOIN (
			SELECT user_id, COUNT(*) as bubbles_count 
			FROM bubble_members 
			WHERE status = 'active' 
			GROUP BY user_id
		) b ON u.id = b.user_id
		LEFT JOIN user_presence up ON u.id = up.user_id
		WHERE u.id = $1 AND u.is_active = true`

	var profile EnhancedProfile
	var username, firstName, lastName *string
	
	err := r.pool.QueryRow(ctx, query, userID).Scan(
		&profile.UserID, &username, &profile.Email, &firstName, &lastName, &profile.AvatarURL,
		&profile.Bio, &profile.Location, &profile.Website, &profile.IsPrivate, &profile.IsVerified, &profile.JoinedAt,
		&profile.FriendsCount, &profile.ContactsCount, &profile.BubblesCount, &profile.LastActiveAt)

	if err != nil {
		r.logger.Error("Failed to get enhanced profile", zap.Error(err))
		return nil, fmt.Errorf("failed to get enhanced profile: %w", err)
	}

	// Handle nullable fields
	if username != nil {
		profile.Username = *username
	}
	if firstName != nil {
		profile.FirstName = *firstName
	}
	if lastName != nil {
		profile.LastName = *lastName
	}

	// Calculate profile completeness
	profile.ProfileCompleteness = r.calculateProfileCompleteness(&profile)

	return &profile, nil
}

// calculateProfileCompleteness calculates how complete a user's profile is
func (r *PostgreSQLRepository) calculateProfileCompleteness(profile *EnhancedProfile) float32 {
	totalFields := 8.0
	completedFields := 0.0

	if profile.Username != "" {
		completedFields++
	}
	if profile.FirstName != "" {
		completedFields++
	}
	if profile.LastName != "" {
		completedFields++
	}
	if profile.AvatarURL != nil && *profile.AvatarURL != "" {
		completedFields++
	}
	if profile.Bio != nil && *profile.Bio != "" {
		completedFields++
	}
	if profile.Location != nil && *profile.Location != "" {
		completedFields++
	}
	if profile.Website != nil && *profile.Website != "" {
		completedFields++
	}
	if profile.Email != "" {
		completedFields++
	}

	return float32(completedFields / totalFields * 100)
}

// GetProfileAnalytics retrieves analytics data for a user profile
func (r *PostgreSQLRepository) GetProfileAnalytics(ctx context.Context, userID string) (*ProfileAnalytics, error) {
	analytics := &ProfileAnalytics{
		UserID: userID,
	}

	// Get mutual friends (this is a simplified query)
	mutualFriendsQuery := `
		SELECT ARRAY_AGG(DISTINCT friend_user_id) 
		FROM friendships 
		WHERE user_id = $1 AND status = 'accepted'`
	
	var mutualFriends []string
	err := r.pool.QueryRow(ctx, mutualFriendsQuery, userID).Scan(&mutualFriends)
	if err == nil {
		analytics.MutualFriends = mutualFriends
	}

	// Get interaction frequency and other metrics
	metricsQuery := `
		SELECT 
			COALESCE(COUNT(DISTINCT m.id), 0) as messages_count,
			COALESCE(COUNT(DISTINCT bm.bubble_id), 0) as shared_bubbles_count,
			COALESCE(MAX(m.created_at), NOW() - INTERVAL '1 year') as last_interaction
		FROM users u
		LEFT JOIN messages m ON u.id = m.sender_id
		LEFT JOIN bubble_members bm ON u.id = bm.user_id
		WHERE u.id = $1`

	err = r.pool.QueryRow(ctx, metricsQuery, userID).Scan(
		&analytics.MessagesExchanged, &analytics.SharedBubblesCount, &analytics.LastInteractionAt)
	
	if err != nil {
		r.logger.Error("Failed to get profile analytics metrics", zap.Error(err))
		return nil, fmt.Errorf("failed to get profile analytics: %w", err)
	}

	// Calculate interaction frequency (messages per day)
	daysSinceJoin := time.Since(analytics.LastInteractionAt).Hours() / 24
	if daysSinceJoin > 0 {
		analytics.InteractionFrequency = float32(analytics.MessagesExchanged) / float32(daysSinceJoin)
	}

	return analytics, nil
}

// GetMutualFriends retrieves mutual friends between two users
func (r *PostgreSQLRepository) GetMutualFriends(ctx context.Context, userID, targetUserID string) ([]string, error) {
	query := `
		SELECT ARRAY_AGG(DISTINCT f1.friend_user_id)
		FROM friendships f1
		INNER JOIN friendships f2 ON f1.friend_user_id = f2.friend_user_id
		WHERE f1.user_id = $1 AND f2.user_id = $2 
		AND f1.status = 'accepted' AND f2.status = 'accepted'`

	var mutualFriends []string
	err := r.pool.QueryRow(ctx, query, userID, targetUserID).Scan(&mutualFriends)
	if err != nil {
		r.logger.Error("Failed to get mutual friends", zap.Error(err))
		return []string{}, nil // Return empty slice instead of error
	}

	if mutualFriends == nil {
		return []string{}, nil
	}

	return mutualFriends, nil
}

// GetMutualContacts retrieves mutual contacts between two users
func (r *PostgreSQLRepository) GetMutualContacts(ctx context.Context, userID, targetUserID string) ([]string, error) {
	query := `
		SELECT ARRAY_AGG(DISTINCT c1.contact_user_id)
		FROM contacts c1
		INNER JOIN contacts c2 ON c1.contact_user_id = c2.contact_user_id
		WHERE c1.user_id = $1 AND c2.user_id = $2 
		AND c1.status = 'accepted' AND c2.status = 'accepted'`

	var mutualContacts []string
	err := r.pool.QueryRow(ctx, query, userID, targetUserID).Scan(&mutualContacts)
	if err != nil {
		r.logger.Error("Failed to get mutual contacts", zap.Error(err))
		return []string{}, nil // Return empty slice instead of error
	}

	if mutualContacts == nil {
		return []string{}, nil
	}

	return mutualContacts, nil
}

// GetCommonBubbles retrieves bubbles that both users are members of
func (r *PostgreSQLRepository) GetCommonBubbles(ctx context.Context, userID, targetUserID string) ([]*CommonBubble, error) {
	query := `
		SELECT DISTINCT b.id, b.name, b.description, b.current_members, b.created_at, b.updated_at
		FROM bubbles b
		INNER JOIN bubble_members bm1 ON b.id = bm1.bubble_id
		INNER JOIN bubble_members bm2 ON b.id = bm2.bubble_id
		WHERE bm1.user_id = $1 AND bm2.user_id = $2 
		AND bm1.status = 'active' AND bm2.status = 'active'
		ORDER BY b.updated_at DESC`

	rows, err := r.pool.Query(ctx, query, userID, targetUserID)
	if err != nil {
		r.logger.Error("Failed to get common bubbles", zap.Error(err))
		return []*CommonBubble{}, nil
	}
	defer rows.Close()

	var commonBubbles []*CommonBubble
	for rows.Next() {
		var bubble CommonBubble
		err := rows.Scan(&bubble.BubbleID, &bubble.BubbleName, &bubble.BubbleDescription, 
			&bubble.MemberCount, &bubble.CreatedAt, &bubble.LastActivityAt)
		if err != nil {
			r.logger.Error("Failed to scan common bubble", zap.Error(err))
			continue
		}
		commonBubbles = append(commonBubbles, &bubble)
	}

	return commonBubbles, nil
}

// CalculateConnectionStrength calculates the connection strength between two users
func (r *PostgreSQLRepository) CalculateConnectionStrength(ctx context.Context, userID, targetUserID string) (*ConnectionStrength, error) {
	strength := &ConnectionStrength{
		UserID:           userID,
		TargetUserID:     targetUserID,
		LastCalculatedAt: time.Now(),
	}

	// Get mutual friends count
	mutualFriends, _ := r.GetMutualFriends(ctx, userID, targetUserID)
	mutualFriendsCount := len(mutualFriends)
	
	// Get common bubbles count
	commonBubbles, _ := r.GetCommonBubbles(ctx, userID, targetUserID)
	commonBubblesCount := len(commonBubbles)

	// Get interaction count (messages between users)
	interactionQuery := `
		SELECT COUNT(*) 
		FROM messages m
		INNER JOIN bubble_members bm1 ON m.bubble_id = bm1.bubble_id
		INNER JOIN bubble_members bm2 ON m.bubble_id = bm2.bubble_id
		WHERE bm1.user_id = $1 AND bm2.user_id = $2 AND m.sender_id IN ($1, $2)`
	
	var interactionCount int
	err := r.pool.QueryRow(ctx, interactionQuery, userID, targetUserID).Scan(&interactionCount)
	if err != nil {
		interactionCount = 0
	}

	// Calculate scores (normalized to 0-1)
	strength.MutualFriendsScore = float32(mutualFriendsCount) / 100.0 // Normalize assuming max 100 mutual friends
	if strength.MutualFriendsScore > 1.0 {
		strength.MutualFriendsScore = 1.0
	}

	strength.SharedBubblesScore = float32(commonBubblesCount) / 20.0 // Normalize assuming max 20 shared bubbles
	if strength.SharedBubblesScore > 1.0 {
		strength.SharedBubblesScore = 1.0
	}

	strength.InteractionScore = float32(interactionCount) / 1000.0 // Normalize assuming max 1000 interactions
	if strength.InteractionScore > 1.0 {
		strength.InteractionScore = 1.0
	}

	// Time-based score (higher for recent interactions)
	strength.TimeBasedScore = 0.5 // Default moderate score

	// Calculate overall strength (weighted average)
	strength.OverallStrength = (strength.MutualFriendsScore*0.3 + 
		strength.SharedBubblesScore*0.3 + 
		strength.InteractionScore*0.3 + 
		strength.TimeBasedScore*0.1)

	return strength, nil
}
