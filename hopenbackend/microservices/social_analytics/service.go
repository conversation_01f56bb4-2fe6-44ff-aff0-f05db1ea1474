package social_analytics

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	socialanalyticsv1 "hopenbackend/protos/gen/social_analytics"
)

// Service handles social analytics operations using gRPC and implements SocialAnalyticsServiceServer
type Service struct {
	socialanalyticsv1.UnimplementedSocialAnalyticsServiceServer
	logger      *zap.Logger
	db          *database.PostgreSQLClient
	repository  *PostgreSQLRepository
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
}

// SocialAnalyticsEvent represents a social analytics-related event for NATS
type SocialAnalyticsEvent struct {
	UserID    string    `json:"user_id"`
	Type      string    `json:"type"`
	Action    string    `json:"action"`
	Data      any       `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the social analytics service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
}

// NewService creates a new social analytics service instance
func NewService(deps *Dependencies) *Service {
	// Initialize repository
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	return &Service{
		logger:      deps.Logger,
		db:          deps.DB,
		repository:  repository,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
	}
}

// NewSocialAnalyticsServiceServer creates a new gRPC server for the social analytics service
func NewSocialAnalyticsServiceServer(service *Service) socialanalyticsv1.SocialAnalyticsServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishSocialAnalyticsEvent publishes social analytics events via NATS
func (s *Service) publishSocialAnalyticsEvent(userID, eventType, action string, data any) {
	if s.natsConn == nil {
		return
	}

	event := SocialAnalyticsEvent{
		UserID:    userID,
		Type:      eventType,
		Action:    action,
		Data:      data,
		Timestamp: time.Now(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal social analytics event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("social_analytics.%s", action)
	if err := s.natsConn.Publish(subject, eventData); err != nil {
		s.logger.Error("Failed to publish social analytics event", zap.Error(err))
	}
}

// GetEnhancedProfile implements the GetEnhancedProfile gRPC method
func (s *Service) GetEnhancedProfile(ctx context.Context, req *socialanalyticsv1.GetEnhancedProfileRequest) (*socialanalyticsv1.GetEnhancedProfileResponse, error) {
	s.logger.Info("GetEnhancedProfile called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &socialanalyticsv1.GetEnhancedProfileResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get enhanced profile data from database
	dbProfile, err := s.repository.GetEnhancedProfile(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get enhanced profile", zap.Error(err))
		return &socialanalyticsv1.GetEnhancedProfileResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve enhanced profile"),
		}, status.Errorf(codes.Internal, "failed to retrieve enhanced profile: %v", err)
	}

	profile := &socialanalyticsv1.EnhancedProfile{
		UserId:         dbProfile.UserID,
		Username:       dbProfile.Username,
		Email:          dbProfile.Email,
		FirstName:      dbProfile.FirstName,
		LastName:       dbProfile.LastName,
		IsOnline:       time.Since(dbProfile.LastActiveAt) < 5*time.Minute, // Consider online if active within 5 minutes
		FriendIds:      []string{},                                         // Would need separate query to get friend IDs
		ContactIds:     []string{},                                         // Would need separate query to get contact IDs
		BlockedUserIds: []string{},                                         // Would need separate query to get blocked user IDs
	}

	return &socialanalyticsv1.GetEnhancedProfileResponse{
		Profile:     profile,
		ApiResponse: createSuccessResponse("Enhanced profile retrieved successfully"),
	}, nil
}

// GetProfileAnalytics implements the GetProfileAnalytics gRPC method
func (s *Service) GetProfileAnalytics(ctx context.Context, req *socialanalyticsv1.GetProfileAnalyticsRequest) (*socialanalyticsv1.GetProfileAnalyticsResponse, error) {
	s.logger.Info("GetProfileAnalytics called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &socialanalyticsv1.GetProfileAnalyticsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get profile analytics from database
	dbAnalytics, err := s.repository.GetProfileAnalytics(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get profile analytics", zap.Error(err))
		return &socialanalyticsv1.GetProfileAnalyticsResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve profile analytics"),
		}, status.Errorf(codes.Internal, "failed to retrieve profile analytics: %v", err)
	}

	analytics := &socialanalyticsv1.ProfileAnalytics{
		UserId:             dbAnalytics.UserID,
		MutualFriends:      dbAnalytics.MutualFriends,
		MutualContacts:     dbAnalytics.MutualContacts,
		CommonBubbles:      []*socialanalyticsv1.CommonBubble{}, // Would need conversion from DB format
		ConnectionStrength: dbAnalytics.ConnectionStrength,
		SocialScore:        dbAnalytics.InteractionFrequency,
		EngagementMetrics:  &socialanalyticsv1.EngagementMetrics{
			// Fields would be set based on actual proto definition
		},
		RecommendedActions: []string{}, // Would be calculated based on analytics
	}

	return &socialanalyticsv1.GetProfileAnalyticsResponse{
		Analytics:   analytics,
		ApiResponse: createSuccessResponse("Profile analytics retrieved successfully"),
	}, nil
}

// GetMutualFriends implements the GetMutualFriends gRPC method
func (s *Service) GetMutualFriends(ctx context.Context, req *socialanalyticsv1.GetMutualFriendsRequest) (*socialanalyticsv1.GetMutualFriendsResponse, error) {
	s.logger.Info("GetMutualFriends called",
		zap.String("user_id", req.UserId),
		zap.String("target_user_id", req.TargetUserId))

	if req.UserId == "" || req.TargetUserId == "" {
		return &socialanalyticsv1.GetMutualFriendsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and target user ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and target user ID are required")
	}

	// Get mutual friends from database
	mutualFriends, err := s.repository.GetMutualFriends(ctx, req.UserId, req.TargetUserId)
	if err != nil {
		s.logger.Error("Failed to get mutual friends", zap.Error(err))
		return &socialanalyticsv1.GetMutualFriendsResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve mutual friends"),
		}, status.Errorf(codes.Internal, "failed to retrieve mutual friends: %v", err)
	}

	return &socialanalyticsv1.GetMutualFriendsResponse{
		MutualFriends: mutualFriends,
		ApiResponse:   createSuccessResponse("Mutual friends retrieved successfully"),
	}, nil
}

// GetMutualContacts implements the GetMutualContacts gRPC method
func (s *Service) GetMutualContacts(ctx context.Context, req *socialanalyticsv1.GetMutualContactsRequest) (*socialanalyticsv1.GetMutualContactsResponse, error) {
	s.logger.Info("GetMutualContacts called",
		zap.String("user_id", req.UserId),
		zap.String("target_user_id", req.TargetUserId))

	if req.UserId == "" || req.TargetUserId == "" {
		return &socialanalyticsv1.GetMutualContactsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and target user ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and target user ID are required")
	}

	// Get mutual contacts from database
	mutualContacts, err := s.repository.GetMutualContacts(ctx, req.UserId, req.TargetUserId)
	if err != nil {
		s.logger.Error("Failed to get mutual contacts", zap.Error(err))
		return &socialanalyticsv1.GetMutualContactsResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve mutual contacts"),
		}, status.Errorf(codes.Internal, "failed to retrieve mutual contacts: %v", err)
	}

	return &socialanalyticsv1.GetMutualContactsResponse{
		MutualContacts: mutualContacts,
		ApiResponse:    createSuccessResponse("Mutual contacts retrieved successfully"),
	}, nil
}

// GetCommonBubbles implements the GetCommonBubbles gRPC method
func (s *Service) GetCommonBubbles(ctx context.Context, req *socialanalyticsv1.GetCommonBubblesRequest) (*socialanalyticsv1.GetCommonBubblesResponse, error) {
	s.logger.Info("GetCommonBubbles called",
		zap.String("user_id", req.UserId),
		zap.String("target_user_id", req.TargetUserId))

	if req.UserId == "" || req.TargetUserId == "" {
		return &socialanalyticsv1.GetCommonBubblesResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and target user ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and target user ID are required")
	}

	// Get common bubbles from database
	dbCommonBubbles, err := s.repository.GetCommonBubbles(ctx, req.UserId, req.TargetUserId)
	if err != nil {
		s.logger.Error("Failed to get common bubbles", zap.Error(err))
		return &socialanalyticsv1.GetCommonBubblesResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to retrieve common bubbles"),
		}, status.Errorf(codes.Internal, "failed to retrieve common bubbles: %v", err)
	}

	// Convert to proto format
	var commonBubbles []*socialanalyticsv1.CommonBubble
	for _, dbBubble := range dbCommonBubbles {
		protoBubble := &socialanalyticsv1.CommonBubble{
			BubbleId:   dbBubble.BubbleID,
			BubbleName: dbBubble.BubbleName,
			// Only use fields that exist in the proto definition
		}
		commonBubbles = append(commonBubbles, protoBubble)
	}

	return &socialanalyticsv1.GetCommonBubblesResponse{
		CommonBubbles: commonBubbles,
		ApiResponse:   createSuccessResponse("Common bubbles retrieved successfully"),
	}, nil
}

// GetConnectionStrength implements the GetConnectionStrength gRPC method
func (s *Service) GetConnectionStrength(ctx context.Context, req *socialanalyticsv1.GetConnectionStrengthRequest) (*socialanalyticsv1.GetConnectionStrengthResponse, error) {
	s.logger.Info("GetConnectionStrength called",
		zap.String("user_id", req.UserId),
		zap.String("target_user_id", req.TargetUserId))

	if req.UserId == "" || req.TargetUserId == "" {
		return &socialanalyticsv1.GetConnectionStrengthResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and target user ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and target user ID are required")
	}

	// Calculate connection strength from database
	dbStrength, err := s.repository.CalculateConnectionStrength(ctx, req.UserId, req.TargetUserId)
	if err != nil {
		s.logger.Error("Failed to calculate connection strength", zap.Error(err))
		return &socialanalyticsv1.GetConnectionStrengthResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to calculate connection strength"),
		}, status.Errorf(codes.Internal, "failed to calculate connection strength: %v", err)
	}

	strength := &socialanalyticsv1.ConnectionStrength{
		UserId:              dbStrength.UserID,
		TargetUserId:        dbStrength.TargetUserID,
		Strength:            dbStrength.OverallStrength,
		MutualFriendsCount:  int32(dbStrength.MutualFriendsScore * 100), // Convert score to count estimate
		MutualContactsCount: 0,                                          // Would need separate calculation
		CommonBubblesCount:  int32(dbStrength.SharedBubblesScore * 20),  // Convert score to count estimate
		InteractionScore:    dbStrength.InteractionScore,
		Factors:             []string{"mutual_friends", "shared_bubbles", "interactions"}, // Factors considered
	}

	return &socialanalyticsv1.GetConnectionStrengthResponse{
		ConnectionStrength: strength,
		ApiResponse:        createSuccessResponse("Connection strength calculated successfully"),
	}, nil
}
