package auth

import (
	"context"
	"fmt"
	"time"

	"hopenbackend/pkg/database"
)

// Repository handles all database operations for the auth service
type Repository struct {
	db *database.PostgreSQLClient
}

// NewRepository creates a new auth repository
func NewRepository(db *database.PostgreSQLClient) *Repository {
	return &Repository{db: db}
}

// UserInfo represents user information from the database
type UserInfo struct {
	ID          string     `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	FirstName   string     `json:"first_name"`
	LastName    string     `json:"last_name"`
	AvatarURL   *string    `json:"avatar_url"`
	DateOfBirth *time.Time `json:"date_of_birth"`
}

// GetUserByID retrieves user information by ID from the database
func (r *Repository) GetUserByID(ctx context.Context, userID string) (*UserInfo, error) {
	query := `SELECT id, username, email, first_name, last_name, avatar_url FROM users WHERE id = $1`

	var user UserInfo
	err := r.db.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
	)

	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	return &user, nil
}

// CreateUser creates a new user in the database
func (r *Repository) CreateUser(ctx context.Context, userID, username, email, firstName, lastName string) error {
	query := `
		INSERT INTO users (id, username, email, first_name, last_name, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
	`

	_, err := r.db.Pool.Exec(ctx, query, userID, username, email, firstName, lastName)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// UpdateUserProfile updates user profile information
func (r *Repository) UpdateUserProfile(ctx context.Context, userID, username, firstName, lastName, avatarURL string) error {
	query := `
		UPDATE users 
		SET username = $2, first_name = $3, last_name = $4, avatar_url = $5, updated_at = NOW()
		WHERE id = $1
	`

	_, err := r.db.Pool.Exec(ctx, query, userID, username, firstName, lastName, avatarURL)
	if err != nil {
		return fmt.Errorf("failed to update user profile: %w", err)
	}

	return nil
}

// GetUserPermissions retrieves user permissions from the database
func (r *Repository) GetUserPermissions(ctx context.Context, userID string) ([]string, error) {
	query := `
		SELECT DISTINCT p.permission_name 
		FROM user_roles ur 
		JOIN role_permissions rp ON ur.role_id = rp.role_id 
		JOIN permissions p ON rp.permission_id = p.id 
		WHERE ur.user_id = $1 AND ur.status = 'active'
	`

	rows, err := r.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %w", err)
	}
	defer rows.Close()

	var permissions []string
	for rows.Next() {
		var permission string
		if err := rows.Scan(&permission); err != nil {
			return nil, fmt.Errorf("failed to scan permission: %w", err)
		}
		permissions = append(permissions, permission)
	}

	return permissions, nil
}

// GetUserRoles retrieves user roles from the database
func (r *Repository) GetUserRoles(ctx context.Context, userID string) ([]string, error) {
	query := `
		SELECT r.role_name 
		FROM user_roles ur 
		JOIN roles r ON ur.role_id = r.id 
		WHERE ur.user_id = $1 AND ur.status = 'active'
	`

	rows, err := r.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user roles: %w", err)
	}
	defer rows.Close()

	var roles []string
	for rows.Next() {
		var role string
		if err := rows.Scan(&role); err != nil {
			return nil, fmt.Errorf("failed to scan role: %w", err)
		}
		roles = append(roles, role)
	}

	return roles, nil
}

// AssignDefaultRole assigns the default "user" role to a user
func (r *Repository) AssignDefaultRole(ctx context.Context, userID string) error {
	query := `
		INSERT INTO user_roles (user_id, role_id, status, created_at, updated_at)
		SELECT $1, r.id, 'active', NOW(), NOW()
		FROM roles r WHERE r.role_name = 'user'
	`

	_, err := r.db.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to assign default role: %w", err)
	}

	return nil
}

// GetUserActiveBubbles retrieves user's active bubble memberships
func (r *Repository) GetUserActiveBubbles(ctx context.Context, userID string) ([]string, error) {
	query := `
		SELECT bubble_id 
		FROM bubble_members 
		WHERE user_id = $1 AND status = 'active'
	`

	rows, err := r.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user bubbles: %w", err)
	}
	defer rows.Close()

	var bubbleIDs []string
	for rows.Next() {
		var bubbleID string
		if err := rows.Scan(&bubbleID); err != nil {
			return nil, fmt.Errorf("failed to scan bubble ID: %w", err)
		}
		bubbleIDs = append(bubbleIDs, bubbleID)
	}

	return bubbleIDs, nil
}

// CheckBubbleMembership checks if a user is an active member of a bubble
func (r *Repository) CheckBubbleMembership(ctx context.Context, userID, bubbleID string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM bubble_members WHERE bubble_id = $1 AND user_id = $2 AND status = 'active')`
	var exists bool
	if err := r.db.Pool.QueryRow(ctx, query, bubbleID, userID).Scan(&exists); err != nil {
		return false, fmt.Errorf("bubble membership check failed: %w", err)
	}
	return exists, nil
}

// InvalidateUserSessions marks all user sessions as inactive
func (r *Repository) InvalidateUserSessions(ctx context.Context, userID string) error {
	query := `
		UPDATE user_sessions 
		SET status = 'inactive', updated_at = NOW()
		WHERE user_id = $1 AND status = 'active'
	`

	_, err := r.db.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to invalidate user sessions: %w", err)
	}

	return nil
}

// GetUserActiveSessions retrieves active sessions for a user
func (r *Repository) GetUserActiveSessions(ctx context.Context, userID string) ([]string, error) {
	query := `
		SELECT session_id 
		FROM user_sessions 
		WHERE user_id = $1 AND status = 'active' AND expires_at > NOW()
	`

	rows, err := r.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query active sessions: %w", err)
	}
	defer rows.Close()

	var sessionIDs []string
	for rows.Next() {
		var sessionID string
		if err := rows.Scan(&sessionID); err != nil {
			return nil, fmt.Errorf("failed to scan session ID: %w", err)
		}
		sessionIDs = append(sessionIDs, sessionID)
	}

	return sessionIDs, nil
}

// GetUserLastLogin retrieves the last login time for a user
func (r *Repository) GetUserLastLogin(ctx context.Context, userID string) (time.Time, error) {
	query := `
		SELECT MAX(created_at) 
		FROM user_sessions 
		WHERE user_id = $1
	`

	var lastLogin time.Time
	err := r.db.Pool.QueryRow(ctx, query, userID).Scan(&lastLogin)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to get last login: %w", err)
	}

	return lastLogin, nil
}
