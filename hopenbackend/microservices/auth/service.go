package auth

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/ory"
	authv1 "hopenbackend/protos/gen/auth"
	commonv1 "hopenbackend/protos/gen/common"
)

// Service handles authentication operations and implements the gRPC AuthServiceServer interface
type Service struct {
	authv1.UnimplementedAuthServiceServer
	logger     *zap.Logger
	config     *config.Config
	oryClient  *ory.Client
	repository *Repository
}

// Make sure Service implements authv1.AuthServiceServer
var _ authv1.AuthServiceServer = (*Service)(nil)

// Dependencies holds the dependencies for the auth service
type Dependencies struct {
	Logger     *zap.Logger
	Config     *config.Config
	OryClient  *ory.Client
	Repository *Repository
}

// New creates a new auth service instance
func New(deps *Dependencies) *Service {
	return &Service{
		logger:     deps.Logger,
		config:     deps.Config,
		oryClient:  deps.OryClient,
		repository: deps.Repository,
	}
}

// NewAuthServiceServer creates a new gRPC server implementation for the auth service
func NewAuthServiceServer(service *Service) authv1.AuthServiceServer {
	return service
}

// generateSecureToken generates a cryptographically secure random token
func (s *Service) generateSecureToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to convert UserInfo to protobuf UserInfo
func (s *Service) userInfoToProto(user *UserInfo) *authv1.UserInfo {
	protoUser := &authv1.UserInfo{
		Id:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
	}

	if user.AvatarURL != nil {
		protoUser.AvatarUrl = *user.AvatarURL
	}

	if user.DateOfBirth != nil {
		protoUser.DateOfBirth = timestamppb.New(*user.DateOfBirth)
	}

	return protoUser
}

// checkMQTTTopicPermission ensures the user can perform action on topic.
func (s *Service) checkMQTTTopicPermission(userID, topic, action string) bool {
	// Personal topics
	if action == "publish" {
		if topic == fmt.Sprintf("hopen/chat/%s", userID) {
			return true
		}
	}
	if action == "subscribe" {
		// Unified personal notifications & requests topic
		if topic == fmt.Sprintf("hopen/requests/%s", userID) {
			return true
		}
	}
	// Bubble topics: hopen/bubbles/{bubble_id}/chat or /notifications
	if strings.HasPrefix(topic, "hopen/bubbles/") {
		parts := strings.Split(topic, "/")
		if len(parts) >= 3 {
			bubbleID := parts[2]
			isMember, err := s.repository.CheckBubbleMembership(context.Background(), userID, bubbleID)
			if err != nil {
				s.logger.Error("Failed to check bubble membership", zap.Error(err))
				return false
			}
			return isMember
		}
	}
	return false
}

// extractBubbleIDFromTopic is kept for backward compatibility.
func (s *Service) extractBubbleIDFromTopic(topic string) string {
	parts := strings.Split(topic, "/")
	if len(parts) >= 3 && parts[0] == "hopen" && parts[1] == "bubbles" {
		return parts[2]
	}
	return ""
}

// gRPC Method Implementations

// RegisterUser implements the RegisterUser gRPC method
func (s *Service) RegisterUser(ctx context.Context, req *authv1.RegisterUserRequest) (*authv1.RegisterUserResponse, error) {
	s.logger.Info("RegisterUser called",
		zap.String("username", req.Username),
		zap.String("email", req.Email))

	// Validate required fields
	if req.Username == "" || req.Email == "" || req.Password == "" {
		return &authv1.RegisterUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "Username, email, and password are required"),
		}, status.Errorf(codes.InvalidArgument, "username, email, and password are required")
	}

	// Create user in Ory Kratos
	traits := map[string]interface{}{
		"username": req.Username,
		"email":    req.Email,
		"name": map[string]interface{}{
			"first": req.FirstName,
			"last":  req.LastName,
		},
	}

	identity, err := s.oryClient.CreateIdentity(ctx, req.Email, req.Password, traits)
	if err != nil {
		s.logger.Error("Failed to create identity in Ory", zap.Error(err))
		return &authv1.RegisterUserResponse{
			ApiResponse: createErrorResponse("ORY_ERROR", "Failed to create user account"),
		}, status.Errorf(codes.Internal, "failed to create user account: %v", err)
	}

	// Store additional user data in PostgreSQL
	err = s.repository.CreateUser(ctx, identity.Id, req.Username, req.Email, req.FirstName, req.LastName)
	if err != nil {
		s.logger.Error("Failed to insert user into database", zap.Error(err))
		// Try to clean up the Ory identity
		_ = s.oryClient.DeleteIdentity(ctx, identity.Id)
		return &authv1.RegisterUserResponse{
			ApiResponse: createErrorResponse("DATABASE_ERROR", "Failed to create user profile"),
		}, status.Errorf(codes.Internal, "failed to create user profile: %v", err)
	}

	// Assign default role to user
	err = s.repository.AssignDefaultRole(ctx, identity.Id)
	if err != nil {
		s.logger.Error("Failed to assign default role", zap.Error(err))
		// Continue anyway as this is not critical
	}

	// Get user info for response
	userInfo, err := s.repository.GetUserByID(ctx, identity.Id)
	if err != nil {
		s.logger.Error("Failed to get user info", zap.Error(err))
		// Continue with basic info
		userInfo = &UserInfo{
			ID:        identity.Id,
			Username:  req.Username,
			Email:     req.Email,
			FirstName: req.FirstName,
			LastName:  req.LastName,
		}
	}

	// Convert to protobuf format
	protoUser := s.userInfoToProto(userInfo)

	return &authv1.RegisterUserResponse{
		User:        protoUser,
		ApiResponse: createSuccessResponse("User registered successfully"),
	}, nil
}

// ValidateSession implements the ValidateSession gRPC method
func (s *Service) ValidateSession(ctx context.Context, req *authv1.ValidateSessionRequest) (*authv1.ValidateSessionResponse, error) {
	s.logger.Info("ValidateSession called")

	if req.SessionToken == "" {
		return &authv1.ValidateSessionResponse{
			ApiResponse: createErrorResponse("INVALID_TOKEN", "Session token is required"),
		}, status.Errorf(codes.InvalidArgument, "session token is required")
	}

	// Validate session with Ory Kratos
	session, err := s.oryClient.ValidateSession(ctx, req.SessionToken)
	if err != nil {
		s.logger.Error("Session validation failed", zap.Error(err))
		return &authv1.ValidateSessionResponse{
			ApiResponse: createErrorResponse("INVALID_SESSION", "Invalid or expired session"),
		}, status.Errorf(codes.Unauthenticated, "invalid session: %v", err)
	}

	if session == nil || session.Active == nil || !*session.Active {
		return &authv1.ValidateSessionResponse{
			ApiResponse: createErrorResponse("INACTIVE_SESSION", "Session is inactive"),
		}, status.Errorf(codes.Unauthenticated, "session is inactive")
	}

	// Get user permissions and roles
	permissions, err := s.repository.GetUserPermissions(ctx, session.Identity.Id)
	if err != nil {
		s.logger.Error("Failed to get user permissions", zap.Error(err))
		// Continue with empty permissions
		permissions = []string{}
	}

	roles, err := s.repository.GetUserRoles(ctx, session.Identity.Id)
	if err != nil {
		s.logger.Error("Failed to get user roles", zap.Error(err))
		// Continue with empty roles
		roles = []string{}
	}

	// Convert session to protobuf format
	sessionInfo := &authv1.SessionInfo{
		UserId:      session.Identity.Id,
		SessionId:   session.Id,
		IsActive:    *session.Active,
		CreatedAt:   timestamppb.New(*session.IssuedAt),
		ExpiresAt:   timestamppb.New(*session.ExpiresAt),
		Permissions: permissions,
		Roles:       roles,
	}

	return &authv1.ValidateSessionResponse{
		SessionInfo: sessionInfo,
		ApiResponse: createSuccessResponse("Session validated successfully"),
	}, nil
}

// RefreshToken implements the RefreshToken gRPC method
func (s *Service) RefreshToken(ctx context.Context, req *authv1.RefreshTokenRequest) (*authv1.RefreshTokenResponse, error) {
	s.logger.Info("RefreshToken called")

	if req.RefreshToken == "" {
		return &authv1.RefreshTokenResponse{
			ApiResponse: createErrorResponse("INVALID_TOKEN", "Refresh token is required"),
		}, status.Errorf(codes.InvalidArgument, "refresh token is required")
	}

	// Validate the session token with Ory Kratos (not OAuth2 refresh token)
	session, err := s.oryClient.ValidateSession(ctx, req.RefreshToken)
	if err != nil {
		s.logger.Error("Session validation failed", zap.Error(err))
		return &authv1.RefreshTokenResponse{
			ApiResponse: createErrorResponse("INVALID_TOKEN", "Invalid session token"),
		}, status.Errorf(codes.Unauthenticated, "invalid session token: %v", err)
	}

	if session == nil || session.Active == nil || !*session.Active {
		return &authv1.RefreshTokenResponse{
			ApiResponse: createErrorResponse("INACTIVE_SESSION", "Session is inactive"),
		}, status.Errorf(codes.Unauthenticated, "session is inactive")
	}

	// Check if session is about to expire (within 1 hour)
	if session.ExpiresAt != nil {
		timeUntilExpiry := time.Until(*session.ExpiresAt)
		if timeUntilExpiry < time.Hour {
			s.logger.Info("Session expiring soon, attempting to extend",
				zap.String("user_id", session.Identity.Id),
				zap.Duration("time_until_expiry", timeUntilExpiry))

			// Try to extend the session
			_, err := s.oryClient.ExtendSession(ctx, req.RefreshToken)
			if err != nil {
				s.logger.Warn("Failed to extend session, using current session", zap.Error(err))
			}
		}
	}

	// Set expiration time based on session expiry
	var expiresAt time.Time
	if session.ExpiresAt != nil {
		expiresAt = *session.ExpiresAt
	} else {
		// Default to 24 hours if no expiry is set
		expiresAt = time.Now().Add(24 * time.Hour)
	}

	// For Kratos sessions, we return the session token
	// The client should use this token for subsequent requests
	// Note: Kratos sessions are long-lived and don't need frequent refresh like JWT tokens
	accessToken := req.RefreshToken  // Use the session token as access token
	refreshToken := req.RefreshToken // For Kratos, refresh token is the same as session token

	return &authv1.RefreshTokenResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    timestamppb.New(expiresAt),
		ApiResponse:  createSuccessResponse("Session token validated successfully"),
	}, nil
}

// Logout implements the Logout gRPC method
func (s *Service) Logout(ctx context.Context, req *authv1.LogoutRequest) (*authv1.LogoutResponse, error) {
	s.logger.Info("Logout called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &authv1.LogoutResponse{
			ApiResponse: createErrorResponse("INVALID_USER", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Invalidate session in Ory Kratos if session ID is provided
	if req.SessionId != "" {
		err := s.oryClient.InvalidateSession(ctx, req.SessionId)
		if err != nil {
			s.logger.Error("Failed to invalidate session in Ory", zap.Error(err))
			// Continue with database invalidation
		}
	}

	// Invalidate all user sessions in database
	err := s.repository.InvalidateUserSessions(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to invalidate user sessions", zap.Error(err))
		return &authv1.LogoutResponse{
			ApiResponse: createErrorResponse("SESSION_INVALIDATION_ERROR", "Failed to logout user"),
		}, status.Errorf(codes.Internal, "failed to logout user: %v", err)
	}

	return &authv1.LogoutResponse{
		Success:     true,
		ApiResponse: createSuccessResponse("User logged out successfully"),
	}, nil
}

// GetUserProfile implements the GetUserProfile gRPC method
func (s *Service) GetUserProfile(ctx context.Context, req *authv1.GetUserProfileRequest) (*authv1.GetUserProfileResponse, error) {
	s.logger.Info("GetUserProfile called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &authv1.GetUserProfileResponse{
			ApiResponse: createErrorResponse("INVALID_USER", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get user from database
	user, err := s.repository.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user profile", zap.Error(err))
		return &authv1.GetUserProfileResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Convert to protobuf format
	protoUser := s.userInfoToProto(user)

	return &authv1.GetUserProfileResponse{
		User:        protoUser,
		ApiResponse: createSuccessResponse("User profile retrieved successfully"),
	}, nil
}

// UpdateUserProfile implements the UpdateUserProfile gRPC method
func (s *Service) UpdateUserProfile(ctx context.Context, req *authv1.UpdateUserProfileRequest) (*authv1.UpdateUserProfileResponse, error) {
	s.logger.Info("UpdateUserProfile called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &authv1.UpdateUserProfileResponse{
			ApiResponse: createErrorResponse("INVALID_USER", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Validate user exists
	_, err := s.repository.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("User not found for profile update", zap.Error(err))
		return &authv1.UpdateUserProfileResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Update user profile in database
	err = s.repository.UpdateUserProfile(ctx, req.UserId, req.Username, req.FirstName, req.LastName, req.AvatarUrl)
	if err != nil {
		s.logger.Error("Failed to update user profile", zap.Error(err))
		return &authv1.UpdateUserProfileResponse{
			ApiResponse: createErrorResponse("UPDATE_ERROR", "Failed to update user profile"),
		}, status.Errorf(codes.Internal, "failed to update user profile: %v", err)
	}

	// Update user traits in Ory Kratos if needed
	traits := map[string]interface{}{
		"username": req.Username,
		"name": map[string]interface{}{
			"first": req.FirstName,
			"last":  req.LastName,
		},
	}

	_, err = s.oryClient.UpdateIdentity(ctx, req.UserId, traits)
	if err != nil {
		s.logger.Error("Failed to update identity in Ory", zap.Error(err))
		// Continue anyway as the database update was successful
	}

	// Return updated user info
	userInfo := &authv1.UserInfo{
		Id:        req.UserId,
		Username:  req.Username,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		AvatarUrl: req.AvatarUrl,
	}

	return &authv1.UpdateUserProfileResponse{
		User:        userInfo,
		ApiResponse: createSuccessResponse("User profile updated successfully"),
	}, nil
}

// GetUserPermissions implements the GetUserPermissions gRPC method
func (s *Service) GetUserPermissions(ctx context.Context, req *authv1.GetUserPermissionsRequest) (*authv1.GetUserPermissionsResponse, error) {
	s.logger.Info("GetUserPermissions called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &authv1.GetUserPermissionsResponse{
			ApiResponse: createErrorResponse("INVALID_USER", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get user permissions from database
	permissions, err := s.repository.GetUserPermissions(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user permissions", zap.Error(err))
		return &authv1.GetUserPermissionsResponse{
			ApiResponse: createErrorResponse("PERMISSION_ERROR", "Failed to retrieve user permissions"),
		}, status.Errorf(codes.Internal, "failed to get user permissions: %v", err)
	}

	return &authv1.GetUserPermissionsResponse{
		Permissions: permissions,
		ApiResponse: createSuccessResponse("User permissions retrieved successfully"),
	}, nil
}

// HasPermission implements the HasPermission gRPC method
func (s *Service) HasPermission(ctx context.Context, req *authv1.HasPermissionRequest) (*authv1.HasPermissionResponse, error) {
	s.logger.Info("HasPermission called",
		zap.String("user_id", req.UserId),
		zap.String("permission", req.Permission))

	if req.UserId == "" || req.Permission == "" {
		return &authv1.HasPermissionResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and permission are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and permission are required")
	}

	// Get user permissions
	permissions, err := s.repository.GetUserPermissions(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user permissions for check", zap.Error(err))
		return &authv1.HasPermissionResponse{
			ApiResponse: createErrorResponse("PERMISSION_ERROR", "Failed to check user permission"),
		}, status.Errorf(codes.Internal, "failed to check user permission: %v", err)
	}

	// Check if the specific permission is in the list
	hasPermission := false
	for _, permission := range permissions {
		if permission == req.Permission {
			hasPermission = true
			break
		}
	}

	return &authv1.HasPermissionResponse{
		HasPermission: hasPermission,
		ApiResponse:   createSuccessResponse("Permission check completed"),
	}, nil
}

// GetUserRoles implements the GetUserRoles gRPC method
func (s *Service) GetUserRoles(ctx context.Context, req *authv1.GetUserRolesRequest) (*authv1.GetUserRolesResponse, error) {
	s.logger.Info("GetUserRoles called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &authv1.GetUserRolesResponse{
			ApiResponse: createErrorResponse("INVALID_USER", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get user roles from database
	roles, err := s.repository.GetUserRoles(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user roles", zap.Error(err))
		return &authv1.GetUserRolesResponse{
			ApiResponse: createErrorResponse("ROLE_ERROR", "Failed to retrieve user roles"),
		}, status.Errorf(codes.Internal, "failed to get user roles: %v", err)
	}

	return &authv1.GetUserRolesResponse{
		Roles:       roles,
		ApiResponse: createSuccessResponse("User roles retrieved successfully"),
	}, nil
}

// ValidateMqttConnection implements the ValidateMqttConnection gRPC method
func (s *Service) ValidateMqttConnection(ctx context.Context, req *authv1.ValidateMqttConnectionRequest) (*authv1.ValidateMqttConnectionResponse, error) {
	s.logger.Info("ValidateMqttConnection called",
		zap.String("user_id", req.UserId),
		zap.String("client_id", req.ClientId))

	if req.UserId == "" || req.ClientId == "" {
		return &authv1.ValidateMqttConnectionResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and client ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and client ID are required")
	}

	// Validate user exists
	_, err := s.repository.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("User not found for MQTT validation", zap.Error(err))
		return &authv1.ValidateMqttConnectionResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Generate allowed topics based on user permissions
	allowedTopics := []string{
		fmt.Sprintf("hopen/chat/%s", req.UserId),
		fmt.Sprintf("hopen/requests/%s", req.UserId),
	}

	// Add bubble topics if user is a member
	bubbleIDs, err := s.repository.GetUserActiveBubbles(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user bubbles for MQTT validation", zap.Error(err))
		// Continue with basic topics
	} else {
		for _, bubbleID := range bubbleIDs {
			allowedTopics = append(allowedTopics,
				fmt.Sprintf("hopen/bubbles/%s/chat", bubbleID),
				fmt.Sprintf("hopen/bubbles/%s/notifications", bubbleID))
		}
	}

	// Set QoS levels for topics
	topicQos := make(map[string]int32)
	for _, topic := range allowedTopics {
		// Set QoS 1 for most topics, QoS 0 for notifications
		if strings.Contains(topic, "notifications") {
			topicQos[topic] = 0
		} else {
			topicQos[topic] = 1
		}
	}

	return &authv1.ValidateMqttConnectionResponse{
		IsValid:       true,
		AllowedTopics: allowedTopics,
		TopicQos:      topicQos,
		ApiResponse:   createSuccessResponse("MQTT connection validated successfully"),
	}, nil
}

// GetAuthStatus implements the GetAuthStatus gRPC method
func (s *Service) GetAuthStatus(ctx context.Context, req *authv1.GetAuthStatusRequest) (*authv1.GetAuthStatusResponse, error) {
	s.logger.Info("GetAuthStatus called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &authv1.GetAuthStatusResponse{
			ApiResponse: createErrorResponse("INVALID_USER", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Check if user exists
	user, err := s.repository.GetUserByID(ctx, req.UserId)
	if err != nil {
		s.logger.Error("User not found for auth status", zap.Error(err))
		return &authv1.GetAuthStatusResponse{
			ApiResponse: createErrorResponse("USER_NOT_FOUND", "User not found"),
		}, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// Get active sessions
	activeSessions, err := s.repository.GetUserActiveSessions(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user active sessions", zap.Error(err))
		activeSessions = []string{} // Use empty slice if error
	}

	// Get last login time
	lastLogin, err := s.repository.GetUserLastLogin(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get user last login", zap.Error(err))
		lastLogin = time.Time{} // Use zero time if error
	}

	// Build auth status
	authStatus := &authv1.AuthStatus{
		UserId:          req.UserId,
		IsAuthenticated: len(activeSessions) > 0, // User is authenticated if they have active sessions
		IsActive:        user != nil,             // User is active if they exist
		LastLogin:       timestamppb.New(lastLogin),
		LoginMethod:     "session", // Default to session-based auth
		ActiveSessions:  activeSessions,
	}

	return &authv1.GetAuthStatusResponse{
		Status:      authStatus,
		ApiResponse: createSuccessResponse("Auth status retrieved successfully"),
	}, nil
}
