package friendship

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	commonv1 "hopenbackend/protos/gen/common"
	friendshipv1 "hopenbackend/protos/gen/friendship"
)

// Service handles friendship operations using gRPC and implements FriendshipServiceServer
type Service struct {
	friendshipv1.UnimplementedFriendshipServiceServer
	logger      *zap.Logger
	repository  *PostgreSQLRepository
	db          *database.PostgreSQLClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	oryClient   *ory.Client
	natsConn    *nats.Conn
}

// FriendshipEvent represents a friendship-related event for NATS
type FriendshipEvent struct {
	UserID    string    `json:"user_id"`
	FriendID  string    `json:"friend_id"`
	Action    string    `json:"action"`
	Timestamp time.Time `json:"timestamp"`
}

// Dependencies holds the dependencies for the friendship service
type Dependencies struct {
	Logger      *zap.Logger
	Repository  *PostgreSQLRepository
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
	NATSConn    *nats.Conn
}

// NewService creates a new friendship service instance
func NewService(deps *Dependencies) *Service {
	service := &Service{
		logger:      deps.Logger,
		repository:  deps.Repository,
		db:          deps.DB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		oryClient:   deps.OryClient,
		natsConn:    deps.NATSConn,
	}

	// Subscribe to bubble expiration events
	service.subscribeToBubbleEvents()

	return service
}

// NewFriendshipServiceServer creates a new gRPC server for the friendship service
func NewFriendshipServiceServer(service *Service) friendshipv1.FriendshipServiceServer {
	return service
}

// Helper function to create a successful API response
func createSuccessResponse(message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   true,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// Helper function to create an error API response
func createErrorResponse(errorCode, message string) *commonv1.ApiResponse {
	return &commonv1.ApiResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   message,
		Timestamp: timestamppb.Now(),
	}
}

// publishFriendshipEvent publishes friendship events via NATS
func (s *Service) publishFriendshipEvent(userID, friendID, action string) {
	if s.natsConn == nil {
		return
	}

	event := FriendshipEvent{
		UserID:    userID,
		FriendID:  friendID,
		Action:    action,
		Timestamp: time.Now(),
	}

	data, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal friendship event", zap.Error(err))
		return
	}

	subject := fmt.Sprintf("friendship.%s", action)
	if err := s.natsConn.Publish(subject, data); err != nil {
		s.logger.Error("Failed to publish friendship event", zap.Error(err))
	}
}

// GetFriendRequests implements the GetFriendRequests gRPC method
func (s *Service) GetFriendRequests(ctx context.Context, req *friendshipv1.GetFriendRequestsRequest) (*friendshipv1.GetFriendRequestsResponse, error) {
	s.logger.Info("GetFriendRequests called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &friendshipv1.GetFriendRequestsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get friend requests for the user
	friendRequests, err := s.repository.GetFriendRequestsByUser(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get friend requests", zap.Error(err))
		return &friendshipv1.GetFriendRequestsResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve friend requests"),
		}, status.Errorf(codes.Internal, "failed to retrieve friend requests")
	}

	// Convert the database models to the protobuf message type
	protoRequests := make([]*friendshipv1.FriendRequest, 0, len(friendRequests))
	for _, request := range friendRequests {
		// Convert status string to protobuf enum
		var status commonv1.FriendshipStatus
		switch request.Status {
		case "pending":
			status = commonv1.FriendshipStatus_FRIENDSHIP_STATUS_PENDING
		case "accepted":
			status = commonv1.FriendshipStatus_FRIENDSHIP_STATUS_ACCEPTED
		case "declined":
			status = commonv1.FriendshipStatus_FRIENDSHIP_STATUS_REJECTED
		default:
			status = commonv1.FriendshipStatus_FRIENDSHIP_STATUS_UNSPECIFIED
		}

		// Create a basic user object for the requester
		// Note: In a production system, you'd want to fetch full user details
		fromUser := &commonv1.User{
			Id: request.RequesterID,
			// Additional user fields would require a separate query to the user service
		}

		// Create the protobuf FriendRequest
		protoRequest := &friendshipv1.FriendRequest{
			Id:         request.ID,
			FromUserId: request.RequesterID,
			ToUserId:   request.RecipientID,
			FromUser:   fromUser,
			Message:    "", // No message field in current model
			Status:     status,
			CreatedAt:  timestamppb.New(request.CreatedAt),
			UpdatedAt:  timestamppb.New(request.UpdatedAt),
		}

		protoRequests = append(protoRequests, protoRequest)
	}

	return &friendshipv1.GetFriendRequestsResponse{
		FriendRequests: protoRequests,
		Pagination:     &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(friendRequests))},
		ApiResponse:    createSuccessResponse("Friend requests retrieved successfully"),
	}, nil
}

// AcceptFriendRequest implements the AcceptFriendRequest gRPC method
func (s *Service) AcceptFriendRequest(ctx context.Context, req *friendshipv1.AcceptFriendRequestRequest) (*friendshipv1.AcceptFriendRequestResponse, error) {
	s.logger.Info("AcceptFriendRequest called",
		zap.String("user_id", req.UserId),
		zap.String("request_id", req.RequestId))

	if req.UserId == "" || req.RequestId == "" {
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and Request ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and request ID are required")
	}

	// Start a transaction for atomic operations
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		s.logger.Error("Failed to start transaction", zap.Error(err))
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to process friend request"),
		}, status.Errorf(codes.Internal, "failed to process friend request")
	}
	defer tx.Rollback(ctx)

	// Get the friend request to verify it exists and user is the recipient
	friendRequest, err := s.repository.GetFriendRequestByID(ctx, tx, req.RequestId)
	if err != nil {
		s.logger.Error("Failed to get friend request", zap.Error(err))
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("NOT_FOUND", "Friend request not found"),
		}, status.Errorf(codes.NotFound, "friend request not found")
	}

	// Verify the user is the recipient of this request
	if friendRequest.RecipientID != req.UserId {
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("PERMISSION_DENIED", "You can only accept requests sent to you"),
		}, status.Errorf(codes.PermissionDenied, "you can only accept requests sent to you")
	}

	// Verify the request is still pending
	if friendRequest.Status != "pending" {
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("INVALID_STATE", "Friend request is no longer pending"),
		}, status.Errorf(codes.FailedPrecondition, "friend request is no longer pending")
	}

	// Update the friend request status to accepted
	err = s.repository.UpdateFriendRequestStatus(ctx, tx, req.RequestId, "accepted")
	if err != nil {
		s.logger.Error("Failed to update friend request status", zap.Error(err))
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to accept friend request"),
		}, status.Errorf(codes.Internal, "failed to accept friend request")
	}

	// Create the friendship
	friendship := &Friendship{
		User1ID:        friendRequest.RequesterID,
		User2ID:        friendRequest.RecipientID,
		SourceBubbleID: friendRequest.SourceBubbleID,
	}

	err = s.repository.CreateFriendship(ctx, tx, friendship)
	if err != nil {
		s.logger.Error("Failed to create friendship", zap.Error(err))
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to create friendship"),
		}, status.Errorf(codes.Internal, "failed to create friendship")
	}

	// Check for reciprocal friend request and accept it if it exists
	reciprocalRequest, err := s.repository.GetReciprocalFriendRequest(ctx, tx, req.UserId, friendRequest.RequesterID)
	if err == nil && reciprocalRequest != nil && reciprocalRequest.Status == "pending" {
		err = s.repository.UpdateFriendRequestStatus(ctx, tx, reciprocalRequest.ID, "accepted")
		if err != nil {
			s.logger.Error("Failed to update reciprocal friend request", zap.Error(err))
			// Don't fail the whole operation for this
		}
	}

	// Commit the transaction
	err = tx.Commit(ctx)
	if err != nil {
		s.logger.Error("Failed to commit transaction", zap.Error(err))
		return &friendshipv1.AcceptFriendRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to complete friend request acceptance"),
		}, status.Errorf(codes.Internal, "failed to complete friend request acceptance")
	}

	// Publish friend request accepted event via NATS
	s.publishFriendshipEvent(req.UserId, friendRequest.RequesterID, "friend_request_accepted")

	return &friendshipv1.AcceptFriendRequestResponse{
		ApiResponse: createSuccessResponse("Friend request accepted successfully"),
	}, nil
}

// DeclineFriendRequest implements the DeclineFriendRequest gRPC method
func (s *Service) DeclineFriendRequest(ctx context.Context, req *friendshipv1.DeclineFriendRequestRequest) (*friendshipv1.DeclineFriendRequestResponse, error) {
	s.logger.Info("DeclineFriendRequest called",
		zap.String("user_id", req.UserId),
		zap.String("request_id", req.RequestId))

	if req.UserId == "" || req.RequestId == "" {
		return &friendshipv1.DeclineFriendRequestResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and Request ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and request ID are required")
	}

	// Get the friend request to verify it exists and to get the requester's ID for the event
	friendRequest, err := s.repository.GetFriendRequestByID(ctx, s.db.Pool, req.RequestId)
	if err != nil {
		s.logger.Error("Failed to get friend request for declining", zap.Error(err), zap.String("request_id", req.RequestId))
		return &friendshipv1.DeclineFriendRequestResponse{
			ApiResponse: createErrorResponse("NOT_FOUND", "Friend request not found"),
		}, status.Errorf(codes.NotFound, "friend request not found")
	}

	// Verify the user is the recipient of this request
	if friendRequest.RecipientID != req.UserId {
		return &friendshipv1.DeclineFriendRequestResponse{
			ApiResponse: createErrorResponse("PERMISSION_DENIED", "You can only decline requests sent to you"),
		}, status.Errorf(codes.PermissionDenied, "you can only decline requests sent to you")
	}

	// Verify the request is still pending
	if friendRequest.Status != "pending" {
		return &friendshipv1.DeclineFriendRequestResponse{
			ApiResponse: createErrorResponse("INVALID_STATE", "Friend request is no longer pending"),
		}, status.Errorf(codes.FailedPrecondition, "friend request is no longer pending")
	}

	// Update the friend request status to declined
	err = s.repository.UpdateFriendRequestStatus(ctx, s.db.Pool, req.RequestId, "declined")
	if err != nil {
		s.logger.Error("Failed to update friend request status to declined", zap.Error(err))
		return &friendshipv1.DeclineFriendRequestResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to decline friend request"),
		}, status.Errorf(codes.Internal, "failed to decline friend request")
	}

	// Publish friendship declined event via NATS
	s.publishFriendshipEvent(req.UserId, friendRequest.RequesterID, "friend_request_declined")

	return &friendshipv1.DeclineFriendRequestResponse{
		ApiResponse: createSuccessResponse("Friend request declined successfully"),
	}, nil
}

// GetFriends implements the GetFriends gRPC method
func (s *Service) GetFriends(ctx context.Context, req *friendshipv1.GetFriendsRequest) (*friendshipv1.GetFriendsResponse, error) {
	s.logger.Info("GetFriends called", zap.String("user_id", req.UserId))

	if req.UserId == "" {
		return &friendshipv1.GetFriendsResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID is required"),
		}, status.Errorf(codes.InvalidArgument, "user ID is required")
	}

	// Get friendships for the user
	friendships, err := s.repository.GetFriendshipsByUser(ctx, req.UserId)
	if err != nil {
		s.logger.Error("Failed to get friendships", zap.Error(err))
		return &friendshipv1.GetFriendsResponse{
			ApiResponse: createErrorResponse("INTERNAL_ERROR", "Failed to retrieve friends"),
		}, status.Errorf(codes.Internal, "failed to retrieve friends")
	}

	// Convert friendships to friend user IDs
	var friendUserIDs []string
	for _, friendship := range friendships {
		// Determine which user is the friend (not the requesting user)
		if friendship.User1ID == req.UserId {
			friendUserIDs = append(friendUserIDs, friendship.User2ID)
		} else {
			friendUserIDs = append(friendUserIDs, friendship.User1ID)
		}
	}

	// Get user details for each friend
	var protoFriends []*commonv1.User
	for _, friendID := range friendUserIDs {
		// Get user details from database
		user, err := s.db.GetUserByID(ctx, friendID)
		if err != nil {
			s.logger.Warn("Failed to get friend user details",
				zap.String("friend_id", friendID),
				zap.Error(err))
			continue // Skip this friend if we can't get their details
		}

		// Convert to protobuf format
		protoFriend := &commonv1.User{
			Id:        user.ID,
			Email:     user.Email,
			CreatedAt: timestamppb.New(user.CreatedAt),
			UpdatedAt: timestamppb.New(user.UpdatedAt),
		}

		// Handle optional string fields
		if user.Username != nil {
			protoFriend.Username = *user.Username
		}
		if user.FirstName != nil {
			protoFriend.FirstName = *user.FirstName
		}
		if user.LastName != nil {
			protoFriend.LastName = *user.LastName
		}
		if user.AvatarURL != nil {
			protoFriend.AvatarUrl = user.AvatarURL
		}

		// Set online status based on presence
		protoFriend.IsOnline = user.IsPresent

		protoFriends = append(protoFriends, protoFriend)
	}

	return &friendshipv1.GetFriendsResponse{
		Friends:     protoFriends,
		Pagination:  &commonv1.Pagination{Page: req.Page, PageSize: req.PageSize, TotalCount: int32(len(protoFriends))},
		ApiResponse: createSuccessResponse("Friends retrieved successfully"),
	}, nil
}

// RemoveFriend implements the RemoveFriend gRPC method
func (s *Service) RemoveFriend(ctx context.Context, req *friendshipv1.RemoveFriendRequest) (*friendshipv1.RemoveFriendResponse, error) {
	s.logger.Info("RemoveFriend called",
		zap.String("user_id", req.UserId),
		zap.String("friend_id", req.FriendId))

	if req.UserId == "" || req.FriendId == "" {
		return &friendshipv1.RemoveFriendResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and Friend ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and friend ID are required")
	}

	// Publish friendship removed event via NATS
	s.publishFriendshipEvent(req.UserId, req.FriendId, "removed")

	return &friendshipv1.RemoveFriendResponse{
		ApiResponse: createSuccessResponse("Friend removed successfully"),
	}, nil
}

// BlockUser implements the BlockUser gRPC method
func (s *Service) BlockUser(ctx context.Context, req *friendshipv1.BlockUserRequest) (*friendshipv1.BlockUserResponse, error) {
	s.logger.Info("BlockUser called",
		zap.String("user_id", req.UserId),
		zap.String("blocked_user_id", req.BlockedUserId))

	if req.UserId == "" || req.BlockedUserId == "" {
		return &friendshipv1.BlockUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and Blocked User ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and blocked user ID are required")
	}

	// Publish user blocked event via NATS
	s.publishFriendshipEvent(req.UserId, req.BlockedUserId, "blocked")

	return &friendshipv1.BlockUserResponse{
		ApiResponse: createSuccessResponse("User blocked successfully"),
	}, nil
}

// UnblockUser implements the UnblockUser gRPC method
func (s *Service) UnblockUser(ctx context.Context, req *friendshipv1.UnblockUserRequest) (*friendshipv1.UnblockUserResponse, error) {
	s.logger.Info("UnblockUser called",
		zap.String("user_id", req.UserId),
		zap.String("blocked_user_id", req.BlockedUserId))

	if req.UserId == "" || req.BlockedUserId == "" {
		return &friendshipv1.UnblockUserResponse{
			ApiResponse: createErrorResponse("INVALID_INPUT", "User ID and Blocked User ID are required"),
		}, status.Errorf(codes.InvalidArgument, "user ID and blocked user ID are required")
	}

	// Publish user unblocked event via NATS
	s.publishFriendshipEvent(req.UserId, req.BlockedUserId, "unblocked")

	return &friendshipv1.UnblockUserResponse{
		ApiResponse: createSuccessResponse("User unblocked successfully"),
	}, nil
}

// BubbleExpiredEvent represents a bubble expiration event from NATS
type BubbleExpiredEvent struct {
	BubbleID  string   `json:"bubble_id"`
	MemberIDs []string `json:"member_ids"`
	Timestamp int64    `json:"timestamp"`
}

// subscribeToBubbleEvents subscribes to bubble expiration events via NATS
func (s *Service) subscribeToBubbleEvents() {
	if s.natsConn == nil {
		s.logger.Warn("NATS connection not available, skipping bubble event subscription")
		return
	}

	// Subscribe to bubble expiration events
	_, err := s.natsConn.Subscribe("events.bubble.expired", func(msg *nats.Msg) {
		s.handleBubbleExpiredEvent(msg.Data)
	})

	if err != nil {
		s.logger.Error("Failed to subscribe to bubble expiration events", zap.Error(err))
		return
	}

	s.logger.Info("Successfully subscribed to bubble expiration events")
}

// handleBubbleExpiredEvent processes bubble expiration events and creates friend requests
func (s *Service) handleBubbleExpiredEvent(data []byte) {
	var event BubbleExpiredEvent
	if err := json.Unmarshal(data, &event); err != nil {
		s.logger.Error("Failed to unmarshal bubble expired event", zap.Error(err))
		return
	}

	s.logger.Info("Processing bubble expired event",
		zap.String("bubble_id", event.BubbleID),
		zap.Strings("member_ids", event.MemberIDs))

	// Create bidirectional friend requests for every pair of former bubble members
	ctx := context.Background()
	for i, userID1 := range event.MemberIDs {
		for j, userID2 := range event.MemberIDs {
			if i >= j { // Skip self and avoid duplicates
				continue
			}

			// Check if friend request already exists for this bubble
			exists, err := s.repository.FriendRequestExists(ctx, userID1, userID2, event.BubbleID)
			if err != nil {
				s.logger.Error("Failed to check friend request existence",
					zap.Error(err),
					zap.String("user1", userID1),
					zap.String("user2", userID2))
				continue
			}

			if exists {
				s.logger.Debug("Friend request already exists for this bubble",
					zap.String("user1", userID1),
					zap.String("user2", userID2),
					zap.String("bubble_id", event.BubbleID))
				continue
			}

			// Check if friendship already exists
			friendshipExists, err := s.repository.FriendshipExists(ctx, userID1, userID2)
			if err != nil {
				s.logger.Error("Failed to check friendship existence",
					zap.Error(err),
					zap.String("user1", userID1),
					zap.String("user2", userID2))
				continue
			}

			if friendshipExists {
				s.logger.Debug("Friendship already exists",
					zap.String("user1", userID1),
					zap.String("user2", userID2))
				continue
			}

			// Create bidirectional friend requests
			s.createBidirectionalFriendRequests(ctx, userID1, userID2, event.BubbleID)
		}
	}
}

// createBidirectionalFriendRequests creates friend requests in both directions
func (s *Service) createBidirectionalFriendRequests(ctx context.Context, userID1, userID2, bubbleID string) {
	// Create request from user1 to user2
	request1 := &FriendRequest{
		RequesterID:    userID1,
		RecipientID:    userID2,
		Status:         "pending",
		SourceBubbleID: &bubbleID,
		AutoGenerated:  true,
	}

	err := s.repository.CreateFriendRequest(ctx, request1)
	if err != nil {
		s.logger.Error("Failed to create friend request",
			zap.Error(err),
			zap.String("requester", userID1),
			zap.String("recipient", userID2))
		return
	}

	// Create request from user2 to user1
	request2 := &FriendRequest{
		RequesterID:    userID2,
		RecipientID:    userID1,
		Status:         "pending",
		SourceBubbleID: &bubbleID,
		AutoGenerated:  true,
	}

	err = s.repository.CreateFriendRequest(ctx, request2)
	if err != nil {
		s.logger.Error("Failed to create reciprocal friend request",
			zap.Error(err),
			zap.String("requester", userID2),
			zap.String("recipient", userID1))
		return
	}

	s.logger.Info("Created bidirectional friend requests",
		zap.String("user1", userID1),
		zap.String("user2", userID2),
		zap.String("bubble_id", bubbleID))

	// NATS events will be published automatically by the friendship creation logic above
	// The notification service will handle sending MQTT notifications based on these events

	// Publish NATS events
	s.publishFriendshipEvent(userID1, userID2, "friend_request_created")
	s.publishFriendshipEvent(userID2, userID1, "friend_request_created")
}
