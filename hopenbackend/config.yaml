# Hopen Backend Configuration
# This file contains all configuration for the Hopen backend microservices

app:
  name: "hopen-backend"
  version: "1.0.0"
  environment: "development"
  port: 4000
  debug: true
  tls:
    enabled: true
    cert_file: "/app/certs/server.crt"
    key_file: "/app/certs/server.key"
    http3: true
    http2: true
    auto_cert: false
    auto_cert_dir: "/app/certs"

databases:
  postgresql:
    host: "postgresql"
    port: 5432
    database: "hopen"
    username: "hopen"
    password: "hopen123"
    ssl_mode: "disable"
    max_connections: 20
    min_connections: 5
    max_connection_lifetime: "1h"
    max_connection_idle_time: "30m"
    health_check_period: "30s"
  
  scylladb:
    hosts: ["scylladb"]
    keyspace: "hopen"
    consistency: "LOCAL_QUORUM"  # Better for ScyllaDB clusters
    num_connections: 10
    timeout: "5s"
    connect_timeout: "10s"
    local_dc: "datacenter1"      # For multi-DC deployments
    shard_aware_port: 19042      # ScyllaDB shard-aware port
    disable_shard_aware_port: false
    enable_host_verification: false
    enable_token_aware_routing: true

valkey:
  host: "valkey"
  port: 6379
  address: "valkey:6379"
  password: "hopen123"
  database: 0
  pool_size: 10
  min_idle_connections: 5
  max_retries: 3
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

nats:
  host: "nats"
  port: 4222
  username: ""
  password: ""
  url: "nats://nats:4222"
  max_reconnects: 10
  reconnect_wait: "2s"
  timeout: "5s"

minio:
  endpoint: "minio:9000"
  external_endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  use_ssl: false
  bucket_name: "hopen-storage"
  region: "us-east-1"

mqtt:
  broker: "emqx:1883"
  client_id: "hopen-backend"
  username: "hopen_user"
  password: "hopen_password"
  keep_alive: 60
  clean_session: true
  qos: 1

jwt:
  secret: "your-jwt-secret-key-here"
  refresh_secret: "your-refresh-secret-key-here"
  issuer: "hopen-backend"
  audience: "hopen-app"
  access_token_expiry: "24h"
  refresh_token_expiry: "168h"

rate_limit:
  enabled: true
  default_limit: 100
  default_window: "1m"
  burst_multiplier: 2.0
  cleanup_interval: "1h"
  
  social_limits:
    bubble_create:
      limit: 10
      window: "1h"
      burst_limit: 3
      burst_window: "5m"
    bubble_join:
      limit: 20
      window: "1h"
      burst_limit: 5
      burst_window: "5m"
    bubble_leave:
      limit: 15
      window: "1h"
      burst_limit: 3
      burst_window: "5m"
    contact_request:
      limit: 50
      window: "24h"
      burst_limit: 10
      burst_window: "1h"
    message_send:
      limit: 1000
      window: "1m"
      burst_limit: 100
      burst_window: "10s"
    call_start:
      limit: 20
      window: "1h"
      burst_limit: 5
      burst_window: "5m"
  
  reputation_multipliers:
    new: 0.5
    trusted: 1.0
    veteran: 1.5
    restricted: 0.1

ory:
  kratos_public_url: "http://kratos:4433"
  kratos_admin_url: "http://kratos:4434"
  hydra_public_url: "http://hydra:4444"
  hydra_admin_url: "http://hydra:4445"

enterprise:
  security:
    enabled: true
    csp_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'"
    hsts_max_age: ********
    max_request_size: ********
    enable_content_scan: false
  
  resilience:
    enabled: true
    circuit_breakers:
      auth_service:
        max_failures: 5
        timeout: "30s"
        reset_timeout: "60s"
      user_service:
        max_failures: 5
        timeout: "30s"
        reset_timeout: "60s"
  
  monitoring:
    enabled: true
    metrics_port: 9090
    social_metrics: true
    business_metrics: true
  
  gateway:
    enabled: false
    port: 8080
    service_discovery: true
    load_balancing: true
    routes: []
  
  database:
    enhanced_pooling: true
    read_write_splitting: false
    health_monitoring: true
    auto_scaling: false
  
  idempotency:
    enabled: true
    default_ttl: "24h"
    storage: "redis"
    enable_fingerprinting: true
    operation_ttls:
      bubble_create: "24h"
      contact_request: "1h"
      message_send: "5m"

logging:
  level: "debug"
  format: "json"
  output: "stdout"

cors:
  allowed_origins: ["*"]
  allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allowed_headers: ["Content-Type", "Authorization", "X-Requested-With"]
  expose_headers: ["X-Total-Count"]
  allow_credentials: true
  max_age: 86400

social:
  bubble:
    initial_expiry_days: 90
    extension_days: 30
    max_members: 5
    min_members: 2
  friendship:
    auto_generate_on_bubble_expiry: true
    request_expiry_days: 7
  contact:
    max_requests_per_day: 50
    request_expiry_days: 30
  chat:
    max_message_length: 1000
    file_upload_max_size: ********
    supported_file_types: ["jpg", "jpeg", "png", "gif", "mp4", "mov"]

media:
  max_file_size: ********
  profile_picture_max_size: 5242880
  allowed_types: ["jpg", "jpeg", "png", "gif", "mp4", "mov"]
  profile_picture_types: ["jpg", "jpeg", "png"]
  profile_picture_output_format: "jpeg"
  thumbnail_size: 200
  cdn:
    enabled: false
    base_url: ""
    cache_control: "public, max-age=********"

firebase:
  service_account_path: "/app/config/firebase-service-account.json"
  project_id: "hopen-id"

aws:
  region: "us-east-1"
  ses_from_email: "<EMAIL>" 