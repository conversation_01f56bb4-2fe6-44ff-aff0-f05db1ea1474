-- Down migration for initial schema
-- This migration drops all tables and extensions created in the up migration

-- Drop functions first
DROP FUNCTION IF EXISTS get_user_bubble_status(UUID);
DROP FUNCTION IF EXISTS get_user_relationship(UUID, UUID);

-- Drop indexes
-- User sessions indexes
DROP INDEX IF EXISTS idx_user_sessions_status;
DROP INDEX IF EXISTS idx_user_sessions_user_id;

-- Notification indexes
DROP INDEX IF EXISTS idx_notifications_type;
DROP INDEX IF EXISTS idx_notifications_user_unread;
DROP INDEX IF EXISTS idx_notifications_user_created;
DROP INDEX IF EXISTS idx_fcm_tokens_token;
DROP INDEX IF EXISTS idx_fcm_tokens_user_active;

-- Media files indexes
DROP INDEX IF EXISTS idx_media_files_storage_path;
DROP INDEX IF EXISTS idx_media_files_public;
DROP INDEX IF EXISTS idx_media_files_type;
DROP INDEX IF EXISTS idx_media_files_user;

-- Request table indexes
DROP INDEX IF EXISTS idx_bubble_request_votes_voter;
DROP INDEX IF EXISTS idx_bubble_request_votes_request;
DROP INDEX IF EXISTS idx_bubble_requests_expires;
DROP INDEX IF EXISTS idx_bubble_requests_type_status;
DROP INDEX IF EXISTS idx_bubble_requests_target;
DROP INDEX IF EXISTS idx_bubble_requests_requester;
DROP INDEX IF EXISTS idx_bubble_requests_bubble;
DROP INDEX IF EXISTS idx_friendships_source_bubble;
DROP INDEX IF EXISTS idx_friendships_user2;
DROP INDEX IF EXISTS idx_friendships_user1;
DROP INDEX IF EXISTS idx_friend_requests_auto_generated;
DROP INDEX IF EXISTS idx_friend_requests_source_bubble;
DROP INDEX IF EXISTS idx_friend_requests_recipient;
DROP INDEX IF EXISTS idx_contacts_status;
DROP INDEX IF EXISTS idx_contacts_recipient;
DROP INDEX IF EXISTS idx_contacts_requester;

-- Core table indexes
DROP INDEX IF EXISTS idx_users_birthday_month_day;
DROP INDEX IF EXISTS idx_users_contact_search_composite;
DROP INDEX IF EXISTS idx_user_relationships_bidirectional;
DROP INDEX IF EXISTS idx_user_relationships_to_user;
DROP INDEX IF EXISTS idx_user_relationships_from_user;
DROP INDEX IF EXISTS idx_bubble_members_bubble_active;
DROP INDEX IF EXISTS idx_bubble_members_user_active;
DROP INDEX IF EXISTS idx_users_active_not_banned;
DROP INDEX IF EXISTS idx_users_last_active;
DROP INDEX IF EXISTS idx_users_is_present;
DROP INDEX IF EXISTS idx_users_search_text;

-- Drop tables (in reverse dependency order)
DROP TABLE IF EXISTS media_files;
DROP TABLE IF EXISTS bubble_request_votes;
DROP TABLE IF EXISTS bubble_requests;
DROP TABLE IF EXISTS friendships;
DROP TABLE IF EXISTS friend_requests;
DROP TABLE IF EXISTS contacts;
DROP TABLE IF EXISTS user_relationships;
DROP TABLE IF EXISTS bubble_members;
DROP TABLE IF EXISTS bubbles;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS users;

-- Drop ENUMs
DROP TYPE IF EXISTS notification_type;
DROP TYPE IF EXISTS contact_request_status;
DROP TYPE IF EXISTS friend_request_status;
DROP TYPE IF EXISTS vote_type;
DROP TYPE IF EXISTS request_status;
DROP TYPE IF EXISTS bubble_request_type;
DROP TYPE IF EXISTS bubble_member_status;
DROP TYPE IF EXISTS bubble_status;

-- Drop extensions
DROP EXTENSION IF EXISTS "pg_trgm";
DROP EXTENSION IF EXISTS "uuid-ossp";
