-- Migration: Initial Schema
-- Version: 000001
-- Description: Create initial database schema with proper indexes for contact search

-- 1. EXTENSIONS AND TYPES
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For full-text search optimization

-- Create ENUMs
DO $$ BEGIN CREATE TYPE bubble_status AS ENUM ('active', 'expired', 'dissolved', 'archived'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE bubble_member_status AS ENUM ('active', 'left', 'removed', 'pending', 'accepted', 'declined'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE bubble_request_type AS ENUM ('invite', 'join', 'kick', 'start'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE request_status AS ENUM ('pending', 'approved', 'rejected', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE vote_type AS ENUM ('approve', 'reject'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE friend_request_status AS ENUM ('pending', 'accepted', 'declined', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE contact_request_status AS ENUM ('pending', 'accepted', 'declined', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
DO $$ BEGIN CREATE TYPE notification_type AS ENUM (
    'contactRequestReceived',
    'contactRequestAccepted',
    'contactRequestDeclined',
    'friendRequestReceived',
    'friendRequestAccepted',
    'friendRequestDeclined',
    'bubbleInviteReceived',
    'bubbleInviteAccepted',
    'bubbleInviteDeclined',
    'bubbleJoinRequestReceived',
    'bubbleJoinRequestAccepted',
    'bubbleJoinRequestDeclined',
    'bubbleKickoutRequestReceived',
    'bubbleKickoutRequestAccepted',
    'bubbleKickoutRequestDeclined',
    'bubbleStartRequestReceived',
    'bubbleStartRequestAccepted',
    'bubbleStartRequestDeclined',
    'bubbleExpired',
    'bubbleDissolved',
    'bubbleCallStarted',
    'bubbleCallEnded',
    'bubbleCallMissed',
    'birthday',
    'systemNotification'
); EXCEPTION WHEN duplicate_object THEN null; END $$;

-- 2. CORE TABLES
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(40) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    avatar_bucket_name VARCHAR(100),
    avatar_object_key VARCHAR(500),
    avatar_url VARCHAR(500), -- For backward compatibility
    date_of_birth DATE NOT NULL,
    is_premium BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    is_private BOOLEAN DEFAULT false,
    is_banned BOOLEAN DEFAULT false,
    banned_at TIMESTAMP WITH TIME ZONE,
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_present BOOLEAN DEFAULT false,
    notification_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User sessions table for authentication tracking
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired')),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT user_sessions_unique_session UNIQUE (session_id)
);

CREATE TABLE IF NOT EXISTS bubbles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id UUID REFERENCES users(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    capacity INTEGER NOT NULL DEFAULT 5 CHECK (capacity >= 2 AND capacity <= 5),
    current_members INTEGER NOT NULL DEFAULT 0 CHECK (current_members >= 0),
    status bubble_status DEFAULT 'active',
    expires_at TIMESTAMP WITH TIME ZONE,
    friend_request_on_expire BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS bubble_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bubble_id UUID NOT NULL REFERENCES bubbles(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status bubble_member_status DEFAULT 'active',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bubble_id, user_id, status)
);

-- User relationships table with enforced single active relationship rule
CREATE TABLE IF NOT EXISTS user_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL, -- 'none', 'contact', 'bubbler', 'maybefriend', 'friend', 'block'
    status VARCHAR(20) DEFAULT 'active',     -- 'active', 'inactive', 'expired'
    created_by UUID REFERENCES users(id),
    reason TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT user_relationships_different_users CHECK (from_user_id != to_user_id),
    CONSTRAINT user_relationships_unique_active UNIQUE (from_user_id, to_user_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- 3. OPTIMIZED INDEXES FOR CONTACT SEARCH

-- User search indexes (critical for contacts page performance)
CREATE INDEX IF NOT EXISTS idx_users_search_text 
ON users USING gin((first_name || ' ' || last_name || ' ' || username || ' ' || display_name) gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_users_is_present 
ON users (is_present) WHERE is_active = true AND is_banned = false;

CREATE INDEX IF NOT EXISTS idx_users_last_active 
ON users (last_active_at DESC) WHERE is_active = true AND is_banned = false;

CREATE INDEX IF NOT EXISTS idx_users_active_not_banned 
ON users (is_active, is_banned, created_at DESC);

-- Bubble membership indexes (for bubble status filtering)
CREATE INDEX IF NOT EXISTS idx_bubble_members_user_active 
ON bubble_members (user_id, status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_bubble_members_bubble_active 
ON bubble_members (bubble_id, status) WHERE status = 'active';

-- User relationships indexes (for contact filtering)
CREATE INDEX IF NOT EXISTS idx_user_relationships_from_user 
ON user_relationships (from_user_id, relationship_type, status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_user_relationships_to_user 
ON user_relationships (to_user_id, relationship_type, status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_user_relationships_bidirectional 
ON user_relationships (from_user_id, to_user_id, relationship_type, status);

-- Composite index for contact search optimization
CREATE INDEX IF NOT EXISTS idx_users_contact_search_composite 
ON users (is_active, is_banned, is_present, last_active_at DESC) 
WHERE is_active = true AND is_banned = false;

-- Index for birthday queries optimization
CREATE INDEX IF NOT EXISTS idx_users_birthday_month_day
ON users (EXTRACT(MONTH FROM date_of_birth), EXTRACT(DAY FROM date_of_birth), is_active)
WHERE is_active = true AND date_of_birth IS NOT NULL;

-- User sessions indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id
ON user_sessions (user_id, status, expires_at);

CREATE INDEX IF NOT EXISTS idx_user_sessions_status
ON user_sessions (status, expires_at);

-- 4. FUNCTIONS FOR CONTACT SEARCH OPTIMIZATION

-- Function to get user's current bubble status
CREATE OR REPLACE FUNCTION get_user_bubble_status(user_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    bubble_count INTEGER;
    bubble_capacity INTEGER;
    bubble_current_members INTEGER;
BEGIN
    -- Check if user is in any active bubble
    SELECT COUNT(*), b.capacity, b.current_members
    INTO bubble_count, bubble_capacity, bubble_current_members
    FROM bubble_members bm
    JOIN bubbles b ON bm.bubble_id = b.id
    WHERE bm.user_id = user_uuid
    AND bm.status = 'active'
    AND b.status = 'active';

    IF bubble_count = 0 THEN
        RETURN 'no_bubble';
    ELSIF bubble_current_members >= bubble_capacity THEN
        RETURN 'full_bubble';
    ELSE
        RETURN 'not_full_bubble';
    END IF;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get relationship between two users
CREATE OR REPLACE FUNCTION get_user_relationship(user1_uuid UUID, user2_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    rel_type TEXT;
BEGIN
    -- Check bidirectional relationship
    SELECT relationship_type INTO rel_type
    FROM user_relationships
    WHERE ((from_user_id = user1_uuid AND to_user_id = user2_uuid)
           OR (from_user_id = user2_uuid AND to_user_id = user1_uuid))
    AND status = 'active'
    ORDER BY created_at DESC
    LIMIT 1;

    RETURN COALESCE(rel_type, 'none');
END;
$$ LANGUAGE plpgsql STABLE;

-- 5. REQUEST TABLES

-- Contact requests table
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status contact_request_status DEFAULT 'pending',
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT contacts_different_users CHECK (requester_id != recipient_id),
    CONSTRAINT contacts_unique_pending UNIQUE (requester_id, recipient_id) DEFERRABLE INITIALLY DEFERRED
);

-- Friend requests table (auto-generated from bubble expiration)
CREATE TABLE IF NOT EXISTS friend_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status friend_request_status DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source_bubble_id UUID REFERENCES bubbles(id) ON DELETE SET NULL,
    auto_generated BOOLEAN DEFAULT true,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT friend_requests_different_users CHECK (requester_id != recipient_id),
    CONSTRAINT friend_requests_unique_bubble UNIQUE (requester_id, recipient_id, source_bubble_id) DEFERRABLE INITIALLY DEFERRED
);

-- Friendships table (established after friend request acceptance)
CREATE TABLE IF NOT EXISTS friendships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user1_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user2_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source_bubble_id UUID REFERENCES bubbles(id) ON DELETE SET NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT friendships_different_users CHECK (user1_id != user2_id),
    CONSTRAINT friendships_unique_pair UNIQUE (user1_id, user2_id) DEFERRABLE INITIALLY DEFERRED
);

-- Bubble requests table (start, invite, join, kickout)
CREATE TABLE IF NOT EXISTS bubble_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
    requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    request_type bubble_request_type NOT NULL,
    status request_status DEFAULT 'pending',
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    CONSTRAINT bubble_requests_different_users CHECK (requester_id != target_user_id)
);

-- Bubble request votes table (for kickout and proposal voting)
CREATE TABLE IF NOT EXISTS bubble_request_votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id UUID NOT NULL REFERENCES bubble_requests(id) ON DELETE CASCADE,
    voter_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vote vote_type NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT bubble_request_votes_unique_vote UNIQUE (request_id, voter_id)
);

-- 6. REQUEST TABLE INDEXES

-- Contact requests indexes
CREATE INDEX IF NOT EXISTS idx_contacts_requester
ON contacts (requester_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_contacts_recipient
ON contacts (recipient_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_contacts_status
ON contacts (status, created_at DESC);

-- Friend requests indexes
CREATE INDEX IF NOT EXISTS idx_friend_requests_recipient
ON friend_requests (recipient_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_friend_requests_source_bubble
ON friend_requests (source_bubble_id, status);

CREATE INDEX IF NOT EXISTS idx_friend_requests_auto_generated
ON friend_requests (auto_generated, status, created_at DESC);

-- Friendships indexes
CREATE INDEX IF NOT EXISTS idx_friendships_user1
ON friendships (user1_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_friendships_user2
ON friendships (user2_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_friendships_source_bubble
ON friendships (source_bubble_id);

-- Bubble requests indexes
CREATE INDEX IF NOT EXISTS idx_bubble_requests_bubble
ON bubble_requests (bubble_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_bubble_requests_requester
ON bubble_requests (requester_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_bubble_requests_target
ON bubble_requests (target_user_id, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_bubble_requests_type_status
ON bubble_requests (request_type, status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_bubble_requests_expires
ON bubble_requests (expires_at) WHERE status = 'pending';

-- Bubble request votes indexes
CREATE INDEX IF NOT EXISTS idx_bubble_request_votes_request
ON bubble_request_votes (request_id, vote);

CREATE INDEX IF NOT EXISTS idx_bubble_request_votes_voter
ON bubble_request_votes (voter_id, created_at DESC);

-- 7. MEDIA FILES TABLE

-- Media files table for file storage metadata
CREATE TABLE IF NOT EXISTS media_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type VARCHAR(20) NOT NULL CHECK (file_type IN ('image', 'video', 'audio', 'document', 'other')),
    storage_path VARCHAR(500) NOT NULL UNIQUE,
    url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    width INTEGER,
    height INTEGER,
    duration INTEGER, -- for video/audio in seconds
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. MEDIA FILES INDEXES

-- Media files indexes
CREATE INDEX IF NOT EXISTS idx_media_files_user
ON media_files (user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_media_files_type
ON media_files (file_type, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_media_files_public
ON media_files (is_public, created_at DESC) WHERE is_public = true;

CREATE INDEX IF NOT EXISTS idx_media_files_storage_path
ON media_files (storage_path);

-- 9. NOTIFICATION TABLES

-- FCM tokens table for push notifications
CREATE TABLE IF NOT EXISTS fcm_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(500) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT fcm_tokens_unique_user_device UNIQUE (user_id, device_id)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    data JSONB,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Notification settings table
CREATE TABLE IF NOT EXISTS notification_settings (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    push_enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    contact_requests_enabled BOOLEAN DEFAULT true,
    bubble_invites_enabled BOOLEAN DEFAULT true,
    friend_requests_enabled BOOLEAN DEFAULT true,
    chat_messages_enabled BOOLEAN DEFAULT true,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. NOTIFICATION INDEXES

-- FCM tokens indexes
CREATE INDEX IF NOT EXISTS idx_fcm_tokens_user_active
ON fcm_tokens (user_id, is_active);

CREATE INDEX IF NOT EXISTS idx_fcm_tokens_token
ON fcm_tokens (token);

-- Notifications indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_created
ON notifications (user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_notifications_user_unread
ON notifications (user_id, is_read, created_at DESC) WHERE is_read = false;

CREATE INDEX IF NOT EXISTS idx_notifications_type
ON notifications (type, created_at DESC);

-- 11. PRESENCE TABLE

-- User presence table for tracking online status
CREATE TABLE IF NOT EXISTS user_presence (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'away', 'busy')),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    device_type VARCHAR(20) DEFAULT 'unknown' CHECK (device_type IN ('mobile', 'web', 'desktop', 'unknown')),
    custom_status VARCHAR(255),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 12. PRESENCE INDEXES

-- Presence indexes
CREATE INDEX IF NOT EXISTS idx_user_presence_status
ON user_presence (status, last_seen DESC);

CREATE INDEX IF NOT EXISTS idx_user_presence_last_seen
ON user_presence (last_seen DESC);

CREATE INDEX IF NOT EXISTS idx_user_presence_online
ON user_presence (user_id, last_seen DESC) WHERE status = 'online';

-- 13. SYNC TABLE

-- Sync status table for tracking data synchronization
CREATE TABLE IF NOT EXISTS sync_status (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id VARCHAR(255) NOT NULL,
    last_sync_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sync_version BIGINT DEFAULT 0,
    sync_type VARCHAR(20) DEFAULT 'incremental' CHECK (sync_type IN ('full', 'incremental')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    error_message TEXT,
    data_checksum VARCHAR(64),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (user_id, device_id)
);

-- 14. SYNC INDEXES

-- Sync status indexes
CREATE INDEX IF NOT EXISTS idx_sync_status_user
ON sync_status (user_id, updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_sync_status_status
ON sync_status (status, updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_sync_status_version
ON sync_status (sync_version DESC);

-- 15. REALTIME TABLES

-- Messages table for chat messages
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
    conversation_id UUID,
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'text' CHECK (type IN ('text', 'image', 'file', 'audio', 'video')),
    reply_to_id UUID REFERENCES messages(id) ON DELETE SET NULL,
    is_edited BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    edited_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Conversations table for direct messages
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    participant1 UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    participant2 UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    last_message TEXT,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT conversations_unique_participants UNIQUE (participant1, participant2),
    CONSTRAINT conversations_different_participants CHECK (participant1 != participant2)
);

-- 16. REALTIME INDEXES

-- Messages indexes
CREATE INDEX IF NOT EXISTS idx_messages_bubble_created
ON messages (bubble_id, created_at DESC) WHERE is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_messages_conversation_created
ON messages (conversation_id, created_at DESC) WHERE is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_messages_sender
ON messages (sender_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_messages_content_search
ON messages USING gin(to_tsvector('english', content)) WHERE is_deleted = false;

-- Conversations indexes
CREATE INDEX IF NOT EXISTS idx_conversations_participant1
ON conversations (participant1, last_activity DESC);

CREATE INDEX IF NOT EXISTS idx_conversations_participant2
ON conversations (participant2, last_activity DESC);

-- 11. REALTIME TABLES

-- Messages table for chat messages
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
    conversation_id UUID,
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'text' CHECK (type IN ('text', 'image', 'file', 'audio', 'video')),
    reply_to_id UUID REFERENCES messages(id) ON DELETE SET NULL,
    is_edited BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    edited_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Conversations table for direct messages
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    participant1 UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    participant2 UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    last_message TEXT,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT conversations_unique_participants UNIQUE (participant1, participant2),
    CONSTRAINT conversations_different_participants CHECK (participant1 != participant2)
);

-- 12. REALTIME INDEXES

-- Messages indexes
CREATE INDEX IF NOT EXISTS idx_messages_bubble_created
ON messages (bubble_id, created_at DESC) WHERE is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_messages_conversation_created
ON messages (conversation_id, created_at DESC) WHERE is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_messages_sender
ON messages (sender_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_messages_content_search
ON messages USING gin(to_tsvector('english', content)) WHERE is_deleted = false;

-- Conversations indexes
CREATE INDEX IF NOT EXISTS idx_conversations_participant1
ON conversations (participant1, last_activity DESC);

CREATE INDEX IF NOT EXISTS idx_conversations_participant2
ON conversations (participant2, last_activity DESC);
