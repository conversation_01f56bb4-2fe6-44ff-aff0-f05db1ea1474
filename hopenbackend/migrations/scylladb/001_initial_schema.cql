-- ScyllaDB Schema for Hopen Social App
-- This file contains all the table definitions for the ScyllaDB database
-- ScyllaDB is used for high-performance chat message storage with shard-aware optimizations

-- Create keyspace if it doesn't exist
-- Using NetworkTopologyStrategy for production multi-datacenter deployments
CREATE KEYSPACE IF NOT EXISTS hopen
WITH REPLICATION = {
    'class': 'NetworkTopologyStrategy',
    'datacenter1': 3
};

USE hopen;

-- Messages table for bubble chat
-- Partitioned by bubble_id for optimal query performance
-- Clustered by created_at for chronological ordering
CREATE TABLE IF NOT EXISTS messages (
    bubble_id UUID,
    message_id UUID,
    sender_id UUID,
    content TEXT,
    message_type TEXT, -- text, image, video, audio, file
    media_url TEXT,
    reply_to_id UUID,
    is_edited BOOLEAN,
    is_deleted BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (bubble_id, created_at, message_id)
) WITH CLUSTERING ORDER BY (created_at DESC)
AND COMMENT = 'Chat messages within bubbles, partitioned by bubble_id for performance'
AND compaction = {'class': 'TimeWindowCompactionStrategy', 'compaction_window_unit': 'DAYS', 'compaction_window_size': 1}
AND gc_grace_seconds = 864000;

-- Conversation messages table for direct messages
-- Partitioned by conversation_id for optimal query performance
CREATE TABLE IF NOT EXISTS conversation_messages (
    conversation_id UUID,
    message_id UUID,
    sender_id UUID,
    recipient_id UUID,
    content TEXT,
    message_type TEXT, -- text, image, video, audio, file
    media_url TEXT,
    reply_to_id UUID,
    is_edited BOOLEAN,
    is_deleted BOOLEAN,
    is_read BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (conversation_id, created_at, message_id)
) WITH CLUSTERING ORDER BY (created_at DESC)
AND COMMENT = 'Direct messages between users, partitioned by conversation_id';

-- Conversations table for managing direct message conversations
CREATE TABLE IF NOT EXISTS conversations (
    conversation_id UUID,
    participant1_id UUID,
    participant2_id UUID,
    last_message_id UUID,
    last_message_at TIMESTAMP,
    created_at TIMESTAMP,
    PRIMARY KEY (conversation_id)
) WITH COMMENT = 'Direct message conversation metadata';

-- User conversations table for efficient user conversation lookup
-- Denormalized for query performance
CREATE TABLE IF NOT EXISTS user_conversations (
    user_id UUID,
    conversation_id UUID,
    other_user_id UUID,
    last_message_at TIMESTAMP,
    unread_count INT,
    is_archived BOOLEAN,
    created_at TIMESTAMP,
    PRIMARY KEY (user_id, last_message_at, conversation_id)
) WITH CLUSTERING ORDER BY (last_message_at DESC)
AND COMMENT = 'User-centric view of conversations for efficient lookup';

-- Message attachments table for tracking file attachments
CREATE TABLE IF NOT EXISTS message_attachments (
    message_id UUID,
    attachment_id UUID,
    file_name TEXT,
    file_size BIGINT,
    content_type TEXT,
    url TEXT,
    thumbnail_url TEXT,
    created_at TIMESTAMP,
    PRIMARY KEY (message_id, attachment_id)
) WITH COMMENT = 'File attachments associated with messages';

-- Typing indicators table for real-time typing status
-- TTL set to automatically expire old typing indicators
CREATE TABLE IF NOT EXISTS typing_indicators (
    bubble_id UUID,
    user_id UUID,
    is_typing BOOLEAN,
    updated_at TIMESTAMP,
    PRIMARY KEY (bubble_id, user_id)
) WITH default_time_to_live = 30
AND COMMENT = 'Real-time typing indicators with automatic expiry';

-- Conversation typing indicators table
CREATE TABLE IF NOT EXISTS conversation_typing_indicators (
    conversation_id UUID,
    user_id UUID,
    is_typing BOOLEAN,
    updated_at TIMESTAMP,
    PRIMARY KEY (conversation_id, user_id)
) WITH default_time_to_live = 30
AND COMMENT = 'Typing indicators for direct conversations';

-- Message delivery status table for tracking message delivery
CREATE TABLE IF NOT EXISTS message_delivery_status (
    bubble_id UUID,
    message_id UUID,
    user_id UUID,
    status TEXT, -- sent, delivered, read
    timestamp TIMESTAMP,
    PRIMARY KEY (bubble_id, message_id, user_id)
) WITH COMMENT = 'Message delivery and read status tracking';

-- User presence table for tracking online status
CREATE TABLE IF NOT EXISTS user_presence (
    user_id UUID,
    status TEXT, -- online, away, offline
    last_seen TIMESTAMP,
    updated_at TIMESTAMP,
    PRIMARY KEY (user_id)
) WITH default_time_to_live = 3600
AND COMMENT = 'User online presence status with automatic expiry';

-- Message search index table for full-text search capabilities
-- This would typically be used with external search engines like Elasticsearch
CREATE TABLE IF NOT EXISTS message_search_index (
    search_term TEXT,
    bubble_id UUID,
    message_id UUID,
    sender_id UUID,
    content TEXT,
    created_at TIMESTAMP,
    PRIMARY KEY (search_term, created_at, message_id)
) WITH CLUSTERING ORDER BY (created_at DESC)
AND COMMENT = 'Search index for message content (consider external search engine)';

-- Bubble activity counters for analytics
CREATE TABLE IF NOT EXISTS bubble_activity_counters (
    bubble_id UUID,
    date DATE,
    message_count COUNTER,
    PRIMARY KEY (bubble_id, date)
) WITH COMMENT = 'Daily message counters per bubble for analytics';

-- Bubble activity details for analytics
CREATE TABLE IF NOT EXISTS bubble_activity_details (
    bubble_id UUID,
    date DATE,
    active_users SET<UUID>,
    PRIMARY KEY (bubble_id, date)
) WITH COMMENT = 'Daily activity details per bubble for analytics';

-- User activity counters for analytics
CREATE TABLE IF NOT EXISTS user_activity_counters (
    user_id UUID,
    date DATE,
    messages_sent COUNTER,
    messages_received COUNTER,
    PRIMARY KEY (user_id, date)
) WITH COMMENT = 'Daily message counters per user for analytics';

-- User activity details for analytics
CREATE TABLE IF NOT EXISTS user_activity_details (
    user_id UUID,
    date DATE,
    bubbles_active SET<UUID>,
    PRIMARY KEY (user_id, date)
) WITH COMMENT = 'Daily activity details per user for analytics';

-- Indexes for efficient querying

-- Secondary index on sender_id for messages
CREATE INDEX IF NOT EXISTS messages_sender_idx ON messages (sender_id);

-- Secondary index on message_type for filtering
CREATE INDEX IF NOT EXISTS messages_type_idx ON messages (message_type);

-- Secondary index on sender_id for conversation messages
CREATE INDEX IF NOT EXISTS conversation_messages_sender_idx ON conversation_messages (sender_id);

-- Secondary index on recipient_id for conversation messages
CREATE INDEX IF NOT EXISTS conversation_messages_recipient_idx ON conversation_messages (recipient_id);

-- Secondary index on is_read for unread message queries
CREATE INDEX IF NOT EXISTS conversation_messages_read_idx ON conversation_messages (is_read);

-- Secondary index on participant IDs for conversation lookup
CREATE INDEX IF NOT EXISTS conversations_participant1_idx ON conversations (participant1_id);
CREATE INDEX IF NOT EXISTS conversations_participant2_idx ON conversations (participant2_id);

-- Comments explaining the schema design

-- COMMENT: Messages table design
-- - Partitioned by bubble_id to ensure all messages for a bubble are on the same node
-- - Clustered by created_at DESC for chronological ordering (newest first)
-- - message_id included in clustering key to ensure uniqueness
-- - This design optimizes for: fetching recent messages, pagination, and bubble-specific queries

-- COMMENT: Conversation messages table design
-- - Similar to messages but for direct conversations
-- - Includes recipient_id for efficient bilateral message queries
-- - is_read field for unread message tracking
-- - Partitioned by conversation_id for optimal performance

-- COMMENT: User conversations table design
-- - Denormalized table for efficient user conversation listing
-- - Partitioned by user_id, clustered by last_message_at DESC
-- - Includes unread_count for efficient unread message display
-- - This design optimizes for: user conversation list, unread counts, conversation ordering

-- COMMENT: Typing indicators design
-- - Uses TTL (Time To Live) to automatically clean up old typing indicators
-- - Partitioned by bubble_id/conversation_id for real-time updates
-- - Short TTL (30 seconds) ensures indicators don't persist indefinitely

-- COMMENT: Message delivery status design
-- - Tracks delivery and read status for each message per user
-- - Enables read receipts and delivery confirmations
-- - Partitioned by bubble_id and message_id for efficient status queries

-- COMMENT: Performance considerations
-- - All tables are designed with query patterns in mind
-- - Partitioning ensures data locality and parallel processing
-- - Clustering orders optimize for common access patterns
-- - Secondary indexes support additional query patterns
-- - TTL tables automatically clean up temporary data

-- COMMENT: Scalability considerations
-- - Bubble-based partitioning distributes load across nodes
-- - Time-based clustering enables efficient range queries
-- - Counter columns for analytics reduce write amplification
-- - Set columns for tracking collections efficiently

-- COMMENT: Consistency considerations
-- - ScyllaDB provides eventual consistency by default
-- - Critical operations may need consistency level adjustments
-- - Denormalization trades consistency for query performance
-- - Application logic must handle eventual consistency scenarios
