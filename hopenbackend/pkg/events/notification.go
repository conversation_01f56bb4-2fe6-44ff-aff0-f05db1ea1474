package events

import (
	"encoding/json"
	"time"
)

// NotificationEvent represents a notification event for asynchronous communication
type NotificationEvent struct {
	UserID      string                 `json:"user_id"`
	Type        string                 `json:"type"` // e.g., "contact_request_received"
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Data        map[string]interface{} `json:"data"`
	GroupingKey *string                `json:"grouping_key,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// NotificationSubjects defines the NATS subjects for notification events
const (
	NotificationSendSubject = "notifications.send"
)

// Marshal marshals the NotificationEvent to JSON
func (e *NotificationEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

// Unmarshal unmarshals JSON data into a NotificationEvent
func UnmarshalNotificationEvent(data []byte) (*NotificationEvent, error) {
	var event NotificationEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return nil, err
	}
	return &event, nil
}
