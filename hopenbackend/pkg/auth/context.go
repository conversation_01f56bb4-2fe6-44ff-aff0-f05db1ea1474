package auth

import (
	"context"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// contextKey is a custom type for context keys to avoid collisions
type contextKey string

const (
	// UserIDKey is the context key for storing user ID
	UserIDKey contextKey = "user_id"
	// UserRoleKey is the context key for storing user role
	UserRoleKey contextKey = "user_role"
	// SessionIDKey is the context key for storing session ID
	SessionIDKey contextKey = "session_id"
)

var (
	// ErrUserNotAuthenticated is returned when no user is found in context
	ErrUserNotAuthenticated = errors.New("user not authenticated")
	// ErrInvalidUserID is returned when user ID is invalid
	ErrInvalidUserID = errors.New("invalid user ID")
)

// WithUserID adds user ID to the context
func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, UserIDKey, userID)
}

// WithUserRole adds user role to the context
func WithUserRole(ctx context.Context, role string) context.Context {
	return context.WithValue(ctx, User<PERSON><PERSON><PERSON><PERSON>, role)
}

// WithSessionID adds session ID to the context
func WithSessionID(ctx context.Context, sessionID string) context.Context {
	return context.WithValue(ctx, SessionIDKey, sessionID)
}

// GetUserID extracts user ID from context
func GetUserID(ctx context.Context) (string, error) {
	userID, ok := ctx.Value(UserIDKey).(string)
	if !ok || userID == "" {
		return "", ErrUserNotAuthenticated
	}
	
	// Check for anonymous users
	if userID == "anonymous" {
		return "", ErrUserNotAuthenticated
	}
	
	return userID, nil
}

// GetUserIDOrDefault extracts user ID from context or returns default value
func GetUserIDOrDefault(ctx context.Context, defaultValue string) string {
	userID, err := GetUserID(ctx)
	if err != nil {
		return defaultValue
	}
	return userID
}

// MustGetUserID extracts user ID from context and panics if not found
// This should only be used in handlers where authentication is guaranteed
func MustGetUserID(ctx context.Context) string {
	userID, err := GetUserID(ctx)
	if err != nil {
		panic("user ID not found in context - this indicates a bug in authentication middleware")
	}
	return userID
}

// GetUserRole extracts user role from context
func GetUserRole(ctx context.Context) (string, error) {
	role, ok := ctx.Value(UserRoleKey).(string)
	if !ok || role == "" {
		return "", errors.New("user role not found in context")
	}
	return role, nil
}

// GetUserRoleOrDefault extracts user role from context or returns default value
func GetUserRoleOrDefault(ctx context.Context, defaultValue string) string {
	role, err := GetUserRole(ctx)
	if err != nil {
		return defaultValue
	}
	return role
}

// GetSessionID extracts session ID from context
func GetSessionID(ctx context.Context) (string, error) {
	sessionID, ok := ctx.Value(SessionIDKey).(string)
	if !ok || sessionID == "" {
		return "", errors.New("session ID not found in context")
	}
	return sessionID, nil
}

// GetSessionIDOrDefault extracts session ID from context or returns default value
func GetSessionIDOrDefault(ctx context.Context, defaultValue string) string {
	sessionID, err := GetSessionID(ctx)
	if err != nil {
		return defaultValue
	}
	return sessionID
}

// RequireAuthentication checks if user is authenticated and returns gRPC error if not
func RequireAuthentication(ctx context.Context) error {
	_, err := GetUserID(ctx)
	if err != nil {
		return status.Errorf(codes.Unauthenticated, "authentication required")
	}
	return nil
}

// RequireRole checks if user has the required role and returns gRPC error if not
func RequireRole(ctx context.Context, requiredRole string) error {
	if err := RequireAuthentication(ctx); err != nil {
		return err
	}
	
	role, err := GetUserRole(ctx)
	if err != nil {
		return status.Errorf(codes.PermissionDenied, "user role not available")
	}
	
	if role != requiredRole {
		return status.Errorf(codes.PermissionDenied, "insufficient permissions")
	}
	
	return nil
}

// IsAuthenticated checks if user is authenticated without returning an error
func IsAuthenticated(ctx context.Context) bool {
	_, err := GetUserID(ctx)
	return err == nil
}

// HasRole checks if user has the specified role without returning an error
func HasRole(ctx context.Context, role string) bool {
	userRole, err := GetUserRole(ctx)
	return err == nil && userRole == role
}

// UserInfo represents user information stored in context
type UserInfo struct {
	ID        string
	Role      string
	SessionID string
}

// GetUserInfo extracts all user information from context
func GetUserInfo(ctx context.Context) (*UserInfo, error) {
	userID, err := GetUserID(ctx)
	if err != nil {
		return nil, err
	}
	
	return &UserInfo{
		ID:        userID,
		Role:      GetUserRoleOrDefault(ctx, "user"),
		SessionID: GetSessionIDOrDefault(ctx, ""),
	}, nil
}

// WithUserInfo adds complete user information to context
func WithUserInfo(ctx context.Context, userInfo *UserInfo) context.Context {
	ctx = WithUserID(ctx, userInfo.ID)
	if userInfo.Role != "" {
		ctx = WithUserRole(ctx, userInfo.Role)
	}
	if userInfo.SessionID != "" {
		ctx = WithSessionID(ctx, userInfo.SessionID)
	}
	return ctx
}
