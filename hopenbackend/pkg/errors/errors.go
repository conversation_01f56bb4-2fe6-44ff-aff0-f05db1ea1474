package errors

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

// ContextKey type for context keys
type ContextKey string

const (
	// CorrelationIDKey is the context key for correlation ID
	CorrelationIDKey ContextKey = "correlation_id"
	// RequestIDKey is the context key for request ID
	RequestIDKey ContextKey = "request_id"
	// UserIDKey is the context key for user ID
	UserIDKey ContextKey = "user_id"
	// ServiceNameKey is the context key for service name
	ServiceNameKey ContextKey = "service_name"
)

// ErrorCode represents application-specific error codes
type ErrorCode string

const (
	// Authentication and Authorization
	ErrorCodeUnauthorized ErrorCode = "UNAUTHORIZED"
	ErrorCodeForbidden    ErrorCode = "FORBIDDEN"
	ErrorCodeInvalidToken ErrorCode = "INVALID_TOKEN"
	ErrorCodeTokenExpired ErrorCode = "TOKEN_EXPIRED"

	// Validation
	ErrorCodeInvalidInput    ErrorCode = "INVALID_INPUT"
	ErrorCodeMissingField    ErrorCode = "MISSING_FIELD"
	ErrorCodeInvalidFormat   ErrorCode = "INVALID_FORMAT"
	ErrorCodeValueOutOfRange ErrorCode = "VALUE_OUT_OF_RANGE"

	// Resource Management
	ErrorCodeNotFound          ErrorCode = "NOT_FOUND"
	ErrorCodeAlreadyExists     ErrorCode = "ALREADY_EXISTS"
	ErrorCodeConflict          ErrorCode = "CONFLICT"
	ErrorCodeResourceExhausted ErrorCode = "RESOURCE_EXHAUSTED"

	// External Services
	ErrorCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrorCodeTimeout            ErrorCode = "TIMEOUT"
	ErrorCodeExternalService    ErrorCode = "EXTERNAL_SERVICE_ERROR"
	ErrorCodeCircuitBreaker     ErrorCode = "CIRCUIT_BREAKER_OPEN"

	// Internal Errors
	ErrorCodeInternal      ErrorCode = "INTERNAL_ERROR"
	ErrorCodeDatabaseError ErrorCode = "DATABASE_ERROR"
	ErrorCodeStorageError  ErrorCode = "STORAGE_ERROR"
	ErrorCodeConfigError   ErrorCode = "CONFIG_ERROR"
)

// AppError represents a structured application error
type AppError struct {
	Code          ErrorCode              `json:"code"`
	Message       string                 `json:"message"`
	Details       string                 `json:"details,omitempty"`
	CorrelationID string                 `json:"correlation_id,omitempty"`
	RequestID     string                 `json:"request_id,omitempty"`
	UserID        string                 `json:"user_id,omitempty"`
	ServiceName   string                 `json:"service_name,omitempty"`
	Timestamp     time.Time              `json:"timestamp"`
	StackTrace    string                 `json:"stack_trace,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	Cause         error                  `json:"-"` // Original error, not serialized
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap returns the underlying error
func (e *AppError) Unwrap() error {
	return e.Cause
}

// WithMetadata adds metadata to the error
func (e *AppError) WithMetadata(key string, value interface{}) *AppError {
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	e.Metadata[key] = value
	return e
}

// WithCause sets the underlying cause
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// NewAppError creates a new application error
func NewAppError(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
	}
}

// NewAppErrorWithDetails creates a new application error with details
func NewAppErrorWithDetails(code ErrorCode, message, details string) *AppError {
	return &AppError{
		Code:      code,
		Message:   message,
		Details:   details,
		Timestamp: time.Now(),
	}
}

// NewAppErrorFromContext creates a new application error with context information
func NewAppErrorFromContext(ctx context.Context, code ErrorCode, message string) *AppError {
	err := NewAppError(code, message)
	err.enrichFromContext(ctx)
	return err
}

// enrichFromContext enriches the error with context information
func (e *AppError) enrichFromContext(ctx context.Context) {
	if correlationID := GetCorrelationID(ctx); correlationID != "" {
		e.CorrelationID = correlationID
	}
	if requestID := GetRequestID(ctx); requestID != "" {
		e.RequestID = requestID
	}
	if userID := GetUserID(ctx); userID != "" {
		e.UserID = userID
	}
	if serviceName := GetServiceName(ctx); serviceName != "" {
		e.ServiceName = serviceName
	}
}

// WithStackTrace adds stack trace to the error
func (e *AppError) WithStackTrace() *AppError {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	e.StackTrace = string(buf[:n])
	return e
}

// ToGRPCError converts AppError to gRPC status error
func (e *AppError) ToGRPCError() error {
	grpcCode := e.toGRPCCode()

	// Create status with details
	st := status.New(grpcCode, e.Message)

	// Add metadata if available
	if e.CorrelationID != "" || e.RequestID != "" {
		md := metadata.Pairs()
		if e.CorrelationID != "" {
			md.Set("correlation-id", e.CorrelationID)
		}
		if e.RequestID != "" {
			md.Set("request-id", e.RequestID)
		}
		// Note: metadata would be set in the response trailer in actual implementation
	}

	return st.Err()
}

// toGRPCCode maps ErrorCode to gRPC codes
func (e *AppError) toGRPCCode() codes.Code {
	switch e.Code {
	case ErrorCodeUnauthorized, ErrorCodeInvalidToken, ErrorCodeTokenExpired:
		return codes.Unauthenticated
	case ErrorCodeForbidden:
		return codes.PermissionDenied
	case ErrorCodeInvalidInput, ErrorCodeMissingField, ErrorCodeInvalidFormat, ErrorCodeValueOutOfRange:
		return codes.InvalidArgument
	case ErrorCodeNotFound:
		return codes.NotFound
	case ErrorCodeAlreadyExists:
		return codes.AlreadyExists
	case ErrorCodeConflict:
		return codes.FailedPrecondition
	case ErrorCodeResourceExhausted:
		return codes.ResourceExhausted
	case ErrorCodeServiceUnavailable, ErrorCodeCircuitBreaker:
		return codes.Unavailable
	case ErrorCodeTimeout:
		return codes.DeadlineExceeded
	case ErrorCodeExternalService:
		return codes.Internal
	case ErrorCodeInternal, ErrorCodeDatabaseError, ErrorCodeStorageError, ErrorCodeConfigError:
		return codes.Internal
	default:
		return codes.Internal
	}
}

// Context helper functions

// WithCorrelationID adds correlation ID to context
func WithCorrelationID(ctx context.Context, correlationID string) context.Context {
	return context.WithValue(ctx, CorrelationIDKey, correlationID)
}

// GetCorrelationID retrieves correlation ID from context
func GetCorrelationID(ctx context.Context) string {
	if id, ok := ctx.Value(CorrelationIDKey).(string); ok {
		return id
	}
	return ""
}

// WithRequestID adds request ID to context
func WithRequestID(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, RequestIDKey, requestID)
}

// GetRequestID retrieves request ID from context
func GetRequestID(ctx context.Context) string {
	if id, ok := ctx.Value(RequestIDKey).(string); ok {
		return id
	}
	return ""
}

// WithUserID adds user ID to context
func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, UserIDKey, userID)
}

// GetUserID retrieves user ID from context
func GetUserID(ctx context.Context) string {
	if id, ok := ctx.Value(UserIDKey).(string); ok {
		return id
	}
	return ""
}

// WithServiceName adds service name to context
func WithServiceName(ctx context.Context, serviceName string) context.Context {
	return context.WithValue(ctx, ServiceNameKey, serviceName)
}

// GetServiceName retrieves service name from context
func GetServiceName(ctx context.Context) string {
	if name, ok := ctx.Value(ServiceNameKey).(string); ok {
		return name
	}
	return ""
}

// GenerateCorrelationID generates a new correlation ID
func GenerateCorrelationID() string {
	return uuid.New().String()
}

// GenerateRequestID generates a new request ID
func GenerateRequestID() string {
	return uuid.New().String()
}

// LogError logs an error with structured context
func LogError(logger *zap.Logger, err error, message string, fields ...zap.Field) {
	if appErr, ok := err.(*AppError); ok {
		logFields := []zap.Field{
			zap.String("error_code", string(appErr.Code)),
			zap.String("error_message", appErr.Message),
			zap.Time("timestamp", appErr.Timestamp),
		}

		if appErr.CorrelationID != "" {
			logFields = append(logFields, zap.String("correlation_id", appErr.CorrelationID))
		}
		if appErr.RequestID != "" {
			logFields = append(logFields, zap.String("request_id", appErr.RequestID))
		}
		if appErr.UserID != "" {
			logFields = append(logFields, zap.String("user_id", appErr.UserID))
		}
		if appErr.ServiceName != "" {
			logFields = append(logFields, zap.String("service_name", appErr.ServiceName))
		}
		if appErr.Details != "" {
			logFields = append(logFields, zap.String("details", appErr.Details))
		}
		if appErr.Cause != nil {
			logFields = append(logFields, zap.Error(appErr.Cause))
		}

		// Add metadata
		for key, value := range appErr.Metadata {
			logFields = append(logFields, zap.Any(key, value))
		}

		// Add custom fields
		logFields = append(logFields, fields...)

		logger.Error(message, logFields...)
	} else {
		// Regular error
		allFields := append([]zap.Field{zap.Error(err)}, fields...)
		logger.Error(message, allFields...)
	}
}

// gRPC Interceptors

// UnaryServerInterceptor returns a gRPC unary server interceptor for correlation ID tracking
func UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Extract or generate correlation ID
		correlationID := extractOrGenerateCorrelationID(ctx)

		// Generate request ID
		requestID := GenerateRequestID()

		// Extract service name from method
		serviceName := extractServiceName(info.FullMethod)

		// Enrich context
		enrichedCtx := WithCorrelationID(ctx, correlationID)
		enrichedCtx = WithRequestID(enrichedCtx, requestID)
		enrichedCtx = WithServiceName(enrichedCtx, serviceName)

		// Call handler
		resp, err := handler(enrichedCtx, req)

		// Convert AppError to gRPC error if needed
		if appErr, ok := err.(*AppError); ok {
			err = appErr.ToGRPCError()
		}

		return resp, err
	}
}

// StreamServerInterceptor returns a gRPC stream server interceptor for correlation ID tracking
func StreamServerInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		// Extract or generate correlation ID
		correlationID := extractOrGenerateCorrelationID(ss.Context())

		// Generate request ID
		requestID := GenerateRequestID()

		// Extract service name from method
		serviceName := extractServiceName(info.FullMethod)

		// Enrich context
		enrichedCtx := WithCorrelationID(ss.Context(), correlationID)
		enrichedCtx = WithRequestID(enrichedCtx, requestID)
		enrichedCtx = WithServiceName(enrichedCtx, serviceName)

		// Create wrapped stream with enriched context
		wrappedStream := &wrappedServerStream{
			ServerStream: ss,
			ctx:          enrichedCtx,
		}

		// Call handler
		err := handler(srv, wrappedStream)

		// Convert AppError to gRPC error if needed
		if appErr, ok := err.(*AppError); ok {
			err = appErr.ToGRPCError()
		}

		return err
	}
}

// wrappedServerStream wraps grpc.ServerStream with custom context
type wrappedServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

func (w *wrappedServerStream) Context() context.Context {
	return w.ctx
}

// extractOrGenerateCorrelationID extracts correlation ID from metadata or generates new one
func extractOrGenerateCorrelationID(ctx context.Context) string {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if correlationIDs := md.Get("correlation-id"); len(correlationIDs) > 0 {
			return correlationIDs[0]
		}
		if correlationIDs := md.Get("x-correlation-id"); len(correlationIDs) > 0 {
			return correlationIDs[0]
		}
	}
	return GenerateCorrelationID()
}

// extractServiceName extracts service name from gRPC method
func extractServiceName(fullMethod string) string {
	// gRPC method format: /package.service/method
	// Example: /hopen.user.v1.UserService/GetUser -> user

	if len(fullMethod) == 0 || fullMethod[0] != '/' {
		return ""
	}

	// Remove leading slash
	method := fullMethod[1:]

	// Split by slash to get service part
	parts := strings.Split(method, "/")
	if len(parts) != 2 {
		return ""
	}

	servicePart := parts[0]

	// Extract service name from package.service format
	serviceParts := strings.Split(servicePart, ".")
	if len(serviceParts) < 2 {
		return ""
	}

	// Get the service name (second to last part before "Service")
	for i := len(serviceParts) - 1; i >= 0; i-- {
		part := serviceParts[i]
		if strings.HasSuffix(part, "Service") {
			// Remove "Service" suffix and convert to lowercase
			serviceName := strings.TrimSuffix(part, "Service")
			return strings.ToLower(serviceName)
		}
	}

	return ""
}
