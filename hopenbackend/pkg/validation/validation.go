package validation

import (
	"context"
	"fmt"
	"net/mail"
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation failed for field '%s': %s", e.Field, e.Message)
}

// ValidationErrors represents multiple validation errors
type ValidationErrors struct {
	Errors []ValidationError `json:"errors"`
}

func (e *ValidationErrors) Error() string {
	if len(e.Errors) == 1 {
		return e.Errors[0].Error()
	}
	return fmt.Sprintf("validation failed with %d errors", len(e.Errors))
}

// Add adds a validation error
func (e *ValidationErrors) Add(field, message string, value ...string) {
	err := ValidationError{
		Field:   field,
		Message: message,
	}
	if len(value) > 0 {
		err.Value = value[0]
	}
	e.Errors = append(e.Errors, err)
}

// HasErrors returns true if there are validation errors
func (e *ValidationErrors) HasErrors() bool {
	return len(e.Errors) > 0
}

// ToGRPCError converts validation errors to gRPC error
func (e *ValidationErrors) ToGRPCError() error {
	if !e.HasErrors() {
		return nil
	}
	
	var messages []string
	for _, err := range e.Errors {
		messages = append(messages, err.Error())
	}
	
	return status.Errorf(codes.InvalidArgument, "validation failed: %s", strings.Join(messages, "; "))
}

// Validator provides validation functions
type Validator struct {
	logger *zap.Logger
}

// NewValidator creates a new validator
func NewValidator(logger *zap.Logger) *Validator {
	return &Validator{
		logger: logger,
	}
}

// String validation

// ValidateRequired validates that a string is not empty
func (v *Validator) ValidateRequired(field, value string) *ValidationError {
	if strings.TrimSpace(value) == "" {
		return &ValidationError{
			Field:   field,
			Message: "is required",
		}
	}
	return nil
}

// ValidateLength validates string length
func (v *Validator) ValidateLength(field, value string, min, max int) *ValidationError {
	length := utf8.RuneCountInString(value)
	if length < min {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must be at least %d characters long", min),
			Value:   value,
		}
	}
	if max > 0 && length > max {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must be at most %d characters long", max),
			Value:   value,
		}
	}
	return nil
}

// ValidateEmail validates email format
func (v *Validator) ValidateEmail(field, email string) *ValidationError {
	if email == "" {
		return nil // Use ValidateRequired separately if needed
	}
	
	if _, err := mail.ParseAddress(email); err != nil {
		return &ValidationError{
			Field:   field,
			Message: "must be a valid email address",
			Value:   email,
		}
	}
	return nil
}

// ValidateUUID validates UUID format
func (v *Validator) ValidateUUID(field, id string) *ValidationError {
	if id == "" {
		return nil // Use ValidateRequired separately if needed
	}
	
	if _, err := uuid.Parse(id); err != nil {
		return &ValidationError{
			Field:   field,
			Message: "must be a valid UUID",
			Value:   id,
		}
	}
	return nil
}

// ValidateAlphanumeric validates that string contains only alphanumeric characters
func (v *Validator) ValidateAlphanumeric(field, value string) *ValidationError {
	if value == "" {
		return nil
	}
	
	for _, r := range value {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) {
			return &ValidationError{
				Field:   field,
				Message: "must contain only letters and numbers",
				Value:   value,
			}
		}
	}
	return nil
}

// ValidatePattern validates string against regex pattern
func (v *Validator) ValidatePattern(field, value, pattern, message string) *ValidationError {
	if value == "" {
		return nil
	}
	
	matched, err := regexp.MatchString(pattern, value)
	if err != nil {
		v.logger.Error("Invalid regex pattern", zap.String("pattern", pattern), zap.Error(err))
		return &ValidationError{
			Field:   field,
			Message: "validation pattern error",
		}
	}
	
	if !matched {
		return &ValidationError{
			Field:   field,
			Message: message,
			Value:   value,
		}
	}
	return nil
}

// ValidateNoSQLInjection validates that string doesn't contain SQL injection patterns
func (v *Validator) ValidateNoSQLInjection(field, value string) *ValidationError {
	if value == "" {
		return nil
	}
	
	// Common SQL injection patterns
	sqlPatterns := []string{
		`(?i)(union\s+select)`,
		`(?i)(drop\s+table)`,
		`(?i)(delete\s+from)`,
		`(?i)(insert\s+into)`,
		`(?i)(update\s+.+set)`,
		`(?i)(exec\s*\()`,
		`(?i)(script\s*>)`,
		`(?i)(<\s*script)`,
		`--`,
		`;`,
		`'.*'`,
		`".*"`,
	}
	
	for _, pattern := range sqlPatterns {
		if matched, _ := regexp.MatchString(pattern, value); matched {
			return &ValidationError{
				Field:   field,
				Message: "contains potentially dangerous content",
				Value:   value,
			}
		}
	}
	return nil
}

// ValidateNoXSS validates that string doesn't contain XSS patterns
func (v *Validator) ValidateNoXSS(field, value string) *ValidationError {
	if value == "" {
		return nil
	}
	
	// Common XSS patterns
	xssPatterns := []string{
		`(?i)<script`,
		`(?i)javascript:`,
		`(?i)on\w+\s*=`,
		`(?i)<iframe`,
		`(?i)<object`,
		`(?i)<embed`,
		`(?i)<link`,
		`(?i)<meta`,
		`(?i)expression\s*\(`,
		`(?i)@import`,
	}
	
	for _, pattern := range xssPatterns {
		if matched, _ := regexp.MatchString(pattern, value); matched {
			return &ValidationError{
				Field:   field,
				Message: "contains potentially dangerous content",
				Value:   value,
			}
		}
	}
	return nil
}

// Numeric validation

// ValidateRange validates that a number is within range
func (v *Validator) ValidateRange(field string, value, min, max int64) *ValidationError {
	if value < min {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must be at least %d", min),
			Value:   fmt.Sprintf("%d", value),
		}
	}
	if max > 0 && value > max {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must be at most %d", max),
			Value:   fmt.Sprintf("%d", value),
		}
	}
	return nil
}

// ValidatePositive validates that a number is positive
func (v *Validator) ValidatePositive(field string, value int64) *ValidationError {
	if value <= 0 {
		return &ValidationError{
			Field:   field,
			Message: "must be positive",
			Value:   fmt.Sprintf("%d", value),
		}
	}
	return nil
}

// Collection validation

// ValidateSliceLength validates slice length
func (v *Validator) ValidateSliceLength(field string, slice []interface{}, min, max int) *ValidationError {
	length := len(slice)
	if length < min {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must contain at least %d items", min),
			Value:   fmt.Sprintf("%d items", length),
		}
	}
	if max > 0 && length > max {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must contain at most %d items", max),
			Value:   fmt.Sprintf("%d items", length),
		}
	}
	return nil
}

// ValidateStringSliceLength validates string slice length
func (v *Validator) ValidateStringSliceLength(field string, slice []string, min, max int) *ValidationError {
	length := len(slice)
	if length < min {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must contain at least %d items", min),
			Value:   fmt.Sprintf("%d items", length),
		}
	}
	if max > 0 && length > max {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("must contain at most %d items", max),
			Value:   fmt.Sprintf("%d items", length),
		}
	}
	return nil
}

// Sanitization functions

// SanitizeString removes potentially dangerous characters
func (v *Validator) SanitizeString(value string) string {
	// Remove null bytes
	value = strings.ReplaceAll(value, "\x00", "")
	
	// Remove control characters except tab, newline, and carriage return
	var result strings.Builder
	for _, r := range value {
		if unicode.IsControl(r) && r != '\t' && r != '\n' && r != '\r' {
			continue
		}
		result.WriteRune(r)
	}
	
	return strings.TrimSpace(result.String())
}

// SanitizeHTML removes HTML tags and entities
func (v *Validator) SanitizeHTML(value string) string {
	// Remove HTML tags
	htmlTagRegex := regexp.MustCompile(`<[^>]*>`)
	value = htmlTagRegex.ReplaceAllString(value, "")
	
	// Remove HTML entities
	htmlEntityRegex := regexp.MustCompile(`&[a-zA-Z0-9#]+;`)
	value = htmlEntityRegex.ReplaceAllString(value, "")
	
	return v.SanitizeString(value)
}

// gRPC Interceptor

// UnaryServerInterceptor returns a gRPC unary server interceptor for input validation
func (v *Validator) UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Validate request based on method
		if err := v.validateRequest(req, info.FullMethod); err != nil {
			v.logger.Warn("Request validation failed",
				zap.String("method", info.FullMethod),
				zap.Error(err))
			return nil, err
		}
		
		return handler(ctx, req)
	}
}

// validateRequest validates gRPC request
func (v *Validator) validateRequest(req interface{}, method string) error {
	// This would be implemented based on your specific protobuf messages
	// For now, we'll implement basic validation that can be extended
	
	// Example: validate common fields if they exist
	// This would be replaced with proper protobuf reflection or generated validation
	
	return nil // Placeholder - implement based on your protobuf schema
}
