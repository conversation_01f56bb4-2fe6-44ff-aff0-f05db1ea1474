package validation

import (
	"reflect"
	"strings"

	"go.uber.org/zap"
)

// ProtobufValidator provides validation for protobuf messages
type ProtobufValidator struct {
	validator *Validator
	logger    *zap.Logger
}

// NewProtobufValidator creates a new protobuf validator
func NewProtobufValidator(logger *zap.Logger) *ProtobufValidator {
	return &ProtobufValidator{
		validator: NewValidator(logger),
		logger:    logger,
	}
}

// ValidateMessage validates a protobuf message using reflection
func (pv *ProtobufValidator) ValidateMessage(msg interface{}) *ValidationErrors {
	errors := &ValidationErrors{}
	
	if msg == nil {
		errors.Add("message", "cannot be nil")
		return errors
	}
	
	pv.validateStruct(reflect.ValueOf(msg), "", errors)
	return errors
}

// validateStruct validates a struct using reflection
func (pv *ProtobufValidator) validateStruct(v reflect.Value, prefix string, errors *ValidationErrors) {
	// Handle pointers
	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return
		}
		v = v.Elem()
	}
	
	if v.Kind() != reflect.Struct {
		return
	}
	
	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		
		// Skip unexported fields
		if !field.CanInterface() {
			continue
		}
		
		fieldName := pv.getFieldName(fieldType, prefix)
		
		// Validate based on field type and tags
		pv.validateField(field, fieldType, fieldName, errors)
	}
}

// validateField validates a single field
func (pv *ProtobufValidator) validateField(field reflect.Value, fieldType reflect.StructField, fieldName string, errors *ValidationErrors) {
	// Handle pointers
	if field.Kind() == reflect.Ptr {
		if field.IsNil() {
			return
		}
		field = field.Elem()
	}
	
	switch field.Kind() {
	case reflect.String:
		pv.validateStringField(field.String(), fieldType, fieldName, errors)
	case reflect.Int32, reflect.Int64:
		pv.validateIntField(field.Int(), fieldType, fieldName, errors)
	case reflect.Uint32, reflect.Uint64:
		pv.validateUintField(field.Uint(), fieldType, fieldName, errors)
	case reflect.Slice:
		pv.validateSliceField(field, fieldType, fieldName, errors)
	case reflect.Struct:
		pv.validateStruct(field, fieldName, errors)
	}
}

// validateStringField validates string fields
func (pv *ProtobufValidator) validateStringField(value string, fieldType reflect.StructField, fieldName string, errors *ValidationErrors) {
	// Check for required tag
	if pv.hasTag(fieldType, "required") && strings.TrimSpace(value) == "" {
		errors.Add(fieldName, "is required")
		return
	}
	
	// Skip validation for empty optional fields
	if value == "" {
		return
	}
	
	// Validate length
	if minLen := pv.getIntTag(fieldType, "min_len"); minLen > 0 {
		if err := pv.validator.ValidateLength(fieldName, value, minLen, 0); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	if maxLen := pv.getIntTag(fieldType, "max_len"); maxLen > 0 {
		if err := pv.validator.ValidateLength(fieldName, value, 0, maxLen); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	// Validate format
	if pv.hasTag(fieldType, "email") {
		if err := pv.validator.ValidateEmail(fieldName, value); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	if pv.hasTag(fieldType, "uuid") {
		if err := pv.validator.ValidateUUID(fieldName, value); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	if pv.hasTag(fieldType, "alphanumeric") {
		if err := pv.validator.ValidateAlphanumeric(fieldName, value); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	// Security validation
	if pv.hasTag(fieldType, "no_sql_injection") {
		if err := pv.validator.ValidateNoSQLInjection(fieldName, value); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	if pv.hasTag(fieldType, "no_xss") {
		if err := pv.validator.ValidateNoXSS(fieldName, value); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	// Pattern validation
	if pattern := pv.getStringTag(fieldType, "pattern"); pattern != "" {
		message := pv.getStringTag(fieldType, "pattern_message")
		if message == "" {
			message = "format is invalid"
		}
		if err := pv.validator.ValidatePattern(fieldName, value, pattern, message); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
}

// validateIntField validates integer fields
func (pv *ProtobufValidator) validateIntField(value int64, fieldType reflect.StructField, fieldName string, errors *ValidationErrors) {
	// Validate range
	if min := pv.getIntTag(fieldType, "min"); min != 0 {
		if err := pv.validator.ValidateRange(fieldName, value, int64(min), 0); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	if max := pv.getIntTag(fieldType, "max"); max != 0 {
		if err := pv.validator.ValidateRange(fieldName, value, 0, int64(max)); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
	
	// Validate positive
	if pv.hasTag(fieldType, "positive") {
		if err := pv.validator.ValidatePositive(fieldName, value); err != nil {
			errors.Add(err.Field, err.Message, err.Value)
		}
	}
}

// validateUintField validates unsigned integer fields
func (pv *ProtobufValidator) validateUintField(value uint64, fieldType reflect.StructField, fieldName string, errors *ValidationErrors) {
	// Convert to int64 for validation (assuming reasonable ranges)
	if value <= 9223372036854775807 { // max int64
		pv.validateIntField(int64(value), fieldType, fieldName, errors)
	}
}

// validateSliceField validates slice fields
func (pv *ProtobufValidator) validateSliceField(field reflect.Value, fieldType reflect.StructField, fieldName string, errors *ValidationErrors) {
	// Validate slice length
	if minLen := pv.getIntTag(fieldType, "min_items"); minLen > 0 {
		if field.Len() < minLen {
			errors.Add(fieldName, "must contain at least "+string(rune(minLen))+" items")
		}
	}
	
	if maxLen := pv.getIntTag(fieldType, "max_items"); maxLen > 0 {
		if field.Len() > maxLen {
			errors.Add(fieldName, "must contain at most "+string(rune(maxLen))+" items")
		}
	}
	
	// Validate slice elements
	for i := 0; i < field.Len(); i++ {
		elem := field.Index(i)
		elemFieldName := fieldName + "[" + string(rune(i)) + "]"
		
		if elem.Kind() == reflect.String {
			pv.validateStringField(elem.String(), fieldType, elemFieldName, errors)
		} else if elem.Kind() == reflect.Struct {
			pv.validateStruct(elem, elemFieldName, errors)
		}
	}
}

// Helper functions for tag parsing

// hasTag checks if a field has a specific validation tag
func (pv *ProtobufValidator) hasTag(field reflect.StructField, tag string) bool {
	validate := field.Tag.Get("validate")
	return strings.Contains(validate, tag)
}

// getStringTag gets a string value from validation tag
func (pv *ProtobufValidator) getStringTag(field reflect.StructField, tag string) string {
	validate := field.Tag.Get("validate")
	parts := strings.Split(validate, ",")
	
	for _, part := range parts {
		if strings.HasPrefix(part, tag+"=") {
			return strings.TrimPrefix(part, tag+"=")
		}
	}
	return ""
}

// getIntTag gets an integer value from validation tag
func (pv *ProtobufValidator) getIntTag(field reflect.StructField, tag string) int {
	value := pv.getStringTag(field, tag)
	if value == "" {
		return 0
	}
	
	// Simple integer parsing (you might want to use strconv.Atoi for production)
	result := 0
	for _, r := range value {
		if r >= '0' && r <= '9' {
			result = result*10 + int(r-'0')
		} else {
			break
		}
	}
	return result
}

// getFieldName gets the field name for validation messages
func (pv *ProtobufValidator) getFieldName(field reflect.StructField, prefix string) string {
	// Use json tag if available, otherwise use field name
	jsonTag := field.Tag.Get("json")
	if jsonTag != "" && jsonTag != "-" {
		name := strings.Split(jsonTag, ",")[0]
		if name != "" {
			if prefix != "" {
				return prefix + "." + name
			}
			return name
		}
	}
	
	// Convert CamelCase to snake_case
	name := pv.camelToSnake(field.Name)
	if prefix != "" {
		return prefix + "." + name
	}
	return name
}

// camelToSnake converts CamelCase to snake_case
func (pv *ProtobufValidator) camelToSnake(s string) string {
	var result strings.Builder
	for i, r := range s {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// Common validation functions for specific message types

// ValidateUserID validates user ID format
func (pv *ProtobufValidator) ValidateUserID(userID string) *ValidationError {
	if err := pv.validator.ValidateRequired("user_id", userID); err != nil {
		return err
	}
	return pv.validator.ValidateUUID("user_id", userID)
}

// ValidateFileName validates file name
func (pv *ProtobufValidator) ValidateFileName(fileName string) *ValidationError {
	if err := pv.validator.ValidateRequired("file_name", fileName); err != nil {
		return err
	}
	if err := pv.validator.ValidateLength("file_name", fileName, 1, 255); err != nil {
		return err
	}
	// Check for dangerous characters
	dangerousChars := []string{"..", "/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	for _, char := range dangerousChars {
		if strings.Contains(fileName, char) {
			return &ValidationError{
				Field:   "file_name",
				Message: "contains invalid characters",
				Value:   fileName,
			}
		}
	}
	return nil
}

// ValidateFileSize validates file size
func (pv *ProtobufValidator) ValidateFileSize(size int64, maxSize int64) *ValidationError {
	if err := pv.validator.ValidatePositive("file_size", size); err != nil {
		return err
	}
	if maxSize > 0 {
		return pv.validator.ValidateRange("file_size", size, 0, maxSize)
	}
	return nil
}
