package idempotency

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/valkey-io/valkey-go"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// Manager handles request deduplication for critical operations
type Manager struct {
	valkeyClient valkey.Client
	logger       *zap.Logger
	ttl          time.Duration
	cache        map[string]*CachedResponse
	cacheMutex   sync.RWMutex
	config       *config.Config
}

// CachedResponse represents a cached idempotent response
type CachedResponse struct {
	StatusCode int                    `json:"status_code"`
	Headers    map[string]string      `json:"headers"`
	Body       interface{}            `json:"body"`
	Timestamp  time.Time              `json:"timestamp"`
	TTL        time.Duration          `json:"ttl"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// IdempotencyKey represents an idempotency key with metadata
type IdempotencyKey struct {
	Key       string                 `json:"key"`
	UserID    string                 `json:"user_id"`
	Method    string                 `json:"method"`
	Path      string                 `json:"path"`
	Body      string                 `json:"body"`
	Headers   map[string]string      `json:"headers"`
	CreatedAt time.Time              `json:"created_at"`
	ExpiresAt time.Time              `json:"expires_at"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// OperationType defines the type of operation for idempotency
type OperationType string

const (
	// Critical operations that require idempotency
	OperationBubbleCreate     OperationType = "bubble_create"
	OperationContactRequest   OperationType = "contact_request"
	OperationFriendRequest    OperationType = "friend_request"
	OperationBubbleJoin       OperationType = "bubble_join"
	OperationBubbleInvite     OperationType = "bubble_invite"
	OperationMessageSend      OperationType = "message_send"
	OperationFileUpload       OperationType = "file_upload"
	OperationCallStart        OperationType = "call_start"
	OperationPayment          OperationType = "payment"
	OperationUserRegistration OperationType = "user_registration"
)

// New creates a new idempotency manager
func New(cfg *config.Config, logger *zap.Logger, valkeyClient valkey.Client) *Manager {
	return &Manager{
		valkeyClient: valkeyClient,
		logger:       logger,
		cache:        make(map[string]*CachedResponse),
		ttl:          time.Hour, // Default TTL
		config:       cfg,
	}
}

// GenerateKey generates an idempotency key from request parameters
func (m *Manager) GenerateKey(userID, method, path string, body []byte, headers map[string]string) string {
	// Create a deterministic hash from request components
	hasher := sha256.New()
	hasher.Write([]byte(userID))
	hasher.Write([]byte(method))
	hasher.Write([]byte(path))
	hasher.Write(body)

	// Include relevant headers (excluding timestamp-based headers)
	relevantHeaders := make(map[string]string)
	for k, v := range headers {
		switch k {
		case "Content-Type", "Authorization", "X-Request-ID", "X-Idempotency-Key":
			relevantHeaders[k] = v
		}
	}

	headerBytes, _ := json.Marshal(relevantHeaders)
	hasher.Write(headerBytes)

	return hex.EncodeToString(hasher.Sum(nil))
}

// GetOrCreateKey gets an existing idempotency key or creates a new one
func (m *Manager) GetOrCreateKey(ctx context.Context, userID, method, path string, body []byte, headers map[string]string, operation OperationType) (*IdempotencyKey, error) {
	key := m.GenerateKey(userID, method, path, body, headers)

	// Check if key exists in Valkey
	valkeyKey := fmt.Sprintf("idempotency:%s", key)
	cmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Get().Key(valkeyKey).Build())
	cached, err := cmd.ToString()
	if err != nil {
		if valkey.IsValkeyNil(err) {
			// Key doesn't exist, create new one
			idempotencyKey := &IdempotencyKey{
				Key:       key,
				UserID:    userID,
				Method:    method,
				Path:      path,
				Body:      string(body),
				Headers:   headers,
				CreatedAt: time.Now(),
				ExpiresAt: time.Now().Add(m.ttl),
				Metadata: map[string]interface{}{
					"operation_type": string(operation),
					"attempts":       1,
				},
			}

			// Store in Valkey
			keyBytes, _ := json.Marshal(idempotencyKey)
			setCmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Set().Key(valkeyKey).Value(string(keyBytes)).Ex(m.ttl).Build())
			if err := setCmd.Error(); err != nil {
				return nil, fmt.Errorf("failed to store idempotency key: %w", err)
			}

			m.logger.Info("Created new idempotency key",
				zap.String("key", key),
				zap.String("user_id", userID),
				zap.String("operation", string(operation)))

			return idempotencyKey, nil
		} else {
			return nil, fmt.Errorf("failed to check idempotency key: %w", err)
		}
	}

	// Key exists, parse and return
	var idempotencyKey IdempotencyKey
	err = json.Unmarshal([]byte(cached), &idempotencyKey)
	if err != nil {
		return nil, fmt.Errorf("failed to parse idempotency key: %w", err)
	}

	// Increment attempt counter
	if attempts, ok := idempotencyKey.Metadata["attempts"].(float64); ok {
		idempotencyKey.Metadata["attempts"] = attempts + 1
	}

	// Update in Valkey
	keyBytes, _ := json.Marshal(idempotencyKey)
	m.valkeyClient.Do(ctx, m.valkeyClient.B().Set().Key(valkeyKey).Value(string(keyBytes)).Ex(m.ttl).Build())

	m.logger.Info("Found existing idempotency key",
		zap.String("key", key),
		zap.String("user_id", userID),
		zap.Float64("attempts", idempotencyKey.Metadata["attempts"].(float64)))

	return &idempotencyKey, nil
}

// StoreResponse stores a response for an idempotency key
func (m *Manager) StoreResponse(ctx context.Context, key string, statusCode int, headers map[string]string, body interface{}, metadata map[string]interface{}) error {
	response := &CachedResponse{
		StatusCode: statusCode,
		Headers:    headers,
		Body:       body,
		Timestamp:  time.Now(),
		TTL:        m.ttl,
		Metadata:   metadata,
	}

	// Store in Valkey
	valkeyKey := fmt.Sprintf("idempotency:response:%s", key)
	responseBytes, err := json.Marshal(response)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	setCmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Set().Key(valkeyKey).Value(string(responseBytes)).Ex(m.ttl).Build())
	if err := setCmd.Error(); err != nil {
		return fmt.Errorf("failed to store response: %w", err)
	}

	// Also store in local cache for faster access
	m.cacheMutex.Lock()
	m.cache[key] = response
	m.cacheMutex.Unlock()

	m.logger.Info("Stored idempotent response",
		zap.String("key", key),
		zap.Int("status_code", statusCode))

	return nil
}

// GetResponse retrieves a cached response for an idempotency key
func (m *Manager) GetResponse(ctx context.Context, key string) (*CachedResponse, error) {
	// Check local cache first
	m.cacheMutex.RLock()
	if response, exists := m.cache[key]; exists {
		m.cacheMutex.RUnlock()

		// Check if response is still valid
		if time.Since(response.Timestamp) < response.TTL {
			return response, nil
		}

		// Remove expired response from cache
		m.cacheMutex.Lock()
		delete(m.cache, key)
		m.cacheMutex.Unlock()
	} else {
		m.cacheMutex.RUnlock()
	}

	// Check Valkey
	valkeyKey := fmt.Sprintf("idempotency:response:%s", key)
	cmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Get().Key(valkeyKey).Build())
	cached, err := cmd.ToString()

	if err != nil {
		if valkey.IsValkeyNil(err) {
			return nil, nil // No cached response
		} else {
			return nil, fmt.Errorf("failed to get cached response: %w", err)
		}
	}

	var response CachedResponse
	err = json.Unmarshal([]byte(cached), &response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse cached response: %w", err)
	}

	// Store in local cache
	m.cacheMutex.Lock()
	m.cache[key] = &response
	m.cacheMutex.Unlock()

	return &response, nil
}

// IsOperationIdempotent checks if an operation type requires idempotency
func (m *Manager) IsOperationIdempotent(operation OperationType) bool {
	idempotentOps := map[OperationType]bool{
		OperationBubbleCreate:     true,
		OperationContactRequest:   true,
		OperationFriendRequest:    true,
		OperationBubbleJoin:       true,
		OperationBubbleInvite:     true,
		OperationMessageSend:      true,
		OperationFileUpload:       true,
		OperationCallStart:        true,
		OperationPayment:          true,
		OperationUserRegistration: true,
	}

	return idempotentOps[operation]
}

// CleanupExpired removes expired idempotency keys and responses
func (m *Manager) CleanupExpired(ctx context.Context) error {
	// Clean up local cache
	m.cacheMutex.Lock()
	for key, response := range m.cache {
		if time.Since(response.Timestamp) >= response.TTL {
			delete(m.cache, key)
		}
	}
	m.cacheMutex.Unlock()

	// Valkey handles TTL automatically, but we can scan for any orphaned keys
	pattern := "idempotency:*"
	iter := m.valkeyClient.Scan(ctx, pattern, 100)

	var expiredKeys []string
	for iter.Next(ctx) {
		key := iter.Val()
		ttlCmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().TTL().Key(key).Build())
		ttl, err := ttlCmd.ToDuration()
		if err != nil {
			return fmt.Errorf("failed to get TTL for key %s: %w", key, err)
		}

		if ttl < 0 { // Key exists but has no TTL set
			expiredKeys = append(expiredKeys, key)
		}
	}

	if err := iter.Err(); err != nil {
		return fmt.Errorf("failed to scan for expired keys: %w", err)
	}

	// Delete orphaned keys
	if len(expiredKeys) > 0 {
		for _, key := range expiredKeys {
			delCmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Del().Key(key).Build())
			if err := delCmd.Error(); err != nil {
				return fmt.Errorf("failed to delete expired key %s: %w", key, err)
			}
		}

		m.logger.Info("Cleaned up expired idempotency keys",
			zap.Int("count", len(expiredKeys)))
	}

	return nil
}

// GetKeyStats returns statistics about idempotency key usage
func (m *Manager) GetKeyStats(ctx context.Context, key string) (map[string]interface{}, error) {
	valkeyKey := fmt.Sprintf("idempotency:%s", key)
	cmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Get().Key(valkeyKey).Build())
	cached, err := cmd.ToString()

	if err != nil {
		if valkey.IsValkeyNil(err) {
			return nil, fmt.Errorf("idempotency key not found")
		} else {
			return nil, fmt.Errorf("failed to get idempotency key: %w", err)
		}
	}

	var idempotencyKey IdempotencyKey
	err = json.Unmarshal([]byte(cached), &idempotencyKey)
	if err != nil {
		return nil, fmt.Errorf("failed to parse idempotency key: %w", err)
	}

	// Check if response exists
	responseKey := fmt.Sprintf("idempotency:response:%s", key)
	existsCmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Exists().Key(responseKey).Build())
	hasResponse := existsCmd.ToInt() > 0

	stats := map[string]interface{}{
		"key":          idempotencyKey.Key,
		"user_id":      idempotencyKey.UserID,
		"method":       idempotencyKey.Method,
		"path":         idempotencyKey.Path,
		"created_at":   idempotencyKey.CreatedAt,
		"expires_at":   idempotencyKey.ExpiresAt,
		"has_response": hasResponse,
		"metadata":     idempotencyKey.Metadata,
	}

	return stats, nil
}

// SetTTL sets the TTL for idempotency keys
func (m *Manager) SetTTL(ttl time.Duration) {
	m.ttl = ttl
}

// GetTTL returns the current TTL setting
func (m *Manager) GetTTL() time.Duration {
	return m.ttl
}

// StartCleanupRoutine starts a background routine to clean up expired keys
func (m *Manager) StartCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if err := m.CleanupExpired(ctx); err != nil {
				m.logger.Error("Failed to cleanup expired idempotency keys", zap.Error(err))
			}
		}
	}
}

// ValidateKey validates an idempotency key format and content
func (m *Manager) ValidateKey(key string) error {
	if len(key) != 64 { // SHA256 hex string length
		return fmt.Errorf("invalid key length: expected 64, got %d", len(key))
	}

	// Check if key contains only hex characters
	for _, char := range key {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f')) {
			return fmt.Errorf("invalid key format: contains non-hex character")
		}
	}

	return nil
}

// GetOperationTTL returns the TTL for a specific operation type
func (m *Manager) GetOperationTTL(operation OperationType) time.Duration {
	// Check configuration first
	if ttl, exists := m.config.Enterprise.Idempotency.OperationTTLs[string(operation)]; exists {
		return ttl
	}

	// Fallback defaults if not configured
	switch operation {
	case OperationBubbleCreate:
		return 24 * time.Hour
	case OperationContactRequest:
		return 1 * time.Hour
	case OperationFriendRequest:
		return 1 * time.Hour
	case OperationBubbleJoin:
		return 30 * time.Minute
	case OperationBubbleInvite:
		return 30 * time.Minute
	case OperationMessageSend:
		return 5 * time.Minute
	case OperationFileUpload:
		return 1 * time.Hour
	case OperationCallStart:
		return 10 * time.Minute
	case OperationPayment:
		return 24 * time.Hour
	case OperationUserRegistration:
		return 24 * time.Hour
	default:
		return m.config.Enterprise.Idempotency.DefaultTTL // Use configured default TTL
	}
}

// CheckKey checks the status of an idempotency key
func (m *Manager) CheckKey(ctx context.Context, key string) (*KeyData, error) {
	valkeyKey := fmt.Sprintf("idempotency:%s", key)

	cmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Get().Key(valkeyKey).Build())
	cached, err := cmd.ToString()
	if err != nil {
		if valkey.IsValkeyNil(err) {
			// Key doesn't exist, create it
			keyData := &KeyData{
				Key:       key,
				CreatedAt: time.Now(),
				Status:    "processing",
			}

			keyBytes, _ := json.Marshal(keyData)
			setCmd := m.valkeyClient.Do(ctx, m.valkeyClient.B().Set().Key(valkeyKey).Value(string(keyBytes)).Ex(m.ttl).Build())
			if err := setCmd.Error(); err != nil {
				return nil, fmt.Errorf("failed to store new key data: %w", err)
			}

			m.logger.Info("Created new key data",
				zap.String("key", key),
				zap.String("status", keyData.Status))

			return keyData, nil
		} else {
			return nil, fmt.Errorf("failed to check key data: %w", err)
		}
	}

	// Key exists, parse and return
	var keyData KeyData
	err = json.Unmarshal([]byte(cached), &keyData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse key data: %w", err)
	}

	return &keyData, nil
}

// KeyData holds information about an idempotency key
type KeyData struct {
	Key       string    `json:"key"`
	CreatedAt time.Time `json:"created_at"`
	Status    string    `json:"status"`
}
