package audit

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
)

// EventType represents the type of audit event
type EventType string

const (
	// Authentication events
	EventTypeLogin          EventType = "LOGIN"
	EventTypeLogout         EventType = "LOGOUT"
	EventTypeLoginFailed    EventType = "LOGIN_FAILED"
	EventTypeTokenRefresh   EventType = "TOKEN_REFRESH"
	EventTypePasswordChange EventType = "PASSWORD_CHANGE"

	// Authorization events
	EventTypeAccessGranted   EventType = "ACCESS_GRANTED"
	EventTypeAccessDenied    EventType = "ACCESS_DENIED"
	EventTypePermissionCheck EventType = "PERMISSION_CHECK"

	// Data access events
	EventTypeDataRead   EventType = "DATA_READ"
	EventTypeDataCreate EventType = "DATA_CREATE"
	EventTypeDataUpdate EventType = "DATA_UPDATE"
	EventTypeDataDelete EventType = "DATA_DELETE"
	EventTypeDataExport EventType = "DATA_EXPORT"

	// File operations
	EventTypeFileUpload   EventType = "FILE_UPLOAD"
	EventTypeFileDownload EventType = "FILE_DOWNLOAD"
	EventTypeFileDelete   EventType = "FILE_DELETE"
	EventTypeFileShare    EventType = "FILE_SHARE"

	// Social operations
	EventTypeContactRequest EventType = "CONTACT_REQUEST"
	EventTypeFriendRequest  EventType = "FRIEND_REQUEST"
	EventTypeBubbleCreate   EventType = "BUBBLE_CREATE"
	EventTypeBubbleJoin     EventType = "BUBBLE_JOIN"
	EventTypeBubbleLeave    EventType = "BUBBLE_LEAVE"
	EventTypeCallStart      EventType = "CALL_START"
	EventTypeCallEnd        EventType = "CALL_END"

	// Security events
	EventTypeSecurityViolation  EventType = "SECURITY_VIOLATION"
	EventTypeRateLimitHit       EventType = "RATE_LIMIT_HIT"
	EventTypeSuspiciousActivity EventType = "SUSPICIOUS_ACTIVITY"
	EventTypeValidationFailure  EventType = "VALIDATION_FAILURE"

	// System events
	EventTypeSystemError  EventType = "SYSTEM_ERROR"
	EventTypeConfigChange EventType = "CONFIG_CHANGE"
	EventTypeServiceStart EventType = "SERVICE_START"
	EventTypeServiceStop  EventType = "SERVICE_STOP"
)

// Severity represents the severity level of an audit event
type Severity string

const (
	SeverityInfo     Severity = "INFO"
	SeverityWarning  Severity = "WARNING"
	SeverityError    Severity = "ERROR"
	SeverityCritical Severity = "CRITICAL"
)

// AuditEvent represents a single audit event
type AuditEvent struct {
	// Core fields
	ID        string    `json:"id"`
	Timestamp time.Time `json:"timestamp"`
	EventType EventType `json:"event_type"`
	Severity  Severity  `json:"severity"`
	Message   string    `json:"message"`

	// Context fields
	UserID        string `json:"user_id,omitempty"`
	SessionID     string `json:"session_id,omitempty"`
	CorrelationID string `json:"correlation_id,omitempty"`
	RequestID     string `json:"request_id,omitempty"`

	// Network fields
	ClientIP  string `json:"client_ip,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`

	// Service fields
	ServiceName string `json:"service_name,omitempty"`
	Method      string `json:"method,omitempty"`

	// Resource fields
	ResourceType string `json:"resource_type,omitempty"`
	ResourceID   string `json:"resource_id,omitempty"`

	// Operation fields
	Action string `json:"action,omitempty"`
	Result string `json:"result,omitempty"`

	// Additional data
	Metadata map[string]interface{} `json:"metadata,omitempty"`

	// Compliance fields
	DataClassification string `json:"data_classification,omitempty"`
	RetentionPeriod    string `json:"retention_period,omitempty"`
}

// AuditLogger handles audit logging
type AuditLogger struct {
	logger      *zap.Logger
	serviceName string
	enabled     bool
}

// NewAuditLogger creates a new audit logger
func NewAuditLogger(logger *zap.Logger, serviceName string) *AuditLogger {
	return &AuditLogger{
		logger:      logger,
		serviceName: serviceName,
		enabled:     true,
	}
}

// LogEvent logs an audit event
func (al *AuditLogger) LogEvent(ctx context.Context, event *AuditEvent) {
	if !al.enabled {
		return
	}

	// Set default values
	if event.ID == "" {
		event.ID = uuid.New().String()
	}
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}
	if event.ServiceName == "" {
		event.ServiceName = al.serviceName
	}

	// Enrich with context information
	al.enrichEventFromContext(ctx, event)

	// Convert to JSON for structured logging
	eventJSON, err := json.Marshal(event)
	if err != nil {
		al.logger.Error("Failed to marshal audit event", zap.Error(err))
		return
	}

	// Log based on severity
	switch event.Severity {
	case SeverityInfo:
		al.logger.Info("AUDIT",
			zap.String("audit_event", string(eventJSON)),
			zap.String("event_type", string(event.EventType)),
			zap.String("user_id", event.UserID),
			zap.String("correlation_id", event.CorrelationID))
	case SeverityWarning:
		al.logger.Warn("AUDIT",
			zap.String("audit_event", string(eventJSON)),
			zap.String("event_type", string(event.EventType)),
			zap.String("user_id", event.UserID),
			zap.String("correlation_id", event.CorrelationID))
	case SeverityError:
		al.logger.Error("AUDIT",
			zap.String("audit_event", string(eventJSON)),
			zap.String("event_type", string(event.EventType)),
			zap.String("user_id", event.UserID),
			zap.String("correlation_id", event.CorrelationID))
	case SeverityCritical:
		al.logger.Error("AUDIT_CRITICAL",
			zap.String("audit_event", string(eventJSON)),
			zap.String("event_type", string(event.EventType)),
			zap.String("user_id", event.UserID),
			zap.String("correlation_id", event.CorrelationID))
	default:
		al.logger.Info("AUDIT",
			zap.String("audit_event", string(eventJSON)),
			zap.String("event_type", string(event.EventType)),
			zap.String("user_id", event.UserID),
			zap.String("correlation_id", event.CorrelationID))
	}
}

// enrichEventFromContext enriches the audit event with context information
func (al *AuditLogger) enrichEventFromContext(ctx context.Context, event *AuditEvent) {
	// Extract correlation ID
	if correlationID := getStringFromContext(ctx, "correlation_id"); correlationID != "" {
		event.CorrelationID = correlationID
	}

	// Extract request ID
	if requestID := getStringFromContext(ctx, "request_id"); requestID != "" {
		event.RequestID = requestID
	}

	// Extract user ID
	if userID := getStringFromContext(ctx, "user_id"); userID != "" {
		event.UserID = userID
	}

	// Extract session ID
	if sessionID := getStringFromContext(ctx, "session_id"); sessionID != "" {
		event.SessionID = sessionID
	}

	// Extract client IP from peer info
	if p, ok := peer.FromContext(ctx); ok {
		event.ClientIP = p.Addr.String()
	}

	// Extract metadata
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		// Extract user agent
		if userAgents := md.Get("user-agent"); len(userAgents) > 0 {
			event.UserAgent = userAgents[0]
		}

		// Extract forwarded IP if available
		if xff := md.Get("x-forwarded-for"); len(xff) > 0 {
			event.ClientIP = xff[0]
		}
	}
}

// getStringFromContext extracts a string value from context
func getStringFromContext(ctx context.Context, key string) string {
	if value := ctx.Value(key); value != nil {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// Convenience methods for common audit events

// LogAuthentication logs authentication events
func (al *AuditLogger) LogAuthentication(ctx context.Context, userID, method string, success bool, reason string) {
	eventType := EventTypeLogin
	severity := SeverityInfo
	result := "SUCCESS"

	if !success {
		eventType = EventTypeLoginFailed
		severity = SeverityWarning
		result = "FAILURE"
	}

	event := &AuditEvent{
		EventType: eventType,
		Severity:  severity,
		Message:   fmt.Sprintf("Authentication attempt via %s", method),
		UserID:    userID,
		Action:    "authenticate",
		Result:    result,
		Metadata: map[string]interface{}{
			"auth_method": method,
			"reason":      reason,
		},
		DataClassification: "SENSITIVE",
		RetentionPeriod:    "7_YEARS",
	}

	al.LogEvent(ctx, event)
}

// LogDataAccess logs data access events
func (al *AuditLogger) LogDataAccess(ctx context.Context, userID, resourceType, resourceID, action string) {
	event := &AuditEvent{
		EventType:          EventTypeDataRead,
		Severity:           SeverityInfo,
		Message:            fmt.Sprintf("Data access: %s %s", action, resourceType),
		UserID:             userID,
		ResourceType:       resourceType,
		ResourceID:         resourceID,
		Action:             action,
		Result:             "SUCCESS",
		DataClassification: "CONFIDENTIAL",
		RetentionPeriod:    "3_YEARS",
	}

	al.LogEvent(ctx, event)
}

// LogFileOperation logs file operation events
func (al *AuditLogger) LogFileOperation(ctx context.Context, userID, operation, fileName string, fileSize int64) {
	var eventType EventType
	switch operation {
	case "upload":
		eventType = EventTypeFileUpload
	case "download":
		eventType = EventTypeFileDownload
	case "delete":
		eventType = EventTypeFileDelete
	case "share":
		eventType = EventTypeFileShare
	default:
		eventType = EventTypeDataUpdate
	}

	event := &AuditEvent{
		EventType:    eventType,
		Severity:     SeverityInfo,
		Message:      fmt.Sprintf("File %s: %s", operation, fileName),
		UserID:       userID,
		ResourceType: "file",
		ResourceID:   fileName,
		Action:       operation,
		Result:       "SUCCESS",
		Metadata: map[string]interface{}{
			"file_name": fileName,
			"file_size": fileSize,
		},
		DataClassification: "INTERNAL",
		RetentionPeriod:    "1_YEAR",
	}

	al.LogEvent(ctx, event)
}

// LogSecurityEvent logs security-related events
func (al *AuditLogger) LogSecurityEvent(ctx context.Context, eventType EventType, severity Severity, message string, metadata map[string]interface{}) {
	event := &AuditEvent{
		EventType:          eventType,
		Severity:           severity,
		Message:            message,
		Action:             "security_check",
		Result:             "DETECTED",
		Metadata:           metadata,
		DataClassification: "RESTRICTED",
		RetentionPeriod:    "10_YEARS",
	}

	al.LogEvent(ctx, event)
}

// LogSocialAction logs social platform actions
func (al *AuditLogger) LogSocialAction(ctx context.Context, userID, action, targetType, targetID string) {
	var eventType EventType
	switch action {
	case "contact_request":
		eventType = EventTypeContactRequest
	case "friend_request":
		eventType = EventTypeFriendRequest
	case "bubble_create":
		eventType = EventTypeBubbleCreate
	case "bubble_join":
		eventType = EventTypeBubbleJoin
	case "bubble_leave":
		eventType = EventTypeBubbleLeave
	case "call_start":
		eventType = EventTypeCallStart
	case "call_end":
		eventType = EventTypeCallEnd
	default:
		eventType = EventTypeDataUpdate
	}

	event := &AuditEvent{
		EventType:          eventType,
		Severity:           SeverityInfo,
		Message:            fmt.Sprintf("Social action: %s on %s", action, targetType),
		UserID:             userID,
		ResourceType:       targetType,
		ResourceID:         targetID,
		Action:             action,
		Result:             "SUCCESS",
		DataClassification: "INTERNAL",
		RetentionPeriod:    "3_YEARS",
	}

	al.LogEvent(ctx, event)
}

// gRPC Interceptor for automatic audit logging

// UnaryServerInterceptor returns a gRPC unary server interceptor for audit logging
func (al *AuditLogger) UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		start := time.Now()

		// Extract service and method
		service, method := extractServiceMethod(info.FullMethod)

		// Call the handler
		resp, err := handler(ctx, req)

		// Log audit event for sensitive operations
		if al.shouldAuditMethod(info.FullMethod) {
			severity := SeverityInfo
			result := "SUCCESS"

			if err != nil {
				severity = SeverityError
				result = "FAILURE"
			}

			event := &AuditEvent{
				EventType: al.getEventTypeForMethod(info.FullMethod),
				Severity:  severity,
				Message:   fmt.Sprintf("gRPC call: %s", info.FullMethod),
				Method:    info.FullMethod,
				Action:    method,
				Result:    result,
				Metadata: map[string]interface{}{
					"service":  service,
					"method":   method,
					"duration": time.Since(start).Milliseconds(),
				},
				DataClassification: al.getDataClassification(info.FullMethod),
				RetentionPeriod:    al.getRetentionPeriod(info.FullMethod),
			}

			if err != nil {
				event.Metadata["error"] = err.Error()
			}

			al.LogEvent(ctx, event)
		}

		return resp, err
	}
}

// shouldAuditMethod determines if a gRPC method should be audited
func (al *AuditLogger) shouldAuditMethod(method string) bool {
	// Audit sensitive operations
	sensitiveOperations := []string{
		"Login", "Logout", "RefreshToken",
		"CreateUser", "UpdateUser", "DeleteUser",
		"UploadFile", "DeleteFile", "UploadProfilePicture",
		"SendContactRequest", "AcceptContactRequest", "RemoveContact",
		"SendFriendRequest", "AcceptFriendRequest", "RemoveFriend",
		"CreateBubble", "JoinBubble", "LeaveBubble",
		"CreateCall", "JoinCall", "EndCall",
		"UpdateProfile", "ChangePassword",
	}

	for _, op := range sensitiveOperations {
		if strings.Contains(method, op) {
			return true
		}
	}

	return false
}

// getEventTypeForMethod maps gRPC methods to audit event types
func (al *AuditLogger) getEventTypeForMethod(method string) EventType {
	if strings.Contains(method, "Login") {
		return EventTypeLogin
	}
	if strings.Contains(method, "Logout") {
		return EventTypeLogout
	}
	if strings.Contains(method, "Upload") {
		return EventTypeFileUpload
	}
	if strings.Contains(method, "Delete") {
		return EventTypeDataDelete
	}
	if strings.Contains(method, "Create") {
		return EventTypeDataCreate
	}
	if strings.Contains(method, "Update") {
		return EventTypeDataUpdate
	}
	if strings.Contains(method, "Contact") {
		return EventTypeContactRequest
	}
	if strings.Contains(method, "Friend") {
		return EventTypeFriendRequest
	}
	if strings.Contains(method, "Bubble") {
		return EventTypeBubbleCreate
	}
	if strings.Contains(method, "Call") {
		return EventTypeCallStart
	}

	return EventTypeDataRead
}

// getDataClassification returns data classification for a method
func (al *AuditLogger) getDataClassification(method string) string {
	if strings.Contains(method, "Login") || strings.Contains(method, "Password") {
		return "RESTRICTED"
	}
	if strings.Contains(method, "User") || strings.Contains(method, "Profile") {
		return "CONFIDENTIAL"
	}
	if strings.Contains(method, "File") || strings.Contains(method, "Media") {
		return "INTERNAL"
	}

	return "INTERNAL"
}

// getRetentionPeriod returns retention period for a method
func (al *AuditLogger) getRetentionPeriod(method string) string {
	if strings.Contains(method, "Login") || strings.Contains(method, "Auth") {
		return "7_YEARS"
	}
	if strings.Contains(method, "Delete") || strings.Contains(method, "Remove") {
		return "10_YEARS"
	}
	if strings.Contains(method, "File") || strings.Contains(method, "Media") {
		return "3_YEARS"
	}

	return "1_YEAR"
}

// extractServiceMethod extracts service and method names from gRPC full method
func extractServiceMethod(fullMethod string) (service, method string) {
	if len(fullMethod) == 0 || fullMethod[0] != '/' {
		return "unknown", "unknown"
	}

	// Remove leading slash
	fullMethod = fullMethod[1:]

	// Split by slash
	parts := strings.Split(fullMethod, "/")
	if len(parts) != 2 {
		return "unknown", "unknown"
	}

	servicePart := parts[0]
	methodPart := parts[1]

	// Extract service name from package.service format
	serviceParts := strings.Split(servicePart, ".")
	if len(serviceParts) >= 2 {
		for i := len(serviceParts) - 1; i >= 0; i-- {
			part := serviceParts[i]
			if strings.HasSuffix(part, "Service") {
				service = strings.TrimSuffix(part, "Service")
				service = strings.ToLower(service)
				break
			}
		}
	}

	if service == "" {
		service = "unknown"
	}

	return service, methodPart
}
