package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	App        AppConfig        `mapstructure:"app"`
	Databases  DatabasesConfig  `mapstructure:"databases"`
	Valkey     ValkeyConfig     `mapstructure:"valkey"`
	NATS       NATSConfig       `mapstructure:"nats"`
	MinIO      MinIOConfig      `mapstructure:"minio"`
	MQTT       MQTTConfig       `mapstructure:"mqtt"`
	LiveKit    LiveKitConfig    `mapstructure:"livekit"`
	JWT        JWTConfig        `mapstructure:"jwt"`
	RateLimit  RateLimitConfig  `mapstructure:"rate_limit"`
	Ory        OryConfig        `mapstructure:"ory"`
	Enterprise EnterpriseConfig `mapstructure:"enterprise"`
	Logging    LoggingConfig    `mapstructure:"logging"`
	CORS       CORSConfig       `mapstructure:"cors"`
	Social     SocialConfig     `mapstructure:"social"`
	Media      MediaConfig      `mapstructure:"media"`
	Firebase   FirebaseConfig   `mapstructure:"firebase"`
	AWS        AWSConfig        `mapstructure:"aws"`
}

type AppConfig struct {
	Name        string    `mapstructure:"name"`
	Version     string    `mapstructure:"version"`
	Environment string    `mapstructure:"environment"`
	Port        int       `mapstructure:"port"`
	Debug       bool      `mapstructure:"debug"`
	TLS         TLSConfig `mapstructure:"tls"`
}

type TLSConfig struct {
	Enabled     bool   `mapstructure:"enabled"`
	CertFile    string `mapstructure:"cert_file"`
	KeyFile     string `mapstructure:"key_file"`
	HTTP3       bool   `mapstructure:"http3"`
	HTTP2       bool   `mapstructure:"http2"`
	AutoCert    bool   `mapstructure:"auto_cert"`
	AutoCertDir string `mapstructure:"auto_cert_dir"`
}

type DatabasesConfig struct {
	PostgreSQL PostgreSQLConfig `mapstructure:"postgresql"`
	ScyllaDB   ScyllaDBConfig   `mapstructure:"scylladb"`
}

type PostgreSQLConfig struct {
	Host                  string        `mapstructure:"host"`
	Port                  int           `mapstructure:"port"`
	Database              string        `mapstructure:"database"`
	Username              string        `mapstructure:"username"`
	Password              string        `mapstructure:"password"`
	SSLMode               string        `mapstructure:"ssl_mode"`
	MaxConnections        int32         `mapstructure:"max_connections"`
	MinConnections        int32         `mapstructure:"min_connections"`
	MaxConnectionLifetime time.Duration `mapstructure:"max_connection_lifetime"`
	MaxConnectionIdleTime time.Duration `mapstructure:"max_connection_idle_time"`
	HealthCheckPeriod     time.Duration `mapstructure:"health_check_period"`
}

type ScyllaDBConfig struct {
	Hosts                   []string      `mapstructure:"hosts"`
	Keyspace                string        `mapstructure:"keyspace"`
	Consistency             string        `mapstructure:"consistency"`
	NumConnections          int           `mapstructure:"num_connections"`
	Timeout                 time.Duration `mapstructure:"timeout"`
	ConnectTimeout          time.Duration `mapstructure:"connect_timeout"`
	LocalDC                 string        `mapstructure:"local_dc"`
	ShardAwarePort          int           `mapstructure:"shard_aware_port"`
	DisableShardAwarePort   bool          `mapstructure:"disable_shard_aware_port"`
	EnableHostVerification  bool          `mapstructure:"enable_host_verification"`
	EnableTokenAwareRouting bool          `mapstructure:"enable_token_aware_routing"`
}

type ValkeyConfig struct {
	Host               string        `mapstructure:"host"`
	Port               int           `mapstructure:"port"`
	Address            string        `mapstructure:"address"`
	Password           string        `mapstructure:"password"`
	Database           int           `mapstructure:"database"`
	PoolSize           int           `mapstructure:"pool_size"`
	MinIdleConnections int           `mapstructure:"min_idle_connections"`
	MaxRetries         int           `mapstructure:"max_retries"`
	DialTimeout        time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout        time.Duration `mapstructure:"read_timeout"`
	WriteTimeout       time.Duration `mapstructure:"write_timeout"`
}

type NATSConfig struct {
	Host          string        `mapstructure:"host"`
	Port          int           `mapstructure:"port"`
	Username      string        `mapstructure:"username"`
	Password      string        `mapstructure:"password"`
	URL           string        `mapstructure:"url"`
	MaxReconnects int           `mapstructure:"max_reconnects"`
	ReconnectWait time.Duration `mapstructure:"reconnect_wait"`
	Timeout       time.Duration `mapstructure:"timeout"`
}

type MinIOConfig struct {
	Endpoint         string `mapstructure:"endpoint"`
	ExternalEndpoint string `mapstructure:"external_endpoint"`
	AccessKey        string `mapstructure:"access_key"`
	SecretKey        string `mapstructure:"secret_key"`
	UseSSL           bool   `mapstructure:"use_ssl"`
	BucketName       string `mapstructure:"bucket_name"`
	Region           string `mapstructure:"region"`
}

type MQTTConfig struct {
	Broker       string `mapstructure:"broker"`
	ClientID     string `mapstructure:"client_id"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	KeepAlive    int    `mapstructure:"keep_alive"`
	CleanSession bool   `mapstructure:"clean_session"`
	QoS          byte   `mapstructure:"qos"`
}

type LiveKitConfig struct {
	ServerURL string `mapstructure:"server_url"`
	APIKey    string `mapstructure:"api_key"`
	APISecret string `mapstructure:"api_secret"`
	Enabled   bool   `mapstructure:"enabled"`
}

type JWTConfig struct {
	Secret             string        `mapstructure:"secret"`
	RefreshSecret      string        `mapstructure:"refresh_secret"`
	Issuer             string        `mapstructure:"issuer"`
	Audience           string        `mapstructure:"audience"`
	AccessTokenExpiry  time.Duration `mapstructure:"access_token_expiry"`
	RefreshTokenExpiry time.Duration `mapstructure:"refresh_token_expiry"`
}

type RateLimitConfig struct {
	Enabled         bool          `mapstructure:"enabled"`
	DefaultLimit    int64         `mapstructure:"default_limit"`
	DefaultWindow   time.Duration `mapstructure:"default_window"`
	BurstMultiplier float64       `mapstructure:"burst_multiplier"`
	CleanupInterval time.Duration `mapstructure:"cleanup_interval"`

	// Social operation rate limits with burst allowance
	SocialLimits map[string]SocialRateLimit `mapstructure:"social_limits"`

	// Reputation-based multipliers
	ReputationMultipliers map[string]float64 `mapstructure:"reputation_multipliers"`
}

// SocialRateLimit defines rate limits for social operations with burst capability
type SocialRateLimit struct {
	Limit       int64         `mapstructure:"limit"`
	Window      time.Duration `mapstructure:"window"`
	BurstLimit  int64         `mapstructure:"burst_limit"`
	BurstWindow time.Duration `mapstructure:"burst_window"`
}

type OryConfig struct {
	KratosPublicURL string `mapstructure:"kratos_public_url"`
	KratosAdminURL  string `mapstructure:"kratos_admin_url"`
	HydraPublicURL  string `mapstructure:"hydra_public_url"`
	HydraAdminURL   string `mapstructure:"hydra_admin_url"`
	// Oathkeeper / Keto removed – authorization handled in-process
}

type EnterpriseConfig struct {
	Security struct {
		Enabled           bool   `mapstructure:"enabled"`
		CSPPolicy         string `mapstructure:"csp_policy"`
		HSTSMaxAge        int    `mapstructure:"hsts_max_age"`
		MaxRequestSize    int64  `mapstructure:"max_request_size"`
		EnableContentScan bool   `mapstructure:"enable_content_scan"`
	} `mapstructure:"security"`
	Resilience struct {
		Enabled         bool `mapstructure:"enabled"`
		CircuitBreakers map[string]struct {
			MaxFailures  int           `mapstructure:"max_failures"`
			Timeout      time.Duration `mapstructure:"timeout"`
			ResetTimeout time.Duration `mapstructure:"reset_timeout"`
		} `mapstructure:"circuit_breakers"`
	} `mapstructure:"resilience"`
	Monitoring struct {
		Enabled         bool `mapstructure:"enabled"`
		MetricsPort     int  `mapstructure:"metrics_port"`
		SocialMetrics   bool `mapstructure:"social_metrics"`
		BusinessMetrics bool `mapstructure:"business_metrics"`
	} `mapstructure:"monitoring"`
	Gateway struct {
		Enabled          bool           `mapstructure:"enabled"`
		Port             int            `mapstructure:"port"`
		ServiceDiscovery bool           `mapstructure:"service_discovery"`
		LoadBalancing    bool           `mapstructure:"load_balancing"`
		Routes           []GatewayRoute `mapstructure:"routes"`
	} `mapstructure:"gateway"`
	Database struct {
		EnhancedPooling    bool `mapstructure:"enhanced_pooling"`
		ReadWriteSplitting bool `mapstructure:"read_write_splitting"`
		HealthMonitoring   bool `mapstructure:"health_monitoring"`
		AutoScaling        bool `mapstructure:"auto_scaling"`
	} `mapstructure:"database"`
	Idempotency struct {
		Enabled              bool                     `mapstructure:"enabled"`
		DefaultTTL           time.Duration            `mapstructure:"default_ttl"`
		Storage              string                   `mapstructure:"storage"`
		EnableFingerprinting bool                     `mapstructure:"enable_fingerprinting"`
		OperationTTLs        map[string]time.Duration `mapstructure:"operation_ttls"`
	} `mapstructure:"idempotency"`
}

type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

type CORSConfig struct {
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
	ExposedHeaders   []string `mapstructure:"expose_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	MaxAge           int      `mapstructure:"max_age"`
}

type SocialConfig struct {
	Bubble struct {
		InitialExpiryDays int `mapstructure:"initial_expiry_days"`
		ExtensionDays     int `mapstructure:"extension_days"`
		MaxMembers        int `mapstructure:"max_members"`
		MinMembers        int `mapstructure:"min_members"`
	} `mapstructure:"bubble"`
	Friendship struct {
		AutoGenerateOnBubbleExpiry bool `mapstructure:"auto_generate_on_bubble_expiry"`
		RequestExpiryDays          int  `mapstructure:"request_expiry_days"`
	} `mapstructure:"friendship"`
	Contact struct {
		MaxRequestsPerDay int `mapstructure:"max_requests_per_day"`
		RequestExpiryDays int `mapstructure:"request_expiry_days"`
	} `mapstructure:"contact"`
	Chat struct {
		MaxMessageLength   int      `mapstructure:"max_message_length"`
		FileUploadMaxSize  int64    `mapstructure:"file_upload_max_size"`
		SupportedFileTypes []string `mapstructure:"supported_file_types"`
	} `mapstructure:"chat"`
}

type MediaConfig struct {
	MaxFileSize                int64     `mapstructure:"max_file_size"`
	ProfilePictureMaxSize      int64     `mapstructure:"profile_picture_max_size"`
	AllowedTypes               []string  `mapstructure:"allowed_types"`
	ProfilePictureTypes        []string  `mapstructure:"profile_picture_types"`
	ProfilePictureOutputFormat string    `mapstructure:"profile_picture_output_format"`
	ThumbnailSize              int       `mapstructure:"thumbnail_size"`
	CDN                        CDNConfig `mapstructure:"cdn"`
}

type CDNConfig struct {
	Enabled      bool   `mapstructure:"enabled"`
	BaseURL      string `mapstructure:"base_url"`
	CacheControl string `mapstructure:"cache_control"`
}

type FirebaseConfig struct {
	ServiceAccountPath string `mapstructure:"service_account_path"`
	ProjectID          string `mapstructure:"project_id"`
}

type AWSConfig struct {
	Region       string `mapstructure:"region"`
	SESFromEmail string `mapstructure:"ses_from_email"`
}

// GatewayRoute defines routing configuration for the API Gateway
type GatewayRoute struct {
	Path        string `mapstructure:"path"`
	ServiceName string `mapstructure:"service_name"`
	StripPrefix bool   `mapstructure:"strip_prefix"`
	Timeout     int    `mapstructure:"timeout_seconds"`
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	viper.AddConfigPath("/etc/hopen")

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvPrefix("HOPEN")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// GetDSN returns PostgreSQL connection string
func (c *PostgreSQLConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.Username, c.Password, c.Database, c.SSLMode)
}
