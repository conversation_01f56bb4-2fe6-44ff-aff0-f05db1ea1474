package pagination

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
)

var (
	// ErrInvalidPageState is returned when page state cannot be decoded
	ErrInvalidPageState = errors.New("invalid page state")
	// ErrEmptyPageState is returned when page state is empty
	ErrEmptyPageState = errors.New("empty page state")
)

// ScyllaDBPageState represents the pagination state for ScyllaDB queries
type ScyllaDBPageState struct {
	// PageState is the raw ScyllaDB page state bytes
	PageState []byte `json:"page_state"`
	// Limit is the page size used for this query
	Limit int `json:"limit"`
	// Version is for future compatibility
	Version int `json:"version"`
}

// EncodePageState encodes ScyllaDB page state to a base64 string
func EncodePageState(pageState []byte, limit int) (string, error) {
	if len(pageState) == 0 {
		return "", nil
	}

	state := ScyllaDBPageState{
		PageState: pageState,
		Limit:     limit,
		Version:   1, // Current version
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(state)
	if err != nil {
		return "", fmt.Errorf("failed to marshal page state: %w", err)
	}

	// Encode to base64 for safe transport
	encoded := base64.URLEncoding.EncodeToString(jsonData)
	return encoded, nil
}

// DecodePageState decodes a base64 string to ScyllaDB page state
func DecodePageState(encodedState string) ([]byte, int, error) {
	if encodedState == "" {
		return nil, 0, ErrEmptyPageState
	}

	// Decode from base64
	jsonData, err := base64.URLEncoding.DecodeString(encodedState)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to decode base64 page state: %w", err)
	}

	// Unmarshal from JSON
	var state ScyllaDBPageState
	if err := json.Unmarshal(jsonData, &state); err != nil {
		return nil, 0, fmt.Errorf("failed to unmarshal page state: %w", err)
	}

	// Validate version
	if state.Version != 1 {
		return nil, 0, fmt.Errorf("unsupported page state version: %d", state.Version)
	}

	return state.PageState, state.Limit, nil
}

// SafeDecodePageState decodes page state and returns nil if invalid
// This is useful when you want to gracefully handle invalid page states
func SafeDecodePageState(encodedState string) ([]byte, int) {
	pageState, limit, err := DecodePageState(encodedState)
	if err != nil {
		// Return nil page state to start from beginning
		return nil, 0
	}
	return pageState, limit
}

// ValidatePageState checks if a page state string is valid
func ValidatePageState(encodedState string) error {
	if encodedState == "" {
		return nil // Empty is valid
	}

	_, _, err := DecodePageState(encodedState)
	return err
}

// PageInfo represents pagination information for responses
type PageInfo struct {
	// HasNextPage indicates if there are more results
	HasNextPage bool `json:"has_next_page"`
	// NextPageState is the encoded page state for the next page
	NextPageState *string `json:"next_page_state,omitempty"`
	// PageSize is the number of items in this page
	PageSize int `json:"page_size"`
	// RequestedLimit is the limit that was requested
	RequestedLimit int `json:"requested_limit"`
}

// CreatePageInfo creates pagination info for a response
func CreatePageInfo(nextPageState []byte, pageSize, requestedLimit int) (*PageInfo, error) {
	info := &PageInfo{
		HasNextPage:    len(nextPageState) > 0,
		PageSize:       pageSize,
		RequestedLimit: requestedLimit,
	}

	if len(nextPageState) > 0 {
		encoded, err := EncodePageState(nextPageState, requestedLimit)
		if err != nil {
			return nil, fmt.Errorf("failed to encode next page state: %w", err)
		}
		info.NextPageState = &encoded
	}

	return info, nil
}

// PaginationOptions represents options for paginated queries
type PaginationOptions struct {
	// Limit is the maximum number of items to return
	Limit int
	// PageState is the encoded page state for continuation
	PageState string
	// DefaultLimit is used when limit is not specified or invalid
	DefaultLimit int
	// MaxLimit is the maximum allowed limit
	MaxLimit int
}

// Validate validates and normalizes pagination options
func (opts *PaginationOptions) Validate() error {
	// Set default limit if not specified
	if opts.Limit <= 0 {
		opts.Limit = opts.DefaultLimit
	}

	// Enforce maximum limit
	if opts.MaxLimit > 0 && opts.Limit > opts.MaxLimit {
		opts.Limit = opts.MaxLimit
	}

	// Validate page state if provided
	if opts.PageState != "" {
		if err := ValidatePageState(opts.PageState); err != nil {
			return fmt.Errorf("invalid page state: %w", err)
		}
	}

	return nil
}

// GetPageState returns the decoded page state bytes
func (opts *PaginationOptions) GetPageState() []byte {
	if opts.PageState == "" {
		return nil
	}

	pageState, _ := SafeDecodePageState(opts.PageState)
	return pageState
}

// NewPaginationOptions creates pagination options with defaults
func NewPaginationOptions(limit int, pageState string) *PaginationOptions {
	return &PaginationOptions{
		Limit:        limit,
		PageState:    pageState,
		DefaultLimit: 50,  // Default page size
		MaxLimit:     500, // Maximum page size
	}
}
