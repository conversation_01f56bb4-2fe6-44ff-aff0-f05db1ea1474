package circuitbreaker

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// State represents the circuit breaker state
type State int

const (
	StateClosed State = iota
	StateHalfOpen
	StateOpen
)

func (s State) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateHalfOpen:
		return "HALF_OPEN"
	case StateOpen:
		return "OPEN"
	default:
		return "UNKNOWN"
	}
}

// Config holds circuit breaker configuration
type Config struct {
	Name               string        // Circuit breaker name for logging
	MaxRequests        uint32        // Max requests allowed in half-open state
	Interval           time.Duration // Time window for failure counting
	Timeout            time.Duration // Time to wait before transitioning from open to half-open
	FailureThreshold   uint32        // Number of failures to trigger open state
	SuccessThreshold   uint32        // Number of successes to close from half-open
	OnStateChange      func(name string, from State, to State)
	IsFailure          func(err error) bool // Custom failure detection
}

// DefaultConfig returns a sensible default configuration
func DefaultConfig(name string) Config {
	return Config{
		Name:             name,
		MaxRequests:      10,
		Interval:         60 * time.Second,
		Timeout:          60 * time.Second,
		FailureThreshold: 5,
		SuccessThreshold: 3,
		IsFailure: func(err error) bool {
			return err != nil
		},
	}
}

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	config   Config
	state    State
	mutex    sync.RWMutex
	logger   *zap.Logger

	// Counters
	requests      uint32
	failures      uint32
	successes     uint32
	lastFailTime  time.Time
	lastStateTime time.Time

	// Metrics
	totalRequests uint64
	totalFailures uint64
	totalSuccesses uint64
}

// New creates a new circuit breaker
func New(config Config, logger *zap.Logger) *CircuitBreaker {
	if config.OnStateChange == nil {
		config.OnStateChange = func(name string, from State, to State) {
			logger.Info("Circuit breaker state changed",
				zap.String("name", name),
				zap.String("from", from.String()),
				zap.String("to", to.String()))
		}
	}

	return &CircuitBreaker{
		config:        config,
		state:         StateClosed,
		logger:        logger,
		lastStateTime: time.Now(),
	}
}

// Execute runs the given function with circuit breaker protection
func (cb *CircuitBreaker) Execute(ctx context.Context, fn func() error) error {
	// Check if we can proceed
	if !cb.canProceed() {
		cb.recordMetrics(false, true) // blocked request
		return &CircuitBreakerError{
			Name:  cb.config.Name,
			State: cb.state,
		}
	}

	// Execute the function
	err := fn()
	
	// Record the result
	cb.recordResult(err)
	
	return err
}

// canProceed determines if a request can proceed
func (cb *CircuitBreaker) canProceed() bool {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()

	switch cb.state {
	case StateClosed:
		// Reset counters if interval has passed
		if now.Sub(cb.lastFailTime) > cb.config.Interval {
			cb.resetCounters()
		}
		return true

	case StateOpen:
		// Check if timeout has passed to transition to half-open
		if now.Sub(cb.lastStateTime) > cb.config.Timeout {
			cb.setState(StateHalfOpen)
			cb.resetCounters()
			return true
		}
		return false

	case StateHalfOpen:
		// Allow limited requests in half-open state
		return cb.requests < cb.config.MaxRequests

	default:
		return false
	}
}

// recordResult records the result of a request
func (cb *CircuitBreaker) recordResult(err error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.requests++
	cb.totalRequests++

	isFailure := cb.config.IsFailure(err)

	if isFailure {
		cb.failures++
		cb.totalFailures++
		cb.lastFailTime = time.Now()

		// Check if we should open the circuit
		if cb.state == StateClosed && cb.failures >= cb.config.FailureThreshold {
			cb.setState(StateOpen)
		} else if cb.state == StateHalfOpen {
			// Any failure in half-open state opens the circuit
			cb.setState(StateOpen)
		}
	} else {
		cb.successes++
		cb.totalSuccesses++

		// Check if we should close the circuit from half-open
		if cb.state == StateHalfOpen && cb.successes >= cb.config.SuccessThreshold {
			cb.setState(StateClosed)
		}
	}

	cb.recordMetrics(isFailure, false)
}

// recordMetrics records metrics for monitoring
func (cb *CircuitBreaker) recordMetrics(isFailure, isBlocked bool) {
	// This will be enhanced when we implement Prometheus metrics
	if isBlocked {
		cb.logger.Debug("Circuit breaker blocked request",
			zap.String("name", cb.config.Name),
			zap.String("state", cb.state.String()))
	}
}

// setState changes the circuit breaker state
func (cb *CircuitBreaker) setState(newState State) {
	if cb.state == newState {
		return
	}

	oldState := cb.state
	cb.state = newState
	cb.lastStateTime = time.Now()

	if cb.config.OnStateChange != nil {
		cb.config.OnStateChange(cb.config.Name, oldState, newState)
	}

	cb.logger.Info("Circuit breaker state changed",
		zap.String("name", cb.config.Name),
		zap.String("from", oldState.String()),
		zap.String("to", newState.String()),
		zap.Uint32("requests", cb.requests),
		zap.Uint32("failures", cb.failures),
		zap.Uint32("successes", cb.successes))
}

// resetCounters resets the internal counters
func (cb *CircuitBreaker) resetCounters() {
	cb.requests = 0
	cb.failures = 0
	cb.successes = 0
}

// GetState returns the current state
func (cb *CircuitBreaker) GetState() State {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// GetMetrics returns current metrics
func (cb *CircuitBreaker) GetMetrics() Metrics {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	return Metrics{
		Name:           cb.config.Name,
		State:          cb.state,
		TotalRequests:  cb.totalRequests,
		TotalFailures:  cb.totalFailures,
		TotalSuccesses: cb.totalSuccesses,
		CurrentRequests: cb.requests,
		CurrentFailures: cb.failures,
		CurrentSuccesses: cb.successes,
	}
}

// Metrics holds circuit breaker metrics
type Metrics struct {
	Name             string `json:"name"`
	State            State  `json:"state"`
	TotalRequests    uint64 `json:"total_requests"`
	TotalFailures    uint64 `json:"total_failures"`
	TotalSuccesses   uint64 `json:"total_successes"`
	CurrentRequests  uint32 `json:"current_requests"`
	CurrentFailures  uint32 `json:"current_failures"`
	CurrentSuccesses uint32 `json:"current_successes"`
}

// CircuitBreakerError is returned when circuit breaker is open
type CircuitBreakerError struct {
	Name  string
	State State
}

func (e *CircuitBreakerError) Error() string {
	return fmt.Sprintf("circuit breaker '%s' is %s", e.Name, e.State.String())
}

// IsCircuitBreakerError checks if an error is a circuit breaker error
func IsCircuitBreakerError(err error) bool {
	var cbErr *CircuitBreakerError
	return errors.As(err, &cbErr)
}
