package circuitbreaker

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Manager manages multiple circuit breakers
type Manager struct {
	breakers map[string]*CircuitBreaker
	mutex    sync.RWMutex
	logger   *zap.Logger
}

// NewManager creates a new circuit breaker manager
func NewManager(logger *zap.Logger) *Manager {
	return &Manager{
		breakers: make(map[string]*CircuitBreaker),
		logger:   logger,
	}
}

// GetOrCreate gets an existing circuit breaker or creates a new one
func (m *Manager) GetOrCreate(name string, config Config) *CircuitBreaker {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if cb, exists := m.breakers[name]; exists {
		return cb
	}

	config.Name = name
	cb := New(config, m.logger)
	m.breakers[name] = cb

	m.logger.Info("Created new circuit breaker",
		zap.String("name", name),
		zap.Uint32("failure_threshold", config.FailureThreshold),
		zap.Duration("timeout", config.Timeout))

	return cb
}

// Get retrieves a circuit breaker by name
func (m *Manager) Get(name string) (*CircuitBreaker, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	cb, exists := m.breakers[name]
	return cb, exists
}

// Execute executes a function with circuit breaker protection
func (m *Manager) Execute(ctx context.Context, name string, fn func() error) error {
	cb, exists := m.Get(name)
	if !exists {
		// If no circuit breaker exists, create one with default config
		config := DefaultConfig(name)
		cb = m.GetOrCreate(name, config)
	}

	return cb.Execute(ctx, fn)
}

// GetAllMetrics returns metrics for all circuit breakers
func (m *Manager) GetAllMetrics() map[string]Metrics {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	metrics := make(map[string]Metrics)
	for name, cb := range m.breakers {
		metrics[name] = cb.GetMetrics()
	}

	return metrics
}

// HealthCheck returns the health status of all circuit breakers
func (m *Manager) HealthCheck() map[string]bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	health := make(map[string]bool)
	for name, cb := range m.breakers {
		health[name] = cb.GetState() != StateOpen
	}

	return health
}

// ServiceCircuitBreakers holds circuit breakers for common external services
type ServiceCircuitBreakers struct {
	Database     *CircuitBreaker
	MinIO        *CircuitBreaker
	LiveKit      *CircuitBreaker
	NATS         *CircuitBreaker
	MQTT         *CircuitBreaker
	Ory          *CircuitBreaker
	FCM          *CircuitBreaker
	manager      *Manager
}

// NewServiceCircuitBreakers creates circuit breakers for common services
func NewServiceCircuitBreakers(logger *zap.Logger) *ServiceCircuitBreakers {
	manager := NewManager(logger)

	// Database circuit breaker - more lenient for critical service
	dbConfig := Config{
		Name:             "database",
		MaxRequests:      20,
		Interval:         30 * time.Second,
		Timeout:          30 * time.Second,
		FailureThreshold: 10, // Higher threshold for database
		SuccessThreshold: 5,
		IsFailure: func(err error) bool {
			// Only count actual database errors, not business logic errors
			return err != nil && isDatabaseError(err)
		},
	}

	// MinIO circuit breaker
	minioConfig := Config{
		Name:             "minio",
		MaxRequests:      15,
		Interval:         60 * time.Second,
		Timeout:          45 * time.Second,
		FailureThreshold: 5,
		SuccessThreshold: 3,
		IsFailure: func(err error) bool {
			return err != nil && isStorageError(err)
		},
	}

	// LiveKit circuit breaker
	livekitConfig := Config{
		Name:             "livekit",
		MaxRequests:      10,
		Interval:         60 * time.Second,
		Timeout:          60 * time.Second,
		FailureThreshold: 3,
		SuccessThreshold: 2,
		IsFailure: func(err error) bool {
			return err != nil && isLiveKitError(err)
		},
	}

	// NATS circuit breaker
	natsConfig := Config{
		Name:             "nats",
		MaxRequests:      25,
		Interval:         30 * time.Second,
		Timeout:          30 * time.Second,
		FailureThreshold: 5,
		SuccessThreshold: 3,
		IsFailure: func(err error) bool {
			return err != nil && isMessagingError(err)
		},
	}

	// MQTT circuit breaker
	mqttConfig := Config{
		Name:             "mqtt",
		MaxRequests:      20,
		Interval:         45 * time.Second,
		Timeout:          45 * time.Second,
		FailureThreshold: 5,
		SuccessThreshold: 3,
		IsFailure: func(err error) bool {
			return err != nil && isMessagingError(err)
		},
	}

	// Ory circuit breaker
	oryConfig := Config{
		Name:             "ory",
		MaxRequests:      15,
		Interval:         60 * time.Second,
		Timeout:          60 * time.Second,
		FailureThreshold: 3,
		SuccessThreshold: 2,
		IsFailure: func(err error) bool {
			return err != nil && isAuthError(err)
		},
	}

	// FCM circuit breaker
	fcmConfig := Config{
		Name:             "fcm",
		MaxRequests:      10,
		Interval:         60 * time.Second,
		Timeout:          90 * time.Second,
		FailureThreshold: 3,
		SuccessThreshold: 2,
		IsFailure: func(err error) bool {
			return err != nil && isNotificationError(err)
		},
	}

	return &ServiceCircuitBreakers{
		Database: manager.GetOrCreate("database", dbConfig),
		MinIO:    manager.GetOrCreate("minio", minioConfig),
		LiveKit:  manager.GetOrCreate("livekit", livekitConfig),
		NATS:     manager.GetOrCreate("nats", natsConfig),
		MQTT:     manager.GetOrCreate("mqtt", mqttConfig),
		Ory:      manager.GetOrCreate("ory", oryConfig),
		FCM:      manager.GetOrCreate("fcm", fcmConfig),
		manager:  manager,
	}
}

// GetManager returns the underlying manager
func (scb *ServiceCircuitBreakers) GetManager() *Manager {
	return scb.manager
}

// Error classification functions
func isDatabaseError(err error) bool {
	// Add specific database error detection logic
	errStr := err.Error()
	return contains(errStr, "connection refused", "timeout", "connection reset", "no such host")
}

func isStorageError(err error) bool {
	// Add specific MinIO/storage error detection logic
	errStr := err.Error()
	return contains(errStr, "connection refused", "timeout", "network", "storage")
}

func isLiveKitError(err error) bool {
	// Add specific LiveKit error detection logic
	errStr := err.Error()
	return contains(errStr, "connection refused", "timeout", "livekit", "room")
}

func isMessagingError(err error) bool {
	// Add specific NATS/MQTT error detection logic
	errStr := err.Error()
	return contains(errStr, "connection refused", "timeout", "nats", "mqtt", "publish")
}

func isAuthError(err error) bool {
	// Add specific Ory error detection logic
	errStr := err.Error()
	return contains(errStr, "connection refused", "timeout", "unauthorized", "auth")
}

func isNotificationError(err error) bool {
	// Add specific FCM error detection logic
	errStr := err.Error()
	return contains(errStr, "connection refused", "timeout", "fcm", "notification")
}

// Helper function to check if string contains any of the given substrings
func contains(s string, substrings ...string) bool {
	for _, substr := range substrings {
		if len(s) >= len(substr) {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}
