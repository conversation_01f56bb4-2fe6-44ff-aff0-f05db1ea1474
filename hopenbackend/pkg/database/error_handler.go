package database

import (
	"errors"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

// HandlePgxError translates PostgreSQL errors into structured error types
func HandlePgxError(err error, resourceName string) error {
	if err == nil {
		return nil
	}

	// Handle no rows found
	if err == pgx.ErrNoRows {
		return NewNotFoundError(resourceName, "record not found")
	}

	// Handle PostgreSQL specific errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		switch pgErr.Code {
		case "23505": // Unique violation
			return NewDuplicateError(resourceName, pgErr.Detail)
		case "23503": // Foreign key violation
			return NewConflictError(resourceName, fmt.Sprintf("foreign key constraint violation: %s", pgErr.Detail))
		case "23514": // Check constraint violation
			return NewValidationError("", fmt.Sprintf("check constraint violation: %s", pgErr.Detail))
		case "23502": // Not null constraint violation
			return NewValidationError(pgErr.ColumnName, "cannot be null")
		case "P0001": // Custom trigger error
			return NewForbiddenError(resourceName, pgErr.Message)
		default:
			return fmt.Errorf("database operation failed on %s: %w", resourceName, err)
		}
	}

	// Return generic error for everything else
	return fmt.Errorf("database operation failed on %s: %w", resourceName, err)
}

// HandlePgxErrorWithContext provides additional context for error handling
func HandlePgxErrorWithContext(err error, resourceName, operation string) error {
	if err == nil {
		return nil
	}

	wrappedErr := HandlePgxError(err, resourceName)
	
	// Add operation context
	switch e := wrappedErr.(type) {
	case *NotFoundError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *DuplicateError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *ValidationError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *ConflictError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *ForbiddenError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *UnauthorizedError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	}
	
	return wrappedErr
}
