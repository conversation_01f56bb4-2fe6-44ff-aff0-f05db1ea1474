package database

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
)

// This file provides RFC 7807 Problem Details error handling for HTTP APIs.
//
// IMPROVEMENT: Error context detection now uses the Resource field from custom error types
// instead of brittle string matching. This makes the error handling more robust and
// maintainable when error messages change.
//
// Example:
//   // OLD (brittle): containsUserContext(message) checks exact string match
//   // NEW (robust): errors.As(err, &nfErr) && nfErr.Resource == "users"

// ProblemDetail represents RFC 7807 Problem Details for HTTP APIs
type ProblemDetail struct {
	Type     string `json:"type"`
	Title    string `json:"title"`
	Status   int    `json:"status"`
	Detail   string `json:"detail,omitempty"`
	Instance string `json:"instance,omitempty"`
}

// Legacy error response for backward compatibility
type HTTPErrorResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// HandleHTTPError converts database errors to RFC 7807 Problem Details responses
func HandleHTTPError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	// Use behavior-based error checking (Go best practice)
	switch {
	case IsNotFound(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusNotFound, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.4",
			Title:  "Not Found",
			Status: http.StatusNotFound,
			Detail: extractErrorMessage(err),
		})
	case IsDuplicate(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusConflict, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.8",
			Title:  "Conflict",
			Status: http.StatusConflict,
			Detail: extractErrorMessage(err),
		})
	case IsValidation(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusBadRequest, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.1",
			Title:  "Bad Request",
			Status: http.StatusBadRequest,
			Detail: "Invalid data provided: " + extractErrorMessage(err),
		})
	case IsForbidden(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusForbidden, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.5.3",
			Title:  "Forbidden",
			Status: http.StatusForbidden,
			Detail: extractErrorMessage(err),
		})
	default:
		// Generic database error - don't expose internal details
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusInternalServerError, ProblemDetail{
			Type:   "https://datatracker.ietf.org/doc/html/rfc7231#section-6.6.1",
			Title:  "Internal Server Error",
			Status: http.StatusInternalServerError,
			Detail: "An internal error occurred while processing your request",
		})
	}
}

// extractErrorMessage safely extracts error message from wrapped errors
func extractErrorMessage(err error) string {
	// Unwrap to get the underlying error message
	for err != nil {
		if nfErr, ok := err.(*NotFoundError); ok {
			return nfErr.Error()
		}
		if dupErr, ok := err.(*DuplicateError); ok {
			return dupErr.Error()
		}
		if valErr, ok := err.(*ValidationError); ok {
			return valErr.Error()
		}
		if forbErr, ok := err.(*ForbiddenError); ok {
			return forbErr.Error()
		}

		// Try to unwrap
		if unwrapped := errors.Unwrap(err); unwrapped != nil {
			err = unwrapped
		} else {
			break
		}
	}

	return err.Error()
}

// Specialized error handlers for different domains

// HandleUserError provides user-specific error handling with RFC 7807 compliance
func HandleUserError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	// Use resource-aware error checking helpers
	switch {
	case IsUserNotFound(err):
		var nfErr *NotFoundError
		errors.As(err, &nfErr) // Safe to call since we already checked with IsUserNotFound
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusNotFound, ProblemDetail{
			Type:   "https://example.com/probs/user-not-found",
			Title:  "User Not Found",
			Status: http.StatusNotFound,
			Detail: nfErr.Error(),
		})
		return
	case IsEmailDuplicate(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusConflict, ProblemDetail{
			Type:   "https://example.com/probs/email-exists",
			Title:  "Email Already Exists",
			Status: http.StatusConflict,
			Detail: "The email address is already registered",
		})
		return
	case IsUsernameDuplicate(err):
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusConflict, ProblemDetail{
			Type:   "https://example.com/probs/username-exists",
			Title:  "Username Already Exists",
			Status: http.StatusConflict,
			Detail: "The username is already taken",
		})
		return
	}

	// Fall back to generic error handling
	HandleHTTPError(c, err, operation)
}

// HandleBubbleError provides bubble-specific error handling
func HandleBubbleError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	// Use resource-aware error checking helpers
	switch {
	case IsBubbleNotFound(err):
		var nfErr *NotFoundError
		errors.As(err, &nfErr) // Safe to call since we already checked with IsBubbleNotFound
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusNotFound, ProblemDetail{
			Type:   "https://example.com/probs/bubble-not-found",
			Title:  "Bubble Not Found",
			Status: http.StatusNotFound,
			Detail: nfErr.Error(),
		})
		return
	case IsBubbleForbidden(err):
		var forbErr *ForbiddenError
		errors.As(err, &forbErr) // Safe to call since we already checked with IsBubbleForbidden
		c.Header("Content-Type", "application/problem+json")
		c.JSON(http.StatusForbidden, ProblemDetail{
			Type:   "https://example.com/probs/bubble-limit-exceeded",
			Title:  "Bubble Limit Exceeded",
			Status: http.StatusForbidden,
			Detail: forbErr.Error(),
		})
		return
	}

	// Fall back to generic error handling
	HandleHTTPError(c, err, operation)
}

// Helper functions to check error context - DEPRECATED: Use Resource field instead
// These are kept for backward compatibility but should be avoided in new code
func containsUserContext(message string) bool {
	return message == "user not found"
}

func containsEmailContext(message string) bool {
	return message == "email already exists"
}

func containsUsernameContext(message string) bool {
	return message == "username already exists"
}

func containsBubbleContext(message string) bool {
	return message == "bubble not found"
}

// Resource-aware error checking helpers (preferred approach)
// These functions provide a more robust way to check error context using the Resource field

// IsUserNotFound checks if the error is a NotFoundError for users
func IsUserNotFound(err error) bool {
	var nfErr *NotFoundError
	return errors.As(err, &nfErr) && nfErr.Resource == "users"
}

// IsBubbleNotFound checks if the error is a NotFoundError for bubbles
func IsBubbleNotFound(err error) bool {
	var nfErr *NotFoundError
	return errors.As(err, &nfErr) && nfErr.Resource == "bubbles"
}

// IsEmailDuplicate checks if the error is a DuplicateError for email
func IsEmailDuplicate(err error) bool {
	var dupErr *DuplicateError
	return errors.As(err, &dupErr) && dupErr.Resource == "email"
}

// IsUsernameDuplicate checks if the error is a DuplicateError for username
func IsUsernameDuplicate(err error) bool {
	var dupErr *DuplicateError
	return errors.As(err, &dupErr) && dupErr.Resource == "username"
}

// IsBubbleForbidden checks if the error is a ForbiddenError for bubbles
func IsBubbleForbidden(err error) bool {
	var forbErr *ForbiddenError
	return errors.As(err, &forbErr) && forbErr.Resource == "bubbles"
}
