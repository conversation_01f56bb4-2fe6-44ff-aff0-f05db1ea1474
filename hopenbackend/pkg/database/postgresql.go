package database

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// Querier interface allows methods to work with both *pgxpool.Pool and pgx.Tx
// This enables the same DAL methods to be used in transactions and standalone operations
type Querier interface {
	Exec(ctx context.Context, sql string, arguments ...interface{}) (pgconn.CommandTag, error)
	Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error)
	QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row
}

// userColumns is the canonical list of columns for the users table.
// Using a constant prevents errors from mismatched column order or count.
const userColumns = `
	id, username, email, first_name, last_name, display_name,
	avatar_bucket_name, avatar_object_key, avatar_url, date_of_birth,
	is_premium, is_active, is_private, is_banned, banned_at,
	last_active_at, is_present, notification_settings, created_at, updated_at
`

// GetUserColumns returns the canonical list of user columns for testing
func GetUserColumns() string {
	return userColumns
}

// scanUser is a helper to scan a row into a User struct.
// This reduces code duplication and the risk of scan errors.
func scanUser(row pgx.Row, user *User) error {
	return row.Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.DisplayName, &user.AvatarBucketName, &user.AvatarObjectKey, &user.AvatarURL,
		&user.DateOfBirth, &user.IsPremium, &user.IsActive, &user.IsPrivate,
		&user.IsBanned, &user.BannedAt, &user.LastActiveAt, &user.IsPresent,
		&user.NotificationSettings, &user.CreatedAt, &user.UpdatedAt,
	)
}

// PostgreSQLClient wraps pgxpool.Pool with additional functionality
type PostgreSQLClient struct {
	Pool   *pgxpool.Pool
	logger *zap.Logger
	config *config.PostgreSQLConfig
}

// NewPostgreSQLClient creates a new PostgreSQL client with production optimizations
func NewPostgreSQLClient(cfg *config.PostgreSQLConfig, logger *zap.Logger) (*PostgreSQLClient, error) {
	// Configure connection pool
	poolConfig, err := pgxpool.ParseConfig(cfg.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("failed to parse PostgreSQL config: %w", err)
	}

	// Set pool configuration with production optimizations
	poolConfig.MaxConns = cfg.MaxConnections
	poolConfig.MinConns = cfg.MinConnections
	poolConfig.MaxConnLifetime = cfg.MaxConnectionLifetime
	poolConfig.MaxConnIdleTime = cfg.MaxConnectionIdleTime
	poolConfig.HealthCheckPeriod = cfg.HealthCheckPeriod

	// Production optimizations
	poolConfig.ConnConfig.ConnectTimeout = 30 * time.Second

	// Enable statement cache for better performance
	poolConfig.ConnConfig.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol

	// Add connection hooks for monitoring and logging
	poolConfig.BeforeAcquire = func(ctx context.Context, conn *pgx.Conn) bool {
		logger.Debug("Database connection acquired")
		return true
	}

	poolConfig.AfterRelease = func(conn *pgx.Conn) bool {
		logger.Debug("Database connection released")
		return true
	}

	poolConfig.BeforeClose = func(conn *pgx.Conn) {
		logger.Debug("Database connection closing")
	}

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create PostgreSQL pool: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	logger.Info("PostgreSQL connection established",
		zap.String("host", cfg.Host),
		zap.Int("port", cfg.Port),
		zap.String("database", cfg.Database),
		zap.Int32("max_connections", cfg.MaxConnections),
	)

	client := &PostgreSQLClient{
		Pool:   pool,
		logger: logger,
		config: cfg,
	}

	// Start connection pool monitoring
	go client.monitorConnectionPool()

	return client, nil

}

// Close closes the PostgreSQL connection pool
func (c *PostgreSQLClient) Close() {
	if c.Pool != nil {
		c.Pool.Close()
		c.logger.Info("PostgreSQL connection pool closed")
	}
}

// Health checks the health of the PostgreSQL connection
func (c *PostgreSQLClient) Health(ctx context.Context) error {
	return c.Pool.Ping(ctx)
}

// Stats returns connection pool statistics
func (c *PostgreSQLClient) Stats() *pgxpool.Stat {
	return c.Pool.Stat()
}

// GetUserByID retrieves a user by ID
func (c *PostgreSQLClient) GetUserByID(ctx context.Context, userID string) (*User, error) {
	query := fmt.Sprintf("SELECT %s FROM users WHERE id = $1", userColumns)

	var user User
	row := c.Pool.QueryRow(ctx, query, userID)
	if err := scanUser(row, &user); err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// createUser is a private helper that creates a user using any Querier (Pool or Tx)
// This enables code reuse between standalone operations and transactions
func (c *PostgreSQLClient) createUser(ctx context.Context, q Querier, user *User) error {
	query := `INSERT INTO users (id, username, email, first_name, last_name, avatar_url, date_of_birth, is_private, notification_settings) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING created_at, updated_at`

	err := q.QueryRow(ctx, query,
		user.ID, user.Username, user.Email, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
	).Scan(&user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create user: %w", HandlePgxError(err, "users"))
	}

	return nil
}

// CreateUser creates a new user
func (c *PostgreSQLClient) CreateUser(ctx context.Context, user *User) error {
	return c.createUser(ctx, c.Pool, user)
}

// SearchUsers searches for users by username, first name, or last name
func (c *PostgreSQLClient) SearchUsers(ctx context.Context, query string, limit int) ([]*User, error) {
	// FIXED: Query now selects all columns to match the User struct
	searchQuery := fmt.Sprintf(`
		SELECT %s
		FROM users
		WHERE is_active = true AND is_private = false
		  AND (username ILIKE $1 OR first_name ILIKE $1 OR last_name ILIKE $1)
		ORDER BY
			CASE
				WHEN username ILIKE $1 THEN 1
				WHEN first_name ILIKE $1 THEN 2
				WHEN last_name ILIKE $1 THEN 3
				ELSE 4
			END,
			username
		LIMIT $2`, userColumns)

	searchPattern := "%" + query + "%"
	rows, err := c.Pool.Query(ctx, searchQuery, searchPattern, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to search users: %w", HandlePgxError(err, "users"))
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		var user User
		// FIXED: Use the scanUser helper function
		if err := scanUser(rows, &user); err != nil {
			c.logger.Error("Failed to scan user during search", zap.Error(err))
			return nil, fmt.Errorf("failed to scan user: %w", HandlePgxError(err, "users"))
		}
		users = append(users, &user)
	}

	return users, nil
}

// UpdateUserParams represents parameters for partial user updates
type UpdateUserParams struct {
	Username             *string                `json:"username"`
	FirstName            *string                `json:"first_name"`
	LastName             *string                `json:"last_name"`
	DisplayName          *string                `json:"display_name"`
	AvatarBucketName     *string                `json:"avatar_bucket_name"`
	AvatarObjectKey      *string                `json:"avatar_object_key"`
	AvatarURL            *string                `json:"avatar_url"`
	DateOfBirth          *time.Time             `json:"date_of_birth"`
	IsPrivate            *bool                  `json:"is_private"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`
}

// UpdateUser updates an existing user
func (c *PostgreSQLClient) UpdateUser(ctx context.Context, user *User) error {
	query := `UPDATE users SET username = $2, first_name = $3, last_name = $4, avatar_url = $5, date_of_birth = $6, is_private = $7, notification_settings = $8, updated_at = NOW() WHERE id = $1 RETURNING updated_at`

	err := c.Pool.QueryRow(ctx, query,
		user.ID, user.Username, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
	).Scan(&user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to update user: %w", HandlePgxError(err, "users"))
	}

	return nil
}

// UpdateUserPartial updates specific fields of a user based on provided parameters
func (c *PostgreSQLClient) UpdateUserPartial(ctx context.Context, userID string, params UpdateUserParams) (*User, error) {
	// Build dynamic query based on provided parameters
	setParts := []string{}
	args := []interface{}{userID} // $1 is always userID
	argIndex := 2

	if params.Username != nil {
		setParts = append(setParts, fmt.Sprintf("username = $%d", argIndex))
		args = append(args, params.Username)
		argIndex++
	}
	if params.FirstName != nil {
		setParts = append(setParts, fmt.Sprintf("first_name = $%d", argIndex))
		args = append(args, params.FirstName)
		argIndex++
	}
	if params.LastName != nil {
		setParts = append(setParts, fmt.Sprintf("last_name = $%d", argIndex))
		args = append(args, params.LastName)
		argIndex++
	}
	if params.DisplayName != nil {
		setParts = append(setParts, fmt.Sprintf("display_name = $%d", argIndex))
		args = append(args, params.DisplayName)
		argIndex++
	}
	if params.AvatarBucketName != nil {
		setParts = append(setParts, fmt.Sprintf("avatar_bucket_name = $%d", argIndex))
		args = append(args, params.AvatarBucketName)
		argIndex++
	}
	if params.AvatarObjectKey != nil {
		setParts = append(setParts, fmt.Sprintf("avatar_object_key = $%d", argIndex))
		args = append(args, params.AvatarObjectKey)
		argIndex++
	}
	if params.AvatarURL != nil {
		setParts = append(setParts, fmt.Sprintf("avatar_url = $%d", argIndex))
		args = append(args, params.AvatarURL)
		argIndex++
	}
	if params.DateOfBirth != nil {
		setParts = append(setParts, fmt.Sprintf("date_of_birth = $%d", argIndex))
		args = append(args, params.DateOfBirth)
		argIndex++
	}
	if params.IsPrivate != nil {
		setParts = append(setParts, fmt.Sprintf("is_private = $%d", argIndex))
		args = append(args, params.IsPrivate)
		argIndex++
	}
	if params.NotificationSettings != nil {
		setParts = append(setParts, fmt.Sprintf("notification_settings = $%d", argIndex))
		args = append(args, params.NotificationSettings)
		argIndex++
	}

	// Always update the timestamp
	setParts = append(setParts, "updated_at = NOW()")

	if len(setParts) == 1 { // Only timestamp update, no actual changes
		return nil, fmt.Errorf("no fields to update")
	}

	// Build the complete query
	query := fmt.Sprintf(`
		UPDATE users
		SET %s
		WHERE id = $1
		RETURNING id, username, email, first_name, last_name, display_name,
		          avatar_bucket_name, avatar_object_key, avatar_url, date_of_birth,
		          is_premium, is_active, is_private, is_banned, banned_at,
		          last_active_at, is_present, notification_settings, created_at, updated_at`,
		strings.Join(setParts, ", "))

	var user User
	err := c.Pool.QueryRow(ctx, query, args...).Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.DisplayName, &user.AvatarBucketName, &user.AvatarObjectKey, &user.AvatarURL,
		&user.DateOfBirth, &user.IsPremium, &user.IsActive, &user.IsPrivate,
		&user.IsBanned, &user.BannedAt, &user.LastActiveAt, &user.IsPresent,
		&user.NotificationSettings, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to update user: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// SoftDeleteUser soft deletes a user by setting is_active to false
func (c *PostgreSQLClient) SoftDeleteUser(ctx context.Context, userID string) error {
	query := `UPDATE users SET is_active = false, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to soft delete user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to soft delete user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil

}

// BanUser bans a user by setting is_banned to true
func (c *PostgreSQLClient) BanUser(ctx context.Context, userID string) error {
	query := `UPDATE users SET is_banned = true, banned_at = NOW(), updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to ban user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to ban user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil

}

// UnbanUser unbans a user by setting is_banned to false
func (c *PostgreSQLClient) UnbanUser(ctx context.Context, userID string) error {
	query := `UPDATE users SET is_banned = false, banned_at = NULL, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to unban user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to unban user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil

}

// ===== USER STATUS DAL METHODS =====

// SetUserPremiumStatus updates a user's premium status.
// This should be called by your payment/subscription service after a successful transaction.
func (c *PostgreSQLClient) SetUserPremiumStatus(ctx context.Context, userID string, isPremium bool) error {
	query := `
		UPDATE users
		SET is_premium = $2, updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID, isPremium)
	if err != nil {
		return fmt.Errorf("failed to set user premium status: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to set user premium status: %w", NewNotFoundError("users", "user not found"))
	}

	return nil
}

// GetUserByEmail retrieves a user by email address
func (c *PostgreSQLClient) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	// FIXED: Query now selects all columns
	query := fmt.Sprintf("SELECT %s FROM users WHERE email = $1 AND is_active = true", userColumns)

	var user User
	row := c.Pool.QueryRow(ctx, query, email)
	if err := scanUser(row, &user); err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// GetUserByUsername retrieves a user by username
func (c *PostgreSQLClient) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	// FIXED: Query now selects all columns
	query := fmt.Sprintf("SELECT %s FROM users WHERE username = $1 AND is_active = true", userColumns)

	var user User
	row := c.Pool.QueryRow(ctx, query, username)
	if err := scanUser(row, &user); err != nil {
		return nil, fmt.Errorf("failed to get user by username: %w", HandlePgxError(err, "users"))
	}

	return &user, nil
}

// User represents the canonical user entity for database operations
// This is the single source of truth for User struct definition
type User struct {
	ID                   string                 `json:"id"`
	Username             *string                `json:"username"`
	Email                string                 `json:"email"`
	FirstName            *string                `json:"first_name"`
	LastName             *string                `json:"last_name"`
	DisplayName          *string                `json:"display_name"`
	AvatarBucketName     *string                `json:"avatar_bucket_name"`
	AvatarObjectKey      *string                `json:"avatar_object_key"`
	AvatarURL            *string                `json:"avatar_url"` // For backward compatibility
	DateOfBirth          *time.Time             `json:"date_of_birth"`
	IsPremium            bool                   `json:"is_premium"`
	IsActive             bool                   `json:"is_active"`
	IsPrivate            bool                   `json:"is_private"`
	IsBanned             bool                   `json:"is_banned"`
	BannedAt             *time.Time             `json:"banned_at"`
	LastActiveAt         *time.Time             `json:"last_active_at"`
	IsPresent            bool                   `json:"is_present"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`
	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
}

// BubbleRequest represents a bubble-related request (invite, join, kick, start)
type BubbleRequest struct {
	ID                string     `json:"id"`
	RequestType       string     `json:"request_type"` // 'invite', 'join', 'kick', 'start'
	BubbleID          string     `json:"bubble_id"`
	RequesterID       string     `json:"requester_id"`       // User who initiated the request
	TargetUserID      *string    `json:"target_user_id"`     // User being invited/kicked (null for join)
	Status            string     `json:"status"`             // 'pending', 'approved', 'rejected', 'expired'
	RequiresUnanimous bool       `json:"requires_unanimous"` // Whether all members must approve
	ExpiresAt         time.Time  `json:"expires_at"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	CompletedAt       *time.Time `json:"completed_at"`
}

// RequestVote represents an individual member's vote on a request
type RequestVote struct {
	ID        string    `json:"id"`
	RequestID string    `json:"request_id"`
	VoterID   string    `json:"voter_id"` // Member who is voting
	Vote      string    `json:"vote"`     // 'approve', 'reject'
	VotedAt   time.Time `json:"voted_at"`
	CreatedAt time.Time `json:"created_at"`
}

// ContactRequest represents a manual contact request between users
type ContactRequest struct {
	ID          string    `json:"id"`
	RequesterID string    `json:"requester_id"`
	RecipientID string    `json:"recipient_id"`
	Status      string    `json:"status"` // 'pending', 'accepted', 'declined', 'expired'
	ExpiresAt   time.Time `json:"expires_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FriendRequest represents an auto-generated friend request from expired bubbles
// When a bubble expires, all former bubblers get 'maybefriend' relationship status
// and friend requests are created. If both accept -> 'friend', if one declines -> 'contact'
type FriendRequest struct {
	ID             string    `json:"id"`
	RequesterID    string    `json:"requester_id"`
	RecipientID    string    `json:"recipient_id"`
	SourceBubbleID string    `json:"source_bubble_id"` // Always present for auto-generated requests
	AutoGenerated  bool      `json:"auto_generated"`   // Always true
	Status         string    `json:"status"`           // 'pending', 'accepted', 'declined', 'expired'
	ExpiresAt      time.Time `json:"expires_at"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ===== CONTACT REQUEST DAL METHODS =====
// CreateContactRequest creates a new contact request
func (c *PostgreSQLClient) CreateContactRequest(ctx context.Context, contactRequest *ContactRequest) error {
	query := `INSERT INTO contact_requests (id, requester_id, recipient_id, status, expires_at) VALUES ($1, $2, $3, $4, $5) RETURNING created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		contactRequest.ID, contactRequest.RequesterID, contactRequest.RecipientID,
		contactRequest.Status, contactRequest.ExpiresAt,
	).Scan(&contactRequest.CreatedAt, &contactRequest.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create contact request: %w", HandlePgxError(err, "contact_requests"))
	}

	return nil

}

// GetContactRequestsByUser retrieves contact requests for a user (sent or received)
func (c *PostgreSQLClient) GetContactRequestsByUser(ctx context.Context, userID string, requestType string) ([]*ContactRequest, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT id, requester_id, recipient_id, status, expires_at, created_at, updated_at FROM contact_requests WHERE requester_id = $1 ORDER BY created_at DESC`
	case "received":
		query = `SELECT id, requester_id, recipient_id, status, expires_at, created_at, updated_at FROM contact_requests WHERE recipient_id = $1 ORDER BY created_at DESC`
	default:
		return nil, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	rows, err := c.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get contact requests: %w", HandlePgxError(err, "contact_requests"))
	}
	defer rows.Close()

	var requests []*ContactRequest
	for rows.Next() {
		var request ContactRequest
		err := rows.Scan(
			&request.ID, &request.RequesterID, &request.RecipientID,
			&request.Status, &request.ExpiresAt,
			&request.CreatedAt, &request.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan contact request: %w", HandlePgxError(err, "contact_requests"))
		}
		requests = append(requests, &request)
	}

	return requests, nil

}

// UpdateContactRequestStatus updates the status of a contact request
func (c *PostgreSQLClient) UpdateContactRequestStatus(ctx context.Context, requestID string, status string) error {
	query := `UPDATE contact_requests SET status = $2, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, requestID, status)
	if err != nil {
		return fmt.Errorf("failed to update contact request status: %w", HandlePgxError(err, "contact_requests"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("contact request not found: %s", requestID)
	}

	return nil

}

// GetPendingContactRequestsCount returns the count of pending contact requests for a user
func (c *PostgreSQLClient) GetPendingContactRequestsCount(ctx context.Context, userID string, requestType string) (int, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT COUNT(*) FROM contact_requests WHERE requester_id = $1 AND status = 'pending'`
	case "received":
		query = `SELECT COUNT(*) FROM contact_requests WHERE recipient_id = $1 AND status = 'pending'`
	default:
		return 0, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	var count int
	err := c.Pool.QueryRow(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get pending contact requests count: %w", HandlePgxError(err, "contact_requests"))
	}

	return count, nil

}

// ===== BUBBLE REQUEST DAL METHODS =====
// GetBubbleRequestsByUser retrieves bubble requests for a user (sent or received)
func (c *PostgreSQLClient) GetBubbleRequestsByUser(ctx context.Context, userID string, requestType string) ([]*BubbleRequest, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT id, request_type, bubble_id, requester_id, target_user_id, status, requires_unanimous, expires_at, created_at, updated_at, completed_at FROM bubble_requests WHERE requester_id = $1 ORDER BY created_at DESC`
	case "received":
		query = `SELECT id, request_type, bubble_id, requester_id, target_user_id, status, requires_unanimous, expires_at, created_at, updated_at, completed_at FROM bubble_requests WHERE target_user_id = $1 ORDER BY created_at DESC`
	default:
		return nil, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	rows, err := c.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bubble requests: %w", HandlePgxError(err, "bubble_requests"))
	}
	defer rows.Close()

	var requests []*BubbleRequest
	for rows.Next() {
		var request BubbleRequest
		err := rows.Scan(
			&request.ID, &request.RequestType, &request.BubbleID, &request.RequesterID,
			&request.TargetUserID, &request.Status, &request.RequiresUnanimous,
			&request.ExpiresAt, &request.CreatedAt, &request.UpdatedAt, &request.CompletedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan bubble request: %w", HandlePgxError(err, "bubble_requests"))
		}
		requests = append(requests, &request)
	}

	return requests, nil

}

// GetPendingBubbleRequestsCount returns the count of pending bubble requests for a user
func (c *PostgreSQLClient) GetPendingBubbleRequestsCount(ctx context.Context, userID string, requestType string) (int, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT COUNT(*) FROM bubble_requests WHERE requester_id = $1 AND status = 'pending'`
	case "received":
		query = `SELECT COUNT(*) FROM bubble_requests WHERE target_user_id = $1 AND status = 'pending'`
	default:
		return 0, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	var count int
	err := c.Pool.QueryRow(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get pending bubble requests count: %w", HandlePgxError(err, "bubble_requests"))
	}

	return count, nil

}

// ===== FRIEND REQUEST DAL METHODS =====
// CreateFriendRequest creates a new auto-generated friend request
func (c *PostgreSQLClient) CreateFriendRequest(ctx context.Context, friendRequest *FriendRequest) error {
	query := `INSERT INTO friend_requests (id, requester_id, recipient_id, source_bubble_id, auto_generated, status, expires_at) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		friendRequest.ID, friendRequest.RequesterID, friendRequest.RecipientID,
		friendRequest.SourceBubbleID, friendRequest.AutoGenerated, friendRequest.Status,
		friendRequest.ExpiresAt,
	).Scan(&friendRequest.CreatedAt, &friendRequest.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create friend request: %w", HandlePgxError(err, "friend_requests"))
	}

	return nil

}

// GetFriendRequestsByUser retrieves friend requests for a user (sent or received)
func (c *PostgreSQLClient) GetFriendRequestsByUser(ctx context.Context, userID string, requestType string) ([]*FriendRequest, error) {
	var query string
	switch requestType {
	case "sent":
		query = `SELECT id, requester_id, recipient_id, source_bubble_id, auto_generated, status, expires_at, created_at, updated_at FROM friend_requests WHERE requester_id = $1 ORDER BY created_at DESC`
	case "received":
		query = `SELECT id, requester_id, recipient_id, source_bubble_id, auto_generated, status, expires_at, created_at, updated_at FROM friend_requests WHERE recipient_id = $1 ORDER BY created_at DESC`
	default:
		return nil, fmt.Errorf("invalid request type: %s. Must be 'sent' or 'received'", requestType)
	}

	rows, err := c.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get friend requests: %w", HandlePgxError(err, "friend_requests"))
	}
	defer rows.Close()

	var requests []*FriendRequest
	for rows.Next() {
		var request FriendRequest
		err := rows.Scan(
			&request.ID, &request.RequesterID, &request.RecipientID, &request.SourceBubbleID,
			&request.AutoGenerated, &request.Status, &request.ExpiresAt,
			&request.CreatedAt, &request.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan friend request: %w", HandlePgxError(err, "friend_requests"))
		}
		requests = append(requests, &request)
	}

	return requests, nil

}

// UpdateFriendRequestStatus updates the status of a friend request
func (c *PostgreSQLClient) UpdateFriendRequestStatus(ctx context.Context, requestID string, status string) error {
	query := `UPDATE friend_requests SET status = $2, updated_at = NOW() WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, requestID, status)
	if err != nil {
		return fmt.Errorf("failed to update friend request status: %w", HandlePgxError(err, "friend_requests"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("friend request not found: %s", requestID)
	}

	return nil

}

// GetUserRelationships retrieves all active relationships for a user
func (c *PostgreSQLClient) GetUserRelationships(ctx context.Context, userID string, relationshipType *string) (map[string][]string, error) {
	query := `SELECT to_user_id, relationship_type FROM user_relationships WHERE from_user_id = $1 AND status = 'active'`

	args := []interface{}{userID}

	if relationshipType != nil {
		query += ` AND relationship_type = $2`
		args = append(args, *relationshipType)
	}

	query += ` ORDER BY created_at DESC`

	rows, err := c.Pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get user relationships: %w", HandlePgxError(err, "user_relationships"))
	}
	defer rows.Close()

	relationships := make(map[string][]string)
	for rows.Next() {
		var toUserID, relType string
		err := rows.Scan(&toUserID, &relType)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user relationship: %w", HandlePgxError(err, "user_relationships"))
		}

		relationships[relType] = append(relationships[relType], toUserID)
	}

	return relationships, nil
}

// ===== NEW PENDING REQUEST DAL METHODS =====

// GetPendingRequestsForUser retrieves all pending requests for a user (sent and received)
func (c *PostgreSQLClient) GetPendingRequestsForUser(ctx context.Context, userID string) (*PendingRequestsSummary, error) {
	// Get pending contact requests
	sentContactRequests, err := c.GetContactRequestsByUser(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent contact requests: %w", err)
	}

	receivedContactRequests, err := c.GetContactRequestsByUser(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received contact requests: %w", err)
	}

	// Get pending bubble requests
	sentBubbleRequests, err := c.GetBubbleRequestsByUser(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent bubble requests: %w", err)
	}

	receivedBubbleRequests, err := c.GetBubbleRequestsByUser(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received bubble requests: %w", err)
	}

	// Get pending friend requests
	sentFriendRequests, err := c.GetFriendRequestsByUser(ctx, userID, "sent")
	if err != nil {
		return nil, fmt.Errorf("failed to get sent friend requests: %w", err)
	}

	receivedFriendRequests, err := c.GetFriendRequestsByUser(ctx, userID, "received")
	if err != nil {
		return nil, fmt.Errorf("failed to get received friend requests: %w", err)
	}

	// Filter to only pending requests
	var pendingSentContactRequests, pendingReceivedContactRequests []*ContactRequest
	var pendingSentBubbleRequests, pendingReceivedBubbleRequests []*BubbleRequest
	var pendingSentFriendRequests, pendingReceivedFriendRequests []*FriendRequest

	for _, req := range sentContactRequests {
		if req.Status == "pending" {
			pendingSentContactRequests = append(pendingSentContactRequests, req)
		}
	}

	for _, req := range receivedContactRequests {
		if req.Status == "pending" {
			pendingReceivedContactRequests = append(pendingReceivedContactRequests, req)
		}
	}

	for _, req := range sentBubbleRequests {
		if req.Status == "pending" {
			pendingSentBubbleRequests = append(pendingSentBubbleRequests, req)
		}
	}

	for _, req := range receivedBubbleRequests {
		if req.Status == "pending" {
			pendingReceivedBubbleRequests = append(pendingReceivedBubbleRequests, req)
		}
	}

	for _, req := range sentFriendRequests {
		if req.Status == "pending" {
			pendingSentFriendRequests = append(pendingSentFriendRequests, req)
		}
	}

	for _, req := range receivedFriendRequests {
		if req.Status == "pending" {
			pendingReceivedFriendRequests = append(pendingReceivedFriendRequests, req)
		}
	}

	return &PendingRequestsSummary{
		SentContactRequests:     pendingSentContactRequests,
		ReceivedContactRequests: pendingReceivedContactRequests,
		SentBubbleRequests:      pendingSentBubbleRequests,
		ReceivedBubbleRequests:  pendingReceivedBubbleRequests,
		SentFriendRequests:      pendingSentFriendRequests,
		ReceivedFriendRequests:  pendingReceivedFriendRequests,
	}, nil
}

// PendingRequestsSummary represents all pending requests for a user
type PendingRequestsSummary struct {
	SentContactRequests     []*ContactRequest `json:"sent_contact_requests"`
	ReceivedContactRequests []*ContactRequest `json:"received_contact_requests"`
	SentBubbleRequests      []*BubbleRequest  `json:"sent_bubble_requests"`
	ReceivedBubbleRequests  []*BubbleRequest  `json:"received_bubble_requests"`
	SentFriendRequests      []*FriendRequest  `json:"sent_friend_requests"`
	ReceivedFriendRequests  []*FriendRequest  `json:"received_friend_requests"`
}

// GetPendingRequestsCount returns the count of all pending requests for a user
// Optimized version that uses a single query with UNION ALL and conditional aggregation
func (c *PostgreSQLClient) GetPendingRequestsCount(ctx context.Context, userID string) (*PendingRequestsCount, error) {
	// Single optimized query that consolidates all 6 previous database calls
	query := `
		SELECT
			SUM(CASE WHEN request_type = 'contact_sent' THEN count_val ELSE 0 END) as sent_contact_requests,
			SUM(CASE WHEN request_type = 'contact_received' THEN count_val ELSE 0 END) as received_contact_requests,
			SUM(CASE WHEN request_type = 'bubble_sent' THEN count_val ELSE 0 END) as sent_bubble_requests,
			SUM(CASE WHEN request_type = 'bubble_received' THEN count_val ELSE 0 END) as received_bubble_requests,
			SUM(CASE WHEN request_type = 'friend_sent' THEN count_val ELSE 0 END) as sent_friend_requests,
			SUM(CASE WHEN request_type = 'friend_received' THEN count_val ELSE 0 END) as received_friend_requests
		FROM (
			-- Contact requests sent
			SELECT 'contact_sent' as request_type, COUNT(*) as count_val
			FROM contact_requests
			WHERE requester_id = $1 AND status = 'pending'

			UNION ALL

			-- Contact requests received
			SELECT 'contact_received' as request_type, COUNT(*) as count_val
			FROM contact_requests
			WHERE recipient_id = $1 AND status = 'pending'

			UNION ALL

			-- Bubble requests sent
			SELECT 'bubble_sent' as request_type, COUNT(*) as count_val
			FROM bubble_requests
			WHERE requester_id = $1 AND status = 'pending'

			UNION ALL

			-- Bubble requests received
			SELECT 'bubble_received' as request_type, COUNT(*) as count_val
			FROM bubble_requests
			WHERE target_user_id = $1 AND status = 'pending'

			UNION ALL

			-- Friend requests sent
			SELECT 'friend_sent' as request_type, COUNT(*) as count_val
			FROM friend_requests
			WHERE requester_id = $1 AND status = 'pending'

			UNION ALL

			-- Friend requests received
			SELECT 'friend_received' as request_type, COUNT(*) as count_val
			FROM friend_requests
			WHERE recipient_id = $1 AND status = 'pending'
		) counts`

	var sentContactCount, receivedContactCount, sentBubbleCount, receivedBubbleCount, sentFriendCount, receivedFriendCount int

	err := c.Pool.QueryRow(ctx, query, userID).Scan(
		&sentContactCount,
		&receivedContactCount,
		&sentBubbleCount,
		&receivedBubbleCount,
		&sentFriendCount,
		&receivedFriendCount,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending requests count: %w", HandlePgxError(err, "pending_requests"))
	}

	total := sentContactCount + receivedContactCount + sentBubbleCount + receivedBubbleCount + sentFriendCount + receivedFriendCount

	return &PendingRequestsCount{
		SentContactRequests:     sentContactCount,
		ReceivedContactRequests: receivedContactCount,
		SentBubbleRequests:      sentBubbleCount,
		ReceivedBubbleRequests:  receivedBubbleCount,
		SentFriendRequests:      sentFriendCount,
		ReceivedFriendRequests:  receivedFriendCount,
		Total:                   total,
	}, nil
}

// PendingRequestsCount represents the count of all pending requests for a user
type PendingRequestsCount struct {
	SentContactRequests     int `json:"sent_contact_requests"`
	ReceivedContactRequests int `json:"received_contact_requests"`
	SentBubbleRequests      int `json:"sent_bubble_requests"`
	ReceivedBubbleRequests  int `json:"received_bubble_requests"`
	SentFriendRequests      int `json:"sent_friend_requests"`
	ReceivedFriendRequests  int `json:"received_friend_requests"`
	Total                   int `json:"total"`
}

// GetBubbleBadgeStatusForUser gets the bubble badge status for a user
func (c *PostgreSQLClient) GetBubbleBadgeStatusForUser(ctx context.Context, userID string) (string, error) {
	query := `
		SELECT
			CASE
				WHEN COUNT(bm.id) = 0 THEN 'no_bubble'
				WHEN COUNT(bm.id) = 1 THEN 'in_bubble'
				ELSE 'multiple_bubbles'
			END as status
		FROM bubble_members bm
		JOIN bubbles b ON bm.bubble_id = b.id
		WHERE bm.user_id = $1 AND bm.status = 'active' AND b.status = 'active'`

	var status string
	err := c.Pool.QueryRow(ctx, query, userID).Scan(&status)
	if err != nil {
		return "", fmt.Errorf("failed to get bubble badge status: %w", err)
	}

	return status, nil
}

// CheckAndDissolveBubble checks if a bubble has less than 2 active members and dissolves it
func (c *PostgreSQLClient) CheckAndDissolveBubble(ctx context.Context, bubbleID string) error {
	// Count active members
	countQuery := `
		SELECT COUNT(*)
		FROM bubble_members
		WHERE bubble_id = $1 AND status = 'active'`

	var activeCount int
	err := c.Pool.QueryRow(ctx, countQuery, bubbleID).Scan(&activeCount)
	if err != nil {
		return fmt.Errorf("failed to count active members: %w", err)
	}

	// If less than 2 active members, dissolve the bubble
	if activeCount < 2 {
		return c.DissolveBubble(ctx, bubbleID)
	}

	return nil
}

// DissolveBubble dissolves a bubble and then archives it
func (c *PostgreSQLClient) DissolveBubble(ctx context.Context, bubbleID string) error {
	tx, err := c.Pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// First, set bubble status to 'dissolved'
	dissolveQuery := `
		UPDATE bubbles
		SET status = 'dissolved', updated_at = NOW()
		WHERE id = $1 AND status = 'active'`

	result, err := tx.Exec(ctx, dissolveQuery, bubbleID)
	if err != nil {
		return fmt.Errorf("failed to dissolve bubble: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		// Bubble doesn't exist or is already in a final state
		return nil
	}

	// Then, archive the dissolved bubble
	archiveQuery := `
		UPDATE bubbles
		SET status = 'archived', updated_at = NOW()
		WHERE id = $1 AND status = 'dissolved'`

	_, err = tx.Exec(ctx, archiveQuery, bubbleID)
	if err != nil {
		return fmt.Errorf("failed to archive dissolved bubble: %w", err)
	}

	return tx.Commit(ctx)
}

// CheckAllBubblesForDissolution checks all active bubbles for automatic dissolution
func (c *PostgreSQLClient) CheckAllBubblesForDissolution(ctx context.Context) error {
	// Get all bubbles that could potentially need dissolution
	query := `
		SELECT b.id
		FROM bubbles b
		WHERE b.status = 'active'`

	rows, err := c.Pool.Query(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to query bubbles for dissolution check: %w", err)
	}
	defer rows.Close()

	var bubbleIDs []string
	for rows.Next() {
		var bubbleID string
		if err := rows.Scan(&bubbleID); err != nil {
			continue // Skip this bubble and continue
		}
		bubbleIDs = append(bubbleIDs, bubbleID)
	}

	// Check each bubble for dissolution
	for _, bubbleID := range bubbleIDs {
		if err := c.CheckAndDissolveBubble(ctx, bubbleID); err != nil {
			// Log error but continue with other bubbles
			// This prevents one failed bubble from stopping the entire process
			continue
		}
	}

	return nil
}

// PERFECT FIX: Add the missing UserRelationship struct
type UserRelationship struct {
	ID               string                 `json:"id"`
	FromUserID       string                 `json:"from_user_id"`
	ToUserID         string                 `json:"to_user_id"`
	RelationshipType string                 `json:"relationship_type"`
	Status           string                 `json:"status"`
	CreatedBy        *string                `json:"created_by"`
	Reason           *string                `json:"reason"`
	Metadata         map[string]interface{} `json:"metadata"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
	ExpiresAt        *time.Time             `json:"expires_at"`
}

// PERFECT FIX: CreateUserRelationship creates a new user relationship.
func (c *PostgreSQLClient) CreateUserRelationship(ctx context.Context, rel *UserRelationship) error {
	query := `
		INSERT INTO user_relationships (
			from_user_id, to_user_id, relationship_type, status, 
			created_by, reason, metadata, expires_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		rel.FromUserID, rel.ToUserID, rel.RelationshipType, rel.Status,
		rel.CreatedBy, rel.Reason, rel.Metadata, rel.ExpiresAt,
	).Scan(&rel.ID, &rel.CreatedAt, &rel.UpdatedAt)

	if err != nil {
		return HandlePgxError(err, "user_relationships")
	}
	return nil
}

// RemoveUserRelationship removes a specific relationship between two users.
func (c *PostgreSQLClient) RemoveUserRelationship(ctx context.Context, fromUserID, toUserID, relationshipType string) error {
	query := `
		DELETE FROM user_relationships 
		WHERE from_user_id = $1 AND to_user_id = $2 AND relationship_type = $3`

	_, err := c.Pool.Exec(ctx, query, fromUserID, toUserID, relationshipType)
	if err != nil {
		return HandlePgxError(err, "user_relationships")
	}
	return nil
}

// IsUserBanned checks if a user has an active 'ban' relationship.
func (c *PostgreSQLClient) IsUserBanned(ctx context.Context, userID string) (bool, *UserRelationship, error) {
	rel, err := c.GetBanDetails(ctx, userID)
	if err != nil {
		// If no rows, user is not banned.
		if err == pgx.ErrNoRows {
			return false, nil, nil
		}
		return false, nil, err
	}
	return true, rel, nil
}

// GetBanDetails retrieves the specific 'ban' relationship for a user.
func (c *PostgreSQLClient) GetBanDetails(ctx context.Context, userID string) (*UserRelationship, error) {
	query := `
		SELECT id, from_user_id, to_user_id, relationship_type, status, 
			   created_by, reason, metadata, created_at, updated_at, expires_at
		FROM user_relationships 
		WHERE to_user_id = $1 AND relationship_type = 'ban' AND status = 'active'
		LIMIT 1`

	var rel UserRelationship
	err := c.Pool.QueryRow(ctx, query, userID).Scan(
		&rel.ID, &rel.FromUserID, &rel.ToUserID, &rel.RelationshipType, &rel.Status,
		&rel.CreatedBy, &rel.Reason, &rel.Metadata, &rel.CreatedAt, &rel.UpdatedAt, &rel.ExpiresAt,
	)
	if err != nil {
		return nil, err // Let the caller handle pgx.ErrNoRows
	}
	return &rel, nil
}

// Production Database Optimizations and Monitoring

// monitorConnectionPool monitors the connection pool health
func (c *PostgreSQLClient) monitorConnectionPool() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		stats := c.Pool.Stat()

		c.logger.Debug("Database connection pool stats",
			zap.Int32("total_connections", stats.TotalConns()),
			zap.Int32("idle_connections", stats.IdleConns()),
			zap.Int32("acquired_connections", stats.AcquiredConns()),
			zap.Int32("constructing_connections", stats.ConstructingConns()),
			zap.Int64("acquire_count", stats.AcquireCount()),
			zap.Duration("acquire_duration", stats.AcquireDuration()),
			zap.Int64("canceled_acquire_count", stats.CanceledAcquireCount()),
			zap.Int64("empty_acquire_count", stats.EmptyAcquireCount()),
		)

		// Log warnings for potential issues
		if stats.IdleConns() == 0 && stats.AcquiredConns() == stats.TotalConns() {
			c.logger.Warn("Database connection pool exhausted - consider increasing max connections")
		}

		if stats.CanceledAcquireCount() > 0 {
			c.logger.Warn("Database connection acquisitions being canceled",
				zap.Int64("canceled_count", stats.CanceledAcquireCount()))
		}
	}
}

// ExecuteWithMetrics executes a query with performance monitoring
func (c *PostgreSQLClient) ExecuteWithMetrics(ctx context.Context, query string, args ...interface{}) (pgconn.CommandTag, error) {
	start := time.Now()

	result, err := c.Pool.Exec(ctx, query, args...)

	duration := time.Since(start)
	c.logQueryMetrics("EXEC", query, duration, err)

	return result, err
}

// QueryWithMetrics executes a query with performance monitoring
func (c *PostgreSQLClient) QueryWithMetrics(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	start := time.Now()

	rows, err := c.Pool.Query(ctx, query, args...)

	duration := time.Since(start)
	c.logQueryMetrics("QUERY", query, duration, err)

	return rows, err
}

// QueryRowWithMetrics executes a query row with performance monitoring
func (c *PostgreSQLClient) QueryRowWithMetrics(ctx context.Context, query string, args ...interface{}) pgx.Row {
	start := time.Now()

	row := c.Pool.QueryRow(ctx, query, args...)

	duration := time.Since(start)
	c.logQueryMetrics("QUERY_ROW", query, duration, nil)

	return row
}

// logQueryMetrics logs query performance metrics
func (c *PostgreSQLClient) logQueryMetrics(operation, query string, duration time.Duration, err error) {
	// Extract table name from query for better metrics
	tableName := extractTableName(query)

	// Log slow queries (>100ms)
	if duration > 100*time.Millisecond {
		c.logger.Warn("Slow database query detected",
			zap.String("operation", operation),
			zap.String("table", tableName),
			zap.Duration("duration", duration),
			zap.String("query", sanitizeQuery(query)),
			zap.Error(err))
	} else {
		c.logger.Debug("Database query executed",
			zap.String("operation", operation),
			zap.String("table", tableName),
			zap.Duration("duration", duration),
			zap.Error(err))
	}
}

// extractTableName extracts the primary table name from a SQL query
func extractTableName(query string) string {
	query = strings.ToLower(strings.TrimSpace(query))

	// Handle different SQL operations
	if strings.HasPrefix(query, "select") {
		if idx := strings.Index(query, "from "); idx != -1 {
			remaining := query[idx+5:]
			parts := strings.Fields(remaining)
			if len(parts) > 0 {
				return strings.Trim(parts[0], "\"'`")
			}
		}
	} else if strings.HasPrefix(query, "insert into ") {
		remaining := query[12:]
		parts := strings.Fields(remaining)
		if len(parts) > 0 {
			return strings.Trim(parts[0], "\"'`")
		}
	} else if strings.HasPrefix(query, "update ") {
		remaining := query[7:]
		parts := strings.Fields(remaining)
		if len(parts) > 0 {
			return strings.Trim(parts[0], "\"'`")
		}
	} else if strings.HasPrefix(query, "delete from ") {
		remaining := query[12:]
		parts := strings.Fields(remaining)
		if len(parts) > 0 {
			return strings.Trim(parts[0], "\"'`")
		}
	}

	return "unknown"
}

// sanitizeQuery removes sensitive data from query for logging
func sanitizeQuery(query string) string {
	// Remove potential sensitive values by replacing parameters
	sanitized := query

	// Replace common sensitive patterns
	sensitized := strings.ReplaceAll(sanitized, "'", "***")
	sensitized = strings.ReplaceAll(sensitized, "\"", "***")

	// Truncate very long queries
	if len(sensitized) > 500 {
		sensitized = sensitized[:500] + "..."
	}

	return sensitized
}

// GetConnectionPoolStats returns current connection pool statistics
func (c *PostgreSQLClient) GetConnectionPoolStats() map[string]interface{} {
	stats := c.Pool.Stat()

	return map[string]interface{}{
		"total_connections":        stats.TotalConns(),
		"idle_connections":         stats.IdleConns(),
		"acquired_connections":     stats.AcquiredConns(),
		"constructing_connections": stats.ConstructingConns(),
		"acquire_count":            stats.AcquireCount(),
		"acquire_duration_ms":      stats.AcquireDuration().Milliseconds(),
		"canceled_acquire_count":   stats.CanceledAcquireCount(),
		"empty_acquire_count":      stats.EmptyAcquireCount(),
		"max_connections":          c.config.MaxConnections,
		"min_connections":          c.config.MinConnections,
	}
}

// HealthCheck performs a comprehensive database health check
func (c *PostgreSQLClient) HealthCheck(ctx context.Context) error {
	// Test basic connectivity
	if err := c.Pool.Ping(ctx); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	// Test query execution
	var result int
	err := c.Pool.QueryRow(ctx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("database query test failed: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("database query returned unexpected result: %d", result)
	}

	// Check connection pool health
	stats := c.Pool.Stat()
	if stats.TotalConns() == 0 {
		return fmt.Errorf("no database connections available")
	}

	return nil
}

// GetUserStatistics retrieves comprehensive user statistics
func (c *PostgreSQLClient) GetUserStatistics(ctx context.Context, userID string) (*UserStatistics, error) {
	// Single optimized query that gets all user statistics
	query := `
		SELECT
			-- Bubbles created
			(SELECT COUNT(*) FROM bubbles WHERE creator_id = $1) as bubbles_created,

			-- Bubbles joined (active memberships)
			(SELECT COUNT(*) FROM bubble_members WHERE user_id = $1 AND status = 'active') as bubbles_joined,

			-- Total contacts (accepted)
			(SELECT COUNT(*) FROM contacts WHERE (requester_id = $1 OR recipient_id = $1) AND status = 'accepted') as total_contacts,

			-- Total friends
			(SELECT COUNT(*) FROM friendships WHERE user1_id = $1 OR user2_id = $1) as total_friends,

			-- Messages sent (from ScyllaDB - this would need to be queried separately)
			0 as messages_sent,

			-- Messages received (from ScyllaDB - this would need to be queried separately)
			0 as messages_received`

	var stats UserStatistics
	err := c.Pool.QueryRow(ctx, query, userID).Scan(
		&stats.BubblesCreated,
		&stats.BubblesJoined,
		&stats.TotalContacts,
		&stats.TotalFriends,
		&stats.MessagesSent,
		&stats.MessagesReceived,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user statistics: %w", HandlePgxError(err, "user_statistics"))
	}

	c.logger.Debug("Retrieved user statistics",
		zap.String("user_id", userID),
		zap.Int64("bubbles_created", stats.BubblesCreated),
		zap.Int64("bubbles_joined", stats.BubblesJoined),
		zap.Int64("total_contacts", stats.TotalContacts),
		zap.Int64("total_friends", stats.TotalFriends))

	return &stats, nil
}

// UserStatistics represents user statistics data
type UserStatistics struct {
	BubblesCreated   int64
	BubblesJoined    int64
	TotalContacts    int64
	TotalFriends     int64
	MessagesSent     int64
	MessagesReceived int64
}
