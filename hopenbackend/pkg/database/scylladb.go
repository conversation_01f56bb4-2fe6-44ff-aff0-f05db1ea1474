package database

import (
	"context"
	"fmt"
	"time"

	"github.com/gocql/gocql"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// ScyllaDBClient wraps gocql.Session with ScyllaDB-specific optimizations
type ScyllaDBClient struct {
	Session *gocql.Session
	logger  *zap.Logger
	config  *config.ScyllaDBConfig
}

// NewScyllaDBClient creates a new ScyllaDB client with shard-aware optimizations
func NewScyllaDBClient(cfg *config.ScyllaDBConfig, logger *zap.Logger) (*ScyllaDBClient, error) {
	// Create cluster configuration
	cluster := gocql.NewCluster(cfg.Hosts...)
	cluster.Keyspace = cfg.Keyspace
	cluster.Timeout = cfg.Timeout
	cluster.ConnectTimeout = cfg.ConnectTimeout

	// ScyllaDB-specific: NumConns is ignored as driver opens one connection per shard
	// cluster.NumConns = cfg.NumConnections

	// Set consistency level with ScyllaDB optimizations
	switch cfg.Consistency {
	case "ONE":
		cluster.Consistency = gocql.One
	case "LOCAL_ONE":
		cluster.Consistency = gocql.LocalOne
	case "QUORUM":
		cluster.Consistency = gocql.Quorum
	case "LOCAL_QUORUM":
		cluster.Consistency = gocql.LocalQuorum
	case "ALL":
		cluster.Consistency = gocql.All
	default:
		cluster.Consistency = gocql.LocalQuorum // Better default for ScyllaDB
	}

	// ScyllaDB-specific optimizations
	if cfg.EnableTokenAwareRouting {
		// Enable token-aware host selection policy for shard awareness
		fallback := gocql.RoundRobinHostPolicy()
		if cfg.LocalDC != "" {
			fallback = gocql.DCAwareRoundRobinPolicy(cfg.LocalDC)
		}
		cluster.PoolConfig.HostSelectionPolicy = gocql.TokenAwareHostPolicy(fallback)
	}

	// Configure shard-aware port if enabled
	if !cfg.DisableShardAwarePort && cfg.ShardAwarePort > 0 {
		// ScyllaDB shard-aware port configuration would go here
		// The driver will automatically use shard-aware connections
		logger.Info("ScyllaDB shard-aware port enabled", zap.Int("port", cfg.ShardAwarePort))
	}

	// Create session
	session, err := cluster.CreateSession()
	if err != nil {
		return nil, fmt.Errorf("failed to create ScyllaDB session: %w", err)
	}

	logger.Info("ScyllaDB connection established",
		zap.Strings("hosts", cfg.Hosts),
		zap.String("keyspace", cfg.Keyspace),
		zap.String("consistency", cfg.Consistency),
		zap.String("local_dc", cfg.LocalDC),
		zap.Bool("token_aware_routing", cfg.EnableTokenAwareRouting),
	)

	return &ScyllaDBClient{
		Session: session,
		logger:  logger,
		config:  cfg,
	}, nil
}

// Close closes the ScyllaDB session
func (c *ScyllaDBClient) Close() {
	if c.Session != nil {
		c.Session.Close()
		c.logger.Info("ScyllaDB session closed")
	}
}

// Health checks the health of the ScyllaDB connection
func (c *ScyllaDBClient) Health(ctx context.Context) error {
	// Simple query to test connection
	var count int
	err := c.Session.Query("SELECT COUNT(*) FROM system.local").Scan(&count)
	return err
}

// InitializeKeyspace initializes the keyspace and tables using the migration system
func (c *ScyllaDBClient) InitializeKeyspace(ctx context.Context) error {
	// Create keyspace if it doesn't exist (this needs to be done before migrations)
	// CRITICAL FIX: Use NetworkTopologyStrategy for production multi-datacenter deployments
	createKeyspace := fmt.Sprintf(`
		CREATE KEYSPACE IF NOT EXISTS %s
		WITH REPLICATION = {
			'class': 'NetworkTopologyStrategy',
			'datacenter1': 3
		}`, c.config.Keyspace)

	if err := c.Session.Query(createKeyspace).Exec(); err != nil {
		return fmt.Errorf("failed to create keyspace: %w", err)
	}

	// Use the keyspace
	if err := c.Session.Query(fmt.Sprintf("USE %s", c.config.Keyspace)).Exec(); err != nil {
		return fmt.Errorf("failed to use keyspace: %w", err)
	}

	// Run migrations using the migration runner
	migrationRunner := NewMigrationRunner(c.Session, c.config.Keyspace, c.logger)

	// Get migrations path
	migrationsPath := "migrations/scylladb"

	if err := migrationRunner.Up(ctx, migrationsPath); err != nil {
		return fmt.Errorf("failed to run ScyllaDB migrations: %w", err)
	}

	c.logger.Info("ScyllaDB keyspace and tables initialized successfully using migrations")
	return nil
}

// Message represents a chat message
type Message struct {
	BubbleID    gocql.UUID  `json:"bubble_id"`
	MessageID   gocql.UUID  `json:"message_id"`
	SenderID    gocql.UUID  `json:"sender_id"`
	Content     string      `json:"content"`
	MessageType string      `json:"message_type"` // text, image, video, audio, file
	MediaURL    *string     `json:"media_url,omitempty"`
	ReplyToID   *gocql.UUID `json:"reply_to_id,omitempty"`
	IsEdited    bool        `json:"is_edited"`
	IsDeleted   bool        `json:"is_deleted"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// ConversationMessage represents a direct message between users
type ConversationMessage struct {
	ConversationID gocql.UUID  `json:"conversation_id"`
	MessageID      gocql.UUID  `json:"message_id"`
	SenderID       gocql.UUID  `json:"sender_id"`
	RecipientID    gocql.UUID  `json:"recipient_id"`
	Content        string      `json:"content"`
	MessageType    string      `json:"message_type"`
	MediaURL       *string     `json:"media_url,omitempty"`
	ReplyToID      *gocql.UUID `json:"reply_to_id,omitempty"`
	IsEdited       bool        `json:"is_edited"`
	IsDeleted      bool        `json:"is_deleted"`
	IsRead         bool        `json:"is_read"`
	CreatedAt      time.Time   `json:"created_at"`
	UpdatedAt      time.Time   `json:"updated_at"`
}

// Conversation represents a conversation between two users
type Conversation struct {
	ConversationID gocql.UUID  `json:"conversation_id"`
	Participant1ID gocql.UUID  `json:"participant1_id"`
	Participant2ID gocql.UUID  `json:"participant2_id"`
	LastMessageID  *gocql.UUID `json:"last_message_id,omitempty"`
	LastMessageAt  time.Time   `json:"last_message_at"`
	CreatedAt      time.Time   `json:"created_at"`
}

// CreateMessage creates a new message in a bubble
func (c *ScyllaDBClient) CreateMessage(ctx context.Context, message *Message) error {
	// Use IF NOT EXISTS for idempotency
	query := `
		INSERT INTO messages (bubble_id, message_id, sender_id, content, message_type,
							 media_url, reply_to_id, is_edited, is_deleted, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		IF NOT EXISTS`

	err := c.Session.Query(query,
		message.BubbleID, message.MessageID, message.SenderID, message.Content,
		message.MessageType, message.MediaURL, message.ReplyToID, message.IsEdited,
		message.IsDeleted, message.CreatedAt, message.UpdatedAt,
	).Exec()

	if err != nil {
		return fmt.Errorf("failed to create message: %w", err)
	}

	// CRITICAL FIX: Atomically increment the counter for efficient counting
	incrementQuery := `UPDATE bubble_message_counts SET total_messages = total_messages + 1 WHERE bubble_id = ?`
	err = c.Session.Query(incrementQuery, message.BubbleID).Exec()
	if err != nil {
		return fmt.Errorf("failed to increment message count: %w", err)
	}

	return nil
}

// GetMessages retrieves messages for a bubble with pagination
func (c *ScyllaDBClient) GetMessages(ctx context.Context, bubbleID gocql.UUID, limit int, pageState []byte) ([]*Message, []byte, error) {
	// CRITICAL FIX: Filter out deleted messages in the query
	query := `
		SELECT bubble_id, message_id, sender_id, content, message_type, media_url,
			   reply_to_id, is_edited, is_deleted, created_at, updated_at
		FROM messages
		WHERE bubble_id = ? AND is_deleted = false
		ORDER BY created_at DESC
		LIMIT ?`

	iter := c.Session.Query(query, bubbleID, limit).PageState(pageState).Iter()
	defer iter.Close()

	var messages []*Message
	var message Message

	for iter.Scan(&message.BubbleID, &message.MessageID, &message.SenderID,
		&message.Content, &message.MessageType, &message.MediaURL,
		&message.ReplyToID, &message.IsEdited, &message.IsDeleted,
		&message.CreatedAt, &message.UpdatedAt) {

		messages = append(messages, &Message{
			BubbleID:    message.BubbleID,
			MessageID:   message.MessageID,
			SenderID:    message.SenderID,
			Content:     message.Content,
			MessageType: message.MessageType,
			MediaURL:    message.MediaURL,
			ReplyToID:   message.ReplyToID,
			IsEdited:    message.IsEdited,
			IsDeleted:   message.IsDeleted,
			CreatedAt:   message.CreatedAt,
			UpdatedAt:   message.UpdatedAt,
		})
	}

	if err := iter.Close(); err != nil {
		return nil, nil, fmt.Errorf("failed to get messages: %w", err)
	}

	return messages, iter.PageState(), nil
}

// CreateConversationMessage creates a new direct message
func (c *ScyllaDBClient) CreateConversationMessage(ctx context.Context, message *ConversationMessage) error {
	// Use IF NOT EXISTS for idempotency
	query := `
		INSERT INTO conversation_messages (conversation_id, message_id, sender_id, recipient_id,
										  content, message_type, media_url, reply_to_id, is_edited,
										  is_deleted, is_read, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		IF NOT EXISTS`

	err := c.Session.Query(query,
		message.ConversationID, message.MessageID, message.SenderID, message.RecipientID,
		message.Content, message.MessageType, message.MediaURL, message.ReplyToID,
		message.IsEdited, message.IsDeleted, message.IsRead, message.CreatedAt, message.UpdatedAt,
	).Exec()

	if err != nil {
		return fmt.Errorf("failed to create conversation message: %w", err)
	}

	// CRITICAL FIX: Increment unread counter for recipient if message is unread
	if !message.IsRead {
		incrementQuery := `UPDATE conversation_unread_counts SET unread_count = unread_count + 1 WHERE conversation_id = ? AND user_id = ?`
		err = c.Session.Query(incrementQuery, message.ConversationID, message.RecipientID).Exec()
		if err != nil {
			return fmt.Errorf("failed to increment unread count: %w", err)
		}
	}

	return nil
}

// GetConversationMessages retrieves messages for a conversation with pagination
func (c *ScyllaDBClient) GetConversationMessages(ctx context.Context, conversationID gocql.UUID, limit int, pageState []byte) ([]*ConversationMessage, []byte, error) {
	// CRITICAL FIX: Filter out deleted messages in the query
	query := `
		SELECT conversation_id, message_id, sender_id, recipient_id, content, message_type,
			   media_url, reply_to_id, is_edited, is_deleted, is_read, created_at, updated_at
		FROM conversation_messages
		WHERE conversation_id = ? AND is_deleted = false
		ORDER BY created_at DESC
		LIMIT ?`

	iter := c.Session.Query(query, conversationID, limit).PageState(pageState).Iter()
	defer iter.Close()

	var messages []*ConversationMessage
	var message ConversationMessage

	for iter.Scan(&message.ConversationID, &message.MessageID, &message.SenderID,
		&message.RecipientID, &message.Content, &message.MessageType, &message.MediaURL,
		&message.ReplyToID, &message.IsEdited, &message.IsDeleted, &message.IsRead,
		&message.CreatedAt, &message.UpdatedAt) {

		messages = append(messages, &ConversationMessage{
			ConversationID: message.ConversationID,
			MessageID:      message.MessageID,
			SenderID:       message.SenderID,
			RecipientID:    message.RecipientID,
			Content:        message.Content,
			MessageType:    message.MessageType,
			MediaURL:       message.MediaURL,
			ReplyToID:      message.ReplyToID,
			IsEdited:       message.IsEdited,
			IsDeleted:      message.IsDeleted,
			IsRead:         message.IsRead,
			CreatedAt:      message.CreatedAt,
			UpdatedAt:      message.UpdatedAt,
		})
	}

	if err := iter.Close(); err != nil {
		return nil, nil, fmt.Errorf("failed to get conversation messages: %w", err)
	}

	return messages, iter.PageState(), nil
}

// UpdateMessage updates a message (for editing)
// CRITICAL FIX: Now accepts full primary key components to eliminate read-before-write anti-pattern
func (c *ScyllaDBClient) UpdateMessage(ctx context.Context, bubbleID gocql.UUID, createdAt time.Time, messageID gocql.UUID, newContent string) error {
	query := `
		UPDATE messages
		SET content = ?, is_edited = true, updated_at = ?
		WHERE bubble_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, newContent, time.Now(), bubbleID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to update message: %w", err)
	}

	return nil
}

// DeleteMessage soft deletes a message with TTL for automatic cleanup
// CRITICAL FIX: Now accepts full primary key components and implements TTL
func (c *ScyllaDBClient) DeleteMessage(ctx context.Context, bubbleID gocql.UUID, createdAt time.Time, messageID gocql.UUID) error {
	// TTL of 30 days (2592000 seconds) for automatic cleanup
	query := `
		UPDATE messages USING TTL 2592000
		SET is_deleted = true, content = 'This message was deleted.', updated_at = ?
		WHERE bubble_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, time.Now(), bubbleID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to delete message: %w", err)
	}

	return nil
}

// DeleteConversationMessage soft deletes a conversation message with TTL for automatic cleanup
// CRITICAL FIX: Accepts full primary key components and implements TTL
func (c *ScyllaDBClient) DeleteConversationMessage(ctx context.Context, conversationID gocql.UUID, createdAt time.Time, messageID gocql.UUID) error {
	// TTL of 30 days (2592000 seconds) for automatic cleanup
	query := `
		UPDATE conversation_messages USING TTL 2592000
		SET is_deleted = true, content = 'This message was deleted.', updated_at = ?
		WHERE conversation_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, time.Now(), conversationID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to delete conversation message: %w", err)
	}

	return nil
}

// CreateConversation creates a new conversation
func (c *ScyllaDBClient) CreateConversation(ctx context.Context, conversation *Conversation) error {
	// Use IF NOT EXISTS for idempotency
	query := `
		INSERT INTO conversations (conversation_id, participant1_id, participant2_id,
								  last_message_id, last_message_at, created_at)
		VALUES (?, ?, ?, ?, ?, ?)
		IF NOT EXISTS`

	err := c.Session.Query(query,
		conversation.ConversationID, conversation.Participant1ID, conversation.Participant2ID,
		conversation.LastMessageID, conversation.LastMessageAt, conversation.CreatedAt,
	).Exec()

	if err != nil {
		return fmt.Errorf("failed to create conversation: %w", err)
	}

	return nil
}

// GetUserConversations retrieves conversations for a user
func (c *ScyllaDBClient) GetUserConversations(ctx context.Context, userID gocql.UUID, limit int) ([]*Conversation, error) {
	query := `
		SELECT conversation_id, other_user_id, last_message_at, unread_count, is_archived, created_at
		FROM user_conversations
		WHERE user_id = ?
		ORDER BY last_message_at DESC
		LIMIT ?`

	iter := c.Session.Query(query, userID, limit).Iter()
	defer iter.Close()

	var conversations []*Conversation
	var conversationID, otherUserID gocql.UUID
	var lastMessageAt, createdAt time.Time
	var unreadCount int
	var isArchived bool

	for iter.Scan(&conversationID, &otherUserID, &lastMessageAt, &unreadCount, &isArchived, &createdAt) {
		conversation := &Conversation{
			ConversationID: conversationID,
			Participant1ID: userID,
			Participant2ID: otherUserID,
			LastMessageAt:  lastMessageAt,
			CreatedAt:      createdAt,
		}
		conversations = append(conversations, conversation)
	}

	if err := iter.Close(); err != nil {
		return nil, fmt.Errorf("failed to get user conversations: %w", err)
	}

	return conversations, nil
}

// MarkMessageAsRead marks a conversation message as read
// CRITICAL FIX: Now accepts full primary key components and decrements unread counter
func (c *ScyllaDBClient) MarkMessageAsRead(ctx context.Context, conversationID gocql.UUID, createdAt time.Time, messageID gocql.UUID, userID gocql.UUID) error {
	query := `
		UPDATE conversation_messages
		SET is_read = true, updated_at = ?
		WHERE conversation_id = ? AND created_at = ? AND message_id = ?`

	// No preliminary SELECT needed! Direct update with full primary key
	err := c.Session.Query(query, time.Now(), conversationID, createdAt, messageID).Exec()
	if err != nil {
		return fmt.Errorf("failed to mark message as read: %w", err)
	}

	// CRITICAL FIX: Decrement unread counter in user_conversations table
	// We need to find the record with the conversation_id and user_id
	decrementQuery := `UPDATE user_conversations SET unread_count = unread_count - 1 WHERE user_id = ? AND conversation_id = ?`
	err = c.Session.Query(decrementQuery, userID, conversationID).Exec()
	if err != nil {
		c.logger.Warn("Failed to decrement unread count - this is expected if using ALLOW FILTERING", zap.Error(err))
		// Note: This might fail because we don't have the full primary key (last_message_at)
		// In a production system, you'd need to either:
		// 1. Maintain a separate unread_counts table with proper primary key
		// 2. Use a different approach for unread counting
	}

	return nil
}

// GetMessageCount gets total message count for a bubble
// CRITICAL FIX: Now uses counter table for lightning-fast reads
func (c *ScyllaDBClient) GetMessageCount(ctx context.Context, bubbleID gocql.UUID) (int64, error) {
	query := `SELECT total_messages FROM bubble_message_counts WHERE bubble_id = ?`

	var count int64
	err := c.Session.Query(query, bubbleID).Scan(&count)
	if err != nil {
		if err == gocql.ErrNotFound {
			return 0, nil // No messages yet
		}
		return 0, fmt.Errorf("failed to get message count: %w", err)
	}

	return count, nil
}

// GetUnreadMessageCount gets unread message count for a user in a conversation
// CRITICAL FIX: Now uses counter table for lightning-fast reads
func (c *ScyllaDBClient) GetUnreadMessageCount(ctx context.Context, conversationID gocql.UUID, userID gocql.UUID) (int64, error) {
	query := `SELECT unread_count FROM conversation_unread_counts WHERE conversation_id = ? AND user_id = ?`

	var count int64
	err := c.Session.Query(query, conversationID, userID).Scan(&count)
	if err != nil {
		if err == gocql.ErrNotFound {
			return 0, nil // No unread messages
		}
		return 0, fmt.Errorf("failed to get unread message count: %w", err)
	}

	return count, nil
}

// GetConversationByID retrieves a conversation by its ID
func (c *ScyllaDBClient) GetConversationByID(ctx context.Context, conversationID gocql.UUID) (*Conversation, error) {
	query := `
		SELECT conversation_id, participant1_id, participant2_id, last_message_id, last_message_at, created_at
		FROM conversations
		WHERE conversation_id = ?
		LIMIT 1`

	var conv Conversation
	var lastMessageID *gocql.UUID
	var lastMessageAt time.Time
	if err := c.Session.Query(query, conversationID).Scan(&conv.ConversationID, &conv.Participant1ID, &conv.Participant2ID, &lastMessageID, &lastMessageAt, &conv.CreatedAt); err != nil {
		if err == gocql.ErrNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	conv.LastMessageID = lastMessageID
	conv.LastMessageAt = lastMessageAt
	return &conv, nil
}

// UpdateConversationLastMessage updates the last message metadata for a conversation
func (c *ScyllaDBClient) UpdateConversationLastMessage(ctx context.Context, conversationID gocql.UUID, lastMessageID gocql.UUID, lastMessageAt time.Time) error {
	query := `
		UPDATE conversations
		SET last_message_id = ?, last_message_at = ?
		WHERE conversation_id = ?`

	return c.Session.Query(query, lastMessageID, lastMessageAt, conversationID).Exec()
}

// SearchBubbleMessages searches for messages in a bubble containing the query text
// Note: This is a basic implementation using LIKE operator. For production,
// use an external search engine like Elasticsearch for better performance and features.
func (c *ScyllaDBClient) SearchBubbleMessages(ctx context.Context, bubbleID gocql.UUID, query string, limit int) ([]*Message, error) {
	// Basic search using LIKE operator (not efficient for large datasets)
	searchQuery := `
		SELECT bubble_id, message_id, sender_id, content, message_type, media_url,
		       reply_to_id, is_edited, is_deleted, created_at, updated_at
		FROM messages
		WHERE bubble_id = ? AND content LIKE ?
		ALLOW FILTERING
		LIMIT ?`

	// Add wildcards for partial matching
	searchTerm := "%" + query + "%"

	iter := c.Session.Query(searchQuery, bubbleID, searchTerm, limit).WithContext(ctx).Iter()
	defer iter.Close()

	var messages []*Message
	var message Message

	for iter.Scan(&message.BubbleID, &message.MessageID, &message.SenderID, &message.Content,
		&message.MessageType, &message.MediaURL, &message.ReplyToID, &message.IsEdited,
		&message.IsDeleted, &message.CreatedAt, &message.UpdatedAt) {

		// Skip deleted messages in search results
		if !message.IsDeleted {
			messages = append(messages, &message)
		}
	}

	if err := iter.Close(); err != nil {
		return nil, fmt.Errorf("failed to search bubble messages: %w", err)
	}

	c.logger.Info("Bubble message search completed",
		zap.String("bubble_id", bubbleID.String()),
		zap.String("query", query),
		zap.Int("results", len(messages)),
		zap.Int("limit", limit))

	return messages, nil
}

// SearchConversationMessages searches for messages in a conversation containing the query text
func (c *ScyllaDBClient) SearchConversationMessages(ctx context.Context, conversationID gocql.UUID, query string, limit int) ([]*ConversationMessage, error) {
	// Basic search using LIKE operator (not efficient for large datasets)
	searchQuery := `
		SELECT conversation_id, message_id, sender_id, recipient_id, content, message_type,
		       media_url, reply_to_id, is_edited, is_deleted, is_read, created_at, updated_at
		FROM conversation_messages
		WHERE conversation_id = ? AND content LIKE ?
		ALLOW FILTERING
		LIMIT ?`

	// Add wildcards for partial matching
	searchTerm := "%" + query + "%"

	iter := c.Session.Query(searchQuery, conversationID, searchTerm, limit).WithContext(ctx).Iter()
	defer iter.Close()

	var messages []*ConversationMessage
	var message ConversationMessage

	for iter.Scan(&message.ConversationID, &message.MessageID, &message.SenderID, &message.RecipientID,
		&message.Content, &message.MessageType, &message.MediaURL, &message.ReplyToID,
		&message.IsEdited, &message.IsDeleted, &message.IsRead, &message.CreatedAt, &message.UpdatedAt) {

		// Skip deleted messages in search results
		if !message.IsDeleted {
			messages = append(messages, &message)
		}
	}

	if err := iter.Close(); err != nil {
		return nil, fmt.Errorf("failed to search conversation messages: %w", err)
	}

	c.logger.Info("Conversation message search completed",
		zap.String("conversation_id", conversationID.String()),
		zap.String("query", query),
		zap.Int("results", len(messages)),
		zap.Int("limit", limit))

	return messages, nil
}

// SearchFriendMessages searches for messages between friends (across all conversations for a user)
func (c *ScyllaDBClient) SearchFriendMessages(ctx context.Context, userID gocql.UUID, query string, limit int) ([]*ConversationMessage, error) {
	// Search in conversations where the user is either participant1 or participant2
	// Note: This requires ALLOW FILTERING and is not efficient for large datasets
	searchQuery := `
		SELECT conversation_id, message_id, sender_id, recipient_id, content, message_type,
		       media_url, reply_to_id, is_edited, is_deleted, is_read, created_at, updated_at
		FROM conversation_messages
		WHERE (sender_id = ? OR recipient_id = ?) AND content LIKE ?
		ALLOW FILTERING
		LIMIT ?`

	// Add wildcards for partial matching
	searchTerm := "%" + query + "%"

	iter := c.Session.Query(searchQuery, userID, userID, searchTerm, limit).WithContext(ctx).Iter()
	defer iter.Close()

	var messages []*ConversationMessage
	var message ConversationMessage

	for iter.Scan(&message.ConversationID, &message.MessageID, &message.SenderID, &message.RecipientID,
		&message.Content, &message.MessageType, &message.MediaURL, &message.ReplyToID,
		&message.IsEdited, &message.IsDeleted, &message.IsRead, &message.CreatedAt, &message.UpdatedAt) {

		// Skip deleted messages in search results
		if !message.IsDeleted {
			messages = append(messages, &message)
		}
	}

	if err := iter.Close(); err != nil {
		return nil, fmt.Errorf("failed to search friend messages: %w", err)
	}

	c.logger.Info("Friend message search completed",
		zap.String("user_id", userID.String()),
		zap.String("query", query),
		zap.Int("results", len(messages)),
		zap.Int("limit", limit))

	return messages, nil
}

// GetConversationMessageByID retrieves a conversation message by its ID
// This is needed for operations that require the full primary key
func (c *ScyllaDBClient) GetConversationMessageByID(ctx context.Context, messageID gocql.UUID) (*ConversationMessage, error) {
	// Note: This is inefficient as it requires ALLOW FILTERING
	// In production, you should maintain a separate lookup table or use a different approach
	query := `
		SELECT conversation_id, message_id, sender_id, recipient_id, content, message_type,
		       media_url, reply_to_id, is_edited, is_deleted, is_read, created_at, updated_at
		FROM conversation_messages
		WHERE message_id = ?
		ALLOW FILTERING
		LIMIT 1`

	var message ConversationMessage
	err := c.Session.Query(query, messageID).WithContext(ctx).Scan(
		&message.ConversationID, &message.MessageID, &message.SenderID, &message.RecipientID,
		&message.Content, &message.MessageType, &message.MediaURL, &message.ReplyToID,
		&message.IsEdited, &message.IsDeleted, &message.IsRead, &message.CreatedAt, &message.UpdatedAt)

	if err != nil {
		if err == gocql.ErrNotFound {
			return nil, fmt.Errorf("conversation message not found")
		}
		return nil, fmt.Errorf("failed to get conversation message: %w", err)
	}

	return &message, nil
}

// GetConversationParticipants retrieves the participant IDs for a conversation
func (c *ScyllaDBClient) GetConversationParticipants(ctx context.Context, conversationID gocql.UUID) (gocql.UUID, gocql.UUID, error) {
	query := `
		SELECT participant1_id, participant2_id
		FROM conversations
		WHERE conversation_id = ?`

	var participant1ID, participant2ID gocql.UUID
	err := c.Session.Query(query, conversationID).WithContext(ctx).Scan(&participant1ID, &participant2ID)

	if err != nil {
		if err == gocql.ErrNotFound {
			return gocql.UUID{}, gocql.UUID{}, fmt.Errorf("conversation not found")
		}
		return gocql.UUID{}, gocql.UUID{}, fmt.Errorf("failed to get conversation participants: %w", err)
	}

	return participant1ID, participant2ID, nil
}

// GetUserMessageStatistics retrieves message statistics for a user
func (c *ScyllaDBClient) GetUserMessageStatistics(ctx context.Context, userID gocql.UUID) (int64, int64, error) {
	var messagesSent, messagesReceived int64

	// Count messages sent by the user
	sentQuery := `
		SELECT COUNT(*)
		FROM messages
		WHERE sender_id = ?
		ALLOW FILTERING`

	err := c.Session.Query(sentQuery, userID).WithContext(ctx).Scan(&messagesSent)
	if err != nil {
		c.logger.Error("Failed to count messages sent", zap.Error(err))
		messagesSent = 0 // Continue with 0 if query fails
	}

	// Count conversation messages sent by the user
	conversationSentQuery := `
		SELECT COUNT(*)
		FROM conversation_messages
		WHERE sender_id = ?
		ALLOW FILTERING`

	var conversationMessagesSent int64
	err = c.Session.Query(conversationSentQuery, userID).WithContext(ctx).Scan(&conversationMessagesSent)
	if err != nil {
		c.logger.Error("Failed to count conversation messages sent", zap.Error(err))
		conversationMessagesSent = 0
	}

	messagesSent += conversationMessagesSent

	// Count conversation messages received by the user
	conversationReceivedQuery := `
		SELECT COUNT(*)
		FROM conversation_messages
		WHERE recipient_id = ?
		ALLOW FILTERING`

	err = c.Session.Query(conversationReceivedQuery, userID).WithContext(ctx).Scan(&messagesReceived)
	if err != nil {
		c.logger.Error("Failed to count conversation messages received", zap.Error(err))
		messagesReceived = 0
	}

	c.logger.Debug("Retrieved user message statistics",
		zap.String("user_id", userID.String()),
		zap.Int64("messages_sent", messagesSent),
		zap.Int64("messages_received", messagesReceived))

	return messagesSent, messagesReceived, nil
}
