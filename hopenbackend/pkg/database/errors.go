package database

import (
	"errors"
	"fmt"
)

// Custom error types for database operations
var (
	ErrNotFound      = errors.New("record not found")
	ErrDuplicate     = errors.New("record with unique constraint already exists")
	ErrValidation    = errors.New("validation error")
	ErrUnauthorized  = errors.New("unauthorized access")
	ErrForbidden     = errors.New("forbidden operation")
	ErrConflict      = errors.New("conflict with existing data")
)

// NotFoundError represents a record not found error
type NotFoundError struct {
	Resource string
	Message  string
}

func (e *NotFoundError) Error() string {
	if e.Message != "" {
		return fmt.Sprintf("%s: %s", e.Resource, e.Message)
	}
	return fmt.Sprintf("%s: %s", e.Resource, ErrNotFound)
}

func (e *NotFoundError) Unwrap() error {
	return ErrNotFound
}

// DuplicateError represents a duplicate record error
type DuplicateError struct {
	Resource string
	Message  string
}

func (e *DuplicateError) Error() string {
	if e.Message != "" {
		return fmt.Sprintf("%s: %s", e.Resource, e.Message)
	}
	return fmt.Sprintf("%s: %s", e.Resource, ErrDuplicate)
}

func (e *DuplicateError) Unwrap() error {
	return ErrDuplicate
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("validation failed for field '%s': %s", e.Field, e.Message)
	}
	return fmt.Sprintf("validation error: %s", e.Message)
}

func (e *ValidationError) Unwrap() error {
	return ErrValidation
}

// UnauthorizedError represents an unauthorized access error
type UnauthorizedError struct {
	Resource string
	Message  string
}

func (e *UnauthorizedError) Error() string {
	if e.Message != "" {
		return fmt.Sprintf("%s: %s", e.Resource, e.Message)
	}
	return fmt.Sprintf("%s: %s", e.Resource, ErrUnauthorized)
}

func (e *UnauthorizedError) Unwrap() error {
	return ErrUnauthorized
}

// ForbiddenError represents a forbidden operation error
type ForbiddenError struct {
	Resource string
	Message  string
}

func (e *ForbiddenError) Error() string {
	if e.Message != "" {
		return fmt.Sprintf("%s: %s", e.Resource, e.Message)
	}
	return fmt.Sprintf("%s: %s", e.Resource, ErrForbidden)
}

func (e *ForbiddenError) Unwrap() error {
	return ErrForbidden
}

// ConflictError represents a conflict error
type ConflictError struct {
	Resource string
	Message  string
}

func (e *ConflictError) Error() string {
	if e.Message != "" {
		return fmt.Sprintf("%s: %s", e.Resource, e.Message)
	}
	return fmt.Sprintf("%s: %s", e.Resource, ErrConflict)
}

func (e *ConflictError) Unwrap() error {
	return ErrConflict
}

// NewNotFoundError creates a new NotFoundError
func NewNotFoundError(resource, message string) *NotFoundError {
	return &NotFoundError{
		Resource: resource,
		Message:  message,
	}
}

// NewDuplicateError creates a new DuplicateError
func NewDuplicateError(resource, message string) *DuplicateError {
	return &DuplicateError{
		Resource: resource,
		Message:  message,
	}
}

// NewValidationError creates a new ValidationError
func NewValidationError(field, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}

// NewUnauthorizedError creates a new UnauthorizedError
func NewUnauthorizedError(resource, message string) *UnauthorizedError {
	return &UnauthorizedError{
		Resource: resource,
		Message:  message,
	}
}

// NewForbiddenError creates a new ForbiddenError
func NewForbiddenError(resource, message string) *ForbiddenError {
	return &ForbiddenError{
		Resource: resource,
		Message:  message,
	}
}

// NewConflictError creates a new ConflictError
func NewConflictError(resource, message string) *ConflictError {
	return &ConflictError{
		Resource: resource,
		Message:  message,
	}
}

// IsNotFound checks if the error is a NotFoundError
func IsNotFound(err error) bool {
	return errors.Is(err, ErrNotFound)
}

// IsDuplicate checks if the error is a DuplicateError
func IsDuplicate(err error) bool {
	return errors.Is(err, ErrDuplicate)
}

// IsValidation checks if the error is a ValidationError
func IsValidation(err error) bool {
	return errors.Is(err, ErrValidation)
}

// IsUnauthorized checks if the error is an UnauthorizedError
func IsUnauthorized(err error) bool {
	return errors.Is(err, ErrUnauthorized)
}

// IsForbidden checks if the error is a ForbiddenError
func IsForbidden(err error) bool {
	return errors.Is(err, ErrForbidden)
}

// IsConflict checks if the error is a ConflictError
func IsConflict(err error) bool {
	return errors.Is(err, ErrConflict)
}
