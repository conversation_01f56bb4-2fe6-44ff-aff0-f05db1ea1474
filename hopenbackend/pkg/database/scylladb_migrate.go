package database

import (
	"context"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gocql/gocql"
	"go.uber.org/zap"
)

// Migration represents a database migration
type Migration struct {
	Version   int
	Name      string
	UpQuery   string
	DownQuery string
}

// MigrationRunner handles ScyllaDB migrations
type MigrationRunner struct {
	session  *gocql.Session
	logger   *zap.Logger
	keyspace string
}

// NewMigrationRunner creates a new migration runner
func NewMigrationRunner(session *gocql.Session, keyspace string, logger *zap.Logger) *MigrationRunner {
	return &MigrationRunner{
		session:  session,
		logger:   logger,
		keyspace: keyspace,
	}
}

// ensureMigrationTable creates the migration tracking table if it doesn't exist
func (m *MigrationRunner) ensureMigrationTable(ctx context.Context) error {
	query := `
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version INT PRIMARY KEY,
			applied_at TIMESTAMP,
			checksum TEXT
		) WITH COMMENT = 'Migration tracking table for ScyllaDB schema versions'`

	return m.session.Query(query).WithContext(ctx).Exec()
}

// getAppliedMigrations returns a map of applied migration versions
func (m *MigrationRunner) getAppliedMigrations(ctx context.Context) (map[int]bool, error) {
	applied := make(map[int]bool)

	query := "SELECT version FROM schema_migrations"
	iter := m.session.Query(query).WithContext(ctx).Iter()
	defer iter.Close()

	var version int
	for iter.Scan(&version) {
		applied[version] = true
	}

	if err := iter.Close(); err != nil {
		return nil, fmt.Errorf("failed to scan applied migrations: %w", err)
	}

	return applied, nil
}

// loadMigrations loads migration files from the specified directory
func (m *MigrationRunner) loadMigrations(migrationsPath string) ([]*Migration, error) {
	var migrations []*Migration

	err := filepath.WalkDir(migrationsPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() || !strings.HasSuffix(path, ".cql") {
			return nil
		}

		// Parse filename to extract version and name
		filename := d.Name()
		parts := strings.SplitN(filename, "_", 2)
		if len(parts) < 2 {
			m.logger.Warn("Skipping migration file with invalid format", zap.String("file", filename))
			return nil
		}

		version, err := strconv.Atoi(parts[0])
		if err != nil {
			m.logger.Warn("Skipping migration file with invalid version", zap.String("file", filename), zap.Error(err))
			return nil
		}

		name := strings.TrimSuffix(parts[1], ".cql")

		// Read file content
		content, err := os.ReadFile(path)
		if err != nil {
			return fmt.Errorf("failed to read migration file %s: %w", path, err)
		}

		migration := &Migration{
			Version: version,
			Name:    name,
			UpQuery: string(content),
			// For ScyllaDB, we don't typically have down migrations
			// as schema changes are often additive
			DownQuery: "",
		}

		migrations = append(migrations, migration)
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to walk migration directory: %w", err)
	}

	// Sort migrations by version
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].Version < migrations[j].Version
	})

	return migrations, nil
}

// Up runs all pending migrations
func (m *MigrationRunner) Up(ctx context.Context, migrationsPath string) error {
	m.logger.Info("Starting ScyllaDB migrations", zap.String("path", migrationsPath))

	// Ensure migration table exists
	if err := m.ensureMigrationTable(ctx); err != nil {
		return fmt.Errorf("failed to ensure migration table: %w", err)
	}

	// Get applied migrations
	applied, err := m.getAppliedMigrations(ctx)
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// Load migration files
	migrations, err := m.loadMigrations(migrationsPath)
	if err != nil {
		return fmt.Errorf("failed to load migrations: %w", err)
	}

	// Run pending migrations
	for _, migration := range migrations {
		if applied[migration.Version] {
			m.logger.Debug("Migration already applied",
				zap.Int("version", migration.Version),
				zap.String("name", migration.Name))
			continue
		}

		m.logger.Info("Applying migration",
			zap.Int("version", migration.Version),
			zap.String("name", migration.Name))

		// Execute migration
		if err := m.session.Query(migration.UpQuery).WithContext(ctx).Exec(); err != nil {
			return fmt.Errorf("failed to apply migration %d (%s): %w", migration.Version, migration.Name, err)
		}

		// Record migration as applied
		recordQuery := `INSERT INTO schema_migrations (version, applied_at, checksum) VALUES (?, ?, ?)`
		checksum := fmt.Sprintf("%d_%s", migration.Version, migration.Name) // Simple checksum for now
		if err := m.session.Query(recordQuery, migration.Version, time.Now(), checksum).WithContext(ctx).Exec(); err != nil {
			return fmt.Errorf("failed to record migration %d: %w", migration.Version, err)
		}

		m.logger.Info("Migration applied successfully",
			zap.Int("version", migration.Version),
			zap.String("name", migration.Name))
	}

	m.logger.Info("All ScyllaDB migrations completed successfully")
	return nil
}

// Down rolls back the last migration
func (m *MigrationRunner) Down(ctx context.Context, migrationsPath string) error {
	m.logger.Info("Rolling back last ScyllaDB migration")

	// Get applied migrations
	applied, err := m.getAppliedMigrations(ctx)
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}

	if len(applied) == 0 {
		m.logger.Info("No migrations to rollback")
		return nil
	}

	// Find the highest applied version
	var highestVersion int
	for version := range applied {
		if version > highestVersion {
			highestVersion = version
		}
	}

	// Load migration files
	migrations, err := m.loadMigrations(migrationsPath)
	if err != nil {
		return fmt.Errorf("failed to load migrations: %w", err)
	}

	// Find the migration to rollback
	var targetMigration *Migration
	for _, migration := range migrations {
		if migration.Version == highestVersion {
			targetMigration = migration
			break
		}
	}

	if targetMigration == nil {
		return fmt.Errorf("migration file not found for version %d", highestVersion)
	}

	// For ScyllaDB, we typically don't support down migrations
	// as schema changes are often additive and irreversible
	m.logger.Warn("ScyllaDB down migrations are not supported - schema changes are typically additive",
		zap.Int("version", targetMigration.Version),
		zap.String("name", targetMigration.Name))

	// Remove the migration record
	deleteQuery := `DELETE FROM schema_migrations WHERE version = ?`
	if err := m.session.Query(deleteQuery, highestVersion).WithContext(ctx).Exec(); err != nil {
		return fmt.Errorf("failed to remove migration record %d: %w", highestVersion, err)
	}

	m.logger.Info("Migration record removed (schema not rolled back)",
		zap.Int("version", targetMigration.Version),
		zap.String("name", targetMigration.Name))

	return nil
}

// Status shows the status of all migrations
func (m *MigrationRunner) Status(ctx context.Context, migrationsPath string) error {
	// Get applied migrations
	applied, err := m.getAppliedMigrations(ctx)
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// Load migration files
	migrations, err := m.loadMigrations(migrationsPath)
	if err != nil {
		return fmt.Errorf("failed to load migrations: %w", err)
	}

	m.logger.Info("Migration Status:")
	for _, migration := range migrations {
		status := "PENDING"
		if applied[migration.Version] {
			status = "APPLIED"
		}
		m.logger.Info("Migration",
			zap.Int("version", migration.Version),
			zap.String("name", migration.Name),
			zap.String("status", status))
	}

	return nil
}
