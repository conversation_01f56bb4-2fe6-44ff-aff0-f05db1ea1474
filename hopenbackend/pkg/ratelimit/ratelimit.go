package ratelimit

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/valkey-io/valkey-go"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
	"google.golang.org/grpc/status"

	"hopenbackend/pkg/config"
)

// RateLimiter implements token bucket rate limiting using Valkey
type RateLimiter struct {
	client valkey.Client
	logger *zap.Logger
	config *config.Config
}

// NewRateLimiter creates a new rate limiter instance
func NewRateLimiter(cfg *config.Config, logger *zap.Logger) (*RateLimiter, error) {
	client, err := valkey.NewClient(valkey.ClientOption{
		InitAddress: []string{cfg.Valkey.Address},
		Password:    cfg.Valkey.Password,
		SelectDB:    cfg.Valkey.Database,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Valkey client: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Do(ctx, client.B().Ping().Build()).Error(); err != nil {
		return nil, fmt.Errorf("failed to ping Valkey: %w", err)
	}

	logger.Info("Valkey connection established for rate limiting",
		zap.String("address", cfg.Valkey.Address),
		zap.Int("database", cfg.Valkey.Database),
	)

	return &RateLimiter{
		client: client,
		logger: logger,
		config: cfg,
	}, nil
}

// Allow checks if a request is allowed based on rate limiting rules
func (rl *RateLimiter) Allow(ctx context.Context, key string) (bool, error) {
	return rl.AllowN(ctx, key, 1, 100, time.Minute)
}

// AllowN checks if N requests are allowed for the given key
func (rl *RateLimiter) AllowN(ctx context.Context, key string, n int64, limit int64, window time.Duration) (bool, error) {
	// Use sliding window log algorithm with Valkey
	now := time.Now().Unix()
	windowStart := now - int64(window.Seconds())

	// Lua script for atomic rate limiting check
	luaScript := `
		local key = KEYS[1]
		local window_start = tonumber(ARGV[1])
		local now = tonumber(ARGV[2])
		local limit = tonumber(ARGV[3])
		local n = tonumber(ARGV[4])
		local window_seconds = tonumber(ARGV[5])

		-- Remove expired entries
		valkey.call('ZREMRANGEBYSCORE', key, '-inf', window_start)

		-- Count current requests in window
		local current = valkey.call('ZCARD', key)

		-- Check if adding n requests would exceed limit
		if current + n > limit then
			return 0
		end

		-- Add new requests
		for i = 1, n do
			valkey.call('ZADD', key, now, now .. ':' .. i)
		end

		-- Set expiration
		valkey.call('EXPIRE', key, window_seconds)

		return 1
	`

	cmd := rl.client.B().Eval().Script(luaScript).Numkeys(1).Key(key).
		Arg(strconv.FormatInt(windowStart, 10)).
		Arg(strconv.FormatInt(now, 10)).
		Arg(strconv.FormatInt(limit, 10)).
		Arg(strconv.FormatInt(n, 10)).
		Arg(strconv.FormatInt(int64(window.Seconds()), 10)).
		Build()

	result, err := rl.client.Do(ctx, cmd).AsInt64()
	if err != nil {
		rl.logger.Error("Rate limit check failed",
			zap.String("key", key),
			zap.Error(err))
		return false, err
	}

	return result == 1, nil
}

// AllowForUser checks rate limit for a specific user
func (rl *RateLimiter) AllowForUser(ctx context.Context, userID string, operation string) (bool, error) {
	key := fmt.Sprintf("rate_limit:user:%s:%s", userID, operation)

	// Different limits for different operations
	switch operation {
	case "bubble_create":
		return rl.AllowN(ctx, key, 1, 10, time.Hour)
	case "contact_request":
		return rl.AllowN(ctx, key, 1, 50, 24*time.Hour)
	case "message_send":
		return rl.AllowN(ctx, key, 1, 1000, time.Minute)
	case "friend_request_accept":
		return rl.AllowN(ctx, key, 1, 100, time.Hour)
	case "user_search":
		return rl.AllowN(ctx, key, 1, 100, time.Minute)
	default:
		return rl.AllowN(ctx, key, 1, 100, time.Minute)
	}
}

// AllowForIP checks rate limit for a specific IP address
func (rl *RateLimiter) AllowForIP(ctx context.Context, ip string) (bool, error) {
	key := fmt.Sprintf("rate_limit:ip:%s", ip)
	return rl.AllowN(ctx, key, 1, 1000, time.Minute) // 1000 requests per minute per IP
}

// GetRemainingRequests returns the number of remaining requests for a key
func (rl *RateLimiter) GetRemainingRequests(ctx context.Context, key string, limit int64, window time.Duration) (int64, error) {
	now := time.Now().Unix()
	windowStart := now - int64(window.Seconds())

	// Remove expired entries and count current requests
	luaScript := `
		local key = KEYS[1]
		local window_start = tonumber(ARGV[1])
		local limit = tonumber(ARGV[2])

		-- Remove expired entries
		valkey.call('ZREMRANGEBYSCORE', key, '-inf', window_start)

		-- Count current requests
		local current = valkey.call('ZCARD', key)

		-- Return remaining requests
		return math.max(0, limit - current)
	`

	cmd := rl.client.B().Eval().Script(luaScript).Numkeys(1).Key(key).
		Arg(strconv.FormatInt(windowStart, 10)).
		Arg(strconv.FormatInt(limit, 10)).
		Build()

	remaining, err := rl.client.Do(ctx, cmd).AsInt64()
	if err != nil {
		return 0, err
	}

	return remaining, nil
}

// Reset resets the rate limit for a specific key
func (rl *RateLimiter) Reset(ctx context.Context, key string) error {
	cmd := rl.client.B().Del().Key(key).Build()
	return rl.client.Do(ctx, cmd).Error()
}

// Close closes the Valkey connection
func (rl *RateLimiter) Close() {
	rl.client.Close()
	rl.logger.Info("Rate limiter connection closed")
}

// Health checks the health of the rate limiter
func (rl *RateLimiter) Health(ctx context.Context) error {
	return rl.client.Do(ctx, rl.client.B().Ping().Build()).Error()
}

// BurstAllowance represents burst allowance configuration
type BurstAllowance struct {
	Limit       int64
	Window      time.Duration
	BurstLimit  int64
	BurstWindow time.Duration
}

// AllowWithBurst checks rate limit with burst allowance
func (rl *RateLimiter) AllowWithBurst(ctx context.Context, key string, burst *BurstAllowance) (bool, error) {
	// Check normal rate limit first
	allowed, err := rl.AllowN(ctx, key, 1, burst.Limit, burst.Window)
	if err != nil {
		return false, err
	}

	if allowed {
		return true, nil
	}

	// Check burst allowance
	burstKey := key + ":burst"
	return rl.AllowN(ctx, burstKey, 1, burst.BurstLimit, burst.BurstWindow)
}

// NOTE: Rate limits are now configured via config.yaml
// See config.RateLimit.SocialLimits for operation-specific rate limits
// All hardcoded rate limit maps have been removed in favor of configuration-driven approach

// UserReputationLevel represents user reputation levels
type UserReputationLevel string

const (
	ReputationNew        UserReputationLevel = "new"
	ReputationTrusted    UserReputationLevel = "trusted"
	ReputationVeteran    UserReputationLevel = "veteran"
	ReputationRestricted UserReputationLevel = "restricted"
)

// getSocialRateLimit returns the rate limit configuration for a social operation
func (rl *RateLimiter) getSocialRateLimit(operation string) (BurstAllowance, bool) {
	if limit, exists := rl.config.RateLimit.SocialLimits[operation]; exists {
		return BurstAllowance{
			Limit:       limit.Limit,
			Window:      limit.Window,
			BurstLimit:  limit.BurstLimit,
			BurstWindow: limit.BurstWindow,
		}, true
	}
	return BurstAllowance{}, false
}

// getReputationMultiplier returns the rate limit multiplier for a user reputation level
func (rl *RateLimiter) getReputationMultiplier(reputation UserReputationLevel) float64 {
	if multiplier, exists := rl.config.RateLimit.ReputationMultipliers[string(reputation)]; exists {
		return multiplier
	}

	// Fallback defaults if not configured
	switch reputation {
	case ReputationNew:
		return 0.5
	case ReputationTrusted:
		return 1.0
	case ReputationVeteran:
		return 1.5
	case ReputationRestricted:
		return 0.1
	default:
		return 1.0
	}
}

// getBubbleRateLimit returns the rate limit configuration for a bubble operation
func (rl *RateLimiter) getBubbleRateLimit(operation string) (BurstAllowance, bool) {
	// For now, bubble limits can be part of social limits or we can add a separate config section
	// Check if it exists in social limits first
	if limit, exists := rl.config.RateLimit.SocialLimits[operation]; exists {
		return BurstAllowance{
			Limit:       limit.Limit,
			Window:      limit.Window,
			BurstLimit:  limit.BurstLimit,
			BurstWindow: limit.BurstWindow,
		}, true
	}
	return BurstAllowance{}, false
}

// AllowSocialOperation checks rate limit for social operations with burst
func (rl *RateLimiter) AllowSocialOperation(ctx context.Context, userID string, operation string) (bool, error) {
	key := fmt.Sprintf("rate_limit:social:%s:%s", userID, operation)

	burstConfig, exists := rl.getSocialRateLimit(operation)
	if !exists {
		// Default rate limit
		return rl.AllowN(ctx, key, 1, 100, time.Minute)
	}

	return rl.AllowWithBurst(ctx, key, &burstConfig)
}

// AllowSocialOperationWithReputation checks rate limit with user reputation consideration
func (rl *RateLimiter) AllowSocialOperationWithReputation(ctx context.Context, userID string, operation string, reputation UserReputationLevel) (bool, error) {
	key := fmt.Sprintf("rate_limit:social:%s:%s", userID, operation)

	burstConfig, exists := rl.getSocialRateLimit(operation)
	if !exists {
		// Default rate limit
		return rl.AllowN(ctx, key, 1, 100, time.Minute)
	}

	// Apply reputation multiplier
	multiplier := rl.getReputationMultiplier(reputation)
	adjustedConfig := BurstAllowance{
		Limit:       int64(float64(burstConfig.Limit) * multiplier),
		Window:      burstConfig.Window,
		BurstLimit:  int64(float64(burstConfig.BurstLimit) * multiplier),
		BurstWindow: burstConfig.BurstWindow,
	}

	// Ensure minimum limits
	if adjustedConfig.Limit < 1 {
		adjustedConfig.Limit = 1
	}
	if adjustedConfig.BurstLimit < 1 {
		adjustedConfig.BurstLimit = 1
	}

	return rl.AllowWithBurst(ctx, key, &adjustedConfig)
}

// AllowBubbleOperation checks rate limit for bubble-specific operations
func (rl *RateLimiter) AllowBubbleOperation(ctx context.Context, userID, bubbleID, operation string) (bool, error) {
	key := fmt.Sprintf("rate_limit:bubble:%s:%s:%s", userID, bubbleID, operation)

	burstConfig, exists := rl.getBubbleRateLimit(operation)
	if !exists {
		// Default bubble rate limit
		burstConfig = BurstAllowance{
			Limit:       50,
			Window:      time.Hour,
			BurstLimit:  10,
			BurstWindow: 5 * time.Minute,
		}
	}

	return rl.AllowWithBurst(ctx, key, &burstConfig)
}

// AllowGlobalBubbleOperation checks global rate limit for bubble operations across all bubbles
func (rl *RateLimiter) AllowGlobalBubbleOperation(ctx context.Context, userID, operation string) (bool, error) {
	key := fmt.Sprintf("rate_limit:global_bubble:%s:%s", userID, operation)

	// Global limits are stricter than per-bubble limits
	globalLimits := map[string]BurstAllowance{
		"bubble_message": {
			Limit:       2000,
			Window:      time.Hour,
			BurstLimit:  200,
			BurstWindow: time.Minute,
		},
		"bubble_member_action": {
			Limit:       50,
			Window:      time.Hour,
			BurstLimit:  10,
			BurstWindow: 5 * time.Minute,
		},
	}

	burstConfig, exists := globalLimits[operation]
	if !exists {
		// Default global bubble rate limit
		burstConfig = BurstAllowance{
			Limit:       200,
			Window:      time.Hour,
			BurstLimit:  40,
			BurstWindow: 5 * time.Minute,
		}
	}

	return rl.AllowWithBurst(ctx, key, &burstConfig)
}

// AdaptiveRateLimit implements adaptive rate limiting based on system load
type AdaptiveRateLimit struct {
	BaseLimit        int64
	MaxLimit         int64
	MinLimit         int64
	LoadThreshold    float64
	CurrentLoad      float64
	AdjustmentFactor float64
}

// AllowAdaptive checks rate limit with adaptive adjustment based on system load
func (rl *RateLimiter) AllowAdaptive(ctx context.Context, key string, adaptive *AdaptiveRateLimit) (bool, error) {
	// Calculate adjusted limit based on current load
	var adjustedLimit int64
	if adaptive.CurrentLoad > adaptive.LoadThreshold {
		// Reduce limit when system is under high load
		reduction := int64(float64(adaptive.BaseLimit) * adaptive.AdjustmentFactor * (adaptive.CurrentLoad - adaptive.LoadThreshold))
		adjustedLimit = adaptive.BaseLimit - reduction
	} else {
		// Increase limit when system load is low
		increase := int64(float64(adaptive.BaseLimit) * adaptive.AdjustmentFactor * (adaptive.LoadThreshold - adaptive.CurrentLoad))
		adjustedLimit = adaptive.BaseLimit + increase
	}

	// Apply bounds
	if adjustedLimit > adaptive.MaxLimit {
		adjustedLimit = adaptive.MaxLimit
	}
	if adjustedLimit < adaptive.MinLimit {
		adjustedLimit = adaptive.MinLimit
	}

	return rl.AllowN(ctx, key, 1, adjustedLimit, time.Minute)
}

// GetUserReputationLevel determines user reputation level based on account age and activity
func (rl *RateLimiter) GetUserReputationLevel(ctx context.Context, userID string, accountAge time.Duration, activityScore int) UserReputationLevel {
	// Check if user is restricted
	restrictedKey := fmt.Sprintf("user:restricted:%s", userID)
	restricted, err := rl.client.Do(ctx, rl.client.B().Exists().Key(restrictedKey).Build()).AsBool()
	if err == nil && restricted {
		return ReputationRestricted
	}

	// Determine reputation based on account age and activity
	if accountAge < 7*24*time.Hour {
		return ReputationNew
	} else if accountAge > 90*24*time.Hour && activityScore > 100 {
		return ReputationVeteran
	} else {
		return ReputationTrusted
	}
}

// RestrictUser temporarily restricts a user's rate limits
func (rl *RateLimiter) RestrictUser(ctx context.Context, userID string, duration time.Duration) error {
	key := fmt.Sprintf("user:restricted:%s", userID)
	cmd := rl.client.B().Setex().Key(key).Seconds(int64(duration.Seconds())).Value("1").Build()
	return rl.client.Do(ctx, cmd).Error()
}

// GetSocialOperationRemaining returns remaining requests for social operations
func (rl *RateLimiter) GetSocialOperationRemaining(ctx context.Context, userID string, operation string) (int64, error) {
	key := fmt.Sprintf("rate_limit:social:%s:%s", userID, operation)

	burstConfig, exists := rl.getSocialRateLimit(operation)
	if !exists {
		return rl.GetRemainingRequests(ctx, key, 100, time.Minute)
	}

	return rl.GetRemainingRequests(ctx, key, burstConfig.Limit, burstConfig.Window)
}

// GetRateLimitInfo returns detailed rate limit information for a user
func (rl *RateLimiter) GetRateLimitInfo(ctx context.Context, userID string, operation string) (*RateLimitInfo, error) {
	key := fmt.Sprintf("rate_limit:social:%s:%s", userID, operation)

	burstConfig, exists := rl.getSocialRateLimit(operation)
	if !exists {
		burstConfig = BurstAllowance{
			Limit:       100,
			Window:      time.Minute,
			BurstLimit:  20,
			BurstWindow: 10 * time.Second,
		}
	}

	remaining, err := rl.GetRemainingRequests(ctx, key, burstConfig.Limit, burstConfig.Window)
	if err != nil {
		return nil, err
	}

	burstRemaining, err := rl.GetRemainingRequests(ctx, key+":burst", burstConfig.BurstLimit, burstConfig.BurstWindow)
	if err != nil {
		return nil, err
	}

	return &RateLimitInfo{
		Operation:      operation,
		Limit:          burstConfig.Limit,
		Remaining:      remaining,
		Window:         burstConfig.Window,
		BurstLimit:     burstConfig.BurstLimit,
		BurstRemaining: burstRemaining,
		BurstWindow:    burstConfig.BurstWindow,
		ResetTime:      time.Now().Add(burstConfig.Window),
		BurstResetTime: time.Now().Add(burstConfig.BurstWindow),
	}, nil
}

// RateLimitInfo contains detailed rate limit information
type RateLimitInfo struct {
	Operation      string        `json:"operation"`
	Limit          int64         `json:"limit"`
	Remaining      int64         `json:"remaining"`
	Window         time.Duration `json:"window"`
	BurstLimit     int64         `json:"burst_limit"`
	BurstRemaining int64         `json:"burst_remaining"`
	BurstWindow    time.Duration `json:"burst_window"`
	ResetTime      time.Time     `json:"reset_time"`
	BurstResetTime time.Time     `json:"burst_reset_time"`
}

// Request Size Limiting

// RequestSizeConfig holds configuration for request size limits
type RequestSizeConfig struct {
	MaxRequestSize     int64            `json:"max_request_size"`      // Default max request size in bytes
	MaxResponseSize    int64            `json:"max_response_size"`     // Default max response size in bytes
	MethodSizeLimits   map[string]int64 `json:"method_size_limits"`    // Per-method size limits
	FileUploadMaxSize  int64            `json:"file_upload_max_size"`  // Max file upload size
	MediaUploadMaxSize int64            `json:"media_upload_max_size"` // Max media upload size
}

// DefaultRequestSizeConfig returns default request size configuration
func DefaultRequestSizeConfig() RequestSizeConfig {
	return RequestSizeConfig{
		MaxRequestSize:     10 * 1024 * 1024,  // 10MB default
		MaxResponseSize:    50 * 1024 * 1024,  // 50MB default
		FileUploadMaxSize:  100 * 1024 * 1024, // 100MB for file uploads
		MediaUploadMaxSize: 500 * 1024 * 1024, // 500MB for media uploads
		MethodSizeLimits: map[string]int64{
			"/hopen.media.v1.MediaService/UploadFile":           100 * 1024 * 1024, // 100MB
			"/hopen.media.v1.MediaService/UploadProfilePicture": 10 * 1024 * 1024,  // 10MB
			"/hopen.media.v1.MediaService/GenerateThumbnail":    50 * 1024 * 1024,  // 50MB
			"/hopen.bubble.v1.BubbleService/CreateBubble":       1 * 1024 * 1024,   // 1MB
			"/hopen.user.v1.UserService/UpdateProfile":          1 * 1024 * 1024,   // 1MB
		},
	}
}

// ValidateRequestSize validates request size against limits
func (rl *RateLimiter) ValidateRequestSize(method string, size int64, config RequestSizeConfig) error {
	// Check method-specific limits first
	if methodLimit, exists := config.MethodSizeLimits[method]; exists {
		if size > methodLimit {
			return status.Errorf(codes.InvalidArgument,
				"request size %d bytes exceeds method limit %d bytes for %s",
				size, methodLimit, method)
		}
		return nil
	}

	// Check default limit
	if size > config.MaxRequestSize {
		return status.Errorf(codes.InvalidArgument,
			"request size %d bytes exceeds maximum allowed %d bytes",
			size, config.MaxRequestSize)
	}

	return nil
}

// gRPC Interceptors

// UnaryServerInterceptor returns a gRPC unary server interceptor for rate limiting and size validation
func (rl *RateLimiter) UnaryServerInterceptor(sizeConfig RequestSizeConfig) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Extract client IP
		clientIP := rl.extractClientIP(ctx)

		// Extract user ID if available
		userID := rl.extractUserID(ctx)

		// Validate request size (approximate)
		if err := rl.validateRequestSizeFromContext(ctx, info.FullMethod, sizeConfig); err != nil {
			rl.logger.Warn("Request size validation failed",
				zap.String("method", info.FullMethod),
				zap.String("client_ip", clientIP),
				zap.String("user_id", userID),
				zap.Error(err))
			return nil, err
		}

		// Check IP-based rate limit
		if allowed, err := rl.AllowForIP(ctx, clientIP); err != nil {
			rl.logger.Error("Rate limit check failed", zap.Error(err))
			return nil, status.Errorf(codes.Internal, "rate limit check failed")
		} else if !allowed {
			rl.logger.Warn("IP rate limit exceeded",
				zap.String("client_ip", clientIP),
				zap.String("method", info.FullMethod))
			return nil, status.Errorf(codes.ResourceExhausted, "rate limit exceeded for IP")
		}

		// Check user-based rate limit if user is authenticated
		if userID != "" {
			operation := rl.extractOperationFromMethod(info.FullMethod)
			if operation != "" {
				if allowed, err := rl.AllowForUser(ctx, userID, operation); err != nil {
					rl.logger.Error("User rate limit check failed", zap.Error(err))
				} else if !allowed {
					rl.logger.Warn("User rate limit exceeded",
						zap.String("user_id", userID),
						zap.String("operation", operation),
						zap.String("method", info.FullMethod))
					return nil, status.Errorf(codes.ResourceExhausted, "rate limit exceeded for user")
				}
			}
		}

		// Call the handler
		resp, err := handler(ctx, req)

		// Log successful requests for monitoring
		if err == nil {
			rl.logger.Debug("Request processed successfully",
				zap.String("method", info.FullMethod),
				zap.String("client_ip", clientIP),
				zap.String("user_id", userID))
		}

		return resp, err
	}
}

// extractClientIP extracts client IP from gRPC context
func (rl *RateLimiter) extractClientIP(ctx context.Context) string {
	// Try to get IP from peer info
	if p, ok := peer.FromContext(ctx); ok {
		return p.Addr.String()
	}

	// Try to get IP from metadata (forwarded headers)
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		// Check X-Forwarded-For header
		if xff := md.Get("x-forwarded-for"); len(xff) > 0 {
			// Take the first IP in the chain
			ips := strings.Split(xff[0], ",")
			if len(ips) > 0 {
				return strings.TrimSpace(ips[0])
			}
		}

		// Check X-Real-IP header
		if xri := md.Get("x-real-ip"); len(xri) > 0 {
			return xri[0]
		}
	}

	return "unknown"
}

// extractUserID extracts user ID from gRPC context
func (rl *RateLimiter) extractUserID(ctx context.Context) string {
	// This would typically come from authentication middleware
	// For now, we'll check metadata
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if userIDs := md.Get("user-id"); len(userIDs) > 0 {
			return userIDs[0]
		}
		if userIDs := md.Get("x-user-id"); len(userIDs) > 0 {
			return userIDs[0]
		}
	}
	return ""
}

// extractOperationFromMethod extracts operation name from gRPC method
func (rl *RateLimiter) extractOperationFromMethod(method string) string {
	// Map gRPC methods to rate limit operations
	methodToOperation := map[string]string{
		"/hopen.contact.v1.ContactService/SendContactRequest":      "contact_request",
		"/hopen.contact.v1.ContactService/AcceptContactRequest":    "contact_accept",
		"/hopen.friendship.v1.FriendshipService/SendFriendRequest": "friend_request",
		"/hopen.bubble.v1.BubbleService/CreateBubble":              "bubble_create",
		"/hopen.bubble.v1.BubbleService/JoinBubble":                "bubble_join",
		"/hopen.media.v1.MediaService/UploadFile":                  "file_upload",
		"/hopen.media.v1.MediaService/UploadProfilePicture":        "profile_upload",
	}

	if operation, exists := methodToOperation[method]; exists {
		return operation
	}

	// Extract operation from method name as fallback
	parts := strings.Split(method, "/")
	if len(parts) >= 2 {
		methodName := parts[len(parts)-1]
		return strings.ToLower(methodName)
	}

	return ""
}

// validateRequestSizeFromContext validates request size from context metadata
func (rl *RateLimiter) validateRequestSizeFromContext(ctx context.Context, method string, config RequestSizeConfig) error {
	// Try to get content-length from metadata
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if contentLengths := md.Get("content-length"); len(contentLengths) > 0 {
			if size, err := strconv.ParseInt(contentLengths[0], 10, 64); err == nil {
				return rl.ValidateRequestSize(method, size, config)
			}
		}

		// Check grpc-message-length if available
		if messageLengths := md.Get("grpc-message-length"); len(messageLengths) > 0 {
			if size, err := strconv.ParseInt(messageLengths[0], 10, 64); err == nil {
				return rl.ValidateRequestSize(method, size, config)
			}
		}
	}

	// If we can't determine size from metadata, allow the request
	// Size validation would happen at the application level
	return nil
}
