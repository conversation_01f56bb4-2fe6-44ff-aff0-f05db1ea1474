package middleware

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/microcosm-cc/bluemonday"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// SecurityMiddleware provides enterprise-grade security middleware
type SecurityMiddleware struct {
	config    *SecurityConfig
	metrics   *SecurityMetrics
	sanitizer *HTMLSanitizer
}

// HTMLSanitizer provides HTML sanitization utilities using bluemonday
type HTMLSanitizer struct {
	strictPolicy   *bluemonday.Policy
	relaxedPolicy  *bluemonday.Policy
	textOnlyPolicy *bluemonday.Policy
}

// NewHTMLSanitizer creates a new HTML sanitizer with pre-configured policies
func NewHTMLSanitizer() *HTMLSanitizer {
	return &HTMLSanitizer{
		strictPolicy:   createStrictPolicy(),
		relaxedPolicy:  createRelaxedPolicy(),
		textOnlyPolicy: createTextOnlyPolicy(),
	}
}

// SanitizeStrict removes all HTML tags and attributes, leaving only plain text
func (s *HTMLSanitizer) SanitizeStrict(input string) string {
	return s.strictPolicy.Sanitize(input)
}

// SanitizeRelaxed allows safe HTML tags and attributes
func (s *HTMLSanitizer) SanitizeRelaxed(input string) string {
	return s.relaxedPolicy.Sanitize(input)
}

// SanitizeTextOnly converts HTML to plain text, preserving line breaks
func (s *HTMLSanitizer) SanitizeTextOnly(input string) string {
	return s.textOnlyPolicy.Sanitize(input)
}

// createStrictPolicy creates a policy that removes all HTML
func createStrictPolicy() *bluemonday.Policy {
	return bluemonday.StrictPolicy()
}

// createRelaxedPolicy creates a policy that allows safe HTML elements
func createRelaxedPolicy() *bluemonday.Policy {
	p := bluemonday.UGCPolicy()

	// Allow additional safe elements for chat/messaging
	p.AllowElements("br", "p", "div", "span")
	p.AllowElements("strong", "b", "em", "i", "u", "s")
	p.AllowElements("ul", "ol", "li")
	p.AllowElements("blockquote", "code", "pre")

	// Allow safe attributes
	p.AllowAttrs("class", "id", "style").OnElements("span", "div", "p")
	p.AllowAttrs("class", "id").OnElements("ul", "ol", "li")
	p.AllowAttrs("class", "id").OnElements("strong", "b", "em", "i", "u", "s")
	p.AllowAttrs("class", "id").OnElements("blockquote", "code", "pre")

	// Allow safe CSS properties
	p.AllowStyles("color", "background-color", "font-weight", "font-style", "text-decoration")
	p.AllowStyles("text-align", "margin", "padding", "border")

	return p
}

// createTextOnlyPolicy creates a policy that converts HTML to plain text
func createTextOnlyPolicy() *bluemonday.Policy {
	p := bluemonday.NewPolicy()

	// For text-only conversion, we use a very restrictive policy
	// that only allows basic text formatting elements
	p.AllowElements("br", "p", "div")
	p.AllowElements("strong", "b", "em", "i", "u", "s")
	p.AllowElements("ul", "ol", "li")
	p.AllowElements("blockquote", "code", "pre")

	// Remove all attributes to ensure clean text output
	p.AllowAttrs().Globally()

	return p
}

// SecurityConfig holds security configuration
type SecurityConfig struct {
	CSPPolicy      string   `yaml:"csp_policy"`
	HSTSMaxAge     int      `yaml:"hsts_max_age"`
	MaxRequestSize int64    `yaml:"max_request_size"`
	TrustedOrigins []string `yaml:"trusted_origins"`
	TrustedProxies []string `yaml:"trusted_proxies"`
	// HTML Sanitization settings
	EnableHTMLSanitization  bool   `yaml:"enable_html_sanitization"`
	DefaultSanitizationMode string `yaml:"default_sanitization_mode"` // "strict", "relaxed", "text_only"
}

// SecurityMetrics holds security-related metrics
type SecurityMetrics struct {
	blockedRequests      *prometheus.CounterVec
	securityViolations   *prometheus.CounterVec
	processingDuration   prometheus.Histogram
	requestSizeHistogram prometheus.Histogram
	htmlSanitized        *prometheus.CounterVec
}

// NewSecurityMiddleware creates a new security middleware instance
func NewSecurityMiddleware(config *SecurityConfig) *SecurityMiddleware {
	metrics := &SecurityMetrics{
		blockedRequests: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_security_blocked_requests_total",
			Help: "Total number of blocked requests by security middleware",
		}, []string{"reason"}),
		securityViolations: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_security_violations_total",
			Help: "Total number of security violations detected",
		}, []string{"type"}),
		processingDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "hopen_security_processing_duration_seconds",
			Help:    "Time spent processing security checks",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0},
		}),
		requestSizeHistogram: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "hopen_request_size_bytes",
			Help:    "Size of incoming requests in bytes",
			Buckets: []float64{1024, 10240, 102400, 1048576, 10485760, 52428800},
		}),
		htmlSanitized: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_html_sanitized_total",
			Help: "Total number of HTML sanitization operations",
		}, []string{"mode"}),
	}

	return &SecurityMiddleware{
		config:    config,
		metrics:   metrics,
		sanitizer: NewHTMLSanitizer(),
	}
}

// SecurityHandler returns the main security middleware handler
func (sm *SecurityMiddleware) SecurityHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Debug: Log that the security middleware is being called
		fmt.Printf("DEBUG: Security middleware called for %s %s\n", c.Request.Method, c.Request.URL.Path)

		// 1. Request size validation
		if err := sm.validateRequestSize(c.Request); err != nil {
			sm.metrics.blockedRequests.WithLabelValues("size_limit").Inc()
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "Request too large"})
			c.Abort()
			return
		}

		// 2. Content type validation
		if err := sm.validateContentType(c.Request); err != nil {
			sm.metrics.blockedRequests.WithLabelValues("invalid_content").Inc()
			c.JSON(http.StatusUnsupportedMediaType, gin.H{"error": "Invalid content type"})
			c.Abort()
			return
		}

		// 3. Set security headers
		sm.setSecurityHeaders(c.Writer)

		// 4. Record metrics
		sm.metrics.processingDuration.Observe(time.Since(start).Seconds())

		c.Next()
	}
}

// HTMLSanitizationHandler returns a middleware that sanitizes HTML content in request bodies
func (sm *SecurityMiddleware) HTMLSanitizationHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !sm.config.EnableHTMLSanitization {
			c.Next()
			return
		}

		// Only process JSON requests
		if !strings.Contains(c.GetHeader("Content-Type"), "application/json") {
			c.Next()
			return
		}

		// Store original body for potential restoration
		originalBody := c.Request.Body
		defer originalBody.Close()

		// Read the request body
		bodyBytes, err := c.GetRawData()
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
			c.Abort()
			return
		}

		// Sanitize the JSON body
		sanitizedBody, sanitizationMode := sm.sanitizeJSONBody(bodyBytes)

		// Record sanitization metrics
		sm.metrics.htmlSanitized.WithLabelValues(sanitizationMode).Inc()

		// Set the sanitized body back to the context
		c.Request.Body = io.NopCloser(strings.NewReader(string(sanitizedBody)))
		c.Request.ContentLength = int64(len(sanitizedBody))

		c.Next()
	}
}

// sanitizeJSONBody sanitizes HTML content within JSON request bodies
func (sm *SecurityMiddleware) sanitizeJSONBody(bodyBytes []byte) ([]byte, string) {
	// Use the default sanitization mode from config
	mode := sm.config.DefaultSanitizationMode
	if mode == "" {
		mode = "relaxed" // Default to relaxed if not specified
	}

	// Parse JSON into a generic map
	var jsonData map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &jsonData); err != nil {
		// If JSON parsing fails, return original body
		return bodyBytes, mode
	}

	// Sanitize the JSON data recursively
	sanitizedData := sm.sanitizeJSONRecursive(jsonData, mode)

	// Marshal back to JSON
	sanitizedBytes, err := json.Marshal(sanitizedData)
	if err != nil {
		// If marshaling fails, return original body
		return bodyBytes, mode
	}

	return sanitizedBytes, mode
}

// sanitizeJSONRecursive recursively sanitizes JSON data
func (sm *SecurityMiddleware) sanitizeJSONRecursive(data interface{}, mode string) interface{} {
	switch v := data.(type) {
	case string:
		// Sanitize string values
		switch mode {
		case "strict":
			return sm.sanitizer.SanitizeStrict(v)
		case "text_only":
			return sm.sanitizer.SanitizeTextOnly(v)
		default: // "relaxed"
			return sm.sanitizer.SanitizeRelaxed(v)
		}
	case map[string]interface{}:
		// Recursively sanitize map values
		result := make(map[string]interface{})
		for key, value := range v {
			result[key] = sm.sanitizeJSONRecursive(value, mode)
		}
		return result
	case []interface{}:
		// Recursively sanitize slice values
		result := make([]interface{}, len(v))
		for i, value := range v {
			result[i] = sm.sanitizeJSONRecursive(value, mode)
		}
		return result
	default:
		// Return other types as-is (numbers, booleans, null)
		return v
	}
}

// SanitizeString provides a convenient way to sanitize strings with the configured mode
func (sm *SecurityMiddleware) SanitizeString(input string) string {
	if !sm.config.EnableHTMLSanitization {
		return input
	}

	mode := sm.config.DefaultSanitizationMode
	if mode == "" {
		mode = "relaxed"
	}

	switch mode {
	case "strict":
		sm.metrics.htmlSanitized.WithLabelValues("strict").Inc()
		return sm.sanitizer.SanitizeStrict(input)
	case "text_only":
		sm.metrics.htmlSanitized.WithLabelValues("text_only").Inc()
		return sm.sanitizer.SanitizeTextOnly(input)
	default: // "relaxed"
		sm.metrics.htmlSanitized.WithLabelValues("relaxed").Inc()
		return sm.sanitizer.SanitizeRelaxed(input)
	}
}

// SanitizeStrict provides strict HTML sanitization
func (sm *SecurityMiddleware) SanitizeStrict(input string) string {
	sm.metrics.htmlSanitized.WithLabelValues("strict").Inc()
	return sm.sanitizer.SanitizeStrict(input)
}

// SanitizeRelaxed provides relaxed HTML sanitization
func (sm *SecurityMiddleware) SanitizeRelaxed(input string) string {
	sm.metrics.htmlSanitized.WithLabelValues("relaxed").Inc()
	return sm.sanitizer.SanitizeRelaxed(input)
}

// SanitizeTextOnly provides text-only HTML sanitization
func (sm *SecurityMiddleware) SanitizeTextOnly(input string) string {
	sm.metrics.htmlSanitized.WithLabelValues("text_only").Inc()
	return sm.sanitizer.SanitizeTextOnly(input)
}

// validateRequestSize validates the request size
func (sm *SecurityMiddleware) validateRequestSize(r *http.Request) error {
	if r.ContentLength > sm.config.MaxRequestSize {
		return fmt.Errorf("request size %d exceeds maximum %d", r.ContentLength, sm.config.MaxRequestSize)
	}

	if r.ContentLength > 0 {
		sm.metrics.requestSizeHistogram.Observe(float64(r.ContentLength))
	}
	return nil
}

// validateContentType validates the content type
func (sm *SecurityMiddleware) validateContentType(r *http.Request) error {
	contentType := r.Header.Get("Content-Type")
	if contentType == "" {
		return nil
	}

	allowedTypes := []string{"application/json", "application/x-www-form-urlencoded", "multipart/form-data", "text/plain", "image/jpeg", "image/png", "image/gif", "video/mp4", "audio/mp3", "audio/wav"}
	for _, allowed := range allowedTypes {
		if strings.HasPrefix(contentType, allowed) {
			return nil
		}
	}
	return fmt.Errorf("content type %s not allowed", contentType)
}

// setSecurityHeaders sets security headers for production
func (sm *SecurityMiddleware) setSecurityHeaders(w http.ResponseWriter) {
	w.Header().Set("Content-Security-Policy", sm.config.CSPPolicy)
	w.Header().Set("Strict-Transport-Security", fmt.Sprintf("max-age=%d; includeSubDomains", sm.config.HSTSMaxAge))
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.Header().Set("X-Frame-Options", "DENY")
	w.Header().Set("X-XSS-Protection", "1; mode=block")
	w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
	w.Header().Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
	w.Header().Set("Server", "")
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("Expires", "0")

	// Advertise HTTP/3 support via Alt-Svc header
	// This tells clients that HTTP/3 is available on the same host and port
	// Use the external port (8443) that clients connect to, not the internal port (4000)
	altSvcValue := "h3=\":8443\"; ma=86400"
	w.Header().Set("Alt-Svc", altSvcValue)

	// Debug: Log that we're setting the Alt-Svc header
	fmt.Printf("DEBUG: Security middleware setting Alt-Svc header: %s\n", altSvcValue)
}
