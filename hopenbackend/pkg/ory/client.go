package ory

import (
	"context"
	"fmt"
	"net/http"

	ory "github.com/ory/client-go"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// Client wraps Ory services (Kratos, Hydra)
type Client struct {
	logger *zap.Logger
	config *config.Config

	// Ory service clients
	Kratos      *ory.APIClient // Public API
	KratosAdmin *ory.APIClient // Admin API
	Hydra       *ory.APIClient
}

// Dependencies holds the dependencies for the Ory client
type Dependencies struct {
	Logger *zap.Logger
	Config *config.Config
}

// New creates a new Ory client instance
func New(deps *Dependencies) *Client {
	client := &Client{
		logger: deps.Logger,
		config: deps.Config,
	}

	// Initialize Ory service clients
	client.initializeClients()

	return client
}

// initializeClients initializes all Ory service clients
func (c *Client) initializeClients() {
	// Initialize Kratos public client (identity management)
	if c.config.Ory.KratosPublicURL != "" {
		kratosConfig := ory.NewConfiguration()
		kratosConfig.Servers = []ory.ServerConfiguration{
			{
				URL: c.config.Ory.KratosPublicURL,
			},
		}
		c.Kratos = ory.NewAPIClient(kratosConfig)
		c.logger.Info("Ory Kratos public client initialized", zap.String("url", c.config.Ory.KratosPublicURL))
	}

	// Initialize Kratos admin client (administrative operations)
	if c.config.Ory.KratosAdminURL != "" {
		kratosAdminConfig := ory.NewConfiguration()
		kratosAdminConfig.Servers = []ory.ServerConfiguration{
			{
				URL: c.config.Ory.KratosAdminURL,
			},
		}
		c.KratosAdmin = ory.NewAPIClient(kratosAdminConfig)
		c.logger.Info("Ory Kratos admin client initialized", zap.String("url", c.config.Ory.KratosAdminURL))
	}

	// Initialize Hydra client (OAuth2/OIDC)
	if c.config.Ory.HydraAdminURL != "" {
		hydraConfig := ory.NewConfiguration()
		hydraConfig.Servers = []ory.ServerConfiguration{
			{
				URL: c.config.Ory.HydraAdminURL,
			},
		}
		c.Hydra = ory.NewAPIClient(hydraConfig)
		c.logger.Info("Ory Hydra client initialized", zap.String("url", c.config.Ory.HydraAdminURL))
	}

	// Oathkeeper/Keto support removed – handled by Linkerd + in-process checks
}

// ValidateSession validates a session using Ory Kratos
func (c *Client) ValidateSession(ctx context.Context, sessionToken string) (*ory.Session, error) {
	if c.Kratos == nil {
		return nil, fmt.Errorf("Kratos client not initialized")
	}

	// Create request with session token using X-Session-Token header (Bearer token method)
	req := c.Kratos.FrontendAPI.ToSession(ctx)
	req = req.XSessionToken(sessionToken)

	session, resp, err := req.Execute()
	if err != nil {
		if resp != nil && resp.StatusCode == http.StatusUnauthorized {
			return nil, fmt.Errorf("invalid session")
		}
		return nil, fmt.Errorf("failed to validate session: %w", err)
	}

	return session, nil
}

// InvalidateSession invalidates a session using Ory Kratos
func (c *Client) InvalidateSession(ctx context.Context, sessionToken string) error {
	if c.Kratos == nil {
		return fmt.Errorf("Kratos client not initialized")
	}

	// Use the session token to perform logout
	_, err := c.Kratos.FrontendAPI.PerformNativeLogout(ctx).PerformNativeLogoutBody(
		ory.PerformNativeLogoutBody{
			SessionToken: sessionToken,
		},
	).Execute()

	if err != nil {
		return fmt.Errorf("failed to invalidate session: %w", err)
	}

	return nil
}

// CreateIdentity creates a new identity in Ory Kratos
func (c *Client) CreateIdentity(ctx context.Context, email, password string, traits map[string]interface{}) (*ory.Identity, error) {
	if c.KratosAdmin == nil {
		return nil, fmt.Errorf("Kratos admin client not initialized")
	}

	// Prepare identity creation request
	createReq := ory.CreateIdentityBody{
		SchemaId: "default",
		Traits:   traits,
		Credentials: &ory.IdentityWithCredentials{
			Password: &ory.IdentityWithCredentialsPassword{
				Config: &ory.IdentityWithCredentialsPasswordConfig{
					Password: &password,
				},
			},
		},
	}

	identity, _, err := c.KratosAdmin.IdentityAPI.CreateIdentity(ctx).CreateIdentityBody(createReq).Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to create identity: %w", err)
	}

	return identity, nil
}

// GetIdentity retrieves an identity by ID from Ory Kratos
func (c *Client) GetIdentity(ctx context.Context, identityID string) (*ory.Identity, error) {
	if c.Kratos == nil {
		return nil, fmt.Errorf("Kratos client not initialized")
	}

	identity, _, err := c.Kratos.IdentityAPI.GetIdentity(ctx, identityID).Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to get identity: %w", err)
	}

	return identity, nil
}

// UpdateIdentity updates an identity in Ory Kratos
func (c *Client) UpdateIdentity(ctx context.Context, identityID string, traits map[string]interface{}) (*ory.Identity, error) {
	if c.Kratos == nil {
		return nil, fmt.Errorf("Kratos client not initialized")
	}

	updateReq := ory.UpdateIdentityBody{
		SchemaId: "default",
		Traits:   traits,
	}

	identity, _, err := c.Kratos.IdentityAPI.UpdateIdentity(ctx, identityID).UpdateIdentityBody(updateReq).Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to update identity: %w", err)
	}

	return identity, nil
}

// DeleteIdentity deletes an identity from Ory Kratos
func (c *Client) DeleteIdentity(ctx context.Context, identityID string) error {
	if c.Kratos == nil {
		return fmt.Errorf("Kratos client not initialized")
	}

	_, err := c.Kratos.IdentityAPI.DeleteIdentity(ctx, identityID).Execute()
	if err != nil {
		return fmt.Errorf("failed to delete identity: %w", err)
	}

	return nil
}

// CreateOAuth2Client creates an OAuth2 client in Ory Hydra
func (c *Client) CreateOAuth2Client(ctx context.Context, clientID, clientSecret string, redirectURIs []string) (*ory.OAuth2Client, error) {
	if c.Hydra == nil {
		return nil, fmt.Errorf("Hydra client not initialized")
	}

	createReq := ory.OAuth2Client{
		ClientId:      &clientID,
		ClientSecret:  &clientSecret,
		RedirectUris:  redirectURIs,
		GrantTypes:    []string{"authorization_code", "refresh_token"},
		ResponseTypes: []string{"code"},
		Scope:         ory.PtrString("openid profile email"),
	}

	client, _, err := c.Hydra.OAuth2API.CreateOAuth2Client(ctx).OAuth2Client(createReq).Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to create OAuth2 client: %w", err)
	}

	return client, nil
}

// IntrospectToken introspects an OAuth2 token using Ory Hydra
func (c *Client) IntrospectToken(ctx context.Context, token string) (*ory.IntrospectedOAuth2Token, error) {
	if c.Hydra == nil {
		return nil, fmt.Errorf("Hydra client not initialized")
	}

	result, _, err := c.Hydra.OAuth2API.IntrospectOAuth2Token(ctx).Token(token).Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to introspect token: %w", err)
	}

	return result, nil
}

// RevokeToken revokes an OAuth2 token using Ory Hydra
func (c *Client) RevokeToken(ctx context.Context, token string) error {
	if c.Hydra == nil {
		return fmt.Errorf("Hydra client not initialized")
	}

	_, err := c.Hydra.OAuth2API.RevokeOAuth2Token(ctx).Token(token).Execute()
	if err != nil {
		return fmt.Errorf("failed to revoke token: %w", err)
	}

	return nil
}

// ExtendSession extends a Kratos session by creating a new session for the same user
// This is the proper way to "refresh" Kratos sessions
func (c *Client) ExtendSession(ctx context.Context, sessionToken string) (*ory.Session, error) {
	if c.Kratos == nil {
		return nil, fmt.Errorf("Kratos client not initialized")
	}

	// First, validate the current session to get user information
	currentSession, err := c.ValidateSession(ctx, sessionToken)
	if err != nil {
		return nil, fmt.Errorf("failed to validate current session: %w", err)
	}

	if currentSession == nil || currentSession.Identity == nil {
		return nil, fmt.Errorf("invalid session or missing identity")
	}

	// For Kratos, session extension typically involves creating a new session
	// with the same identity. This would require the user to re-authenticate
	// or use a session extension API if available.

	// For now, we'll return the current session if it's still valid
	// In a production system, you might implement a proper session extension
	// using Kratos admin API or a custom session management system

	return currentSession, nil
}
