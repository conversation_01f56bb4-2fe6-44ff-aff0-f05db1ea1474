package retry

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Manager manages multiple retriers for different services
type Manager struct {
	retriers map[string]*Retrier
	mutex    sync.RWMutex
	logger   *zap.Logger
}

// NewManager creates a new retry manager
func NewManager(logger *zap.Logger) *Manager {
	return &Manager{
		retriers: make(map[string]*Retrier),
		logger:   logger,
	}
}

// GetOrCreate gets an existing retrier or creates a new one
func (m *Manager) GetOrCreate(name string, config Config) *Retrier {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if retrier, exists := m.retriers[name]; exists {
		return retrier
	}

	retrier := New(config, m.logger)
	m.retriers[name] = retrier

	m.logger.Info("Created new retrier",
		zap.String("name", name),
		zap.Int("max_attempts", config.MaxAttempts),
		zap.Duration("initial_delay", config.InitialDelay),
		zap.String("strategy", strategyToString(config.Strategy)))

	return retrier
}

// Get retrieves a retrier by name
func (m *Manager) Get(name string) (*Retrier, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	retrier, exists := m.retriers[name]
	return retrier, exists
}

// Execute executes a function with retry logic for a named service
func (m *Manager) Execute(ctx context.Context, serviceName string, operation func() error) error {
	retrier, exists := m.Get(serviceName)
	if !exists {
		// Create default retrier if none exists
		config := DefaultConfig()
		retrier = m.GetOrCreate(serviceName, config)
	}

	return retrier.Execute(ctx, operation)
}

// ServiceRetriers holds retriers for common services
type ServiceRetriers struct {
	Database *Retrier
	MinIO    *Retrier
	LiveKit  *Retrier
	NATS     *Retrier
	MQTT     *Retrier
	Ory      *Retrier
	FCM      *Retrier
	manager  *Manager
}

// NewServiceRetriers creates retriers for common services
func NewServiceRetriers(logger *zap.Logger) *ServiceRetriers {
	manager := NewManager(logger)

	// Database retrier - more aggressive for critical service
	dbConfig := DatabaseConfig()
	dbConfig.MaxAttempts = 5
	dbConfig.InitialDelay = 50 * time.Millisecond
	dbConfig.MaxDelay = 5 * time.Second

	// MinIO retrier
	minioConfig := ExternalServiceConfig()
	minioConfig.MaxAttempts = 3
	minioConfig.InitialDelay = 100 * time.Millisecond
	minioConfig.MaxDelay = 10 * time.Second

	// LiveKit retrier
	livekitConfig := ExternalServiceConfig()
	livekitConfig.MaxAttempts = 2 // Less aggressive for real-time service
	livekitConfig.InitialDelay = 200 * time.Millisecond
	livekitConfig.MaxDelay = 5 * time.Second

	// NATS retrier
	natsConfig := Config{
		MaxAttempts:  4,
		InitialDelay: 100 * time.Millisecond,
		MaxDelay:     3 * time.Second,
		Multiplier:   1.5,
		JitterFactor: 0.1,
		Strategy:     StrategyExponentialJitter,
		IsRetryable: func(err error) bool {
			if err == nil {
				return false
			}
			errStr := err.Error()
			return contains(errStr, "connection refused") ||
				contains(errStr, "timeout") ||
				contains(errStr, "nats") ||
				contains(errStr, "publish failed")
		},
	}

	// MQTT retrier
	mqttConfig := Config{
		MaxAttempts:  3,
		InitialDelay: 200 * time.Millisecond,
		MaxDelay:     5 * time.Second,
		Multiplier:   2.0,
		JitterFactor: 0.2,
		Strategy:     StrategyExponentialJitter,
		IsRetryable: func(err error) bool {
			if err == nil {
				return false
			}
			errStr := err.Error()
			return contains(errStr, "connection refused") ||
				contains(errStr, "timeout") ||
				contains(errStr, "mqtt") ||
				contains(errStr, "publish failed")
		},
	}

	// Ory retrier
	oryConfig := ExternalServiceConfig()
	oryConfig.MaxAttempts = 2 // Less aggressive for auth service
	oryConfig.InitialDelay = 300 * time.Millisecond
	oryConfig.MaxDelay = 3 * time.Second

	// FCM retrier
	fcmConfig := Config{
		MaxAttempts:  3,
		InitialDelay: 500 * time.Millisecond,
		MaxDelay:     10 * time.Second,
		Multiplier:   2.0,
		JitterFactor: 0.1,
		Strategy:     StrategyExponentialJitter,
		IsRetryable: func(err error) bool {
			if err == nil {
				return false
			}
			errStr := err.Error()
			return contains(errStr, "timeout") ||
				contains(errStr, "network") ||
				contains(errStr, "unavailable") ||
				contains(errStr, "502") ||
				contains(errStr, "503") ||
				contains(errStr, "504")
		},
	}

	return &ServiceRetriers{
		Database: manager.GetOrCreate("database", dbConfig),
		MinIO:    manager.GetOrCreate("minio", minioConfig),
		LiveKit:  manager.GetOrCreate("livekit", livekitConfig),
		NATS:     manager.GetOrCreate("nats", natsConfig),
		MQTT:     manager.GetOrCreate("mqtt", mqttConfig),
		Ory:      manager.GetOrCreate("ory", oryConfig),
		FCM:      manager.GetOrCreate("fcm", fcmConfig),
		manager:  manager,
	}
}

// GetManager returns the underlying manager
func (sr *ServiceRetriers) GetManager() *Manager {
	return sr.manager
}

// ExecuteWithDatabaseRetry executes operation with database retry logic
func (sr *ServiceRetriers) ExecuteWithDatabaseRetry(ctx context.Context, operation func() error) error {
	return sr.Database.Execute(ctx, operation)
}

// ExecuteWithMinIORetry executes operation with MinIO retry logic
func (sr *ServiceRetriers) ExecuteWithMinIORetry(ctx context.Context, operation func() error) error {
	return sr.MinIO.Execute(ctx, operation)
}

// ExecuteWithLiveKitRetry executes operation with LiveKit retry logic
func (sr *ServiceRetriers) ExecuteWithLiveKitRetry(ctx context.Context, operation func() error) error {
	return sr.LiveKit.Execute(ctx, operation)
}

// ExecuteWithNATSRetry executes operation with NATS retry logic
func (sr *ServiceRetriers) ExecuteWithNATSRetry(ctx context.Context, operation func() error) error {
	return sr.NATS.Execute(ctx, operation)
}

// ExecuteWithMQTTRetry executes operation with MQTT retry logic
func (sr *ServiceRetriers) ExecuteWithMQTTRetry(ctx context.Context, operation func() error) error {
	return sr.MQTT.Execute(ctx, operation)
}

// ExecuteWithOryRetry executes operation with Ory retry logic
func (sr *ServiceRetriers) ExecuteWithOryRetry(ctx context.Context, operation func() error) error {
	return sr.Ory.Execute(ctx, operation)
}

// ExecuteWithFCMRetry executes operation with FCM retry logic
func (sr *ServiceRetriers) ExecuteWithFCMRetry(ctx context.Context, operation func() error) error {
	return sr.FCM.Execute(ctx, operation)
}

// GetRetryMetrics returns retry metrics for all services
func (sr *ServiceRetriers) GetRetryMetrics() map[string]RetryMetrics {
	sr.manager.mutex.RLock()
	defer sr.manager.mutex.RUnlock()

	metrics := make(map[string]RetryMetrics)
	for name, retrier := range sr.manager.retriers {
		// Get actual metrics from the retrier
		totalAttempts, successfulOps, failedOps, averageRetries := retrier.GetMetrics()
		metrics[name] = RetryMetrics{
			ServiceName:    name,
			TotalAttempts:  totalAttempts,
			SuccessfulOps:  successfulOps,
			FailedOps:      failedOps,
			AverageRetries: averageRetries,
		}
	}

	return metrics
}

// RetryMetrics holds retry statistics
type RetryMetrics struct {
	ServiceName    string  `json:"service_name"`
	TotalAttempts  uint64  `json:"total_attempts"`
	SuccessfulOps  uint64  `json:"successful_operations"`
	FailedOps      uint64  `json:"failed_operations"`
	AverageRetries float64 `json:"average_retries"`
}

// strategyToString converts strategy enum to string
func strategyToString(strategy Strategy) string {
	switch strategy {
	case StrategyFixed:
		return "fixed"
	case StrategyLinear:
		return "linear"
	case StrategyExponential:
		return "exponential"
	case StrategyExponentialJitter:
		return "exponential_jitter"
	default:
		return "unknown"
	}
}

// CombinedRetryCircuitBreaker combines retry logic with circuit breaker
type CombinedRetryCircuitBreaker struct {
	retrier *Retrier
	logger  *zap.Logger
}

// NewCombinedRetryCircuitBreaker creates a combined retry and circuit breaker
func NewCombinedRetryCircuitBreaker(retryConfig Config, logger *zap.Logger) *CombinedRetryCircuitBreaker {
	return &CombinedRetryCircuitBreaker{
		retrier: New(retryConfig, logger),
		logger:  logger,
	}
}

// Execute executes operation with both retry and circuit breaker protection
func (crcb *CombinedRetryCircuitBreaker) Execute(ctx context.Context, operation func() error) error {
	return crcb.retrier.Execute(ctx, func() error {
		// The circuit breaker would be called here in integration
		return operation()
	})
}
