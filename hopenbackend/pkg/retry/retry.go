package retry

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// Strategy defines the retry strategy
type Strategy int

// MetricsTracker tracks retry metrics using atomic operations for thread safety
type MetricsTracker struct {
	totalAttempts uint64
	successfulOps uint64
	failedOps     uint64
	totalRetries  uint64
}

// NewMetricsTracker creates a new metrics tracker
func NewMetricsTracker() *MetricsTracker {
	return &MetricsTracker{}
}

// RecordAttempt records a retry attempt
func (m *MetricsTracker) RecordAttempt() {
	atomic.AddUint64(&m.totalAttempts, 1)
}

// RecordSuccess records a successful operation
func (m *MetricsTracker) RecordSuccess(retryCount int) {
	atomic.AddUint64(&m.successfulOps, 1)
	if retryCount > 0 {
		atomic.AddUint64(&m.totalRetries, uint64(retryCount))
	}
}

// RecordFailure records a failed operation
func (m *MetricsTracker) RecordFailure(retryCount int) {
	atomic.AddUint64(&m.failedOps, 1)
	atomic.AddUint64(&m.totalRetries, uint64(retryCount))
}

// GetMetrics returns current metrics
func (m *MetricsTracker) GetMetrics() (uint64, uint64, uint64, float64) {
	totalAttempts := atomic.LoadUint64(&m.totalAttempts)
	successfulOps := atomic.LoadUint64(&m.successfulOps)
	failedOps := atomic.LoadUint64(&m.failedOps)
	totalRetries := atomic.LoadUint64(&m.totalRetries)

	var averageRetries float64
	totalOps := successfulOps + failedOps
	if totalOps > 0 {
		averageRetries = float64(totalRetries) / float64(totalOps)
	}

	return totalAttempts, successfulOps, failedOps, averageRetries
}

const (
	StrategyFixed Strategy = iota
	StrategyLinear
	StrategyExponential
	StrategyExponentialJitter
)

// Config holds retry configuration
type Config struct {
	MaxAttempts     int              // Maximum number of retry attempts
	InitialDelay    time.Duration    // Initial delay before first retry
	MaxDelay        time.Duration    // Maximum delay between retries
	Multiplier      float64          // Multiplier for exponential backoff
	JitterFactor    float64          // Jitter factor (0.0 to 1.0)
	Strategy        Strategy         // Retry strategy
	RetryableErrors []string         // List of retryable error patterns
	IsRetryable     func(error) bool // Custom retry condition
}

// DefaultConfig returns a sensible default configuration
func DefaultConfig() Config {
	return Config{
		MaxAttempts:  3,
		InitialDelay: 100 * time.Millisecond,
		MaxDelay:     30 * time.Second,
		Multiplier:   2.0,
		JitterFactor: 0.1,
		Strategy:     StrategyExponentialJitter,
		RetryableErrors: []string{
			"connection refused",
			"timeout",
			"network",
			"temporary",
			"unavailable",
			"circuit breaker",
		},
		IsRetryable: func(err error) bool {
			if err == nil {
				return false
			}
			errStr := err.Error()
			retryablePatterns := []string{
				"connection refused",
				"timeout",
				"network",
				"temporary",
				"unavailable",
				"circuit breaker",
			}
			for _, pattern := range retryablePatterns {
				if contains(errStr, pattern) {
					return true
				}
			}
			return false
		},
	}
}

// DatabaseConfig returns retry configuration optimized for database operations
func DatabaseConfig() Config {
	config := DefaultConfig()
	config.MaxAttempts = 5
	config.InitialDelay = 50 * time.Millisecond
	config.MaxDelay = 5 * time.Second
	config.RetryableErrors = []string{
		"connection refused",
		"connection reset",
		"timeout",
		"temporary",
		"deadlock",
		"lock timeout",
	}
	return config
}

// ExternalServiceConfig returns retry configuration for external services
func ExternalServiceConfig() Config {
	config := DefaultConfig()
	config.MaxAttempts = 3
	config.InitialDelay = 200 * time.Millisecond
	config.MaxDelay = 10 * time.Second
	config.RetryableErrors = []string{
		"connection refused",
		"timeout",
		"network",
		"unavailable",
		"circuit breaker",
		"502",
		"503",
		"504",
	}
	return config
}

// Retrier handles retry logic
type Retrier struct {
	config  Config
	logger  *zap.Logger
	metrics *MetricsTracker
}

// New creates a new retrier
func New(config Config, logger *zap.Logger) *Retrier {
	return &Retrier{
		config:  config,
		logger:  logger,
		metrics: NewMetricsTracker(),
	}
}

// Execute executes a function with retry logic
func (r *Retrier) Execute(ctx context.Context, operation func() error) error {
	return r.ExecuteWithResult(ctx, func() (interface{}, error) {
		return nil, operation()
	})
}

// ExecuteWithResult executes a function with retry logic and returns a result
func (r *Retrier) ExecuteWithResult(ctx context.Context, operation func() (interface{}, error)) error {
	var lastErr error

	for attempt := 0; attempt < r.config.MaxAttempts; attempt++ {
		// Record attempt
		r.metrics.RecordAttempt()

		// Check if context is cancelled
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Execute the operation
		_, err := operation()
		if err == nil {
			// Success - record metrics
			r.metrics.RecordSuccess(attempt)
			if attempt > 0 {
				r.logger.Info("Operation succeeded after retry",
					zap.Int("attempt", attempt+1),
					zap.Int("max_attempts", r.config.MaxAttempts))
			}
			return nil
		}

		lastErr = err

		// Check if error is retryable
		if !r.isRetryable(err) {
			r.logger.Debug("Error is not retryable, giving up",
				zap.Error(err),
				zap.Int("attempt", attempt+1))
			return err
		}

		// Don't delay after the last attempt
		if attempt == r.config.MaxAttempts-1 {
			r.logger.Error("All retry attempts exhausted",
				zap.Error(err),
				zap.Int("attempts", r.config.MaxAttempts))
			break
		}

		// Calculate delay
		delay := r.calculateDelay(attempt)

		r.logger.Warn("Operation failed, retrying",
			zap.Error(err),
			zap.Int("attempt", attempt+1),
			zap.Int("max_attempts", r.config.MaxAttempts),
			zap.Duration("delay", delay))

		// Wait before retry
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
		}
	}

	// Record final failure
	r.metrics.RecordFailure(r.config.MaxAttempts - 1)
	return fmt.Errorf("operation failed after %d attempts: %w", r.config.MaxAttempts, lastErr)
}

// GetMetrics returns retry metrics for this retrier
func (r *Retrier) GetMetrics() (uint64, uint64, uint64, float64) {
	return r.metrics.GetMetrics()
}

// isRetryable determines if an error is retryable
func (r *Retrier) isRetryable(err error) bool {
	if r.config.IsRetryable != nil {
		return r.config.IsRetryable(err)
	}

	errStr := err.Error()
	for _, pattern := range r.config.RetryableErrors {
		if contains(errStr, pattern) {
			return true
		}
	}

	return false
}

// calculateDelay calculates the delay for the given attempt
func (r *Retrier) calculateDelay(attempt int) time.Duration {
	var delay time.Duration

	switch r.config.Strategy {
	case StrategyFixed:
		delay = r.config.InitialDelay

	case StrategyLinear:
		delay = r.config.InitialDelay * time.Duration(attempt+1)

	case StrategyExponential:
		delay = time.Duration(float64(r.config.InitialDelay) * math.Pow(r.config.Multiplier, float64(attempt)))

	case StrategyExponentialJitter:
		baseDelay := time.Duration(float64(r.config.InitialDelay) * math.Pow(r.config.Multiplier, float64(attempt)))
		jitter := time.Duration(float64(baseDelay) * r.config.JitterFactor * rand.Float64())
		delay = baseDelay + jitter

	default:
		delay = r.config.InitialDelay
	}

	// Cap the delay at MaxDelay
	if delay > r.config.MaxDelay {
		delay = r.config.MaxDelay
	}

	return delay
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr) != -1
}

func findSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// Convenience functions for common retry patterns

// WithExponentialBackoff executes operation with exponential backoff
func WithExponentialBackoff(ctx context.Context, logger *zap.Logger, operation func() error) error {
	retrier := New(DefaultConfig(), logger)
	return retrier.Execute(ctx, operation)
}

// WithDatabaseRetry executes database operation with appropriate retry logic
func WithDatabaseRetry(ctx context.Context, logger *zap.Logger, operation func() error) error {
	retrier := New(DatabaseConfig(), logger)
	return retrier.Execute(ctx, operation)
}

// WithExternalServiceRetry executes external service call with retry logic
func WithExternalServiceRetry(ctx context.Context, logger *zap.Logger, operation func() error) error {
	retrier := New(ExternalServiceConfig(), logger)
	return retrier.Execute(ctx, operation)
}

// RetryableError wraps an error to indicate it should be retried
type RetryableError struct {
	Err error
}

func (e *RetryableError) Error() string {
	return e.Err.Error()
}

func (e *RetryableError) Unwrap() error {
	return e.Err
}

// NewRetryableError creates a new retryable error
func NewRetryableError(err error) *RetryableError {
	return &RetryableError{Err: err}
}

// IsRetryableError checks if an error is marked as retryable
func IsRetryableError(err error) bool {
	var retryableErr *RetryableError
	return err != nil && (retryableErr != nil && retryableErr.Err != nil)
}
