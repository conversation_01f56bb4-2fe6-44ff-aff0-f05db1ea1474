package main

import (
	"context"
	"database/sql"
	"errors"
	"flag"
	"fmt"
	"log"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/lib/pq"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

func main() {
	var (
		action   = flag.String("action", "up", "Migration action: up, down, status")
		database = flag.String("database", "all", "Database to migrate: postgresql, scylladb, all")
		verbose  = flag.Bool("verbose", false, "Enable verbose logging")
	)
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Setup logging
	var logger *zap.Logger
	if *verbose {
		logger, _ = zap.NewDevelopment()
	} else {
		logger, _ = zap.NewProduction()
	}
	defer logger.Sync()

	// Create context
	ctx := context.Background()

	// Execute migration action based on database type
	switch *database {
	case "postgresql":
		if err := runPostgreSQLMigrations(ctx, cfg, logger, *action); err != nil {
			logger.Fatal("PostgreSQL migration failed", zap.Error(err))
		}
	case "scylladb":
		if err := runScyllaDBMigrations(ctx, cfg, logger, *action); err != nil {
			logger.Fatal("ScyllaDB migration failed", zap.Error(err))
		}
	case "all":
		// Run PostgreSQL migrations first
		logger.Info("Running PostgreSQL migrations...")
		if err := runPostgreSQLMigrations(ctx, cfg, logger, *action); err != nil {
			logger.Fatal("PostgreSQL migration failed", zap.Error(err))
		}

		// Run ScyllaDB migrations
		logger.Info("Running ScyllaDB migrations...")
		if err := runScyllaDBMigrations(ctx, cfg, logger, *action); err != nil {
			logger.Fatal("ScyllaDB migration failed", zap.Error(err))
		}

		logger.Info("All migrations completed successfully")
	default:
		logger.Fatal("Invalid database type", zap.String("database", *database))
	}
}

// runPostgreSQLMigrations handles PostgreSQL migrations using golang-migrate
func runPostgreSQLMigrations(ctx context.Context, cfg *config.Config, logger *zap.Logger, action string) error {
	// Create PostgreSQL client
	postgresql, err := database.NewPostgreSQLClient(&cfg.Databases.PostgreSQL, logger)
	if err != nil {
		return fmt.Errorf("failed to create PostgreSQL client: %w", err)
	}
	defer postgresql.Close()

	// Create a separate SQL connection for migrations using lib/pq driver
	config := postgresql.Pool.Config()
	connConfig := config.ConnConfig

	// Build connection string for lib/pq
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		connConfig.User,
		connConfig.Password,
		connConfig.Host,
		connConfig.Port,
		connConfig.Database,
	)

	// Open SQL connection using lib/pq driver
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return fmt.Errorf("failed to open database connection for migrations: %w", err)
	}
	defer db.Close()

	// Create migration driver using the postgres driver
	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Point to the migration files
	m, err := migrate.NewWithDatabaseInstance(
		"file://migrations/postgresql",
		"postgres",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}

	// Execute migration action
	switch action {
	case "up":
		logger.Info("Running PostgreSQL migrations up")
		if err := m.Up(); err != nil {
			// ErrNoChange is ok, it means the database is already up-to-date
			if !errors.Is(err, migrate.ErrNoChange) {
				return fmt.Errorf("failed to run PostgreSQL migrations: %w", err)
			} else {
				logger.Info("PostgreSQL database schema is up-to-date")
			}
		} else {
			logger.Info("PostgreSQL migrations applied successfully")
		}

	case "down":
		logger.Info("Running PostgreSQL migrations down")
		if err := m.Steps(-1); err != nil {
			return fmt.Errorf("failed to rollback PostgreSQL migrations: %w", err)
		}
		logger.Info("PostgreSQL migration rollback completed successfully")

	case "status":
		logger.Info("Checking PostgreSQL migration status")
		version, dirty, err := m.Version()
		if err != nil {
			return fmt.Errorf("failed to check PostgreSQL migration status: %w", err)
		}
		logger.Info("PostgreSQL migration status",
			zap.Int("version", int(version)),
			zap.Bool("dirty", dirty))

	default:
		return fmt.Errorf("invalid action: %s", action)
	}

	return nil
}

// runScyllaDBMigrations handles ScyllaDB migrations using the custom migration runner
func runScyllaDBMigrations(ctx context.Context, cfg *config.Config, logger *zap.Logger, action string) error {
	// Create ScyllaDB client
	scylladbClient, err := database.NewScyllaDBClient(&cfg.Databases.ScyllaDB, logger)
	if err != nil {
		return fmt.Errorf("failed to create ScyllaDB client: %w", err)
	}
	defer scylladbClient.Close()

	// Create migration runner
	migrationRunner := database.NewMigrationRunner(scylladbClient.Session, cfg.Databases.ScyllaDB.Keyspace, logger)

	// Get migrations path
	migrationsPath := "migrations/scylladb"

	// Execute migration action
	switch action {
	case "up":
		logger.Info("Running ScyllaDB migrations up")
		if err := migrationRunner.Up(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to run ScyllaDB migrations: %w", err)
		}
		logger.Info("ScyllaDB migrations completed successfully")

	case "down":
		logger.Info("Running ScyllaDB migrations down")
		if err := migrationRunner.Down(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to run ScyllaDB migrations down: %w", err)
		}
		logger.Info("ScyllaDB migration rollback completed successfully")

	case "status":
		logger.Info("Checking ScyllaDB migration status")
		if err := migrationRunner.Status(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to check ScyllaDB migration status: %w", err)
		}

	default:
		return fmt.Errorf("invalid action: %s", action)
	}

	return nil
}
