package main

import (
	"context"
	"database/sql"
	"errors"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/jackc/pgx/v5/pgxpool"
	_ "github.com/lib/pq"
	"github.com/valkey-io/valkey-go"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"google.golang.org/grpc/peer"

	"hopenbackend/internal/enterprise/monitoring"
	"hopenbackend/internal/enterprise/resilience"
	authservice "hopenbackend/microservices/auth"
	"hopenbackend/microservices/bubble"
	"hopenbackend/microservices/call"
	"hopenbackend/microservices/contact"
	"hopenbackend/microservices/friendship"
	"hopenbackend/microservices/media"
	"hopenbackend/microservices/notification"
	"hopenbackend/microservices/presence"
	"hopenbackend/microservices/realtime"
	"hopenbackend/microservices/social_analytics"
	"hopenbackend/microservices/sync"
	"hopenbackend/microservices/user"
	"hopenbackend/pkg/auth"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
	authv1 "hopenbackend/protos/gen/auth"
	bubblev1 "hopenbackend/protos/gen/bubble"
	callv1 "hopenbackend/protos/gen/call"
	contactv1 "hopenbackend/protos/gen/contact"
	friendshipv1 "hopenbackend/protos/gen/friendship"
	mediav1 "hopenbackend/protos/gen/media"
	notificationv1 "hopenbackend/protos/gen/notification"
	presencev1 "hopenbackend/protos/gen/presence"
	realtimev1 "hopenbackend/protos/gen/realtime"
	socialanalyticsv1 "hopenbackend/protos/gen/social_analytics"
	syncv1 "hopenbackend/protos/gen/sync"
	userv1 "hopenbackend/protos/gen/user"
)

// Application holds all the dependencies
type Application struct {
	config *config.Config
	logger *zap.Logger

	// Database clients
	postgresql *database.PostgreSQLClient
	scylladb   *database.ScyllaDBClient

	// External services
	rateLimiter  *ratelimit.RateLimiter
	natsConn     *nats.Conn
	mqttClient   mqtt.Client
	valkeyClient valkey.Client
	oryClient    *ory.Client

	// Enterprise modules
	circuitBreakers *resilience.CircuitBreakerManager
	socialMetrics   *monitoring.SocialMetrics

	// Microservices
	authService            *authservice.Service
	userService            *user.Service
	bubbleService          *bubble.Service
	contactService         *contact.Service
	friendshipService      *friendship.Service
	socialAnalyticsService *social_analytics.Service
	callService            *call.Service
	notificationService    *notification.Service
	presenceService        *presence.Service
	realtimeService        *realtime.Service
	mediaService           *media.Service
	syncService            *sync.Service

	// gRPC server
	grpcServer *grpc.Server
	httpServer *http.Server
}

func main() {
	// Parse command line arguments
	var (
		command = flag.String("command", "server", "Command to run: server, migrate")
		action  = flag.String("action", "up", "Migration action: up, down, status")
		dbType  = flag.String("database", "all", "Database to migrate: postgresql, scylladb, all")
	)
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("Failed to load config: %v", err))
	}

	// Initialize logger
	logger, err := initLogger(cfg)
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize logger: %v", err))
	}
	defer logger.Sync()

	// Execute command
	switch *command {
	case "migrate":
		// Run migrations
		if err := runMigrationsCommand(cfg, logger, *action, *dbType); err != nil {
			logger.Fatal("Migration failed", zap.Error(err))
		}
		logger.Info("Migration completed successfully")
		return

	case "server":
		// Run the main server
		runServer(cfg, logger)
		return

	default:
		logger.Fatal("Invalid command", zap.String("command", *command))
	}
}

// runServer runs the main gRPC server
func runServer(cfg *config.Config, logger *zap.Logger) {
	logger.Info("Starting Hopen Backend (Pure gRPC)",
		zap.String("version", cfg.App.Version),
		zap.String("environment", cfg.App.Environment),
		zap.Int("grpc_port", cfg.App.Port),
		zap.Int("http_port", cfg.App.Port+1),
	)

	// Create application instance
	app := &Application{
		config: cfg,
		logger: logger,
	}

	// Initialize application
	if err := app.initialize(); err != nil {
		logger.Fatal("Failed to initialize application", zap.Error(err))
	}

	// Start servers
	if err := app.start(); err != nil {
		logger.Fatal("Failed to start servers", zap.Error(err))
	}

	// Wait for shutdown signal
	app.waitForShutdown()
}

// runMigrationsCommand runs the migration command
func runMigrationsCommand(cfg *config.Config, logger *zap.Logger, action, dbType string) error {
	ctx := context.Background()

	// Execute migration action based on database type
	switch dbType {
	case "postgresql":
		if err := runPostgreSQLMigrations(ctx, cfg, logger, action); err != nil {
			return fmt.Errorf("PostgreSQL migration failed: %w", err)
		}
	case "scylladb":
		if err := runScyllaDBMigrations(ctx, cfg, logger, action); err != nil {
			return fmt.Errorf("ScyllaDB migration failed: %w", err)
		}
	case "all":
		// Run PostgreSQL migrations first
		logger.Info("Running PostgreSQL migrations...")
		if err := runPostgreSQLMigrations(ctx, cfg, logger, action); err != nil {
			return fmt.Errorf("PostgreSQL migration failed: %w", err)
		}

		// Run ScyllaDB migrations
		logger.Info("Running ScyllaDB migrations...")
		if err := runScyllaDBMigrations(ctx, cfg, logger, action); err != nil {
			return fmt.Errorf("ScyllaDB migration failed: %w", err)
		}

		logger.Info("All migrations completed successfully")
	default:
		return fmt.Errorf("invalid database type: %s", dbType)
	}

	return nil
}

// runPostgreSQLMigrations handles PostgreSQL migrations using golang-migrate
func runPostgreSQLMigrations(ctx context.Context, cfg *config.Config, logger *zap.Logger, action string) error {
	// Create PostgreSQL client
	postgresql, err := database.NewPostgreSQLClient(&cfg.Databases.PostgreSQL, logger)
	if err != nil {
		return fmt.Errorf("failed to create PostgreSQL client: %w", err)
	}
	defer postgresql.Close()

	// Create a separate SQL connection for migrations using lib/pq driver
	config := postgresql.Pool.Config()
	connConfig := config.ConnConfig

	// Build connection string for lib/pq
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		connConfig.User,
		connConfig.Password,
		connConfig.Host,
		connConfig.Port,
		connConfig.Database,
	)

	// Open SQL connection using lib/pq driver
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return fmt.Errorf("failed to open database connection for migrations: %w", err)
	}
	defer db.Close()

	// Create migration driver using the postgres driver
	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Point to the migration files
	m, err := migrate.NewWithDatabaseInstance(
		"file://migrations/postgresql",
		"postgres",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}

	// Execute migration action
	switch action {
	case "up":
		logger.Info("Running PostgreSQL migrations up")
		if err := m.Up(); err != nil {
			// ErrNoChange is ok, it means the database is already up-to-date
			if !errors.Is(err, migrate.ErrNoChange) {
				return fmt.Errorf("failed to run PostgreSQL migrations: %w", err)
			} else {
				logger.Info("PostgreSQL database schema is up-to-date")
			}
		} else {
			logger.Info("PostgreSQL migrations applied successfully")
		}

	case "down":
		logger.Info("Running PostgreSQL migrations down")
		if err := m.Steps(-1); err != nil {
			return fmt.Errorf("failed to rollback PostgreSQL migrations: %w", err)
		}
		logger.Info("PostgreSQL migration rollback completed successfully")

	case "status":
		logger.Info("Checking PostgreSQL migration status")
		version, dirty, err := m.Version()
		if err != nil {
			return fmt.Errorf("failed to check PostgreSQL migration status: %w", err)
		}
		logger.Info("PostgreSQL migration status",
			zap.Int("version", int(version)),
			zap.Bool("dirty", dirty))

	default:
		return fmt.Errorf("invalid action: %s", action)
	}

	return nil
}

// runScyllaDBMigrations handles ScyllaDB migrations using the custom migration runner
func runScyllaDBMigrations(ctx context.Context, cfg *config.Config, logger *zap.Logger, action string) error {
	// Create ScyllaDB client
	scylladbClient, err := database.NewScyllaDBClient(&cfg.Databases.ScyllaDB, logger)
	if err != nil {
		return fmt.Errorf("failed to create ScyllaDB client: %w", err)
	}
	defer scylladbClient.Close()

	// Create migration runner
	migrationRunner := database.NewMigrationRunner(scylladbClient.Session, cfg.Databases.ScyllaDB.Keyspace, logger)

	// Get migrations path
	migrationsPath := "migrations/scylladb"

	// Execute migration action
	switch action {
	case "up":
		logger.Info("Running ScyllaDB migrations up")
		if err := migrationRunner.Up(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to run ScyllaDB migrations: %w", err)
		}
		logger.Info("ScyllaDB migrations completed successfully")

	case "down":
		logger.Info("Running ScyllaDB migrations down")
		if err := migrationRunner.Down(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to run ScyllaDB migrations down: %w", err)
		}
		logger.Info("ScyllaDB migration rollback completed successfully")

	case "status":
		logger.Info("Checking ScyllaDB migration status")
		if err := migrationRunner.Status(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to check ScyllaDB migration status: %w", err)
		}

	default:
		return fmt.Errorf("invalid action: %s", action)
	}

	return nil
}

// initialize sets up all application dependencies
func (app *Application) initialize() error {
	ctx := context.Background()

	// Initialize database connections
	if err := app.initializeDatabases(ctx); err != nil {
		return fmt.Errorf("failed to initialize databases: %w", err)
	}

	// Initialize external services
	if err := app.initializeExternalServices(); err != nil {
		return fmt.Errorf("failed to initialize external services: %w", err)
	}

	// Initialize enterprise modules
	if err := app.initializeEnterpriseModules(); err != nil {
		return fmt.Errorf("failed to initialize enterprise modules: %w", err)
	}

	// Initialize microservices
	if err := app.initializeMicroservices(); err != nil {
		return fmt.Errorf("failed to initialize microservices: %w", err)
	}

	return nil
}

// initializeDatabases sets up database connections
func (app *Application) initializeDatabases(ctx context.Context) error {
	// PostgreSQL
	postgresql, err := database.NewPostgreSQLClient(&app.config.Databases.PostgreSQL, app.logger)
	if err != nil {
		return fmt.Errorf("failed to create PostgreSQL client: %w", err)
	}
	app.postgresql = postgresql

	// Run database migrations on startup
	// We run "up" for the "postgresql" database.
	app.logger.Info("Running PostgreSQL migrations on startup...")
	if err := runPostgreSQLMigrations(ctx, app.config, app.logger, "up"); err != nil {
		// We only check for migrate.ErrNoChange in the function itself, so any error here is fatal.
		return fmt.Errorf("failed to run PostgreSQL migrations on startup: %w", err)
	}

	// ScyllaDB
	scylladb, err := database.NewScyllaDBClient(&app.config.Databases.ScyllaDB, app.logger)
	if err != nil {
		return fmt.Errorf("failed to create ScyllaDB client: %w", err)
	}
	app.scylladb = scylladb

	// Initialize ScyllaDB keyspace
	if err := app.scylladb.InitializeKeyspace(ctx); err != nil {
		return fmt.Errorf("failed to initialize ScyllaDB keyspace: %w", err)
	}

	return nil
}

// initializeExternalServices sets up external service connections
func (app *Application) initializeExternalServices() error {
	app.logger.Info("🚀🚀🚀 EXTERNAL SERVICES INIT STARTED 🚀🚀🚀")

	// Valkey client for caching and idempotency
	valkeyClient, err := valkey.NewClient(valkey.ClientOption{
		InitAddress: []string{app.config.Valkey.Address},
		Password:    app.config.Valkey.Password,
		SelectDB:    app.config.Valkey.Database,
	})
	if err != nil {
		return fmt.Errorf("failed to create Valkey client: %w", err)
	}
	app.valkeyClient = valkeyClient

	// Test Valkey connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := app.valkeyClient.Do(ctx, app.valkeyClient.B().Ping().Build()).Error(); err != nil {
		return fmt.Errorf("failed to connect to Valkey: %w", err)
	}

	// Rate limiter
	rateLimiter, err := ratelimit.NewRateLimiter(app.config, app.logger)
	if err != nil {
		return fmt.Errorf("failed to create rate limiter: %w", err)
	}
	app.rateLimiter = rateLimiter

	// NATS connection for event-driven architecture
	app.logger.Info("Connecting to NATS...")
	natsURL := fmt.Sprintf("nats://%s:%d", app.config.NATS.Host, app.config.NATS.Port)
	natsConn, err := nats.Connect(natsURL,
		nats.UserInfo(app.config.NATS.Username, app.config.NATS.Password),
		nats.ReconnectWait(time.Second*2),
		nats.MaxReconnects(10),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			app.logger.Warn("NATS disconnected", zap.Error(err))
		}),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			app.logger.Info("NATS reconnected", zap.String("url", nc.ConnectedUrl()))
		}),
	)
	if err != nil {
		return fmt.Errorf("failed to connect to NATS: %w", err)
	}
	app.natsConn = natsConn

	// MQTT client for real-time messaging
	app.logger.Info("Initializing MQTT client...")
	if err := app.initializeMQTT(); err != nil {
		app.logger.Warn("Failed to initialize MQTT client", zap.Error(err))
		// Don't fail startup if MQTT is not available
	}

	// Ory client for authentication and authorization
	app.logger.Info("Creating Ory client...")
	oryDeps := &ory.Dependencies{
		Logger: app.logger,
		Config: app.config,
	}
	app.oryClient = ory.New(oryDeps)
	app.logger.Info("Ory client created successfully")

	app.logger.Info("External services initialized successfully")
	return nil
}

// initializeMQTT initializes the MQTT client for real-time messaging
func (app *Application) initializeMQTT() error {
	// Check if MQTT is configured
	if app.config.MQTT.Broker == "" {
		app.logger.Warn("MQTT broker not configured, real-time messaging disabled")
		return nil
	}

	opts := mqtt.NewClientOptions()
	opts.AddBroker(app.config.MQTT.Broker)
	opts.SetClientID(fmt.Sprintf("hopen-backend-%d", time.Now().Unix()))
	opts.SetUsername(app.config.MQTT.Username)
	opts.SetPassword(app.config.MQTT.Password)
	opts.SetKeepAlive(time.Duration(app.config.MQTT.KeepAlive) * time.Second)
	opts.SetCleanSession(true)

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		app.logger.Info("Backend connected to MQTT broker")
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		app.logger.Error("Backend lost connection to MQTT broker", zap.Error(err))
	})

	app.mqttClient = mqtt.NewClient(opts)
	if token := app.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to connect to MQTT broker: %w", token.Error())
	}

	app.logger.Info("MQTT client initialized successfully")
	return nil
}

// initializeEnterpriseModules sets up enterprise modules
func (app *Application) initializeEnterpriseModules() error {
	// Circuit breakers
	app.circuitBreakers = resilience.NewCircuitBreakerManager(app.config, app.logger)

	// Social metrics
	app.socialMetrics = monitoring.NewSocialMetrics()

	return nil
}

// initializeMicroservices sets up all microservices
func (app *Application) initializeMicroservices() error {
	// Auth service
	authRepository := authservice.NewRepository(app.postgresql)
	authDeps := &authservice.Dependencies{
		Logger:     app.logger,
		Config:     app.config,
		OryClient:  app.oryClient,
		Repository: authRepository,
	}
	app.authService = authservice.New(authDeps)

	// User service
	userDeps := &user.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		ScyllaDB:    app.scylladb,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
	}
	app.userService = user.NewService(userDeps)

	// Notification service (needed by other services)
	notificationDeps := &notification.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
		MQTTClient:  app.mqttClient,
	}
	app.notificationService = notification.NewService(notificationDeps)

	// Presence service
	presenceDeps := &presence.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
		MQTTClient:  app.mqttClient,
	}
	app.presenceService = presence.NewService(presenceDeps)

	// Bubble service (now handles ALL membership operations)
	bubbleDeps := &bubble.Dependencies{
		Logger:              app.logger,
		DB:                  app.postgresql,
		OryClient:           app.oryClient,
		NATSConn:            app.natsConn,
		NotificationService: app.notificationService,
	}
	app.bubbleService = bubble.NewService(bubbleDeps)

	// Contact service
	contactRepository := contact.NewPostgreSQLRepository(app.postgresql.Pool, app.logger)
	contactDeps := &contact.Dependencies{
		Logger:      app.logger,
		Repository:  contactRepository,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
	}
	app.contactService = contact.NewService(contactDeps)

	// Friendship service
	friendshipRepository := friendship.NewPostgreSQLRepository(app.postgresql.Pool, app.logger)
	friendshipDeps := &friendship.Dependencies{
		Logger:      app.logger,
		Repository:  friendshipRepository,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
	}
	app.friendshipService = friendship.NewService(friendshipDeps)

	// Social analytics service
	socialAnalyticsDeps := &social_analytics.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
	}
	app.socialAnalyticsService = social_analytics.NewService(socialAnalyticsDeps)

	// Call service
	bubbleServiceWrapper := &call.BubbleServiceWrapper{
		DB: app.postgresql,
	}

	callDeps := &call.Dependencies{
		Logger:        app.logger,
		DB:            app.postgresql,
		Config:        app.config,
		RateLimiter:   app.rateLimiter,
		OryClient:     app.oryClient,
		NATSConn:      app.natsConn,
		UserService:   app.userService,
		BubbleService: bubbleServiceWrapper,
	}
	app.callService = call.NewService(callDeps)

	// Realtime service
	realtimeDeps := &realtime.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		ScyllaDB:    app.scylladb,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
		MQTTClient:  app.mqttClient,
	}
	app.realtimeService = realtime.NewService(realtimeDeps)

	// Media service
	mediaDeps := &media.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
	}
	app.mediaService = media.NewService(mediaDeps)

	// Sync service
	syncDeps := &sync.Dependencies{
		Logger:      app.logger,
		DB:          app.postgresql,
		Config:      app.config,
		RateLimiter: app.rateLimiter,
		OryClient:   app.oryClient,
		NATSConn:    app.natsConn,
	}
	app.syncService = sync.NewService(syncDeps)

	return nil
}

// start starts the gRPC and HTTP servers
func (app *Application) start() error {
	// Start gRPC server
	if err := app.startGRPCServer(); err != nil {
		return fmt.Errorf("failed to start gRPC server: %w", err)
	}

	// Start lightweight HTTP server for health checks and metrics
	app.startHTTPServer()

	return nil
}

// startGRPCServer starts the gRPC server with all services registered
func (app *Application) startGRPCServer() error {
	// Create gRPC server with interceptors
	app.grpcServer = grpc.NewServer(
		grpc.UnaryInterceptor(app.unaryInterceptor),
		grpc.StreamInterceptor(app.streamInterceptor),
	)

	// Register all services
	app.registerServices()

	// Enable reflection for debugging
	reflection.Register(app.grpcServer)

	// Start server
	addr := fmt.Sprintf(":%d", app.config.App.Port)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	app.logger.Info("Starting gRPC server", zap.String("address", addr))
	go func() {
		if err := app.grpcServer.Serve(lis); err != nil {
			app.logger.Error("gRPC server failed", zap.Error(err))
		}
	}()

	return nil
}

// registerServices registers all gRPC services
func (app *Application) registerServices() {
	// Register auth service (working)
	authServiceServer := authservice.NewAuthServiceServer(app.authService)
	authv1.RegisterAuthServiceServer(app.grpcServer, authServiceServer)

	// Register user service (implemented)
	userServiceServer := user.NewUserServiceServer(app.userService)
	userv1.RegisterUserServiceServer(app.grpcServer, userServiceServer)

	// Register bubble service (implemented)
	bubbleServiceServer := bubble.NewBubbleServiceServer(app.bubbleService)
	bubblev1.RegisterBubbleServiceServer(app.grpcServer, bubbleServiceServer)

	// Register contact service (implemented)
	contactServiceServer := contact.NewContactServiceServer(app.contactService)
	contactv1.RegisterContactServiceServer(app.grpcServer, contactServiceServer)

	// Register friendship service (refactored to gRPC)
	friendshipServiceServer := friendship.NewFriendshipServiceServer(app.friendshipService)
	friendshipv1.RegisterFriendshipServiceServer(app.grpcServer, friendshipServiceServer)

	// Register notification service (refactored to gRPC)
	notificationServiceServer := notification.NewNotificationServiceServer(app.notificationService)
	notificationv1.RegisterNotificationServiceServer(app.grpcServer, notificationServiceServer)

	// Register presence service (refactored to gRPC)
	presenceServiceServer := presence.NewPresenceServiceServer(app.presenceService)
	presencev1.RegisterPresenceServiceServer(app.grpcServer, presenceServiceServer)

	// Register media service (refactored to gRPC)
	mediaServiceServer := media.NewMediaServiceServer(app.mediaService)
	mediav1.RegisterMediaServiceServer(app.grpcServer, mediaServiceServer)

	// Register social analytics service (refactored to gRPC)
	socialAnalyticsServiceServer := social_analytics.NewSocialAnalyticsServiceServer(app.socialAnalyticsService)
	socialanalyticsv1.RegisterSocialAnalyticsServiceServer(app.grpcServer, socialAnalyticsServiceServer)

	// Register sync service (refactored to gRPC)
	syncServiceServer := sync.NewSyncServiceServer(app.syncService)
	syncv1.RegisterSyncServiceServer(app.grpcServer, syncServiceServer)

	// Register call service (refactored to gRPC)
	callServiceServer := call.NewCallServiceServer(app.callService)
	callv1.RegisterCallServiceServer(app.grpcServer, callServiceServer)

	// Register realtime service (already gRPC-based, enhanced)
	realtimeServiceServer := realtime.NewRealtimeServiceServer(app.realtimeService)
	realtimev1.RegisterRealtimeServiceServer(app.grpcServer, realtimeServiceServer)

	app.logger.Info("All gRPC services registered successfully",
		zap.Strings("services", []string{
			"auth", "user", "bubble", "contact", "friendship",
			"notification", "presence", "media", "social_analytics",
			"sync", "call", "realtime",
		}))
}

// startHTTPServer starts a lightweight HTTP server for health checks and metrics
func (app *Application) startHTTPServer() {
	mux := http.NewServeMux()

	// Health check endpoints
	mux.HandleFunc("/health", app.healthCheck)
	mux.HandleFunc("/ready", app.readinessCheck)
	mux.HandleFunc("/metrics", app.metricsHandler)

	// Start HTTP server
	addr := fmt.Sprintf(":%d", app.config.App.Port+1)
	app.httpServer = &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	app.logger.Info("Starting HTTP server for health checks", zap.String("address", addr))
	go func() {
		if err := app.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.logger.Error("HTTP server failed", zap.Error(err))
		}
	}()
}

// unaryInterceptor implements gRPC unary interceptor for authentication, rate limiting, logging, and metrics
func (app *Application) unaryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	start := time.Now()
	clientIP := "unknown"

	// Get client IP
	if p, ok := peer.FromContext(ctx); ok {
		clientIP = p.Addr.String()
	}

	// Extract user ID from metadata for logging
	userID := "anonymous"
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if authHeader := md["authorization"]; len(authHeader) > 0 {
			// Extract user ID from token (simplified)
			userID = "authenticated_user"
		}
	}

	app.logger.Info("gRPC request started",
		zap.String("method", info.FullMethod),
		zap.String("client_ip", clientIP),
		zap.String("user_id", userID),
	)

	// Authentication
	userID, err := app.authenticate(ctx)
	if err != nil {
		app.logger.Warn("Authentication failed",
			zap.String("method", info.FullMethod),
			zap.String("client_ip", clientIP),
			zap.Error(err),
		)
		return nil, err
	}

	// Add user ID to context for handlers to use
	ctx = auth.WithUserID(ctx, userID)

	// Rate limiting
	if !app.isHealthCheckMethod(info.FullMethod) {
		allowed, err := app.rateLimiter.AllowForUser(ctx, userID, info.FullMethod)
		if err != nil {
			app.logger.Error("Rate limit check failed",
				zap.String("method", info.FullMethod),
				zap.String("user_id", userID),
				zap.Error(err),
			)
			// Continue anyway to avoid blocking legitimate requests due to rate limiter issues
		} else if !allowed {
			app.logger.Warn("Rate limit exceeded",
				zap.String("method", info.FullMethod),
				zap.String("user_id", userID),
			)
			return nil, status.Errorf(codes.ResourceExhausted, "rate limit exceeded")
		}
	}

	// Call handler
	resp, err := handler(ctx, req)

	// Log completion
	duration := time.Since(start)
	app.logger.Info("gRPC request completed",
		zap.String("method", info.FullMethod),
		zap.String("client_ip", clientIP),
		zap.String("user_id", userID),
		zap.Duration("duration", duration),
		zap.Bool("success", err == nil),
	)

	// Update metrics
	if app.socialMetrics != nil {
		// TODO : This would update Prometheus metrics in a real implementation
	}

	return resp, err
}

// streamInterceptor implements gRPC stream interceptor
func (app *Application) streamInterceptor(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	// For streaming, we'll do basic authentication and logging
	ctx := ss.Context()
	clientIP := "unknown"

	// Get client IP
	if p, ok := peer.FromContext(ctx); ok {
		clientIP = p.Addr.String()
	}

	app.logger.Info("gRPC stream started",
		zap.String("method", info.FullMethod),
		zap.String("client_ip", clientIP),
	)

	// Authentication
	_, err := app.authenticate(ctx)
	if err != nil {
		app.logger.Warn("Streaming authentication failed",
			zap.String("method", info.FullMethod),
			zap.String("client_ip", clientIP),
			zap.Error(err),
		)
		return err
	}

	// Call handler
	err = handler(srv, ss)

	app.logger.Info("gRPC stream completed",
		zap.String("method", info.FullMethod),
		zap.String("client_ip", clientIP),
		zap.Bool("success", err == nil),
	)

	return err
}

// authenticate validates the authentication token and returns user ID
func (app *Application) authenticate(ctx context.Context) (string, error) {
	// Extract JWT token from metadata
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		// Allow unauthenticated access to health check methods
		return "anonymous", nil
	}

	authHeader, ok := md["authorization"]
	if !ok || len(authHeader) == 0 {
		// Allow unauthenticated access to health check methods
		return "anonymous", nil
	}

	// Extract token from "Bearer <token>" format
	token := ""
	if len(authHeader) > 0 {
		parts := strings.SplitN(authHeader[0], " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			token = parts[1]
		}
	}

	if token == "" {
		// Allow unauthenticated access to health check methods
		return "anonymous", nil
	}

	// Validate session token with Ory Kratos
	session, err := app.oryClient.ValidateSession(ctx, token)
	if err != nil || session == nil || session.Active == nil || !*session.Active {
		return "", status.Errorf(codes.Unauthenticated, "invalid or inactive session")
	}

	return session.Identity.Id, nil
}

// isHealthCheckMethod checks if the method is a health check method that doesn't require authentication
func (app *Application) isHealthCheckMethod(method string) bool {
	healthCheckMethods := []string{
		"/grpc.health.v1.Health/Check",
		"/grpc.health.v1.Health/Watch",
	}

	for _, m := range healthCheckMethods {
		if method == m {
			return true
		}
	}
	return false
}

// waitForShutdown waits for shutdown signal and gracefully shuts down
func (app *Application) waitForShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	app.logger.Info("Shutting down servers...")

	// Gracefully stop gRPC server
	if app.grpcServer != nil {
		app.grpcServer.GracefulStop()
		app.logger.Info("gRPC server stopped")
	}

	// Gracefully stop HTTP server
	if app.httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := app.httpServer.Shutdown(ctx); err != nil {
			app.logger.Error("HTTP server shutdown failed", zap.Error(err))
		} else {
			app.logger.Info("HTTP server stopped")
		}
	}

	// Close database connections
	if app.postgresql != nil {
		app.postgresql.Close()
	}
	if app.scylladb != nil {
		app.scylladb.Close()
	}

	// Close external service connections
	if app.natsConn != nil {
		app.natsConn.Close()
		app.logger.Info("NATS connection closed")
	}

	if app.mqttClient != nil && app.mqttClient.IsConnected() {
		app.mqttClient.Disconnect(250)
		app.logger.Info("MQTT connection closed")
	}

	if app.valkeyClient != nil {
		app.valkeyClient.Close()
		app.logger.Info("Valkey connection closed")
	}

	if app.rateLimiter != nil {
		app.rateLimiter.Close()
		app.logger.Info("Rate limiter closed")
	}

	app.logger.Info("All servers shutdown successfully")
}

// Health check handlers

func (app *Application) healthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"status": "healthy", "timestamp": "%s"}`, time.Now().UTC().Format(time.RFC3339))
}

func (app *Application) readinessCheck(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	checks := map[string]string{
		"database": "ok",
		"valkey":   "ok",
		"nats":     "ok",
	}

	// Check PostgreSQL
	if app.postgresql != nil {
		if err := app.postgresql.Health(ctx); err != nil {
			checks["database"] = fmt.Sprintf("error: %v", err)
		}
	}

	// Check Valkey
	if app.valkeyClient != nil {
		if err := app.valkeyClient.Do(ctx, app.valkeyClient.B().Ping().Build()).Error(); err != nil {
			checks["valkey"] = fmt.Sprintf("error: %v", err)
		}
	}

	// Check NATS
	if app.natsConn != nil && !app.natsConn.IsConnected() {
		checks["nats"] = "disconnected"
	}

	// Determine overall status
	status := "ready"
	for _, check := range checks {
		if check != "ok" {
			status = "not ready"
			break
		}
	}

	if status == "ready" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"status": "%s", "checks": {"database": "%s", "valkey": "%s", "nats": "%s"}}`, status, checks["database"], checks["valkey"], checks["nats"])
	} else {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusServiceUnavailable)
		fmt.Fprintf(w, `{"status": "%s", "checks": {"database": "%s", "valkey": "%s", "nats": "%s"}}`, status, checks["database"], checks["valkey"], checks["nats"])
	}
}

func (app *Application) metricsHandler(w http.ResponseWriter, r *http.Request) {
	// This would typically serve Prometheus metrics
	// For now, return basic metrics
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"metrics": {"uptime_seconds": %f}}`, time.Since(time.Now()).Seconds())
}

// initLogger initializes the logger based on configuration
func initLogger(cfg *config.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.App.Environment == "production" {
		logger, err = zap.NewProduction()
	} else {
		logger, err = zap.NewDevelopment()
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create logger: %w", err)
	}

	return logger, nil
}

// runMigrations executes database migrations using golang-migrate
func runMigrations(dbPool *pgxpool.Pool, logger *zap.Logger) error {
	// Create a separate SQL connection for migrations using lib/pq driver
	// We need to construct the connection string from the pgx pool config
	config := dbPool.Config()
	connConfig := config.ConnConfig

	// Build connection string for lib/pq
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		connConfig.User,
		connConfig.Password,
		connConfig.Host,
		connConfig.Port,
		connConfig.Database,
	)

	// Open SQL connection using lib/pq driver
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return fmt.Errorf("failed to open database connection for migrations: %w", err)
	}
	defer db.Close()

	// Create migration driver using the postgres driver
	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Point to the migration files
	m, err := migrate.NewWithDatabaseInstance(
		"file://migrations",
		"postgres",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}

	// Run the migrations
	if err := m.Up(); err != nil {
		// ErrNoChange is ok, it means the database is already up-to-date
		if !errors.Is(err, migrate.ErrNoChange) {
			return fmt.Errorf("failed to run migrations: %w", err)
		} else {
			logger.Info("Database schema is up-to-date")
		}
	} else {
		logger.Info("Database migrations applied successfully")
	}

	return nil
}
