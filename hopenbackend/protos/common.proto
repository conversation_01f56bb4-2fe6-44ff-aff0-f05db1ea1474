syntax = "proto3";

package hopen.common.v1;

option go_package = "hopenbackend/protos/gen/common;common";
option java_multiple_files = true;
option java_package = "com.hopen.common.v1";

import "google/protobuf/timestamp.proto";

// Common response wrapper for all API responses
message ApiResponse {
  bool success = 1;
  string message = 2;
  string error_code = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// User information used across services (no bio field as specified)
message User {
  string id = 1;
  string username = 2;
  string email = 3;
  string first_name = 4;
  string last_name = 5;
  optional string avatar_url = 6;
  optional string bubble_id = 7;
  bool is_online = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

// Bubble information used across services (no description field as specified)
message Bubble {
  string id = 1;
  string name = 2;
  string creator_id = 3;
  google.protobuf.Timestamp expires_at = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  BubbleStatus status = 7;
  int32 member_count = 8;
  repeated string member_ids = 9;
}

// Bubble status enum
enum BubbleStatus {
  BUBBLE_STATUS_UNSPECIFIED = 0;
  BUBBLE_STATUS_ACTIVE = 1;
  BUBBLE_STATUS_EXPIRED = 2;
  BUBBLE_STATUS_DELETED = 3;
}

// Contact information
message Contact {
  string id = 1;
  string user_id = 2;
  string contact_user_id = 3;
  ContactStatus status = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

// Contact status enum
enum ContactStatus {
  CONTACT_STATUS_UNSPECIFIED = 0;
  CONTACT_STATUS_PENDING = 1;
  CONTACT_STATUS_ACCEPTED = 2;
  CONTACT_STATUS_REJECTED = 3;
  CONTACT_STATUS_BLOCKED = 4;
}

// Friendship information
message Friendship {
  string id = 1;
  string user_id = 2;
  string friend_id = 3;
  FriendshipStatus status = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

// Friendship status enum
enum FriendshipStatus {
  FRIENDSHIP_STATUS_UNSPECIFIED = 0;
  FRIENDSHIP_STATUS_PENDING = 1;
  FRIENDSHIP_STATUS_ACCEPTED = 2;
  FRIENDSHIP_STATUS_REJECTED = 3;
  FRIENDSHIP_STATUS_BLOCKED = 4;
}

// Notification information
message Notification {
  string id = 1;
  string user_id = 2;
  string title = 3;
  string body = 4;
  NotificationType type = 5;
  map<string, string> data = 6;
  bool is_read = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp read_at = 9;
}

// Notification type enum
enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;
  NOTIFICATION_TYPE_CONTACT_REQUEST = 1;
  NOTIFICATION_TYPE_FRIEND_REQUEST = 2;
  NOTIFICATION_TYPE_BUBBLE_INVITE = 3;
  NOTIFICATION_TYPE_MESSAGE = 4;
  NOTIFICATION_TYPE_CALL = 5;
  NOTIFICATION_TYPE_SYSTEM = 6;
}

// Pagination information
message Pagination {
  int32 page = 1;
  int32 page_size = 2;
  int32 total_count = 3;
  int32 total_pages = 4;
  bool has_next = 5;
  bool has_prev = 6;
}

// Error information
message Error {
  string code = 1;
  string message = 2;
  string details = 3;
  map<string, string> metadata = 4;
}

// Empty response for operations that don't return data
message Empty {}

// Health check response
message HealthResponse {
  string status = 1;
  string version = 2;
  google.protobuf.Timestamp timestamp = 3;
  map<string, string> services = 4;
} 