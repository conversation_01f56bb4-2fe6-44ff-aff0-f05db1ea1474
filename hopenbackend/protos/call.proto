syntax = "proto3";

package hopen.call.v1;

option go_package = "hopenbackend/protos/gen/call;call";
option java_multiple_files = true;
option java_package = "com.hopen.call.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Call service for WebRTC call management
service CallService {
  // Start a call
  rpc StartCall(StartCallRequest) returns (StartCallResponse);
  
  // Join a call
  rpc JoinCall(JoinCallRequest) returns (JoinCallResponse);
  
  // Leave a call
  rpc LeaveCall(LeaveCallRequest) returns (LeaveCallResponse);
  
  // End a call
  rpc EndCall(EndCallRequest) returns (EndCallResponse);
  
  // Get call information
  rpc GetCall(GetCallRequest) returns (GetCallResponse);
  
  // Update call media state
  rpc UpdateCallMedia(UpdateCallMediaRequest) returns (UpdateCallMediaResponse);
  
  // Get call participants
  rpc GetCallParticipants(GetCallParticipantsRequest) returns (GetCallParticipantsResponse);
  
  // Mute/unmute participant
  rpc UpdateParticipantMute(UpdateParticipantMuteRequest) returns (UpdateParticipantMuteResponse);
  
  // Get call history
  rpc GetCallHistory(GetCallHistoryRequest) returns (GetCallHistoryResponse);
  
  // Get call statistics
  rpc GetCallStats(GetCallStatsRequest) returns (GetCallStatsResponse);
}

// Start call request
message StartCallRequest {
  string user_id = 1;
  string bubble_id = 2;
  CallType call_type = 3;
  bool with_video = 4;
  bool with_audio = 5;
}

// Start call response
message StartCallResponse {
  string call_id = 1;
  CallInfo call_info = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Join call request
message JoinCallRequest {
  string user_id = 1;
  string call_id = 2;
  bool with_video = 3;
  bool with_audio = 4;
}

// Join call response
message JoinCallResponse {
  CallInfo call_info = 1;
  hopen.common.v1.ApiResponse api_response = 2;
  string access_token = 3;  // LiveKit access token for client connection
  string livekit_url = 4;   // LiveKit server URL
}

// Leave call request
message LeaveCallRequest {
  string user_id = 1;
  string call_id = 2;
}

// Leave call response
message LeaveCallResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// End call request
message EndCallRequest {
  string user_id = 1;
  string call_id = 2;
}

// End call response
message EndCallResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get call request
message GetCallRequest {
  string call_id = 1;
}

// Get call response
message GetCallResponse {
  CallInfo call_info = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update call media request
message UpdateCallMediaRequest {
  string user_id = 1;
  string call_id = 2;
  bool with_video = 3;
  bool with_audio = 4;
  bool with_screen_share = 5;
}

// Update call media response
message UpdateCallMediaResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get call participants request
message GetCallParticipantsRequest {
  string call_id = 1;
}

// Get call participants response
message GetCallParticipantsResponse {
  repeated CallParticipant participants = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update participant mute request
message UpdateParticipantMuteRequest {
  string user_id = 1;
  string call_id = 2;
  string target_user_id = 3;
  bool is_muted = 4;
}

// Update participant mute response
message UpdateParticipantMuteResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get call history request
message GetCallHistoryRequest {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// Get call history response
message GetCallHistoryResponse {
  repeated CallRecord call_history = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Get call stats request
message GetCallStatsRequest {
  string user_id = 1;
  string period = 2; // daily, weekly, monthly
}

// Get call stats response
message GetCallStatsResponse {
  CallStats stats = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Call information
message CallInfo {
  string call_id = 1;
  string bubble_id = 2;
  string initiator_id = 3;
  CallType call_type = 4;
  CallStatus status = 5;
  google.protobuf.Timestamp started_at = 6;
  google.protobuf.Timestamp ended_at = 7;
  repeated CallParticipant participants = 8;
  CallMediaState media_state = 9;
  CallSettings settings = 10;
}

// Call participant
message CallParticipant {
  string user_id = 1;
  hopen.common.v1.User user = 2;
  google.protobuf.Timestamp joined_at = 3;
  google.protobuf.Timestamp left_at = 4;
  bool is_muted = 5;
  bool has_video = 6;
  bool has_audio = 7;
  bool is_screen_sharing = 8;
  bool is_present = 9;
  ParticipantRole role = 10;
}

// Call media state
message CallMediaState {
  bool has_video = 1;
  bool has_audio = 2;
  bool has_screen_share = 3;
  string active_speaker = 4;
  repeated string muted_participants = 5;
  repeated string video_participants = 6;
}

// Call settings
message CallSettings {
  int32 max_participants = 1;
  bool allow_screen_share = 2;
  bool allow_recording = 3;
  string layout = 4; // grid, spotlight, presentation
  bool auto_mute_on_join = 5;
}

// Call record for history
message CallRecord {
  string call_id = 1;
  string bubble_id = 2;
  string bubble_name = 3;
  CallType call_type = 4;
  google.protobuf.Timestamp started_at = 5;
  google.protobuf.Timestamp ended_at = 6;
  int32 duration_seconds = 7;
  int32 participant_count = 8;
  bool was_initiator = 9;
  CallEndReason end_reason = 10;
}

// Call statistics
message CallStats {
  int32 total_calls = 1;
  int32 total_duration_minutes = 2;
  int32 average_duration_minutes = 3;
  int32 total_participants = 4;
  map<string, int32> calls_by_type = 5;
  map<string, int32> calls_by_day = 6;
  float success_rate = 7;
}

// Call types
enum CallType {
  CALL_TYPE_UNSPECIFIED = 0;
  CALL_TYPE_AUDIO = 1;
  CALL_TYPE_VIDEO = 2;
  CALL_TYPE_SCREEN_SHARE = 3;
}

// Call status
enum CallStatus {
  CALL_STATUS_UNSPECIFIED = 0;
  CALL_STATUS_INITIATING = 1;
  CALL_STATUS_RINGING = 2;
  CALL_STATUS_CONNECTED = 3;
  CALL_STATUS_ENDED = 4;
  CALL_STATUS_FAILED = 5;
  CALL_STATUS_MISSED = 6;
}

// Participant role
enum ParticipantRole {
  PARTICIPANT_ROLE_UNSPECIFIED = 0;
  PARTICIPANT_ROLE_INITIATOR = 1;
  PARTICIPANT_ROLE_PARTICIPANT = 2;
  PARTICIPANT_ROLE_MODERATOR = 3;
}

// Call end reason
enum CallEndReason {
  CALL_END_REASON_UNSPECIFIED = 0;
  CALL_END_REASON_HANGUP = 1;
  CALL_END_REASON_BUSY = 2;
  CALL_END_REASON_NO_ANSWER = 3;
  CALL_END_REASON_REJECTED = 4;
  CALL_END_REASON_NETWORK_ERROR = 5;
  CALL_END_REASON_SYSTEM_ERROR = 6;
} 