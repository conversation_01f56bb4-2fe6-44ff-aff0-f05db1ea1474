syntax = "proto3";

package hopen.auth.v1;

option go_package = "hopenbackend/protos/gen/auth;auth";
option java_multiple_files = true;
option java_package = "com.hopen.auth.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Auth service for authentication and authorization
service AuthService {
  // Register a new user
  rpc RegisterUser(RegisterUserRequest) returns (RegisterUserResponse);
  
  // Validate session token
  rpc ValidateSession(ValidateSessionRequest) returns (ValidateSessionResponse);
  
  // Refresh access token
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  
  // Logout user
  rpc Logout(LogoutRequest) returns (LogoutResponse);
  
  // Get user profile
  rpc GetUserProfile(GetUserProfileRequest) returns (GetUserProfileResponse);
  
  // Update user profile
  rpc UpdateUserProfile(UpdateUserProfileRequest) returns (UpdateUserProfileResponse);
  
  // Get user permissions
  rpc GetUserPermissions(GetUserPermissionsRequest) returns (GetUserPermissionsResponse);
  
  // Check if user has permission
  rpc HasPermission(HasPermissionRequest) returns (HasPermissionResponse);
  
  // Get user roles
  rpc GetUserRoles(GetUserRolesRequest) returns (GetUserRolesResponse);
  
  // Validate MQTT connection
  rpc ValidateMqttConnection(ValidateMqttConnectionRequest) returns (ValidateMqttConnectionResponse);
  
  // Get auth status
  rpc GetAuthStatus(GetAuthStatusRequest) returns (GetAuthStatusResponse);
}

// Register user request
message RegisterUserRequest {
  string username = 1;
  string email = 2;
  string password = 3;
  string first_name = 4;
  string last_name = 5;
  string date_of_birth = 6; // Optional, format: YYYY-MM-DD
}

// Register user response
message RegisterUserResponse {
  UserInfo user = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// User information
message UserInfo {
  string id = 1;
  string username = 2;
  string email = 3;
  string first_name = 4;
  string last_name = 5;
  string avatar_url = 6;
  google.protobuf.Timestamp date_of_birth = 7;
}

// Get user profile request
message GetUserProfileRequest {
  string user_id = 1;
}

// Get user profile response
message GetUserProfileResponse {
  UserInfo user = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update user profile request
message UpdateUserProfileRequest {
  string user_id = 1;
  string username = 2;
  string first_name = 3;
  string last_name = 4;
  string display_name = 5;
  string avatar_url = 6;
  string avatar_bucket_name = 7;
  string avatar_object_key = 8;
  string date_of_birth = 9; // Optional, format: YYYY-MM-DD
  bool is_private = 10;
  map<string, string> notification_settings = 11;
}

// Update user profile response
message UpdateUserProfileResponse {
  UserInfo user = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Validate session request
message ValidateSessionRequest {
  string session_token = 1;
}

// Validate session response
message ValidateSessionResponse {
  SessionInfo session_info = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Session information
message SessionInfo {
  string user_id = 1;
  string session_id = 2;
  bool is_active = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp expires_at = 5;
  repeated string permissions = 6;
  repeated string roles = 7;
}

// Refresh token request
message RefreshTokenRequest {
  string refresh_token = 1;
}

// Refresh token response
message RefreshTokenResponse {
  string access_token = 1;
  string refresh_token = 2;
  google.protobuf.Timestamp expires_at = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// Logout request
message LogoutRequest {
  string user_id = 1;
  string session_id = 2;
}

// Logout response
message LogoutResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get user permissions request
message GetUserPermissionsRequest {
  string user_id = 1;
}

// Get user permissions response
message GetUserPermissionsResponse {
  repeated string permissions = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Has permission request
message HasPermissionRequest {
  string user_id = 1;
  string permission = 2;
}

// Has permission response
message HasPermissionResponse {
  bool has_permission = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get user roles request
message GetUserRolesRequest {
  string user_id = 1;
}

// Get user roles response
message GetUserRolesResponse {
  repeated string roles = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Validate MQTT connection request
message ValidateMqttConnectionRequest {
  string user_id = 1;
  string client_id = 2;
  string username = 3;
  string password = 4;
}

// Validate MQTT connection response
message ValidateMqttConnectionResponse {
  bool is_valid = 1;
  repeated string allowed_topics = 2;
  map<string, int32> topic_qos = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// Get auth status request
message GetAuthStatusRequest {
  string user_id = 1;
}

// Get auth status response
message GetAuthStatusResponse {
  AuthStatus status = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Auth status
message AuthStatus {
  string user_id = 1;
  bool is_authenticated = 2;
  bool is_active = 3;
  google.protobuf.Timestamp last_login = 4;
  string login_method = 5;
  repeated string active_sessions = 6;
} 