syntax = "proto3";

package hopen.user.v1;

option go_package = "hopenbackend/protos/gen/user;user";
option java_multiple_files = true;
option java_package = "com.hopen.user.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// User service for user management and profiles
service UserService {
  // Get user profile by ID
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  
  // Update user profile
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
  
  // Search users with privacy controls
  rpc SearchUsers(SearchUsersRequest) returns (SearchUsersResponse);
  
  // Check username availability
  rpc CheckUsernameAvailability(CheckUsernameAvailabilityRequest) returns (CheckUsernameAvailabilityResponse);
  
  // Check email availability
  rpc CheckEmailAvailability(CheckEmailAvailabilityRequest) returns (CheckEmailAvailabilityResponse);
  
  // Get user privacy settings
  rpc GetUserPrivacySettings(GetUserPrivacySettingsRequest) returns (GetUserPrivacySettingsResponse);
  
  // Update user privacy settings
  rpc UpdateUserPrivacySettings(UpdateUserPrivacySettingsRequest) returns (UpdateUserPrivacySettingsResponse);
  
  // Delete user account
  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse);
  
  // Get user statistics
  rpc GetUserStats(GetUserStatsRequest) returns (GetUserStatsResponse);

  // Block user
  rpc BlockUser(BlockUserRequest) returns (BlockUserResponse);

  // Unblock user
  rpc UnblockUser(UnblockUserRequest) returns (UnblockUserResponse);

  // Get blocked users
  rpc GetBlockedUsers(GetBlockedUsersRequest) returns (GetBlockedUsersResponse);
}

// Get user request
message GetUserRequest {
  string user_id = 1;
}

// Get user response
message GetUserResponse {
  hopen.common.v1.User user = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update user request
message UpdateUserRequest {
  string user_id = 1;
  optional string username = 2;
  optional string first_name = 3;
  optional string last_name = 4;
  optional string avatar_url = 5;
}

// Update user response
message UpdateUserResponse {
  hopen.common.v1.User user = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Search users request
message SearchUsersRequest {
  string query = 1;
  int32 page = 2;
  int32 page_size = 3;
  bool include_private = 4;
}

// Search users response
message SearchUsersResponse {
  repeated hopen.common.v1.User users = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Check username availability request
message CheckUsernameAvailabilityRequest {
  string username = 1;
}

// Check username availability response
message CheckUsernameAvailabilityResponse {
  bool available = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Check email availability request
message CheckEmailAvailabilityRequest {
  string email = 1;
}

// Check email availability response
message CheckEmailAvailabilityResponse {
  bool available = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// User privacy settings
message UserPrivacySettings {
  string user_id = 1;
  bool is_private = 2;
  bool allow_search_by_email = 3;
  bool allow_search_by_username = 4;
  bool show_online_status = 5;
  bool show_bubble_membership = 6;
  google.protobuf.Timestamp updated_at = 7;
}

// Get user privacy settings request
message GetUserPrivacySettingsRequest {
  string user_id = 1;
}

// Get user privacy settings response
message GetUserPrivacySettingsResponse {
  UserPrivacySettings settings = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update user privacy settings request
message UpdateUserPrivacySettingsRequest {
  string user_id = 1;
  optional bool is_private = 2;
  optional bool allow_search_by_email = 3;
  optional bool allow_search_by_username = 4;
  optional bool show_online_status = 5;
  optional bool show_bubble_membership = 6;
}

// Update user privacy settings response
message UpdateUserPrivacySettingsResponse {
  UserPrivacySettings settings = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Delete user request
message DeleteUserRequest {
  string user_id = 1;
  string confirmation_token = 2;
}

// Delete user response
message DeleteUserResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// User statistics
message UserStats {
  string user_id = 1;
  int32 total_bubbles_created = 2;
  int32 total_bubbles_joined = 3;
  int32 total_contacts = 4;
  int32 total_friends = 5;
  int32 total_messages_sent = 6;
  int32 total_messages_received = 7;
  google.protobuf.Timestamp last_active_at = 8;
  google.protobuf.Timestamp created_at = 9;
}

// Get user stats request
message GetUserStatsRequest {
  string user_id = 1;
}

// Get user stats response
message GetUserStatsResponse {
  UserStats stats = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Block user request
message BlockUserRequest {
  string user_id = 1;
  string blocked_user_id = 2;
  string reason = 3;
}

// Block user response
message BlockUserResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Unblock user request
message UnblockUserRequest {
  string user_id = 1;
  string blocked_user_id = 2;
}

// Unblock user response
message UnblockUserResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Get blocked users request
message GetBlockedUsersRequest {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// Get blocked users response
message GetBlockedUsersResponse {
  repeated hopen.common.v1.User blocked_users = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}