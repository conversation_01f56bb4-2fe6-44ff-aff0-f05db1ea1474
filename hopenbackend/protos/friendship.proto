syntax = "proto3";

package hopen.friendship.v1;

option go_package = "hopenbackend/protos/gen/friendship;friendship";
option java_multiple_files = true;
option java_package = "com.hopen.friendship.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Friendship service for friend request and friendship management
service FriendshipService {
  // Get friend requests for a user
  rpc GetFriendRequests(GetFriendRequestsRequest) returns (GetFriendRequestsResponse);
  
  // Accept friend request
  rpc AcceptFriendRequest(AcceptFriendRequestRequest) returns (AcceptFriendRequestResponse);
  
  // Decline friend request
  rpc DeclineFriendRequest(DeclineFriendRequestRequest) returns (DeclineFriendRequestResponse);
  
  // Get user's friends
  rpc GetFriends(GetFriendsRequest) returns (GetFriendsResponse);
  
  // Remove friend
  rpc RemoveFriend(RemoveFriendRequest) returns (RemoveFriendResponse);
  
  // Block user
  rpc BlockUser(BlockUserRequest) returns (BlockUserResponse);
  
  // Unblock user
  rpc UnblockUser(UnblockUserRequest) returns (UnblockUserResponse);
  
  // Get blocked users
  rpc GetBlockedUsers(GetBlockedUsersRequest) returns (GetBlockedUsersResponse);
  
  // Check friendship status
  rpc CheckFriendshipStatus(CheckFriendshipStatusRequest) returns (CheckFriendshipStatusResponse);
  
  // Get mutual friends
  rpc GetMutualFriends(GetMutualFriendsRequest) returns (GetMutualFriendsResponse);
  
  // Get friendship suggestions
  rpc GetFriendshipSuggestions(GetFriendshipSuggestionsRequest) returns (GetFriendshipSuggestionsResponse);
}

// Get friend requests request
message GetFriendRequestsRequest {
  string user_id = 1;
  string status = 2; // pending, accepted, declined
  int32 page = 3;
  int32 page_size = 4;
}

// Get friend requests response
message GetFriendRequestsResponse {
  repeated FriendRequest friend_requests = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Friend request
message FriendRequest {
  string id = 1;
  string from_user_id = 2;
  string to_user_id = 3;
  hopen.common.v1.User from_user = 4;
  string message = 5;
  hopen.common.v1.FriendshipStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// Accept friend request request
message AcceptFriendRequestRequest {
  string user_id = 1;
  string request_id = 2;
}

// Accept friend request response
message AcceptFriendRequestResponse {
  string friendship_id = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Decline friend request request
message DeclineFriendRequestRequest {
  string user_id = 1;
  string request_id = 2;
  string reason = 3;
}

// Decline friend request response
message DeclineFriendRequestResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get friends request
message GetFriendsRequest {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// Get friends response
message GetFriendsResponse {
  repeated hopen.common.v1.User friends = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Remove friend request
message RemoveFriendRequest {
  string user_id = 1;
  string friend_id = 2;
}

// Remove friend response
message RemoveFriendResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Block user request
message BlockUserRequest {
  string user_id = 1;
  string blocked_user_id = 2;
  string reason = 3;
}

// Block user response
message BlockUserResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Unblock user request
message UnblockUserRequest {
  string user_id = 1;
  string blocked_user_id = 2;
}

// Unblock user response
message UnblockUserResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get blocked users request
message GetBlockedUsersRequest {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// Get blocked users response
message GetBlockedUsersResponse {
  repeated hopen.common.v1.User blocked_users = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Check friendship status request
message CheckFriendshipStatusRequest {
  string user_id = 1;
  string target_user_id = 2;
}

// Check friendship status response
message CheckFriendshipStatusResponse {
  hopen.common.v1.FriendshipStatus status = 1;
  string friendship_id = 2;
  google.protobuf.Timestamp created_at = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// Get mutual friends request
message GetMutualFriendsRequest {
  string user_id = 1;
  string target_user_id = 2;
  int32 limit = 3;
}

// Get mutual friends response
message GetMutualFriendsResponse {
  repeated hopen.common.v1.User mutual_friends = 1;
  int32 count = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Get friendship suggestions request
message GetFriendshipSuggestionsRequest {
  string user_id = 1;
  int32 limit = 2;
  repeated string exclude_user_ids = 3;
}

// Get friendship suggestions response
message GetFriendshipSuggestionsResponse {
  repeated FriendshipSuggestion suggestions = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Friendship suggestion
message FriendshipSuggestion {
  hopen.common.v1.User user = 1;
  float score = 2;
  repeated string reasons = 3;
  int32 mutual_friends_count = 4;
  int32 mutual_contacts_count = 5;
} 