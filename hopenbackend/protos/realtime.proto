syntax = "proto3";

package hopen.realtime.v1;

option go_package = "hopenbackend/protos/gen/realtime;realtime";
option java_multiple_files = true;
option java_package = "com.hopen.realtime.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";
import "sync.proto";

// Realtime service for chat and real-time messaging
service RealtimeService {
  // Send a message to a bubble
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
  
  // Get chat messages for a bubble
  rpc GetChatMessages(GetChatMessagesRequest) returns (GetChatMessagesResponse);
  
  // Edit a message
  rpc EditMessage(EditMessageRequest) returns (EditMessageResponse);
  
  // Delete a message
  rpc DeleteMessage(DeleteMessageRequest) returns (DeleteMessageResponse);
  
  // Send typing indicator
  rpc SendTypingIndicator(SendTypingIndicatorRequest) returns (SendTypingIndicatorResponse);
  
  // Get conversations
  rpc GetConversations(GetConversationsRequest) returns (GetConversationsResponse);
  
  // Send direct message
  rpc SendDirectMessage(SendDirectMessageRequest) returns (SendDirectMessageResponse);
  
  // Get conversation messages
  rpc GetConversationMessages(GetConversationMessagesRequest) returns (GetConversationMessagesResponse);
  
  // Search chat messages
  rpc SearchChatMessages(SearchChatMessagesRequest) returns (SearchChatMessagesResponse);
  
  // Mark message as read
  rpc MarkMessageAsRead(MarkMessageAsReadRequest) returns (MarkMessageAsReadResponse);
}

// Message represents a chat message
message Message {
  string message_id = 1;
  string bubble_id = 2;
  string sender_id = 3;
  string content = 4;
  string message_type = 5;
  optional string media_url = 6;
  optional string reply_to_id = 7;
  bool is_edited = 8;
  bool is_deleted = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
}

// ConversationMessage represents a direct message
message ConversationMessage {
  string message_id = 1;
  string conversation_id = 2;
  string sender_id = 3;
  string recipient_id = 4;
  string content = 5;
  string message_type = 6;
  optional string media_url = 7;
  optional string reply_to_id = 8;
  bool is_edited = 9;
  bool is_deleted = 10;
  bool is_read = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
}

// SendMessageRequest represents a message sending request
message SendMessageRequest {
  string bubble_id = 1;
  string content = 2;
  string message_type = 3;
  optional string media_url = 4;
  optional string reply_to_id = 5;
}

// SendMessageResponse represents a message sending response
message SendMessageResponse {
  Message message = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// GetChatMessagesRequest represents a request to get chat messages
message GetChatMessagesRequest {
  string bubble_id = 1;
  int32 limit = 2;
  string page_state = 3;
}

// GetChatMessagesResponse represents a response with chat messages
message GetChatMessagesResponse {
  repeated Message messages = 1;
  int32 count = 2;
  optional string next_page_state = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// EditMessageRequest represents a request to edit a message
message EditMessageRequest {
  string message_id = 1;
  string content = 2;
  google.protobuf.Timestamp created_at = 3;
}

// EditMessageResponse represents a response to edit a message
message EditMessageResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// DeleteMessageRequest represents a request to delete a message
message DeleteMessageRequest {
  string message_id = 1;
  google.protobuf.Timestamp created_at = 2;
}

// DeleteMessageResponse represents a response to delete a message
message DeleteMessageResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// SendTypingIndicatorRequest represents a typing indicator request
message SendTypingIndicatorRequest {
  string bubble_id = 1;
  bool is_typing = 2;
}

// SendTypingIndicatorResponse represents a typing indicator response
message SendTypingIndicatorResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// GetConversationsRequest represents a request to get conversations
message GetConversationsRequest {
  int32 limit = 1;
}

// GetConversationsResponse represents a response with conversations
message GetConversationsResponse {
  repeated hopen.sync.v1.Conversation conversations = 1;
  int32 count = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// SendDirectMessageRequest represents a direct message request
message SendDirectMessageRequest {
  string conversation_id = 1;
  string content = 2;
  string message_type = 3;
  optional string media_url = 4;
  optional string reply_to_id = 5;
}

// SendDirectMessageResponse represents a direct message response
message SendDirectMessageResponse {
  hopen.sync.v1.Message message = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// GetConversationMessagesRequest represents a request to get conversation messages
message GetConversationMessagesRequest {
  string conversation_id = 1;
  int32 limit = 2;
  optional string page_state = 3;
}

// GetConversationMessagesResponse represents a response with conversation messages
message GetConversationMessagesResponse {
  repeated hopen.sync.v1.Message messages = 1;
  int32 count = 2;
  optional string next_page_state = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// SearchChatMessagesRequest represents a request to search chat messages
message SearchChatMessagesRequest {
  string conversation_id = 1;
  string query = 2;
  int32 limit = 3;
}

// SearchChatMessagesResponse represents a response with search results
message SearchChatMessagesResponse {
  repeated Message messages = 1;
  int32 count = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// MarkMessageAsReadRequest represents a request to mark a message as read
message MarkMessageAsReadRequest {
  string message_id = 1;
}

// MarkMessageAsReadResponse represents a response to mark a message as read
message MarkMessageAsReadResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}
