syntax = "proto3";

package hopen.bubble.v1;

option go_package = "hopenbackend/protos/gen/bubble;bubble";
option java_multiple_files = true;
option java_package = "com.hopen.bubble.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Bubble service for bubble management and membership operations
service BubbleService {
  // Create a new bubble
  rpc CreateBubble(CreateBubbleRequest) returns (CreateBubbleResponse);
  
  // Get bubble by ID
  rpc GetBubble(GetBubbleRequest) returns (GetBubbleResponse);
  
  // Update bubble information
  rpc UpdateBubble(UpdateBubbleRequest) returns (UpdateBubbleResponse);
  
  // Delete bubble
  rpc DeleteBubble(DeleteBubbleRequest) returns (DeleteBubbleResponse);
  
  // Join bubble
  rpc JoinBubble(JoinBubbleRequest) returns (JoinBubbleResponse);
  
  // Leave bubble
  rpc LeaveBubble(LeaveBubbleRequest) returns (LeaveBubbleResponse);
  
  // Kick member from bubble
  rpc KickMember(KickMemberRequest) returns (KickMemberResponse);
  
  // Get bubble members
  rpc GetBubbleMembers(GetBubbleMembersRequest) returns (GetBubbleMembersResponse);
  
  // Get user's active bubbles
  rpc GetUserBubbles(GetUserBubblesRequest) returns (GetUserBubblesResponse);
  
  // Get bubble requests (join/invite)
  rpc GetBubbleRequests(GetBubbleRequestsRequest) returns (GetBubbleRequestsResponse);
  
  // Accept bubble request
  rpc AcceptBubbleRequest(AcceptBubbleRequestRequest) returns (AcceptBubbleRequestResponse);
  
  // Reject bubble request
  rpc RejectBubbleRequest(RejectBubbleRequestRequest) returns (RejectBubbleRequestResponse);
  
  // Send bubble invite
  rpc SendBubbleInvite(SendBubbleInviteRequest) returns (SendBubbleInviteResponse);
  
  // Get bubble analytics
  rpc GetBubbleAnalytics(GetBubbleAnalyticsRequest) returns (GetBubbleAnalyticsResponse);
}

// Create bubble request
message CreateBubbleRequest {
  string name = 1;
  string description = 2;
  string creator_id = 3;
  google.protobuf.Timestamp expires_at = 4;
}

// Create bubble response
message CreateBubbleResponse {
  hopen.common.v1.Bubble bubble = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get bubble request
message GetBubbleRequest {
  string bubble_id = 1;
}

// Get bubble response
message GetBubbleResponse {
  hopen.common.v1.Bubble bubble = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update bubble request
message UpdateBubbleRequest {
  string bubble_id = 1;
  optional string name = 2;
  optional string description = 3;
  optional google.protobuf.Timestamp expires_at = 4;
}

// Update bubble response
message UpdateBubbleResponse {
  hopen.common.v1.Bubble bubble = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Delete bubble request
message DeleteBubbleRequest {
  string bubble_id = 1;
  string user_id = 2;
}

// Delete bubble response
message DeleteBubbleResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Join bubble request
message JoinBubbleRequest {
  string bubble_id = 1;
  string user_id = 2;
}

// Join bubble response
message JoinBubbleResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Leave bubble request
message LeaveBubbleRequest {
  string bubble_id = 1;
  string user_id = 2;
}

// Leave bubble response
message LeaveBubbleResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Kick member request
message KickMemberRequest {
  string bubble_id = 1;
  string user_id = 2;
  string member_id = 3;
}

// Kick member response
message KickMemberResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Bubble member information
message BubbleMember {
  string bubble_id = 1;
  string user_id = 2;
  hopen.common.v1.User user = 3;
  google.protobuf.Timestamp joined_at = 4;
  google.protobuf.Timestamp left_at = 5;
  bool is_active = 6;
}

// Get bubble members request
message GetBubbleMembersRequest {
  string bubble_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  bool include_inactive = 4;
}

// Get bubble members response
message GetBubbleMembersResponse {
  repeated BubbleMember members = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Get user bubbles request
message GetUserBubblesRequest {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  bool include_expired = 4;
}

// Get user bubbles response
message GetUserBubblesResponse {
  repeated hopen.common.v1.Bubble bubbles = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Bubble request types
enum BubbleRequestType {
  BUBBLE_REQUEST_TYPE_UNSPECIFIED = 0;
  BUBBLE_REQUEST_TYPE_JOIN = 1;
  BUBBLE_REQUEST_TYPE_INVITE = 2;
}

// Bubble request status
enum BubbleRequestStatus {
  BUBBLE_REQUEST_STATUS_UNSPECIFIED = 0;
  BUBBLE_REQUEST_STATUS_PENDING = 1;
  BUBBLE_REQUEST_STATUS_ACCEPTED = 2;
  BUBBLE_REQUEST_STATUS_REJECTED = 3;
  BUBBLE_REQUEST_STATUS_CANCELLED = 4;
}

// Bubble request
message BubbleRequest {
  string id = 1;
  string bubble_id = 2;
  string user_id = 3;
  string requester_id = 4;
  BubbleRequestType type = 5;
  BubbleRequestStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// Get bubble requests request
message GetBubbleRequestsRequest {
  string bubble_id = 1;
  string user_id = 2;
  BubbleRequestStatus status = 3;
  int32 page = 4;
  int32 page_size = 5;
}

// Get bubble requests response
message GetBubbleRequestsResponse {
  repeated BubbleRequest requests = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Accept bubble request request
message AcceptBubbleRequestRequest {
  string request_id = 1;
  string user_id = 2;
}

// Accept bubble request response
message AcceptBubbleRequestResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Reject bubble request request
message RejectBubbleRequestRequest {
  string request_id = 1;
  string user_id = 2;
  string reason = 3;
}

// Reject bubble request response
message RejectBubbleRequestResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Send bubble invite request
message SendBubbleInviteRequest {
  string bubble_id = 1;
  string inviter_id = 2;
  repeated string invitee_ids = 3;
  string message = 4;
}

// Send bubble invite response
message SendBubbleInviteResponse {
  repeated string request_ids = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Bubble analytics
message BubbleAnalytics {
  string bubble_id = 1;
  int32 total_members = 2;
  int32 active_members = 3;
  int32 total_messages = 4;
  int32 total_calls = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp last_activity_at = 7;
  map<string, int32> member_activity = 8;
}

// Get bubble analytics request
message GetBubbleAnalyticsRequest {
  string bubble_id = 1;
}

// Get bubble analytics response
message GetBubbleAnalyticsResponse {
  BubbleAnalytics analytics = 1;
  hopen.common.v1.ApiResponse api_response = 2;
} 