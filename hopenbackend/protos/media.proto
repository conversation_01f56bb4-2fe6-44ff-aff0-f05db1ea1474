syntax = "proto3";

package hopen.media.v1;

option go_package = "hopenbackend/protos/gen/media;media";
option java_multiple_files = true;
option java_package = "com.hopen.media.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Media service for file upload and media management
service MediaService {
  // Upload file
  rpc UploadFile(UploadFileRequest) returns (UploadFileResponse);
  
  // Get file information
  rpc GetFileInfo(GetFileInfoRequest) returns (GetFileInfoResponse);
  
  // Delete file
  rpc DeleteFile(DeleteFileRequest) returns (DeleteFileResponse);
  
  // Get file URL
  rpc GetFileUrl(GetFileUrlRequest) returns (GetFileUrlResponse);
  
  // Upload profile picture
  rpc UploadProfilePicture(UploadProfilePictureRequest) returns (UploadProfilePictureResponse);
  
  // Get user media
  rpc GetUserMedia(GetUserMediaRequest) returns (GetUserMediaResponse);
  
  // Generate thumbnail
  rpc GenerateThumbnail(GenerateThumbnailRequest) returns (GenerateThumbnailResponse);
  
  // Get media statistics
  rpc GetMediaStats(GetMediaStatsRequest) returns (GetMediaStatsResponse);
  
  // Stream file upload
  rpc StreamFileUpload(stream FileChunk) returns (UploadFileResponse);
}

// Upload file request
message UploadFileRequest {
  string user_id = 1;
  string file_name = 2;
  string file_type = 3;
  int64 file_size = 4;
  string bubble_id = 5;
  string message_id = 6;
  bytes file_data = 7;
  map<string, string> metadata = 8;
}

// Upload file response
message UploadFileResponse {
  string file_id = 1;
  FileInfo file_info = 2;
  string upload_url = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// Get file info request
message GetFileInfoRequest {
  string file_id = 1;
}

// Get file info response
message GetFileInfoResponse {
  FileInfo file_info = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Delete file request
message DeleteFileRequest {
  string user_id = 1;
  string file_id = 2;
}

// Delete file response
message DeleteFileResponse {
  bool success = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get file URL request
message GetFileUrlRequest {
  string file_id = 1;
  string access_type = 2; // public, private, signed
  int32 expiry_seconds = 3;
}

// Get file URL response
message GetFileUrlResponse {
  string file_url = 1;
  google.protobuf.Timestamp expires_at = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Upload profile picture request
message UploadProfilePictureRequest {
  string user_id = 1;
  string file_name = 2;
  string file_type = 3;
  int64 file_size = 4;
  bytes image_data = 5;
  bool generate_thumbnail = 6;
}

// Upload profile picture response
message UploadProfilePictureResponse {
  string file_id = 1;
  FileInfo file_info = 2;
  string thumbnail_url = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// Get user media request
message GetUserMediaRequest {
  string user_id = 1;
  string media_type = 2; // image, video, audio, document
  int32 page = 3;
  int32 page_size = 4;
}

// Get user media response
message GetUserMediaResponse {
  repeated FileInfo media_files = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Generate thumbnail request
message GenerateThumbnailRequest {
  string file_id = 1;
  int32 width = 2;
  int32 height = 3;
  string format = 4; // jpeg, png, webp
}

// Generate thumbnail response
message GenerateThumbnailResponse {
  string thumbnail_id = 1;
  string thumbnail_url = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Get media stats request
message GetMediaStatsRequest {
  string user_id = 1;
  string period = 2; // daily, weekly, monthly
}

// Get media stats response
message GetMediaStatsResponse {
  MediaStats stats = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// File chunk for streaming upload
message FileChunk {
  string file_id = 1;
  string user_id = 2;
  int32 chunk_index = 3;
  int32 total_chunks = 4;
  bytes chunk_data = 5;
  bool is_last_chunk = 6;
  map<string, string> metadata = 7;
}

// File information
message FileInfo {
  string file_id = 1;
  string user_id = 2;
  string file_name = 3;
  string file_type = 4;
  string mime_type = 5;
  int64 file_size = 6;
  string file_url = 7;
  string thumbnail_url = 8;
  string bubble_id = 9;
  string message_id = 10;
  FileStatus status = 11;
  map<string, string> metadata = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  FileDimensions dimensions = 15;
  FileDuration duration = 16;
}

// File dimensions for images/videos
message FileDimensions {
  int32 width = 1;
  int32 height = 2;
  string aspect_ratio = 3;
}

// File duration for videos/audio
message FileDuration {
  float duration_seconds = 1;
  string formatted_duration = 2;
}

// Media statistics
message MediaStats {
  int32 total_files = 1;
  int64 total_size_bytes = 2;
  map<string, int32> files_by_type = 3;
  map<string, int64> size_by_type = 4;
  repeated string recent_uploads = 5;
  float average_file_size = 6;
  google.protobuf.Timestamp last_upload = 7;
}

// File status enum
enum FileStatus {
  FILE_STATUS_UNSPECIFIED = 0;
  FILE_STATUS_UPLOADING = 1;
  FILE_STATUS_PROCESSING = 2;
  FILE_STATUS_READY = 3;
  FILE_STATUS_FAILED = 4;
  FILE_STATUS_DELETED = 5;
} 