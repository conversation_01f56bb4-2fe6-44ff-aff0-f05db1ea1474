syntax = "proto3";

package hopen.presence.v1;

option go_package = "hopenbackend/protos/gen/presence;presence";
option java_multiple_files = true;
option java_package = "com.hopen.presence.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Presence service for user presence management
service PresenceService {
  // Get user presence
  rpc GetUserPresence(GetUserPresenceRequest) returns (GetUserPresenceResponse);
  
  // Update user presence
  rpc UpdateUserPresence(UpdateUserPresenceRequest) returns (UpdateUserPresenceResponse);
  
  // Get batch presence for multiple users
  rpc GetBatchPresence(GetBatchPresenceRequest) returns (GetBatchPresenceResponse);
  
  // Get online users
  rpc GetOnlineUsers(GetOnlineUsersRequest) returns (GetOnlineUsersResponse);
  
  // Get presence history
  rpc GetPresenceHistory(GetPresenceHistoryRequest) returns (GetPresenceHistoryResponse);
  
  // Set user away
  rpc SetUserAway(SetUserAwayRequest) returns (SetUserAwayResponse);
  
  // Set user online
  rpc SetUserOnline(SetUserOnlineRequest) returns (SetUserOnlineResponse);
  
  // Get presence statistics
  rpc GetPresenceStats(GetPresenceStatsRequest) returns (GetPresenceStatsResponse);
  
  // Subscribe to presence updates
  rpc SubscribeToPresenceUpdates(SubscribeToPresenceUpdatesRequest) returns (stream PresenceUpdate);
}

// Get user presence request
message GetUserPresenceRequest {
  string user_id = 1;
}

// Get user presence response
message GetUserPresenceResponse {
  UserPresence presence = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update user presence request
message UpdateUserPresenceRequest {
  string user_id = 1;
  PresenceStatus status = 2;
  string status_message = 3;
  bool is_typing = 4;
  string current_activity = 5;
}

// Update user presence response
message UpdateUserPresenceResponse {
  UserPresence presence = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get batch presence request
message GetBatchPresenceRequest {
  repeated string user_ids = 1;
}

// Get batch presence response
message GetBatchPresenceResponse {
  map<string, UserPresence> presences = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get online users request
message GetOnlineUsersRequest {
  string bubble_id = 1;
  int32 limit = 2;
  int32 offset = 3;
}

// Get online users response
message GetOnlineUsersResponse {
  repeated UserPresence online_users = 1;
  int32 total_count = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Get presence history request
message GetPresenceHistoryRequest {
  string user_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  int32 limit = 4;
}

// Get presence history response
message GetPresenceHistoryResponse {
  repeated PresenceHistoryEntry history = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Set user away request
message SetUserAwayRequest {
  string user_id = 1;
  string away_message = 2;
}

// Set user away response
message SetUserAwayResponse {
  UserPresence presence = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Set user online request
message SetUserOnlineRequest {
  string user_id = 1;
  string status_message = 2;
}

// Set user online response
message SetUserOnlineResponse {
  UserPresence presence = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get presence stats request
message GetPresenceStatsRequest {
  string bubble_id = 1;
  string period = 2; // hourly, daily, weekly
}

// Get presence stats response
message GetPresenceStatsResponse {
  PresenceStats stats = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Subscribe to presence updates request
message SubscribeToPresenceUpdatesRequest {
  string user_id = 1;
  repeated string target_user_ids = 2;
}

// Presence update stream
message PresenceUpdate {
  string user_id = 1;
  UserPresence presence = 2;
  google.protobuf.Timestamp timestamp = 3;
  string update_type = 4; // status_change, activity_change, typing
}

// User presence information
message UserPresence {
  string user_id = 1;
  PresenceStatus status = 2;
  string status_message = 3;
  google.protobuf.Timestamp last_seen = 4;
  google.protobuf.Timestamp updated_at = 5;
  bool is_online = 6;
  bool is_typing = 7;
  string current_activity = 8;
  string current_bubble_id = 9;
  repeated string active_sessions = 10;
}

// Presence history entry
message PresenceHistoryEntry {
  string user_id = 1;
  PresenceStatus status = 2;
  google.protobuf.Timestamp timestamp = 3;
  string status_message = 4;
  string activity = 5;
}

// Presence statistics
message PresenceStats {
  int32 total_users = 1;
  int32 online_users = 2;
  int32 away_users = 3;
  int32 offline_users = 4;
  float online_percentage = 5;
  map<string, int32> status_distribution = 6;
  repeated string most_active_users = 7;
  google.protobuf.Timestamp last_updated = 8;
}

// Presence status enum
enum PresenceStatus {
  PRESENCE_STATUS_UNSPECIFIED = 0;
  PRESENCE_STATUS_ONLINE = 1;
  PRESENCE_STATUS_AWAY = 2;
  PRESENCE_STATUS_OFFLINE = 3;
  PRESENCE_STATUS_BUSY = 4;
  PRESENCE_STATUS_DO_NOT_DISTURB = 5;
} 