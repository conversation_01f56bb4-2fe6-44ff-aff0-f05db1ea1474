syntax = "proto3";

package hopen.notification.v1;

option go_package = "hopenbackend/protos/gen/notification;notification";
option java_multiple_files = true;
option java_package = "com.hopen.notification.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Notification service for push notifications and notification management
service NotificationService {
  // Register FCM token
  rpc RegisterFCMToken(RegisterFCMTokenRequest) returns (RegisterFCMTokenResponse);
  
  // Unregister FCM token
  rpc UnregisterFCMToken(UnregisterFCMTokenRequest) returns (UnregisterFCMTokenResponse);
  
  // Send push notification
  rpc SendPushNotification(SendPushNotificationRequest) returns (SendPushNotificationResponse);
  
  // Send push notification to topic
  rpc SendPushNotificationToTopic(SendPushNotificationToTopicRequest) returns (SendPushNotificationToTopicResponse);
  
  // Get user notifications
  rpc GetUserNotifications(GetUserNotificationsRequest) returns (GetUserNotificationsResponse);
  
  // Mark notification as read
  rpc MarkNotificationAsRead(MarkNotificationAsReadRequest) returns (MarkNotificationAsReadResponse);
  
  // Mark all notifications as read
  rpc MarkAllNotificationsAsRead(MarkAllNotificationsAsReadRequest) returns (MarkAllNotificationsAsReadResponse);
  
  // Delete notification
  rpc DeleteNotification(DeleteNotificationRequest) returns (DeleteNotificationResponse);
  
  // Get notification settings
  rpc GetNotificationSettings(GetNotificationSettingsRequest) returns (GetNotificationSettingsResponse);
  
  // Update notification settings
  rpc UpdateNotificationSettings(UpdateNotificationSettingsRequest) returns (UpdateNotificationSettingsResponse);
  
  // Get unread notification count
  rpc GetUnreadNotificationCount(GetUnreadNotificationCountRequest) returns (GetUnreadNotificationCountResponse);
}

// Register FCM token request
message RegisterFCMTokenRequest {
  string user_id = 1;
  string fcm_token = 2;
  string device_id = 3;
  string platform = 4;
  string app_version = 5;
}

// Register FCM token response
message RegisterFCMTokenResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Unregister FCM token request
message UnregisterFCMTokenRequest {
  string user_id = 1;
  string fcm_token = 2;
}

// Unregister FCM token response
message UnregisterFCMTokenResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Send push notification request
message SendPushNotificationRequest {
  string user_id = 1;
  string title = 2;
  string body = 3;
  map<string, string> data = 4;
  hopen.common.v1.NotificationType type = 5;
  string image_url = 6;
  string sound = 7;
  int32 badge = 8;
  bool priority_high = 9;
  int32 ttl_seconds = 10;
}

// Send push notification response
message SendPushNotificationResponse {
  string message_id = 1;
  bool success = 2;
  string error_message = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// Send push notification to topic request
message SendPushNotificationToTopicRequest {
  string topic = 1;
  string title = 2;
  string body = 3;
  map<string, string> data = 4;
  hopen.common.v1.NotificationType type = 5;
  string image_url = 6;
  string sound = 7;
  int32 badge = 8;
  bool priority_high = 9;
  int32 ttl_seconds = 10;
}

// Send push notification to topic response
message SendPushNotificationToTopicResponse {
  string message_id = 1;
  int32 success_count = 2;
  int32 failure_count = 3;
  hopen.common.v1.ApiResponse api_response = 4;
}

// Get user notifications request
message GetUserNotificationsRequest {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  bool include_read = 4;
  hopen.common.v1.NotificationType type = 5;
}

// Get user notifications response
message GetUserNotificationsResponse {
  repeated hopen.common.v1.Notification notifications = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Mark notification as read request
message MarkNotificationAsReadRequest {
  string user_id = 1;
  string notification_id = 2;
}

// Mark notification as read response
message MarkNotificationAsReadResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Mark all notifications as read request
message MarkAllNotificationsAsReadRequest {
  string user_id = 1;
}

// Mark all notifications as read response
message MarkAllNotificationsAsReadResponse {
  int32 updated_count = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Delete notification request
message DeleteNotificationRequest {
  string user_id = 1;
  string notification_id = 2;
}

// Delete notification response
message DeleteNotificationResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Notification settings
message NotificationSettings {
  string user_id = 1;
  bool push_enabled = 2;
  bool contact_requests_enabled = 3;
  bool friend_requests_enabled = 4;
  bool bubble_invites_enabled = 5;
  bool messages_enabled = 6;
  bool calls_enabled = 7;
  bool system_notifications_enabled = 8;
  bool sound_enabled = 9;
  bool vibration_enabled = 10;
  string quiet_hours_start = 11;
  string quiet_hours_end = 12;
  bool quiet_hours_enabled = 13;
  google.protobuf.Timestamp updated_at = 14;
}

// Get notification settings request
message GetNotificationSettingsRequest {
  string user_id = 1;
}

// Get notification settings response
message GetNotificationSettingsResponse {
  NotificationSettings settings = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Update notification settings request
message UpdateNotificationSettingsRequest {
  string user_id = 1;
  optional bool push_enabled = 2;
  optional bool contact_requests_enabled = 3;
  optional bool friend_requests_enabled = 4;
  optional bool bubble_invites_enabled = 5;
  optional bool messages_enabled = 6;
  optional bool calls_enabled = 7;
  optional bool system_notifications_enabled = 8;
  optional bool sound_enabled = 9;
  optional bool vibration_enabled = 10;
  optional string quiet_hours_start = 11;
  optional string quiet_hours_end = 12;
  optional bool quiet_hours_enabled = 13;
}

// Update notification settings response
message UpdateNotificationSettingsResponse {
  NotificationSettings settings = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get unread notification count request
message GetUnreadNotificationCountRequest {
  string user_id = 1;
}

// Get unread notification count response
message GetUnreadNotificationCountResponse {
  int32 count = 1;
  map<string, int32> count_by_type = 2;
  hopen.common.v1.ApiResponse api_response = 3;
} 