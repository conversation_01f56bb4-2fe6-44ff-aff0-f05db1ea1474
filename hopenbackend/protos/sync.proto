syntax = "proto3";

package hopen.sync.v1;

option go_package = "hopenbackend/protos/gen/sync;sync";
option java_multiple_files = true;
option java_package = "com.hopen.sync.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Sync service for initial data synchronization
service SyncService {
  // Get complete initial state for authenticated user
  rpc SyncInitialState(SyncInitialStateRequest) returns (SyncInitialStateResponse);
  
  // Get sync status
  rpc GetSyncStatus(GetSyncStatusRequest) returns (GetSyncStatusResponse);
  
  // Force sync refresh
  rpc ForceSyncRefresh(ForceSyncRefreshRequest) returns (ForceSyncRefreshResponse);
}

// Sync initial state request
message SyncInitialStateRequest {
  string user_id = 1;
  google.protobuf.Timestamp last_sync_at = 2;
  bool include_deleted = 3;
}

// Sync initial state response
message SyncInitialStateResponse {
  SyncData data = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Complete sync data structure
message SyncData {
  // User profile
  hopen.common.v1.User user = 1;
  
  // User settings and preferences
  UserSettings user_settings = 2;
  
  // Active bubbles
  repeated hopen.common.v1.Bubble active_bubbles = 3;
  
  // Contacts
  repeated hopen.common.v1.Contact contacts = 4;
  
  // Friends
  repeated hopen.common.v1.Friendship friendships = 5;
  
  // Pending requests
  PendingRequests pending_requests = 6;
  
  // Chat conversations
  repeated Conversation conversations = 7;
  
  // Notifications
  repeated hopen.common.v1.Notification notifications = 8;
  
  // Sync metadata
  SyncMetadata metadata = 9;
}

// User settings
message UserSettings {
  string user_id = 1;
  bool notifications_enabled = 2;
  bool sound_enabled = 3;
  bool vibration_enabled = 4;
  string theme = 5;
  string language = 6;
  google.protobuf.Timestamp updated_at = 7;
}

// Pending requests
message PendingRequests {
  repeated ContactRequest contact_requests = 1;
  repeated FriendRequest friend_requests = 2;
  repeated BubbleRequest bubble_requests = 3;
  int32 total_count = 4;
}

// Contact request
message ContactRequest {
  string id = 1;
  string from_user_id = 2;
  string to_user_id = 3;
  hopen.common.v1.User from_user = 4;
  string message = 5;
  hopen.common.v1.ContactStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// Friend request
message FriendRequest {
  string id = 1;
  string from_user_id = 2;
  string to_user_id = 3;
  hopen.common.v1.User from_user = 4;
  string message = 5;
  hopen.common.v1.FriendshipStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// Bubble request
message BubbleRequest {
  string id = 1;
  string bubble_id = 2;
  string user_id = 3;
  string requester_id = 4;
  hopen.common.v1.Bubble bubble = 5;
  hopen.common.v1.User requester = 6;
  BubbleRequestType type = 7;
  BubbleRequestStatus status = 8;
  string message = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
}

// Bubble request types
enum BubbleRequestType {
  BUBBLE_REQUEST_TYPE_UNSPECIFIED = 0;
  BUBBLE_REQUEST_TYPE_JOIN = 1;
  BUBBLE_REQUEST_TYPE_INVITE = 2;
}

// Bubble request status
enum BubbleRequestStatus {
  BUBBLE_REQUEST_STATUS_UNSPECIFIED = 0;
  BUBBLE_REQUEST_STATUS_PENDING = 1;
  BUBBLE_REQUEST_STATUS_ACCEPTED = 2;
  BUBBLE_REQUEST_STATUS_REJECTED = 3;
  BUBBLE_REQUEST_STATUS_CANCELLED = 4;
}

// Conversation
message Conversation {
  string id = 1;
  string bubble_id = 2;
  string name = 3;
  repeated string participant_ids = 4;
  repeated hopen.common.v1.User participants = 5;
  Message last_message = 6;
  int32 unread_count = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
}

// Message
message Message {
  string id = 1;
  string conversation_id = 2;
  string sender_id = 3;
  hopen.common.v1.User sender = 4;
  string content = 5;
  MessageType type = 6;
  map<string, string> metadata = 7;
  bool is_read = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

// Message types
enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;
  MESSAGE_TYPE_TEXT = 1;
  MESSAGE_TYPE_IMAGE = 2;
  MESSAGE_TYPE_VIDEO = 3;
  MESSAGE_TYPE_AUDIO = 4;
  MESSAGE_TYPE_FILE = 5;
  MESSAGE_TYPE_SYSTEM = 6;
}

// Sync metadata
message SyncMetadata {
  google.protobuf.Timestamp sync_timestamp = 1;
  string sync_version = 2;
  bool has_more_data = 3;
  int32 total_items = 4;
  map<string, int32> item_counts = 5;
}

// Get sync status request
message GetSyncStatusRequest {
  string user_id = 1;
}

// Get sync status response
message GetSyncStatusResponse {
  SyncStatus status = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Sync status
message SyncStatus {
  string user_id = 1;
  google.protobuf.Timestamp last_sync_at = 2;
  string sync_version = 3;
  bool is_syncing = 4;
  string sync_status = 5;
  int32 pending_changes = 6;
  google.protobuf.Timestamp next_sync_at = 7;
}

// Force sync refresh request
message ForceSyncRefreshRequest {
  string user_id = 1;
  bool full_sync = 2;
}

// Force sync refresh response
message ForceSyncRefreshResponse {
  SyncStatus status = 1;
  hopen.common.v1.ApiResponse api_response = 2;
} 