syntax = "proto3";

package hopen.contact.v1;

option go_package = "hopenbackend/protos/gen/contact;contact";
option java_multiple_files = true;
option java_package = "com.hopen.contact.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Contact service for contact management
service ContactService {
  // Send contact request
  rpc SendContactRequest(SendContactRequestRequest) returns (SendContactRequestResponse);
  
  // Accept contact request
  rpc AcceptContactRequest(AcceptContactRequestRequest) returns (AcceptContactRequestResponse);
  
  // Reject contact request
  rpc RejectContactRequest(RejectContactRequestRequest) returns (RejectContactRequestResponse);
  
  // Cancel contact request
  rpc CancelContactRequest(CancelContactRequestRequest) returns (CancelContactRequestResponse);
  
  // Remove contact
  rpc RemoveContact(RemoveContactRequest) returns (RemoveContactResponse);
  
  // Block contact
  rpc BlockContact(BlockContactRequest) returns (BlockContactResponse);
  
  // Unblock contact
  rpc UnblockContact(UnblockContactRequest) returns (UnblockContactResponse);
  
  // Get user contacts
  rpc GetUserContacts(GetUserContactsRequest) returns (GetUserContactsResponse);
  
  // Get contact requests
  rpc GetContactRequests(GetContactRequestsRequest) returns (GetContactRequestsResponse);
  
  // Get contact by ID
  rpc GetContact(GetContactRequest) returns (GetContactResponse);
  
  // Search contacts
  rpc SearchContacts(SearchContactsRequest) returns (SearchContactsResponse);
  
  // Get contact suggestions
  rpc GetContactSuggestions(GetContactSuggestionsRequest) returns (GetContactSuggestionsResponse);
}

// Send contact request request
message SendContactRequestRequest {
  string from_user_id = 1;
  string to_user_id = 2;
  string message = 3;
}

// Send contact request response
message SendContactRequestResponse {
  string request_id = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Accept contact request request
message AcceptContactRequestRequest {
  string request_id = 1;
  string user_id = 2;
}

// Accept contact request response
message AcceptContactRequestResponse {
  hopen.common.v1.Contact contact = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Reject contact request request
message RejectContactRequestRequest {
  string request_id = 1;
  string user_id = 2;
  string reason = 3;
}

// Reject contact request response
message RejectContactRequestResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Cancel contact request request
message CancelContactRequestRequest {
  string request_id = 1;
  string user_id = 2;
}

// Cancel contact request response
message CancelContactRequestResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Remove contact request
message RemoveContactRequest {
  string user_id = 1;
  string contact_id = 2;
}

// Remove contact response
message RemoveContactResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Block contact request
message BlockContactRequest {
  string user_id = 1;
  string contact_id = 2;
  string reason = 3;
}

// Block contact response
message BlockContactResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Unblock contact request
message UnblockContactRequest {
  string user_id = 1;
  string contact_id = 2;
}

// Unblock contact response
message UnblockContactResponse {
  hopen.common.v1.ApiResponse api_response = 1;
}

// Get user contacts request
message GetUserContactsRequest {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  bool include_blocked = 4;
}

// Get user contacts response
message GetUserContactsResponse {
  repeated ContactWithUser contacts = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Contact with user information
message ContactWithUser {
  hopen.common.v1.Contact contact = 1;
  hopen.common.v1.User user = 2;
  google.protobuf.Timestamp last_interaction_at = 3;
  int32 mutual_bubbles = 4;
  int32 mutual_friends = 5;
}

// Get contact requests request
message GetContactRequestsRequest {
  string user_id = 1;
  hopen.common.v1.ContactStatus status = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Get contact requests response
message GetContactRequestsResponse {
  repeated ContactRequestWithUser requests = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Contact request with user information
message ContactRequestWithUser {
  string id = 1;
  string from_user_id = 2;
  string to_user_id = 3;
  hopen.common.v1.User from_user = 4;
  string message = 5;
  hopen.common.v1.ContactStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// Get contact request
message GetContactRequest {
  string contact_id = 1;
  string user_id = 2;
}

// Get contact response
message GetContactResponse {
  ContactWithUser contact = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Search contacts request
message SearchContactsRequest {
  string user_id = 1;
  string query = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Search contacts response
message SearchContactsResponse {
  repeated ContactWithUser contacts = 1;
  hopen.common.v1.Pagination pagination = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}

// Get contact suggestions request
message GetContactSuggestionsRequest {
  string user_id = 1;
  int32 limit = 2;
  repeated string exclude_ids = 3;
}

// Get contact suggestions response
message GetContactSuggestionsResponse {
  repeated ContactSuggestion suggestions = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Contact suggestion
message ContactSuggestion {
  hopen.common.v1.User user = 1;
  float relevance_score = 2;
  repeated string reasons = 3;
  int32 mutual_connections = 4;
  int32 mutual_bubbles = 5;
} 