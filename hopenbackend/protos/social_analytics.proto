syntax = "proto3";

package hopen.social_analytics.v1;

option go_package = "hopenbackend/protos/gen/social_analytics;socialanalytics";
option java_multiple_files = true;
option java_package = "com.hopen.social_analytics.v1";

import "google/protobuf/timestamp.proto";
import "common.proto";

// Social analytics service for social graph analysis
service SocialAnalyticsService {
  // Get enhanced profile with comprehensive relationship data
  rpc GetEnhancedProfile(GetEnhancedProfileRequest) returns (GetEnhancedProfileResponse);
  
  // Get comprehensive profile analytics including mutual connections
  rpc GetProfileAnalytics(GetProfileAnalyticsRequest) returns (GetProfileAnalyticsResponse);
  
  // Get mutual friends between users
  rpc GetMutualFriends(GetMutualFriendsRequest) returns (GetMutualFriendsResponse);
  
  // Get mutual contacts between users
  rpc GetMutualContacts(GetMutualContactsRequest) returns (GetMutualContactsResponse);
  
  // Get common bubbles between users
  rpc GetCommonBubbles(GetCommonBubblesRequest) returns (GetCommonBubblesResponse);
  
  // Get connection strength between users
  rpc GetConnectionStrength(GetConnectionStrengthRequest) returns (GetConnectionStrengthResponse);
  
  // Get social graph data
  rpc GetSocialGraph(GetSocialGraphRequest) returns (GetSocialGraphResponse);
  
  // Get engagement metrics
  rpc GetEngagementMetrics(GetEngagementMetricsRequest) returns (GetEngagementMetricsResponse);
}

// Get enhanced profile request
message GetEnhancedProfileRequest {
  string user_id = 1;
}

// Get enhanced profile response
message GetEnhancedProfileResponse {
  EnhancedProfile profile = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Enhanced profile with comprehensive relationship data
message EnhancedProfile {
  string user_id = 1;
  string username = 2;
  string email = 3;
  string first_name = 4;
  string last_name = 5;
  optional string avatar_url = 6;
  optional string bubble_id = 7;
  bool is_online = 8;
  repeated string friend_ids = 9;
  repeated string contact_ids = 10;
  repeated string blocked_user_ids = 11;
}

// Get profile analytics request
message GetProfileAnalyticsRequest {
  string user_id = 1;
  string target_user_id = 2;
}

// Get profile analytics response
message GetProfileAnalyticsResponse {
  ProfileAnalytics analytics = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Profile analytics with comprehensive data
message ProfileAnalytics {
  string user_id = 1;
  repeated string mutual_friends = 2;
  repeated string mutual_contacts = 3;
  repeated CommonBubble common_bubbles = 4;
  float connection_strength = 5;
  float social_score = 6;
  EngagementMetrics engagement_metrics = 7;
  repeated string recommended_actions = 8;
}

// Common bubble information
message CommonBubble {
  string bubble_id = 1;
  string bubble_name = 2;
  string status = 3;
  google.protobuf.Timestamp joined_at = 4;
  optional google.protobuf.Timestamp left_at = 5;
}

// Engagement metrics
message EngagementMetrics {
  int32 total_bubbles = 1;
  int32 active_bubbles = 2;
  int32 total_friends = 3;
  int32 total_contacts = 4;
  int32 messages_sent = 5;
  int32 messages_received = 6;
  float avg_response_time_minutes = 7;
  string last_active_at = 8;
  float engagement_score = 9;
}

// Get mutual friends request
message GetMutualFriendsRequest {
  string user_id = 1;
  string target_user_id = 2;
}

// Get mutual friends response
message GetMutualFriendsResponse {
  repeated string mutual_friends = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get mutual contacts request
message GetMutualContactsRequest {
  string user_id = 1;
  string target_user_id = 2;
}

// Get mutual contacts response
message GetMutualContactsResponse {
  repeated string mutual_contacts = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get common bubbles request
message GetCommonBubblesRequest {
  string user_id = 1;
  string target_user_id = 2;
}

// Get common bubbles response
message GetCommonBubblesResponse {
  repeated CommonBubble common_bubbles = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Get connection strength request
message GetConnectionStrengthRequest {
  string user_id = 1;
  string target_user_id = 2;
}

// Get connection strength response
message GetConnectionStrengthResponse {
  ConnectionStrength connection_strength = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Connection strength information
message ConnectionStrength {
  string user_id = 1;
  string target_user_id = 2;
  float strength = 3;
  int32 mutual_friends_count = 4;
  int32 mutual_contacts_count = 5;
  int32 common_bubbles_count = 6;
  float interaction_score = 7;
  repeated string factors = 8;
}

// Get social graph request
message GetSocialGraphRequest {
  string user_id = 1;
  int32 depth = 2;
  int32 limit = 3;
}

// Get social graph response
message GetSocialGraphResponse {
  SocialGraph social_graph = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}

// Social graph data
message SocialGraph {
  string user_id = 1;
  repeated SocialNode nodes = 2;
  repeated SocialEdge edges = 3;
  repeated SocialCluster clusters = 4;
  SocialGraphMetrics metrics = 5;
}

// Social node
message SocialNode {
  string user_id = 1;
  string node_type = 2;
  float centrality = 3;
  string cluster_id = 4;
}

// Social edge
message SocialEdge {
  string from_user_id = 1;
  string to_user_id = 2;
  string edge_type = 3;
  float weight = 4;
  string created_at = 5;
}

// Social cluster
message SocialCluster {
  string cluster_id = 1;
  repeated string members = 2;
  float cohesion = 3;
  string description = 4;
}

// Social graph metrics
message SocialGraphMetrics {
  int32 total_nodes = 1;
  int32 total_edges = 2;
  float density = 3;
  float clustering_coefficient = 4;
  float avg_path_length = 5;
  float centrality_score = 6;
}

// Get engagement metrics request
message GetEngagementMetricsRequest {
  string user_id = 1;
}

// Get engagement metrics response
message GetEngagementMetricsResponse {
  EngagementMetrics engagement_metrics = 1;
  hopen.common.v1.ApiResponse api_response = 2;
} 