// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: sync.proto

package sync

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Bubble request types
type BubbleRequestType int32

const (
	BubbleRequestType_BUBBLE_REQUEST_TYPE_UNSPECIFIED BubbleRequestType = 0
	BubbleRequestType_BUBBLE_REQUEST_TYPE_JOIN        BubbleRequestType = 1
	BubbleRequestType_BUBBLE_REQUEST_TYPE_INVITE      BubbleRequestType = 2
)

// Enum value maps for BubbleRequestType.
var (
	BubbleRequestType_name = map[int32]string{
		0: "BUBBLE_REQUEST_TYPE_UNSPECIFIED",
		1: "BUBBLE_REQUEST_TYPE_JOIN",
		2: "BUBBLE_REQUEST_TYPE_INVITE",
	}
	BubbleRequestType_value = map[string]int32{
		"BUBBLE_REQUEST_TYPE_UNSPECIFIED": 0,
		"BUBBLE_REQUEST_TYPE_JOIN":        1,
		"BUBBLE_REQUEST_TYPE_INVITE":      2,
	}
)

func (x BubbleRequestType) Enum() *BubbleRequestType {
	p := new(BubbleRequestType)
	*p = x
	return p
}

func (x BubbleRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BubbleRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_sync_proto_enumTypes[0].Descriptor()
}

func (BubbleRequestType) Type() protoreflect.EnumType {
	return &file_sync_proto_enumTypes[0]
}

func (x BubbleRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BubbleRequestType.Descriptor instead.
func (BubbleRequestType) EnumDescriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{0}
}

// Bubble request status
type BubbleRequestStatus int32

const (
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_UNSPECIFIED BubbleRequestStatus = 0
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_PENDING     BubbleRequestStatus = 1
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_ACCEPTED    BubbleRequestStatus = 2
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_REJECTED    BubbleRequestStatus = 3
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_CANCELLED   BubbleRequestStatus = 4
)

// Enum value maps for BubbleRequestStatus.
var (
	BubbleRequestStatus_name = map[int32]string{
		0: "BUBBLE_REQUEST_STATUS_UNSPECIFIED",
		1: "BUBBLE_REQUEST_STATUS_PENDING",
		2: "BUBBLE_REQUEST_STATUS_ACCEPTED",
		3: "BUBBLE_REQUEST_STATUS_REJECTED",
		4: "BUBBLE_REQUEST_STATUS_CANCELLED",
	}
	BubbleRequestStatus_value = map[string]int32{
		"BUBBLE_REQUEST_STATUS_UNSPECIFIED": 0,
		"BUBBLE_REQUEST_STATUS_PENDING":     1,
		"BUBBLE_REQUEST_STATUS_ACCEPTED":    2,
		"BUBBLE_REQUEST_STATUS_REJECTED":    3,
		"BUBBLE_REQUEST_STATUS_CANCELLED":   4,
	}
)

func (x BubbleRequestStatus) Enum() *BubbleRequestStatus {
	p := new(BubbleRequestStatus)
	*p = x
	return p
}

func (x BubbleRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BubbleRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_sync_proto_enumTypes[1].Descriptor()
}

func (BubbleRequestStatus) Type() protoreflect.EnumType {
	return &file_sync_proto_enumTypes[1]
}

func (x BubbleRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BubbleRequestStatus.Descriptor instead.
func (BubbleRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{1}
}

// Message types
type MessageType int32

const (
	MessageType_MESSAGE_TYPE_UNSPECIFIED MessageType = 0
	MessageType_MESSAGE_TYPE_TEXT        MessageType = 1
	MessageType_MESSAGE_TYPE_IMAGE       MessageType = 2
	MessageType_MESSAGE_TYPE_VIDEO       MessageType = 3
	MessageType_MESSAGE_TYPE_AUDIO       MessageType = 4
	MessageType_MESSAGE_TYPE_FILE        MessageType = 5
	MessageType_MESSAGE_TYPE_SYSTEM      MessageType = 6
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "MESSAGE_TYPE_UNSPECIFIED",
		1: "MESSAGE_TYPE_TEXT",
		2: "MESSAGE_TYPE_IMAGE",
		3: "MESSAGE_TYPE_VIDEO",
		4: "MESSAGE_TYPE_AUDIO",
		5: "MESSAGE_TYPE_FILE",
		6: "MESSAGE_TYPE_SYSTEM",
	}
	MessageType_value = map[string]int32{
		"MESSAGE_TYPE_UNSPECIFIED": 0,
		"MESSAGE_TYPE_TEXT":        1,
		"MESSAGE_TYPE_IMAGE":       2,
		"MESSAGE_TYPE_VIDEO":       3,
		"MESSAGE_TYPE_AUDIO":       4,
		"MESSAGE_TYPE_FILE":        5,
		"MESSAGE_TYPE_SYSTEM":      6,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_sync_proto_enumTypes[2].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_sync_proto_enumTypes[2]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{2}
}

// Sync initial state request
type SyncInitialStateRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	LastSyncAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_sync_at,json=lastSyncAt,proto3" json:"last_sync_at,omitempty"`
	IncludeDeleted bool                   `protobuf:"varint,3,opt,name=include_deleted,json=includeDeleted,proto3" json:"include_deleted,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SyncInitialStateRequest) Reset() {
	*x = SyncInitialStateRequest{}
	mi := &file_sync_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncInitialStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncInitialStateRequest) ProtoMessage() {}

func (x *SyncInitialStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncInitialStateRequest.ProtoReflect.Descriptor instead.
func (*SyncInitialStateRequest) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{0}
}

func (x *SyncInitialStateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SyncInitialStateRequest) GetLastSyncAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncAt
	}
	return nil
}

func (x *SyncInitialStateRequest) GetIncludeDeleted() bool {
	if x != nil {
		return x.IncludeDeleted
	}
	return false
}

// Sync initial state response
type SyncInitialStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *SyncData              `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncInitialStateResponse) Reset() {
	*x = SyncInitialStateResponse{}
	mi := &file_sync_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncInitialStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncInitialStateResponse) ProtoMessage() {}

func (x *SyncInitialStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncInitialStateResponse.ProtoReflect.Descriptor instead.
func (*SyncInitialStateResponse) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{1}
}

func (x *SyncInitialStateResponse) GetData() *SyncData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SyncInitialStateResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Complete sync data structure
type SyncData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// User profile
	User *common.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// User settings and preferences
	UserSettings *UserSettings `protobuf:"bytes,2,opt,name=user_settings,json=userSettings,proto3" json:"user_settings,omitempty"`
	// Active bubbles
	ActiveBubbles []*common.Bubble `protobuf:"bytes,3,rep,name=active_bubbles,json=activeBubbles,proto3" json:"active_bubbles,omitempty"`
	// Contacts
	Contacts []*common.Contact `protobuf:"bytes,4,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// Friends
	Friendships []*common.Friendship `protobuf:"bytes,5,rep,name=friendships,proto3" json:"friendships,omitempty"`
	// Pending requests
	PendingRequests *PendingRequests `protobuf:"bytes,6,opt,name=pending_requests,json=pendingRequests,proto3" json:"pending_requests,omitempty"`
	// Chat conversations
	Conversations []*Conversation `protobuf:"bytes,7,rep,name=conversations,proto3" json:"conversations,omitempty"`
	// Notifications
	Notifications []*common.Notification `protobuf:"bytes,8,rep,name=notifications,proto3" json:"notifications,omitempty"`
	// Sync metadata
	Metadata      *SyncMetadata `protobuf:"bytes,9,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncData) Reset() {
	*x = SyncData{}
	mi := &file_sync_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncData) ProtoMessage() {}

func (x *SyncData) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncData.ProtoReflect.Descriptor instead.
func (*SyncData) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{2}
}

func (x *SyncData) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *SyncData) GetUserSettings() *UserSettings {
	if x != nil {
		return x.UserSettings
	}
	return nil
}

func (x *SyncData) GetActiveBubbles() []*common.Bubble {
	if x != nil {
		return x.ActiveBubbles
	}
	return nil
}

func (x *SyncData) GetContacts() []*common.Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *SyncData) GetFriendships() []*common.Friendship {
	if x != nil {
		return x.Friendships
	}
	return nil
}

func (x *SyncData) GetPendingRequests() *PendingRequests {
	if x != nil {
		return x.PendingRequests
	}
	return nil
}

func (x *SyncData) GetConversations() []*Conversation {
	if x != nil {
		return x.Conversations
	}
	return nil
}

func (x *SyncData) GetNotifications() []*common.Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *SyncData) GetMetadata() *SyncMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// User settings
type UserSettings struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	UserId               string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	NotificationsEnabled bool                   `protobuf:"varint,2,opt,name=notifications_enabled,json=notificationsEnabled,proto3" json:"notifications_enabled,omitempty"`
	SoundEnabled         bool                   `protobuf:"varint,3,opt,name=sound_enabled,json=soundEnabled,proto3" json:"sound_enabled,omitempty"`
	VibrationEnabled     bool                   `protobuf:"varint,4,opt,name=vibration_enabled,json=vibrationEnabled,proto3" json:"vibration_enabled,omitempty"`
	Theme                string                 `protobuf:"bytes,5,opt,name=theme,proto3" json:"theme,omitempty"`
	Language             string                 `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	UpdatedAt            *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UserSettings) Reset() {
	*x = UserSettings{}
	mi := &file_sync_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSettings) ProtoMessage() {}

func (x *UserSettings) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSettings.ProtoReflect.Descriptor instead.
func (*UserSettings) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{3}
}

func (x *UserSettings) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserSettings) GetNotificationsEnabled() bool {
	if x != nil {
		return x.NotificationsEnabled
	}
	return false
}

func (x *UserSettings) GetSoundEnabled() bool {
	if x != nil {
		return x.SoundEnabled
	}
	return false
}

func (x *UserSettings) GetVibrationEnabled() bool {
	if x != nil {
		return x.VibrationEnabled
	}
	return false
}

func (x *UserSettings) GetTheme() string {
	if x != nil {
		return x.Theme
	}
	return ""
}

func (x *UserSettings) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserSettings) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Pending requests
type PendingRequests struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ContactRequests []*ContactRequest      `protobuf:"bytes,1,rep,name=contact_requests,json=contactRequests,proto3" json:"contact_requests,omitempty"`
	FriendRequests  []*FriendRequest       `protobuf:"bytes,2,rep,name=friend_requests,json=friendRequests,proto3" json:"friend_requests,omitempty"`
	BubbleRequests  []*BubbleRequest       `protobuf:"bytes,3,rep,name=bubble_requests,json=bubbleRequests,proto3" json:"bubble_requests,omitempty"`
	TotalCount      int32                  `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PendingRequests) Reset() {
	*x = PendingRequests{}
	mi := &file_sync_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PendingRequests) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PendingRequests) ProtoMessage() {}

func (x *PendingRequests) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PendingRequests.ProtoReflect.Descriptor instead.
func (*PendingRequests) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{4}
}

func (x *PendingRequests) GetContactRequests() []*ContactRequest {
	if x != nil {
		return x.ContactRequests
	}
	return nil
}

func (x *PendingRequests) GetFriendRequests() []*FriendRequest {
	if x != nil {
		return x.FriendRequests
	}
	return nil
}

func (x *PendingRequests) GetBubbleRequests() []*BubbleRequest {
	if x != nil {
		return x.BubbleRequests
	}
	return nil
}

func (x *PendingRequests) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// Contact request
type ContactRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FromUserId    string                 `protobuf:"bytes,2,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId      string                 `protobuf:"bytes,3,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	FromUser      *common.User           `protobuf:"bytes,4,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Message       string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Status        common.ContactStatus   `protobuf:"varint,6,opt,name=status,proto3,enum=hopen.common.v1.ContactStatus" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactRequest) Reset() {
	*x = ContactRequest{}
	mi := &file_sync_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactRequest) ProtoMessage() {}

func (x *ContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactRequest.ProtoReflect.Descriptor instead.
func (*ContactRequest) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{5}
}

func (x *ContactRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContactRequest) GetFromUserId() string {
	if x != nil {
		return x.FromUserId
	}
	return ""
}

func (x *ContactRequest) GetToUserId() string {
	if x != nil {
		return x.ToUserId
	}
	return ""
}

func (x *ContactRequest) GetFromUser() *common.User {
	if x != nil {
		return x.FromUser
	}
	return nil
}

func (x *ContactRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ContactRequest) GetStatus() common.ContactStatus {
	if x != nil {
		return x.Status
	}
	return common.ContactStatus(0)
}

func (x *ContactRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ContactRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Friend request
type FriendRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Id            string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FromUserId    string                  `protobuf:"bytes,2,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId      string                  `protobuf:"bytes,3,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	FromUser      *common.User            `protobuf:"bytes,4,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Message       string                  `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Status        common.FriendshipStatus `protobuf:"varint,6,opt,name=status,proto3,enum=hopen.common.v1.FriendshipStatus" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp  `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FriendRequest) Reset() {
	*x = FriendRequest{}
	mi := &file_sync_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendRequest) ProtoMessage() {}

func (x *FriendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendRequest.ProtoReflect.Descriptor instead.
func (*FriendRequest) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{6}
}

func (x *FriendRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FriendRequest) GetFromUserId() string {
	if x != nil {
		return x.FromUserId
	}
	return ""
}

func (x *FriendRequest) GetToUserId() string {
	if x != nil {
		return x.ToUserId
	}
	return ""
}

func (x *FriendRequest) GetFromUser() *common.User {
	if x != nil {
		return x.FromUser
	}
	return nil
}

func (x *FriendRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *FriendRequest) GetStatus() common.FriendshipStatus {
	if x != nil {
		return x.Status
	}
	return common.FriendshipStatus(0)
}

func (x *FriendRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FriendRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Bubble request
type BubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BubbleId      string                 `protobuf:"bytes,2,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RequesterId   string                 `protobuf:"bytes,4,opt,name=requester_id,json=requesterId,proto3" json:"requester_id,omitempty"`
	Bubble        *common.Bubble         `protobuf:"bytes,5,opt,name=bubble,proto3" json:"bubble,omitempty"`
	Requester     *common.User           `protobuf:"bytes,6,opt,name=requester,proto3" json:"requester,omitempty"`
	Type          BubbleRequestType      `protobuf:"varint,7,opt,name=type,proto3,enum=hopen.sync.v1.BubbleRequestType" json:"type,omitempty"`
	Status        BubbleRequestStatus    `protobuf:"varint,8,opt,name=status,proto3,enum=hopen.sync.v1.BubbleRequestStatus" json:"status,omitempty"`
	Message       string                 `protobuf:"bytes,9,opt,name=message,proto3" json:"message,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BubbleRequest) Reset() {
	*x = BubbleRequest{}
	mi := &file_sync_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BubbleRequest) ProtoMessage() {}

func (x *BubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BubbleRequest.ProtoReflect.Descriptor instead.
func (*BubbleRequest) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{7}
}

func (x *BubbleRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BubbleRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *BubbleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BubbleRequest) GetRequesterId() string {
	if x != nil {
		return x.RequesterId
	}
	return ""
}

func (x *BubbleRequest) GetBubble() *common.Bubble {
	if x != nil {
		return x.Bubble
	}
	return nil
}

func (x *BubbleRequest) GetRequester() *common.User {
	if x != nil {
		return x.Requester
	}
	return nil
}

func (x *BubbleRequest) GetType() BubbleRequestType {
	if x != nil {
		return x.Type
	}
	return BubbleRequestType_BUBBLE_REQUEST_TYPE_UNSPECIFIED
}

func (x *BubbleRequest) GetStatus() BubbleRequestStatus {
	if x != nil {
		return x.Status
	}
	return BubbleRequestStatus_BUBBLE_REQUEST_STATUS_UNSPECIFIED
}

func (x *BubbleRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BubbleRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BubbleRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Conversation
type Conversation struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BubbleId       string                 `protobuf:"bytes,2,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	Name           string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	ParticipantIds []string               `protobuf:"bytes,4,rep,name=participant_ids,json=participantIds,proto3" json:"participant_ids,omitempty"`
	Participants   []*common.User         `protobuf:"bytes,5,rep,name=participants,proto3" json:"participants,omitempty"`
	LastMessage    *Message               `protobuf:"bytes,6,opt,name=last_message,json=lastMessage,proto3" json:"last_message,omitempty"`
	UnreadCount    int32                  `protobuf:"varint,7,opt,name=unread_count,json=unreadCount,proto3" json:"unread_count,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Conversation) Reset() {
	*x = Conversation{}
	mi := &file_sync_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Conversation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Conversation) ProtoMessage() {}

func (x *Conversation) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Conversation.ProtoReflect.Descriptor instead.
func (*Conversation) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{8}
}

func (x *Conversation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Conversation) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *Conversation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Conversation) GetParticipantIds() []string {
	if x != nil {
		return x.ParticipantIds
	}
	return nil
}

func (x *Conversation) GetParticipants() []*common.User {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *Conversation) GetLastMessage() *Message {
	if x != nil {
		return x.LastMessage
	}
	return nil
}

func (x *Conversation) GetUnreadCount() int32 {
	if x != nil {
		return x.UnreadCount
	}
	return 0
}

func (x *Conversation) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Conversation) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Message
type Message struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ConversationId string                 `protobuf:"bytes,2,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	SenderId       string                 `protobuf:"bytes,3,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	Sender         *common.User           `protobuf:"bytes,4,opt,name=sender,proto3" json:"sender,omitempty"`
	Content        string                 `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Type           MessageType            `protobuf:"varint,6,opt,name=type,proto3,enum=hopen.sync.v1.MessageType" json:"type,omitempty"`
	Metadata       map[string]string      `protobuf:"bytes,7,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IsRead         bool                   `protobuf:"varint,8,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_sync_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{9}
}

func (x *Message) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Message) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *Message) GetSenderId() string {
	if x != nil {
		return x.SenderId
	}
	return ""
}

func (x *Message) GetSender() *common.User {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *Message) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Message) GetType() MessageType {
	if x != nil {
		return x.Type
	}
	return MessageType_MESSAGE_TYPE_UNSPECIFIED
}

func (x *Message) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Message) GetIsRead() bool {
	if x != nil {
		return x.IsRead
	}
	return false
}

func (x *Message) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Message) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Sync metadata
type SyncMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SyncTimestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=sync_timestamp,json=syncTimestamp,proto3" json:"sync_timestamp,omitempty"`
	SyncVersion   string                 `protobuf:"bytes,2,opt,name=sync_version,json=syncVersion,proto3" json:"sync_version,omitempty"`
	HasMoreData   bool                   `protobuf:"varint,3,opt,name=has_more_data,json=hasMoreData,proto3" json:"has_more_data,omitempty"`
	TotalItems    int32                  `protobuf:"varint,4,opt,name=total_items,json=totalItems,proto3" json:"total_items,omitempty"`
	ItemCounts    map[string]int32       `protobuf:"bytes,5,rep,name=item_counts,json=itemCounts,proto3" json:"item_counts,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncMetadata) Reset() {
	*x = SyncMetadata{}
	mi := &file_sync_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMetadata) ProtoMessage() {}

func (x *SyncMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMetadata.ProtoReflect.Descriptor instead.
func (*SyncMetadata) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{10}
}

func (x *SyncMetadata) GetSyncTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.SyncTimestamp
	}
	return nil
}

func (x *SyncMetadata) GetSyncVersion() string {
	if x != nil {
		return x.SyncVersion
	}
	return ""
}

func (x *SyncMetadata) GetHasMoreData() bool {
	if x != nil {
		return x.HasMoreData
	}
	return false
}

func (x *SyncMetadata) GetTotalItems() int32 {
	if x != nil {
		return x.TotalItems
	}
	return 0
}

func (x *SyncMetadata) GetItemCounts() map[string]int32 {
	if x != nil {
		return x.ItemCounts
	}
	return nil
}

// Get sync status request
type GetSyncStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSyncStatusRequest) Reset() {
	*x = GetSyncStatusRequest{}
	mi := &file_sync_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSyncStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSyncStatusRequest) ProtoMessage() {}

func (x *GetSyncStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSyncStatusRequest.ProtoReflect.Descriptor instead.
func (*GetSyncStatusRequest) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{11}
}

func (x *GetSyncStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get sync status response
type GetSyncStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *SyncStatus            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSyncStatusResponse) Reset() {
	*x = GetSyncStatusResponse{}
	mi := &file_sync_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSyncStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSyncStatusResponse) ProtoMessage() {}

func (x *GetSyncStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSyncStatusResponse.ProtoReflect.Descriptor instead.
func (*GetSyncStatusResponse) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{12}
}

func (x *GetSyncStatusResponse) GetStatus() *SyncStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSyncStatusResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Sync status
type SyncStatus struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	LastSyncAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_sync_at,json=lastSyncAt,proto3" json:"last_sync_at,omitempty"`
	SyncVersion    string                 `protobuf:"bytes,3,opt,name=sync_version,json=syncVersion,proto3" json:"sync_version,omitempty"`
	IsSyncing      bool                   `protobuf:"varint,4,opt,name=is_syncing,json=isSyncing,proto3" json:"is_syncing,omitempty"`
	SyncStatus     string                 `protobuf:"bytes,5,opt,name=sync_status,json=syncStatus,proto3" json:"sync_status,omitempty"`
	PendingChanges int32                  `protobuf:"varint,6,opt,name=pending_changes,json=pendingChanges,proto3" json:"pending_changes,omitempty"`
	NextSyncAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=next_sync_at,json=nextSyncAt,proto3" json:"next_sync_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SyncStatus) Reset() {
	*x = SyncStatus{}
	mi := &file_sync_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStatus) ProtoMessage() {}

func (x *SyncStatus) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStatus.ProtoReflect.Descriptor instead.
func (*SyncStatus) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{13}
}

func (x *SyncStatus) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SyncStatus) GetLastSyncAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncAt
	}
	return nil
}

func (x *SyncStatus) GetSyncVersion() string {
	if x != nil {
		return x.SyncVersion
	}
	return ""
}

func (x *SyncStatus) GetIsSyncing() bool {
	if x != nil {
		return x.IsSyncing
	}
	return false
}

func (x *SyncStatus) GetSyncStatus() string {
	if x != nil {
		return x.SyncStatus
	}
	return ""
}

func (x *SyncStatus) GetPendingChanges() int32 {
	if x != nil {
		return x.PendingChanges
	}
	return 0
}

func (x *SyncStatus) GetNextSyncAt() *timestamppb.Timestamp {
	if x != nil {
		return x.NextSyncAt
	}
	return nil
}

// Force sync refresh request
type ForceSyncRefreshRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FullSync      bool                   `protobuf:"varint,2,opt,name=full_sync,json=fullSync,proto3" json:"full_sync,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForceSyncRefreshRequest) Reset() {
	*x = ForceSyncRefreshRequest{}
	mi := &file_sync_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForceSyncRefreshRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceSyncRefreshRequest) ProtoMessage() {}

func (x *ForceSyncRefreshRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceSyncRefreshRequest.ProtoReflect.Descriptor instead.
func (*ForceSyncRefreshRequest) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{14}
}

func (x *ForceSyncRefreshRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ForceSyncRefreshRequest) GetFullSync() bool {
	if x != nil {
		return x.FullSync
	}
	return false
}

// Force sync refresh response
type ForceSyncRefreshResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *SyncStatus            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForceSyncRefreshResponse) Reset() {
	*x = ForceSyncRefreshResponse{}
	mi := &file_sync_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForceSyncRefreshResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceSyncRefreshResponse) ProtoMessage() {}

func (x *ForceSyncRefreshResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceSyncRefreshResponse.ProtoReflect.Descriptor instead.
func (*ForceSyncRefreshResponse) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{15}
}

func (x *ForceSyncRefreshResponse) GetStatus() *SyncStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ForceSyncRefreshResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

var File_sync_proto protoreflect.FileDescriptor

const file_sync_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"sync.proto\x12\rhopen.sync.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"\x99\x01\n" +
	"\x17SyncInitialStateRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12<\n" +
	"\flast_sync_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastSyncAt\x12'\n" +
	"\x0finclude_deleted\x18\x03 \x01(\bR\x0eincludeDeleted\"\x88\x01\n" +
	"\x18SyncInitialStateResponse\x12+\n" +
	"\x04data\x18\x01 \x01(\v2\x17.hopen.sync.v1.SyncDataR\x04data\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xb8\x04\n" +
	"\bSyncData\x12)\n" +
	"\x04user\x18\x01 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x12@\n" +
	"\ruser_settings\x18\x02 \x01(\v2\x1b.hopen.sync.v1.UserSettingsR\fuserSettings\x12>\n" +
	"\x0eactive_bubbles\x18\x03 \x03(\v2\x17.hopen.common.v1.BubbleR\ractiveBubbles\x124\n" +
	"\bcontacts\x18\x04 \x03(\v2\x18.hopen.common.v1.ContactR\bcontacts\x12=\n" +
	"\vfriendships\x18\x05 \x03(\v2\x1b.hopen.common.v1.FriendshipR\vfriendships\x12I\n" +
	"\x10pending_requests\x18\x06 \x01(\v2\x1e.hopen.sync.v1.PendingRequestsR\x0fpendingRequests\x12A\n" +
	"\rconversations\x18\a \x03(\v2\x1b.hopen.sync.v1.ConversationR\rconversations\x12C\n" +
	"\rnotifications\x18\b \x03(\v2\x1d.hopen.common.v1.NotificationR\rnotifications\x127\n" +
	"\bmetadata\x18\t \x01(\v2\x1b.hopen.sync.v1.SyncMetadataR\bmetadata\"\x9b\x02\n" +
	"\fUserSettings\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x123\n" +
	"\x15notifications_enabled\x18\x02 \x01(\bR\x14notificationsEnabled\x12#\n" +
	"\rsound_enabled\x18\x03 \x01(\bR\fsoundEnabled\x12+\n" +
	"\x11vibration_enabled\x18\x04 \x01(\bR\x10vibrationEnabled\x12\x14\n" +
	"\x05theme\x18\x05 \x01(\tR\x05theme\x12\x1a\n" +
	"\blanguage\x18\x06 \x01(\tR\blanguage\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x8a\x02\n" +
	"\x0fPendingRequests\x12H\n" +
	"\x10contact_requests\x18\x01 \x03(\v2\x1d.hopen.sync.v1.ContactRequestR\x0fcontactRequests\x12E\n" +
	"\x0ffriend_requests\x18\x02 \x03(\v2\x1c.hopen.sync.v1.FriendRequestR\x0efriendRequests\x12E\n" +
	"\x0fbubble_requests\x18\x03 \x03(\v2\x1c.hopen.sync.v1.BubbleRequestR\x0ebubbleRequests\x12\x1f\n" +
	"\vtotal_count\x18\x04 \x01(\x05R\n" +
	"totalCount\"\xdc\x02\n" +
	"\x0eContactRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12 \n" +
	"\ffrom_user_id\x18\x02 \x01(\tR\n" +
	"fromUserId\x12\x1c\n" +
	"\n" +
	"to_user_id\x18\x03 \x01(\tR\btoUserId\x122\n" +
	"\tfrom_user\x18\x04 \x01(\v2\x15.hopen.common.v1.UserR\bfromUser\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x126\n" +
	"\x06status\x18\x06 \x01(\x0e2\x1e.hopen.common.v1.ContactStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xde\x02\n" +
	"\rFriendRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12 \n" +
	"\ffrom_user_id\x18\x02 \x01(\tR\n" +
	"fromUserId\x12\x1c\n" +
	"\n" +
	"to_user_id\x18\x03 \x01(\tR\btoUserId\x122\n" +
	"\tfrom_user\x18\x04 \x01(\v2\x15.hopen.common.v1.UserR\bfromUser\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x129\n" +
	"\x06status\x18\x06 \x01(\x0e2!.hopen.common.v1.FriendshipStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xe0\x03\n" +
	"\rBubbleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1b\n" +
	"\tbubble_id\x18\x02 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12!\n" +
	"\frequester_id\x18\x04 \x01(\tR\vrequesterId\x12/\n" +
	"\x06bubble\x18\x05 \x01(\v2\x17.hopen.common.v1.BubbleR\x06bubble\x123\n" +
	"\trequester\x18\x06 \x01(\v2\x15.hopen.common.v1.UserR\trequester\x124\n" +
	"\x04type\x18\a \x01(\x0e2 .hopen.sync.v1.BubbleRequestTypeR\x04type\x12:\n" +
	"\x06status\x18\b \x01(\x0e2\".hopen.sync.v1.BubbleRequestStatusR\x06status\x12\x18\n" +
	"\amessage\x18\t \x01(\tR\amessage\x129\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x87\x03\n" +
	"\fConversation\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1b\n" +
	"\tbubble_id\x18\x02 \x01(\tR\bbubbleId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12'\n" +
	"\x0fparticipant_ids\x18\x04 \x03(\tR\x0eparticipantIds\x129\n" +
	"\fparticipants\x18\x05 \x03(\v2\x15.hopen.common.v1.UserR\fparticipants\x129\n" +
	"\flast_message\x18\x06 \x01(\v2\x16.hopen.sync.v1.MessageR\vlastMessage\x12!\n" +
	"\funread_count\x18\a \x01(\x05R\vunreadCount\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xe6\x03\n" +
	"\aMessage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12'\n" +
	"\x0fconversation_id\x18\x02 \x01(\tR\x0econversationId\x12\x1b\n" +
	"\tsender_id\x18\x03 \x01(\tR\bsenderId\x12-\n" +
	"\x06sender\x18\x04 \x01(\v2\x15.hopen.common.v1.UserR\x06sender\x12\x18\n" +
	"\acontent\x18\x05 \x01(\tR\acontent\x12.\n" +
	"\x04type\x18\x06 \x01(\x0e2\x1a.hopen.sync.v1.MessageTypeR\x04type\x12@\n" +
	"\bmetadata\x18\a \x03(\v2$.hopen.sync.v1.Message.MetadataEntryR\bmetadata\x12\x17\n" +
	"\ais_read\x18\b \x01(\bR\x06isRead\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xc6\x02\n" +
	"\fSyncMetadata\x12A\n" +
	"\x0esync_timestamp\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\rsyncTimestamp\x12!\n" +
	"\fsync_version\x18\x02 \x01(\tR\vsyncVersion\x12\"\n" +
	"\rhas_more_data\x18\x03 \x01(\bR\vhasMoreData\x12\x1f\n" +
	"\vtotal_items\x18\x04 \x01(\x05R\n" +
	"totalItems\x12L\n" +
	"\vitem_counts\x18\x05 \x03(\v2+.hopen.sync.v1.SyncMetadata.ItemCountsEntryR\n" +
	"itemCounts\x1a=\n" +
	"\x0fItemCountsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"/\n" +
	"\x14GetSyncStatusRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x8b\x01\n" +
	"\x15GetSyncStatusResponse\x121\n" +
	"\x06status\x18\x01 \x01(\v2\x19.hopen.sync.v1.SyncStatusR\x06status\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xad\x02\n" +
	"\n" +
	"SyncStatus\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12<\n" +
	"\flast_sync_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastSyncAt\x12!\n" +
	"\fsync_version\x18\x03 \x01(\tR\vsyncVersion\x12\x1d\n" +
	"\n" +
	"is_syncing\x18\x04 \x01(\bR\tisSyncing\x12\x1f\n" +
	"\vsync_status\x18\x05 \x01(\tR\n" +
	"syncStatus\x12'\n" +
	"\x0fpending_changes\x18\x06 \x01(\x05R\x0ependingChanges\x12<\n" +
	"\fnext_sync_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"nextSyncAt\"O\n" +
	"\x17ForceSyncRefreshRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfull_sync\x18\x02 \x01(\bR\bfullSync\"\x8e\x01\n" +
	"\x18ForceSyncRefreshResponse\x121\n" +
	"\x06status\x18\x01 \x01(\v2\x19.hopen.sync.v1.SyncStatusR\x06status\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse*v\n" +
	"\x11BubbleRequestType\x12#\n" +
	"\x1fBUBBLE_REQUEST_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18BUBBLE_REQUEST_TYPE_JOIN\x10\x01\x12\x1e\n" +
	"\x1aBUBBLE_REQUEST_TYPE_INVITE\x10\x02*\xcc\x01\n" +
	"\x13BubbleRequestStatus\x12%\n" +
	"!BUBBLE_REQUEST_STATUS_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dBUBBLE_REQUEST_STATUS_PENDING\x10\x01\x12\"\n" +
	"\x1eBUBBLE_REQUEST_STATUS_ACCEPTED\x10\x02\x12\"\n" +
	"\x1eBUBBLE_REQUEST_STATUS_REJECTED\x10\x03\x12#\n" +
	"\x1fBUBBLE_REQUEST_STATUS_CANCELLED\x10\x04*\xba\x01\n" +
	"\vMessageType\x12\x1c\n" +
	"\x18MESSAGE_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11MESSAGE_TYPE_TEXT\x10\x01\x12\x16\n" +
	"\x12MESSAGE_TYPE_IMAGE\x10\x02\x12\x16\n" +
	"\x12MESSAGE_TYPE_VIDEO\x10\x03\x12\x16\n" +
	"\x12MESSAGE_TYPE_AUDIO\x10\x04\x12\x15\n" +
	"\x11MESSAGE_TYPE_FILE\x10\x05\x12\x17\n" +
	"\x13MESSAGE_TYPE_SYSTEM\x10\x062\xb3\x02\n" +
	"\vSyncService\x12c\n" +
	"\x10SyncInitialState\x12&.hopen.sync.v1.SyncInitialStateRequest\x1a'.hopen.sync.v1.SyncInitialStateResponse\x12Z\n" +
	"\rGetSyncStatus\x12#.hopen.sync.v1.GetSyncStatusRequest\x1a$.hopen.sync.v1.GetSyncStatusResponse\x12c\n" +
	"\x10ForceSyncRefresh\x12&.hopen.sync.v1.ForceSyncRefreshRequest\x1a'.hopen.sync.v1.ForceSyncRefreshResponseB8\n" +
	"\x11com.hopen.sync.v1P\x01Z!hopenbackend/protos/gen/sync;syncb\x06proto3"

var (
	file_sync_proto_rawDescOnce sync.Once
	file_sync_proto_rawDescData []byte
)

func file_sync_proto_rawDescGZIP() []byte {
	file_sync_proto_rawDescOnce.Do(func() {
		file_sync_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_sync_proto_rawDesc), len(file_sync_proto_rawDesc)))
	})
	return file_sync_proto_rawDescData
}

var file_sync_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_sync_proto_goTypes = []any{
	(BubbleRequestType)(0),           // 0: hopen.sync.v1.BubbleRequestType
	(BubbleRequestStatus)(0),         // 1: hopen.sync.v1.BubbleRequestStatus
	(MessageType)(0),                 // 2: hopen.sync.v1.MessageType
	(*SyncInitialStateRequest)(nil),  // 3: hopen.sync.v1.SyncInitialStateRequest
	(*SyncInitialStateResponse)(nil), // 4: hopen.sync.v1.SyncInitialStateResponse
	(*SyncData)(nil),                 // 5: hopen.sync.v1.SyncData
	(*UserSettings)(nil),             // 6: hopen.sync.v1.UserSettings
	(*PendingRequests)(nil),          // 7: hopen.sync.v1.PendingRequests
	(*ContactRequest)(nil),           // 8: hopen.sync.v1.ContactRequest
	(*FriendRequest)(nil),            // 9: hopen.sync.v1.FriendRequest
	(*BubbleRequest)(nil),            // 10: hopen.sync.v1.BubbleRequest
	(*Conversation)(nil),             // 11: hopen.sync.v1.Conversation
	(*Message)(nil),                  // 12: hopen.sync.v1.Message
	(*SyncMetadata)(nil),             // 13: hopen.sync.v1.SyncMetadata
	(*GetSyncStatusRequest)(nil),     // 14: hopen.sync.v1.GetSyncStatusRequest
	(*GetSyncStatusResponse)(nil),    // 15: hopen.sync.v1.GetSyncStatusResponse
	(*SyncStatus)(nil),               // 16: hopen.sync.v1.SyncStatus
	(*ForceSyncRefreshRequest)(nil),  // 17: hopen.sync.v1.ForceSyncRefreshRequest
	(*ForceSyncRefreshResponse)(nil), // 18: hopen.sync.v1.ForceSyncRefreshResponse
	nil,                              // 19: hopen.sync.v1.Message.MetadataEntry
	nil,                              // 20: hopen.sync.v1.SyncMetadata.ItemCountsEntry
	(*timestamppb.Timestamp)(nil),    // 21: google.protobuf.Timestamp
	(*common.ApiResponse)(nil),       // 22: hopen.common.v1.ApiResponse
	(*common.User)(nil),              // 23: hopen.common.v1.User
	(*common.Bubble)(nil),            // 24: hopen.common.v1.Bubble
	(*common.Contact)(nil),           // 25: hopen.common.v1.Contact
	(*common.Friendship)(nil),        // 26: hopen.common.v1.Friendship
	(*common.Notification)(nil),      // 27: hopen.common.v1.Notification
	(common.ContactStatus)(0),        // 28: hopen.common.v1.ContactStatus
	(common.FriendshipStatus)(0),     // 29: hopen.common.v1.FriendshipStatus
}
var file_sync_proto_depIdxs = []int32{
	21, // 0: hopen.sync.v1.SyncInitialStateRequest.last_sync_at:type_name -> google.protobuf.Timestamp
	5,  // 1: hopen.sync.v1.SyncInitialStateResponse.data:type_name -> hopen.sync.v1.SyncData
	22, // 2: hopen.sync.v1.SyncInitialStateResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	23, // 3: hopen.sync.v1.SyncData.user:type_name -> hopen.common.v1.User
	6,  // 4: hopen.sync.v1.SyncData.user_settings:type_name -> hopen.sync.v1.UserSettings
	24, // 5: hopen.sync.v1.SyncData.active_bubbles:type_name -> hopen.common.v1.Bubble
	25, // 6: hopen.sync.v1.SyncData.contacts:type_name -> hopen.common.v1.Contact
	26, // 7: hopen.sync.v1.SyncData.friendships:type_name -> hopen.common.v1.Friendship
	7,  // 8: hopen.sync.v1.SyncData.pending_requests:type_name -> hopen.sync.v1.PendingRequests
	11, // 9: hopen.sync.v1.SyncData.conversations:type_name -> hopen.sync.v1.Conversation
	27, // 10: hopen.sync.v1.SyncData.notifications:type_name -> hopen.common.v1.Notification
	13, // 11: hopen.sync.v1.SyncData.metadata:type_name -> hopen.sync.v1.SyncMetadata
	21, // 12: hopen.sync.v1.UserSettings.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 13: hopen.sync.v1.PendingRequests.contact_requests:type_name -> hopen.sync.v1.ContactRequest
	9,  // 14: hopen.sync.v1.PendingRequests.friend_requests:type_name -> hopen.sync.v1.FriendRequest
	10, // 15: hopen.sync.v1.PendingRequests.bubble_requests:type_name -> hopen.sync.v1.BubbleRequest
	23, // 16: hopen.sync.v1.ContactRequest.from_user:type_name -> hopen.common.v1.User
	28, // 17: hopen.sync.v1.ContactRequest.status:type_name -> hopen.common.v1.ContactStatus
	21, // 18: hopen.sync.v1.ContactRequest.created_at:type_name -> google.protobuf.Timestamp
	21, // 19: hopen.sync.v1.ContactRequest.updated_at:type_name -> google.protobuf.Timestamp
	23, // 20: hopen.sync.v1.FriendRequest.from_user:type_name -> hopen.common.v1.User
	29, // 21: hopen.sync.v1.FriendRequest.status:type_name -> hopen.common.v1.FriendshipStatus
	21, // 22: hopen.sync.v1.FriendRequest.created_at:type_name -> google.protobuf.Timestamp
	21, // 23: hopen.sync.v1.FriendRequest.updated_at:type_name -> google.protobuf.Timestamp
	24, // 24: hopen.sync.v1.BubbleRequest.bubble:type_name -> hopen.common.v1.Bubble
	23, // 25: hopen.sync.v1.BubbleRequest.requester:type_name -> hopen.common.v1.User
	0,  // 26: hopen.sync.v1.BubbleRequest.type:type_name -> hopen.sync.v1.BubbleRequestType
	1,  // 27: hopen.sync.v1.BubbleRequest.status:type_name -> hopen.sync.v1.BubbleRequestStatus
	21, // 28: hopen.sync.v1.BubbleRequest.created_at:type_name -> google.protobuf.Timestamp
	21, // 29: hopen.sync.v1.BubbleRequest.updated_at:type_name -> google.protobuf.Timestamp
	23, // 30: hopen.sync.v1.Conversation.participants:type_name -> hopen.common.v1.User
	12, // 31: hopen.sync.v1.Conversation.last_message:type_name -> hopen.sync.v1.Message
	21, // 32: hopen.sync.v1.Conversation.created_at:type_name -> google.protobuf.Timestamp
	21, // 33: hopen.sync.v1.Conversation.updated_at:type_name -> google.protobuf.Timestamp
	23, // 34: hopen.sync.v1.Message.sender:type_name -> hopen.common.v1.User
	2,  // 35: hopen.sync.v1.Message.type:type_name -> hopen.sync.v1.MessageType
	19, // 36: hopen.sync.v1.Message.metadata:type_name -> hopen.sync.v1.Message.MetadataEntry
	21, // 37: hopen.sync.v1.Message.created_at:type_name -> google.protobuf.Timestamp
	21, // 38: hopen.sync.v1.Message.updated_at:type_name -> google.protobuf.Timestamp
	21, // 39: hopen.sync.v1.SyncMetadata.sync_timestamp:type_name -> google.protobuf.Timestamp
	20, // 40: hopen.sync.v1.SyncMetadata.item_counts:type_name -> hopen.sync.v1.SyncMetadata.ItemCountsEntry
	16, // 41: hopen.sync.v1.GetSyncStatusResponse.status:type_name -> hopen.sync.v1.SyncStatus
	22, // 42: hopen.sync.v1.GetSyncStatusResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	21, // 43: hopen.sync.v1.SyncStatus.last_sync_at:type_name -> google.protobuf.Timestamp
	21, // 44: hopen.sync.v1.SyncStatus.next_sync_at:type_name -> google.protobuf.Timestamp
	16, // 45: hopen.sync.v1.ForceSyncRefreshResponse.status:type_name -> hopen.sync.v1.SyncStatus
	22, // 46: hopen.sync.v1.ForceSyncRefreshResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	3,  // 47: hopen.sync.v1.SyncService.SyncInitialState:input_type -> hopen.sync.v1.SyncInitialStateRequest
	14, // 48: hopen.sync.v1.SyncService.GetSyncStatus:input_type -> hopen.sync.v1.GetSyncStatusRequest
	17, // 49: hopen.sync.v1.SyncService.ForceSyncRefresh:input_type -> hopen.sync.v1.ForceSyncRefreshRequest
	4,  // 50: hopen.sync.v1.SyncService.SyncInitialState:output_type -> hopen.sync.v1.SyncInitialStateResponse
	15, // 51: hopen.sync.v1.SyncService.GetSyncStatus:output_type -> hopen.sync.v1.GetSyncStatusResponse
	18, // 52: hopen.sync.v1.SyncService.ForceSyncRefresh:output_type -> hopen.sync.v1.ForceSyncRefreshResponse
	50, // [50:53] is the sub-list for method output_type
	47, // [47:50] is the sub-list for method input_type
	47, // [47:47] is the sub-list for extension type_name
	47, // [47:47] is the sub-list for extension extendee
	0,  // [0:47] is the sub-list for field type_name
}

func init() { file_sync_proto_init() }
func file_sync_proto_init() {
	if File_sync_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_sync_proto_rawDesc), len(file_sync_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sync_proto_goTypes,
		DependencyIndexes: file_sync_proto_depIdxs,
		EnumInfos:         file_sync_proto_enumTypes,
		MessageInfos:      file_sync_proto_msgTypes,
	}.Build()
	File_sync_proto = out.File
	file_sync_proto_goTypes = nil
	file_sync_proto_depIdxs = nil
}
