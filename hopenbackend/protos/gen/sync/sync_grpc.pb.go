// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: sync.proto

package sync

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SyncService_SyncInitialState_FullMethodName = "/hopen.sync.v1.SyncService/SyncInitialState"
	SyncService_GetSyncStatus_FullMethodName    = "/hopen.sync.v1.SyncService/GetSyncStatus"
	SyncService_ForceSyncRefresh_FullMethodName = "/hopen.sync.v1.SyncService/ForceSyncRefresh"
)

// SyncServiceClient is the client API for SyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Sync service for initial data synchronization
type SyncServiceClient interface {
	// Get complete initial state for authenticated user
	SyncInitialState(ctx context.Context, in *SyncInitialStateRequest, opts ...grpc.CallOption) (*SyncInitialStateResponse, error)
	// Get sync status
	GetSyncStatus(ctx context.Context, in *GetSyncStatusRequest, opts ...grpc.CallOption) (*GetSyncStatusResponse, error)
	// Force sync refresh
	ForceSyncRefresh(ctx context.Context, in *ForceSyncRefreshRequest, opts ...grpc.CallOption) (*ForceSyncRefreshResponse, error)
}

type syncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSyncServiceClient(cc grpc.ClientConnInterface) SyncServiceClient {
	return &syncServiceClient{cc}
}

func (c *syncServiceClient) SyncInitialState(ctx context.Context, in *SyncInitialStateRequest, opts ...grpc.CallOption) (*SyncInitialStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncInitialStateResponse)
	err := c.cc.Invoke(ctx, SyncService_SyncInitialState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncServiceClient) GetSyncStatus(ctx context.Context, in *GetSyncStatusRequest, opts ...grpc.CallOption) (*GetSyncStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSyncStatusResponse)
	err := c.cc.Invoke(ctx, SyncService_GetSyncStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncServiceClient) ForceSyncRefresh(ctx context.Context, in *ForceSyncRefreshRequest, opts ...grpc.CallOption) (*ForceSyncRefreshResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ForceSyncRefreshResponse)
	err := c.cc.Invoke(ctx, SyncService_ForceSyncRefresh_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SyncServiceServer is the server API for SyncService service.
// All implementations must embed UnimplementedSyncServiceServer
// for forward compatibility.
//
// Sync service for initial data synchronization
type SyncServiceServer interface {
	// Get complete initial state for authenticated user
	SyncInitialState(context.Context, *SyncInitialStateRequest) (*SyncInitialStateResponse, error)
	// Get sync status
	GetSyncStatus(context.Context, *GetSyncStatusRequest) (*GetSyncStatusResponse, error)
	// Force sync refresh
	ForceSyncRefresh(context.Context, *ForceSyncRefreshRequest) (*ForceSyncRefreshResponse, error)
	mustEmbedUnimplementedSyncServiceServer()
}

// UnimplementedSyncServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSyncServiceServer struct{}

func (UnimplementedSyncServiceServer) SyncInitialState(context.Context, *SyncInitialStateRequest) (*SyncInitialStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncInitialState not implemented")
}
func (UnimplementedSyncServiceServer) GetSyncStatus(context.Context, *GetSyncStatusRequest) (*GetSyncStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncStatus not implemented")
}
func (UnimplementedSyncServiceServer) ForceSyncRefresh(context.Context, *ForceSyncRefreshRequest) (*ForceSyncRefreshResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceSyncRefresh not implemented")
}
func (UnimplementedSyncServiceServer) mustEmbedUnimplementedSyncServiceServer() {}
func (UnimplementedSyncServiceServer) testEmbeddedByValue()                     {}

// UnsafeSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SyncServiceServer will
// result in compilation errors.
type UnsafeSyncServiceServer interface {
	mustEmbedUnimplementedSyncServiceServer()
}

func RegisterSyncServiceServer(s grpc.ServiceRegistrar, srv SyncServiceServer) {
	// If the following call pancis, it indicates UnimplementedSyncServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SyncService_ServiceDesc, srv)
}

func _SyncService_SyncInitialState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncInitialStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServiceServer).SyncInitialState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncService_SyncInitialState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServiceServer).SyncInitialState(ctx, req.(*SyncInitialStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncService_GetSyncStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServiceServer).GetSyncStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncService_GetSyncStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServiceServer).GetSyncStatus(ctx, req.(*GetSyncStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncService_ForceSyncRefresh_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForceSyncRefreshRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServiceServer).ForceSyncRefresh(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncService_ForceSyncRefresh_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServiceServer).ForceSyncRefresh(ctx, req.(*ForceSyncRefreshRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SyncService_ServiceDesc is the grpc.ServiceDesc for SyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.sync.v1.SyncService",
	HandlerType: (*SyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncInitialState",
			Handler:    _SyncService_SyncInitialState_Handler,
		},
		{
			MethodName: "GetSyncStatus",
			Handler:    _SyncService_GetSyncStatus_Handler,
		},
		{
			MethodName: "ForceSyncRefresh",
			Handler:    _SyncService_ForceSyncRefresh_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sync.proto",
}
