// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: bubble.proto

package bubble

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BubbleService_CreateBubble_FullMethodName        = "/hopen.bubble.v1.BubbleService/CreateBubble"
	BubbleService_GetBubble_FullMethodName           = "/hopen.bubble.v1.BubbleService/GetBubble"
	BubbleService_UpdateBubble_FullMethodName        = "/hopen.bubble.v1.BubbleService/UpdateBubble"
	BubbleService_DeleteBubble_FullMethodName        = "/hopen.bubble.v1.BubbleService/DeleteBubble"
	BubbleService_JoinBubble_FullMethodName          = "/hopen.bubble.v1.BubbleService/JoinBubble"
	BubbleService_LeaveBubble_FullMethodName         = "/hopen.bubble.v1.BubbleService/LeaveBubble"
	BubbleService_KickMember_FullMethodName          = "/hopen.bubble.v1.BubbleService/KickMember"
	BubbleService_GetBubbleMembers_FullMethodName    = "/hopen.bubble.v1.BubbleService/GetBubbleMembers"
	BubbleService_GetUserBubbles_FullMethodName      = "/hopen.bubble.v1.BubbleService/GetUserBubbles"
	BubbleService_GetBubbleRequests_FullMethodName   = "/hopen.bubble.v1.BubbleService/GetBubbleRequests"
	BubbleService_AcceptBubbleRequest_FullMethodName = "/hopen.bubble.v1.BubbleService/AcceptBubbleRequest"
	BubbleService_RejectBubbleRequest_FullMethodName = "/hopen.bubble.v1.BubbleService/RejectBubbleRequest"
	BubbleService_SendBubbleInvite_FullMethodName    = "/hopen.bubble.v1.BubbleService/SendBubbleInvite"
	BubbleService_GetBubbleAnalytics_FullMethodName  = "/hopen.bubble.v1.BubbleService/GetBubbleAnalytics"
)

// BubbleServiceClient is the client API for BubbleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Bubble service for bubble management and membership operations
type BubbleServiceClient interface {
	// Create a new bubble
	CreateBubble(ctx context.Context, in *CreateBubbleRequest, opts ...grpc.CallOption) (*CreateBubbleResponse, error)
	// Get bubble by ID
	GetBubble(ctx context.Context, in *GetBubbleRequest, opts ...grpc.CallOption) (*GetBubbleResponse, error)
	// Update bubble information
	UpdateBubble(ctx context.Context, in *UpdateBubbleRequest, opts ...grpc.CallOption) (*UpdateBubbleResponse, error)
	// Delete bubble
	DeleteBubble(ctx context.Context, in *DeleteBubbleRequest, opts ...grpc.CallOption) (*DeleteBubbleResponse, error)
	// Join bubble
	JoinBubble(ctx context.Context, in *JoinBubbleRequest, opts ...grpc.CallOption) (*JoinBubbleResponse, error)
	// Leave bubble
	LeaveBubble(ctx context.Context, in *LeaveBubbleRequest, opts ...grpc.CallOption) (*LeaveBubbleResponse, error)
	// Kick member from bubble
	KickMember(ctx context.Context, in *KickMemberRequest, opts ...grpc.CallOption) (*KickMemberResponse, error)
	// Get bubble members
	GetBubbleMembers(ctx context.Context, in *GetBubbleMembersRequest, opts ...grpc.CallOption) (*GetBubbleMembersResponse, error)
	// Get user's active bubbles
	GetUserBubbles(ctx context.Context, in *GetUserBubblesRequest, opts ...grpc.CallOption) (*GetUserBubblesResponse, error)
	// Get bubble requests (join/invite)
	GetBubbleRequests(ctx context.Context, in *GetBubbleRequestsRequest, opts ...grpc.CallOption) (*GetBubbleRequestsResponse, error)
	// Accept bubble request
	AcceptBubbleRequest(ctx context.Context, in *AcceptBubbleRequestRequest, opts ...grpc.CallOption) (*AcceptBubbleRequestResponse, error)
	// Reject bubble request
	RejectBubbleRequest(ctx context.Context, in *RejectBubbleRequestRequest, opts ...grpc.CallOption) (*RejectBubbleRequestResponse, error)
	// Send bubble invite
	SendBubbleInvite(ctx context.Context, in *SendBubbleInviteRequest, opts ...grpc.CallOption) (*SendBubbleInviteResponse, error)
	// Get bubble analytics
	GetBubbleAnalytics(ctx context.Context, in *GetBubbleAnalyticsRequest, opts ...grpc.CallOption) (*GetBubbleAnalyticsResponse, error)
}

type bubbleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBubbleServiceClient(cc grpc.ClientConnInterface) BubbleServiceClient {
	return &bubbleServiceClient{cc}
}

func (c *bubbleServiceClient) CreateBubble(ctx context.Context, in *CreateBubbleRequest, opts ...grpc.CallOption) (*CreateBubbleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBubbleResponse)
	err := c.cc.Invoke(ctx, BubbleService_CreateBubble_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) GetBubble(ctx context.Context, in *GetBubbleRequest, opts ...grpc.CallOption) (*GetBubbleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBubbleResponse)
	err := c.cc.Invoke(ctx, BubbleService_GetBubble_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) UpdateBubble(ctx context.Context, in *UpdateBubbleRequest, opts ...grpc.CallOption) (*UpdateBubbleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBubbleResponse)
	err := c.cc.Invoke(ctx, BubbleService_UpdateBubble_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) DeleteBubble(ctx context.Context, in *DeleteBubbleRequest, opts ...grpc.CallOption) (*DeleteBubbleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBubbleResponse)
	err := c.cc.Invoke(ctx, BubbleService_DeleteBubble_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) JoinBubble(ctx context.Context, in *JoinBubbleRequest, opts ...grpc.CallOption) (*JoinBubbleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JoinBubbleResponse)
	err := c.cc.Invoke(ctx, BubbleService_JoinBubble_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) LeaveBubble(ctx context.Context, in *LeaveBubbleRequest, opts ...grpc.CallOption) (*LeaveBubbleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeaveBubbleResponse)
	err := c.cc.Invoke(ctx, BubbleService_LeaveBubble_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) KickMember(ctx context.Context, in *KickMemberRequest, opts ...grpc.CallOption) (*KickMemberResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KickMemberResponse)
	err := c.cc.Invoke(ctx, BubbleService_KickMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) GetBubbleMembers(ctx context.Context, in *GetBubbleMembersRequest, opts ...grpc.CallOption) (*GetBubbleMembersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBubbleMembersResponse)
	err := c.cc.Invoke(ctx, BubbleService_GetBubbleMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) GetUserBubbles(ctx context.Context, in *GetUserBubblesRequest, opts ...grpc.CallOption) (*GetUserBubblesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserBubblesResponse)
	err := c.cc.Invoke(ctx, BubbleService_GetUserBubbles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) GetBubbleRequests(ctx context.Context, in *GetBubbleRequestsRequest, opts ...grpc.CallOption) (*GetBubbleRequestsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBubbleRequestsResponse)
	err := c.cc.Invoke(ctx, BubbleService_GetBubbleRequests_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) AcceptBubbleRequest(ctx context.Context, in *AcceptBubbleRequestRequest, opts ...grpc.CallOption) (*AcceptBubbleRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcceptBubbleRequestResponse)
	err := c.cc.Invoke(ctx, BubbleService_AcceptBubbleRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) RejectBubbleRequest(ctx context.Context, in *RejectBubbleRequestRequest, opts ...grpc.CallOption) (*RejectBubbleRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RejectBubbleRequestResponse)
	err := c.cc.Invoke(ctx, BubbleService_RejectBubbleRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) SendBubbleInvite(ctx context.Context, in *SendBubbleInviteRequest, opts ...grpc.CallOption) (*SendBubbleInviteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendBubbleInviteResponse)
	err := c.cc.Invoke(ctx, BubbleService_SendBubbleInvite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bubbleServiceClient) GetBubbleAnalytics(ctx context.Context, in *GetBubbleAnalyticsRequest, opts ...grpc.CallOption) (*GetBubbleAnalyticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBubbleAnalyticsResponse)
	err := c.cc.Invoke(ctx, BubbleService_GetBubbleAnalytics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BubbleServiceServer is the server API for BubbleService service.
// All implementations must embed UnimplementedBubbleServiceServer
// for forward compatibility.
//
// Bubble service for bubble management and membership operations
type BubbleServiceServer interface {
	// Create a new bubble
	CreateBubble(context.Context, *CreateBubbleRequest) (*CreateBubbleResponse, error)
	// Get bubble by ID
	GetBubble(context.Context, *GetBubbleRequest) (*GetBubbleResponse, error)
	// Update bubble information
	UpdateBubble(context.Context, *UpdateBubbleRequest) (*UpdateBubbleResponse, error)
	// Delete bubble
	DeleteBubble(context.Context, *DeleteBubbleRequest) (*DeleteBubbleResponse, error)
	// Join bubble
	JoinBubble(context.Context, *JoinBubbleRequest) (*JoinBubbleResponse, error)
	// Leave bubble
	LeaveBubble(context.Context, *LeaveBubbleRequest) (*LeaveBubbleResponse, error)
	// Kick member from bubble
	KickMember(context.Context, *KickMemberRequest) (*KickMemberResponse, error)
	// Get bubble members
	GetBubbleMembers(context.Context, *GetBubbleMembersRequest) (*GetBubbleMembersResponse, error)
	// Get user's active bubbles
	GetUserBubbles(context.Context, *GetUserBubblesRequest) (*GetUserBubblesResponse, error)
	// Get bubble requests (join/invite)
	GetBubbleRequests(context.Context, *GetBubbleRequestsRequest) (*GetBubbleRequestsResponse, error)
	// Accept bubble request
	AcceptBubbleRequest(context.Context, *AcceptBubbleRequestRequest) (*AcceptBubbleRequestResponse, error)
	// Reject bubble request
	RejectBubbleRequest(context.Context, *RejectBubbleRequestRequest) (*RejectBubbleRequestResponse, error)
	// Send bubble invite
	SendBubbleInvite(context.Context, *SendBubbleInviteRequest) (*SendBubbleInviteResponse, error)
	// Get bubble analytics
	GetBubbleAnalytics(context.Context, *GetBubbleAnalyticsRequest) (*GetBubbleAnalyticsResponse, error)
	mustEmbedUnimplementedBubbleServiceServer()
}

// UnimplementedBubbleServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBubbleServiceServer struct{}

func (UnimplementedBubbleServiceServer) CreateBubble(context.Context, *CreateBubbleRequest) (*CreateBubbleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBubble not implemented")
}
func (UnimplementedBubbleServiceServer) GetBubble(context.Context, *GetBubbleRequest) (*GetBubbleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBubble not implemented")
}
func (UnimplementedBubbleServiceServer) UpdateBubble(context.Context, *UpdateBubbleRequest) (*UpdateBubbleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBubble not implemented")
}
func (UnimplementedBubbleServiceServer) DeleteBubble(context.Context, *DeleteBubbleRequest) (*DeleteBubbleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBubble not implemented")
}
func (UnimplementedBubbleServiceServer) JoinBubble(context.Context, *JoinBubbleRequest) (*JoinBubbleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinBubble not implemented")
}
func (UnimplementedBubbleServiceServer) LeaveBubble(context.Context, *LeaveBubbleRequest) (*LeaveBubbleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveBubble not implemented")
}
func (UnimplementedBubbleServiceServer) KickMember(context.Context, *KickMemberRequest) (*KickMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KickMember not implemented")
}
func (UnimplementedBubbleServiceServer) GetBubbleMembers(context.Context, *GetBubbleMembersRequest) (*GetBubbleMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBubbleMembers not implemented")
}
func (UnimplementedBubbleServiceServer) GetUserBubbles(context.Context, *GetUserBubblesRequest) (*GetUserBubblesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserBubbles not implemented")
}
func (UnimplementedBubbleServiceServer) GetBubbleRequests(context.Context, *GetBubbleRequestsRequest) (*GetBubbleRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBubbleRequests not implemented")
}
func (UnimplementedBubbleServiceServer) AcceptBubbleRequest(context.Context, *AcceptBubbleRequestRequest) (*AcceptBubbleRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptBubbleRequest not implemented")
}
func (UnimplementedBubbleServiceServer) RejectBubbleRequest(context.Context, *RejectBubbleRequestRequest) (*RejectBubbleRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectBubbleRequest not implemented")
}
func (UnimplementedBubbleServiceServer) SendBubbleInvite(context.Context, *SendBubbleInviteRequest) (*SendBubbleInviteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendBubbleInvite not implemented")
}
func (UnimplementedBubbleServiceServer) GetBubbleAnalytics(context.Context, *GetBubbleAnalyticsRequest) (*GetBubbleAnalyticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBubbleAnalytics not implemented")
}
func (UnimplementedBubbleServiceServer) mustEmbedUnimplementedBubbleServiceServer() {}
func (UnimplementedBubbleServiceServer) testEmbeddedByValue()                       {}

// UnsafeBubbleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BubbleServiceServer will
// result in compilation errors.
type UnsafeBubbleServiceServer interface {
	mustEmbedUnimplementedBubbleServiceServer()
}

func RegisterBubbleServiceServer(s grpc.ServiceRegistrar, srv BubbleServiceServer) {
	// If the following call pancis, it indicates UnimplementedBubbleServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BubbleService_ServiceDesc, srv)
}

func _BubbleService_CreateBubble_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBubbleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).CreateBubble(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_CreateBubble_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).CreateBubble(ctx, req.(*CreateBubbleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_GetBubble_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBubbleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).GetBubble(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_GetBubble_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).GetBubble(ctx, req.(*GetBubbleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_UpdateBubble_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBubbleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).UpdateBubble(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_UpdateBubble_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).UpdateBubble(ctx, req.(*UpdateBubbleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_DeleteBubble_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBubbleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).DeleteBubble(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_DeleteBubble_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).DeleteBubble(ctx, req.(*DeleteBubbleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_JoinBubble_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinBubbleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).JoinBubble(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_JoinBubble_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).JoinBubble(ctx, req.(*JoinBubbleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_LeaveBubble_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaveBubbleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).LeaveBubble(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_LeaveBubble_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).LeaveBubble(ctx, req.(*LeaveBubbleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_KickMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).KickMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_KickMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).KickMember(ctx, req.(*KickMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_GetBubbleMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBubbleMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).GetBubbleMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_GetBubbleMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).GetBubbleMembers(ctx, req.(*GetBubbleMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_GetUserBubbles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBubblesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).GetUserBubbles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_GetUserBubbles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).GetUserBubbles(ctx, req.(*GetUserBubblesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_GetBubbleRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBubbleRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).GetBubbleRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_GetBubbleRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).GetBubbleRequests(ctx, req.(*GetBubbleRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_AcceptBubbleRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptBubbleRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).AcceptBubbleRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_AcceptBubbleRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).AcceptBubbleRequest(ctx, req.(*AcceptBubbleRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_RejectBubbleRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectBubbleRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).RejectBubbleRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_RejectBubbleRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).RejectBubbleRequest(ctx, req.(*RejectBubbleRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_SendBubbleInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendBubbleInviteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).SendBubbleInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_SendBubbleInvite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).SendBubbleInvite(ctx, req.(*SendBubbleInviteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BubbleService_GetBubbleAnalytics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBubbleAnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BubbleServiceServer).GetBubbleAnalytics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BubbleService_GetBubbleAnalytics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BubbleServiceServer).GetBubbleAnalytics(ctx, req.(*GetBubbleAnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BubbleService_ServiceDesc is the grpc.ServiceDesc for BubbleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BubbleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.bubble.v1.BubbleService",
	HandlerType: (*BubbleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBubble",
			Handler:    _BubbleService_CreateBubble_Handler,
		},
		{
			MethodName: "GetBubble",
			Handler:    _BubbleService_GetBubble_Handler,
		},
		{
			MethodName: "UpdateBubble",
			Handler:    _BubbleService_UpdateBubble_Handler,
		},
		{
			MethodName: "DeleteBubble",
			Handler:    _BubbleService_DeleteBubble_Handler,
		},
		{
			MethodName: "JoinBubble",
			Handler:    _BubbleService_JoinBubble_Handler,
		},
		{
			MethodName: "LeaveBubble",
			Handler:    _BubbleService_LeaveBubble_Handler,
		},
		{
			MethodName: "KickMember",
			Handler:    _BubbleService_KickMember_Handler,
		},
		{
			MethodName: "GetBubbleMembers",
			Handler:    _BubbleService_GetBubbleMembers_Handler,
		},
		{
			MethodName: "GetUserBubbles",
			Handler:    _BubbleService_GetUserBubbles_Handler,
		},
		{
			MethodName: "GetBubbleRequests",
			Handler:    _BubbleService_GetBubbleRequests_Handler,
		},
		{
			MethodName: "AcceptBubbleRequest",
			Handler:    _BubbleService_AcceptBubbleRequest_Handler,
		},
		{
			MethodName: "RejectBubbleRequest",
			Handler:    _BubbleService_RejectBubbleRequest_Handler,
		},
		{
			MethodName: "SendBubbleInvite",
			Handler:    _BubbleService_SendBubbleInvite_Handler,
		},
		{
			MethodName: "GetBubbleAnalytics",
			Handler:    _BubbleService_GetBubbleAnalytics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bubble.proto",
}
