// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: bubble.proto

package bubble

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Bubble request types
type BubbleRequestType int32

const (
	BubbleRequestType_BUBBLE_REQUEST_TYPE_UNSPECIFIED BubbleRequestType = 0
	BubbleRequestType_BUBBLE_REQUEST_TYPE_JOIN        BubbleRequestType = 1
	BubbleRequestType_BUBBLE_REQUEST_TYPE_INVITE      BubbleRequestType = 2
)

// Enum value maps for BubbleRequestType.
var (
	BubbleRequestType_name = map[int32]string{
		0: "BUBBLE_REQUEST_TYPE_UNSPECIFIED",
		1: "BUBBLE_REQUEST_TYPE_JOIN",
		2: "BUBBLE_REQUEST_TYPE_INVITE",
	}
	BubbleRequestType_value = map[string]int32{
		"BUBBLE_REQUEST_TYPE_UNSPECIFIED": 0,
		"BUBBLE_REQUEST_TYPE_JOIN":        1,
		"BUBBLE_REQUEST_TYPE_INVITE":      2,
	}
)

func (x BubbleRequestType) Enum() *BubbleRequestType {
	p := new(BubbleRequestType)
	*p = x
	return p
}

func (x BubbleRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BubbleRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_bubble_proto_enumTypes[0].Descriptor()
}

func (BubbleRequestType) Type() protoreflect.EnumType {
	return &file_bubble_proto_enumTypes[0]
}

func (x BubbleRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BubbleRequestType.Descriptor instead.
func (BubbleRequestType) EnumDescriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{0}
}

// Bubble request status
type BubbleRequestStatus int32

const (
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_UNSPECIFIED BubbleRequestStatus = 0
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_PENDING     BubbleRequestStatus = 1
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_ACCEPTED    BubbleRequestStatus = 2
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_REJECTED    BubbleRequestStatus = 3
	BubbleRequestStatus_BUBBLE_REQUEST_STATUS_CANCELLED   BubbleRequestStatus = 4
)

// Enum value maps for BubbleRequestStatus.
var (
	BubbleRequestStatus_name = map[int32]string{
		0: "BUBBLE_REQUEST_STATUS_UNSPECIFIED",
		1: "BUBBLE_REQUEST_STATUS_PENDING",
		2: "BUBBLE_REQUEST_STATUS_ACCEPTED",
		3: "BUBBLE_REQUEST_STATUS_REJECTED",
		4: "BUBBLE_REQUEST_STATUS_CANCELLED",
	}
	BubbleRequestStatus_value = map[string]int32{
		"BUBBLE_REQUEST_STATUS_UNSPECIFIED": 0,
		"BUBBLE_REQUEST_STATUS_PENDING":     1,
		"BUBBLE_REQUEST_STATUS_ACCEPTED":    2,
		"BUBBLE_REQUEST_STATUS_REJECTED":    3,
		"BUBBLE_REQUEST_STATUS_CANCELLED":   4,
	}
)

func (x BubbleRequestStatus) Enum() *BubbleRequestStatus {
	p := new(BubbleRequestStatus)
	*p = x
	return p
}

func (x BubbleRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BubbleRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_bubble_proto_enumTypes[1].Descriptor()
}

func (BubbleRequestStatus) Type() protoreflect.EnumType {
	return &file_bubble_proto_enumTypes[1]
}

func (x BubbleRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BubbleRequestStatus.Descriptor instead.
func (BubbleRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{1}
}

// Create bubble request
type CreateBubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	CreatorId     string                 `protobuf:"bytes,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateBubbleRequest) Reset() {
	*x = CreateBubbleRequest{}
	mi := &file_bubble_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBubbleRequest) ProtoMessage() {}

func (x *CreateBubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBubbleRequest.ProtoReflect.Descriptor instead.
func (*CreateBubbleRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBubbleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateBubbleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateBubbleRequest) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *CreateBubbleRequest) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

// Create bubble response
type CreateBubbleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bubble        *common.Bubble         `protobuf:"bytes,1,opt,name=bubble,proto3" json:"bubble,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateBubbleResponse) Reset() {
	*x = CreateBubbleResponse{}
	mi := &file_bubble_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBubbleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBubbleResponse) ProtoMessage() {}

func (x *CreateBubbleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBubbleResponse.ProtoReflect.Descriptor instead.
func (*CreateBubbleResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBubbleResponse) GetBubble() *common.Bubble {
	if x != nil {
		return x.Bubble
	}
	return nil
}

func (x *CreateBubbleResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get bubble request
type GetBubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBubbleRequest) Reset() {
	*x = GetBubbleRequest{}
	mi := &file_bubble_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleRequest) ProtoMessage() {}

func (x *GetBubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleRequest.ProtoReflect.Descriptor instead.
func (*GetBubbleRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{2}
}

func (x *GetBubbleRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

// Get bubble response
type GetBubbleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bubble        *common.Bubble         `protobuf:"bytes,1,opt,name=bubble,proto3" json:"bubble,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBubbleResponse) Reset() {
	*x = GetBubbleResponse{}
	mi := &file_bubble_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleResponse) ProtoMessage() {}

func (x *GetBubbleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleResponse.ProtoReflect.Descriptor instead.
func (*GetBubbleResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{3}
}

func (x *GetBubbleResponse) GetBubble() *common.Bubble {
	if x != nil {
		return x.Bubble
	}
	return nil
}

func (x *GetBubbleResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update bubble request
type UpdateBubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Description   *string                `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3,oneof" json:"expires_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBubbleRequest) Reset() {
	*x = UpdateBubbleRequest{}
	mi := &file_bubble_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBubbleRequest) ProtoMessage() {}

func (x *UpdateBubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBubbleRequest.ProtoReflect.Descriptor instead.
func (*UpdateBubbleRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateBubbleRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *UpdateBubbleRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateBubbleRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateBubbleRequest) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

// Update bubble response
type UpdateBubbleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bubble        *common.Bubble         `protobuf:"bytes,1,opt,name=bubble,proto3" json:"bubble,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBubbleResponse) Reset() {
	*x = UpdateBubbleResponse{}
	mi := &file_bubble_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBubbleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBubbleResponse) ProtoMessage() {}

func (x *UpdateBubbleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBubbleResponse.ProtoReflect.Descriptor instead.
func (*UpdateBubbleResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateBubbleResponse) GetBubble() *common.Bubble {
	if x != nil {
		return x.Bubble
	}
	return nil
}

func (x *UpdateBubbleResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Delete bubble request
type DeleteBubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteBubbleRequest) Reset() {
	*x = DeleteBubbleRequest{}
	mi := &file_bubble_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteBubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBubbleRequest) ProtoMessage() {}

func (x *DeleteBubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBubbleRequest.ProtoReflect.Descriptor instead.
func (*DeleteBubbleRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteBubbleRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *DeleteBubbleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Delete bubble response
type DeleteBubbleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteBubbleResponse) Reset() {
	*x = DeleteBubbleResponse{}
	mi := &file_bubble_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteBubbleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBubbleResponse) ProtoMessage() {}

func (x *DeleteBubbleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBubbleResponse.ProtoReflect.Descriptor instead.
func (*DeleteBubbleResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteBubbleResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Join bubble request
type JoinBubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinBubbleRequest) Reset() {
	*x = JoinBubbleRequest{}
	mi := &file_bubble_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinBubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinBubbleRequest) ProtoMessage() {}

func (x *JoinBubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinBubbleRequest.ProtoReflect.Descriptor instead.
func (*JoinBubbleRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{8}
}

func (x *JoinBubbleRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *JoinBubbleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Join bubble response
type JoinBubbleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinBubbleResponse) Reset() {
	*x = JoinBubbleResponse{}
	mi := &file_bubble_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinBubbleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinBubbleResponse) ProtoMessage() {}

func (x *JoinBubbleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinBubbleResponse.ProtoReflect.Descriptor instead.
func (*JoinBubbleResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{9}
}

func (x *JoinBubbleResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Leave bubble request
type LeaveBubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveBubbleRequest) Reset() {
	*x = LeaveBubbleRequest{}
	mi := &file_bubble_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveBubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveBubbleRequest) ProtoMessage() {}

func (x *LeaveBubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveBubbleRequest.ProtoReflect.Descriptor instead.
func (*LeaveBubbleRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{10}
}

func (x *LeaveBubbleRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *LeaveBubbleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Leave bubble response
type LeaveBubbleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveBubbleResponse) Reset() {
	*x = LeaveBubbleResponse{}
	mi := &file_bubble_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveBubbleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveBubbleResponse) ProtoMessage() {}

func (x *LeaveBubbleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveBubbleResponse.ProtoReflect.Descriptor instead.
func (*LeaveBubbleResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{11}
}

func (x *LeaveBubbleResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Kick member request
type KickMemberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	MemberId      string                 `protobuf:"bytes,3,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KickMemberRequest) Reset() {
	*x = KickMemberRequest{}
	mi := &file_bubble_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KickMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickMemberRequest) ProtoMessage() {}

func (x *KickMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickMemberRequest.ProtoReflect.Descriptor instead.
func (*KickMemberRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{12}
}

func (x *KickMemberRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *KickMemberRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *KickMemberRequest) GetMemberId() string {
	if x != nil {
		return x.MemberId
	}
	return ""
}

// Kick member response
type KickMemberResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KickMemberResponse) Reset() {
	*x = KickMemberResponse{}
	mi := &file_bubble_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KickMemberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickMemberResponse) ProtoMessage() {}

func (x *KickMemberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickMemberResponse.ProtoReflect.Descriptor instead.
func (*KickMemberResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{13}
}

func (x *KickMemberResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Bubble member information
type BubbleMember struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	User          *common.User           `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	JoinedAt      *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	LeftAt        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=left_at,json=leftAt,proto3" json:"left_at,omitempty"`
	IsActive      bool                   `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BubbleMember) Reset() {
	*x = BubbleMember{}
	mi := &file_bubble_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BubbleMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BubbleMember) ProtoMessage() {}

func (x *BubbleMember) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BubbleMember.ProtoReflect.Descriptor instead.
func (*BubbleMember) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{14}
}

func (x *BubbleMember) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *BubbleMember) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BubbleMember) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BubbleMember) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

func (x *BubbleMember) GetLeftAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LeftAt
	}
	return nil
}

func (x *BubbleMember) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

// Get bubble members request
type GetBubbleMembersRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BubbleId        string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	Page            int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize        int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IncludeInactive bool                   `protobuf:"varint,4,opt,name=include_inactive,json=includeInactive,proto3" json:"include_inactive,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetBubbleMembersRequest) Reset() {
	*x = GetBubbleMembersRequest{}
	mi := &file_bubble_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleMembersRequest) ProtoMessage() {}

func (x *GetBubbleMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleMembersRequest.ProtoReflect.Descriptor instead.
func (*GetBubbleMembersRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{15}
}

func (x *GetBubbleMembersRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *GetBubbleMembersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetBubbleMembersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetBubbleMembersRequest) GetIncludeInactive() bool {
	if x != nil {
		return x.IncludeInactive
	}
	return false
}

// Get bubble members response
type GetBubbleMembersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Members       []*BubbleMember        `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBubbleMembersResponse) Reset() {
	*x = GetBubbleMembersResponse{}
	mi := &file_bubble_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleMembersResponse) ProtoMessage() {}

func (x *GetBubbleMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleMembersResponse.ProtoReflect.Descriptor instead.
func (*GetBubbleMembersResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{16}
}

func (x *GetBubbleMembersResponse) GetMembers() []*BubbleMember {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *GetBubbleMembersResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetBubbleMembersResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get user bubbles request
type GetUserBubblesRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page           int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IncludeExpired bool                   `protobuf:"varint,4,opt,name=include_expired,json=includeExpired,proto3" json:"include_expired,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetUserBubblesRequest) Reset() {
	*x = GetUserBubblesRequest{}
	mi := &file_bubble_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserBubblesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBubblesRequest) ProtoMessage() {}

func (x *GetUserBubblesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBubblesRequest.ProtoReflect.Descriptor instead.
func (*GetUserBubblesRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{17}
}

func (x *GetUserBubblesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserBubblesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUserBubblesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetUserBubblesRequest) GetIncludeExpired() bool {
	if x != nil {
		return x.IncludeExpired
	}
	return false
}

// Get user bubbles response
type GetUserBubblesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bubbles       []*common.Bubble       `protobuf:"bytes,1,rep,name=bubbles,proto3" json:"bubbles,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserBubblesResponse) Reset() {
	*x = GetUserBubblesResponse{}
	mi := &file_bubble_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserBubblesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBubblesResponse) ProtoMessage() {}

func (x *GetUserBubblesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBubblesResponse.ProtoReflect.Descriptor instead.
func (*GetUserBubblesResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{18}
}

func (x *GetUserBubblesResponse) GetBubbles() []*common.Bubble {
	if x != nil {
		return x.Bubbles
	}
	return nil
}

func (x *GetUserBubblesResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetUserBubblesResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Bubble request
type BubbleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BubbleId      string                 `protobuf:"bytes,2,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RequesterId   string                 `protobuf:"bytes,4,opt,name=requester_id,json=requesterId,proto3" json:"requester_id,omitempty"`
	Type          BubbleRequestType      `protobuf:"varint,5,opt,name=type,proto3,enum=hopen.bubble.v1.BubbleRequestType" json:"type,omitempty"`
	Status        BubbleRequestStatus    `protobuf:"varint,6,opt,name=status,proto3,enum=hopen.bubble.v1.BubbleRequestStatus" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BubbleRequest) Reset() {
	*x = BubbleRequest{}
	mi := &file_bubble_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BubbleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BubbleRequest) ProtoMessage() {}

func (x *BubbleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BubbleRequest.ProtoReflect.Descriptor instead.
func (*BubbleRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{19}
}

func (x *BubbleRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BubbleRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *BubbleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BubbleRequest) GetRequesterId() string {
	if x != nil {
		return x.RequesterId
	}
	return ""
}

func (x *BubbleRequest) GetType() BubbleRequestType {
	if x != nil {
		return x.Type
	}
	return BubbleRequestType_BUBBLE_REQUEST_TYPE_UNSPECIFIED
}

func (x *BubbleRequest) GetStatus() BubbleRequestStatus {
	if x != nil {
		return x.Status
	}
	return BubbleRequestStatus_BUBBLE_REQUEST_STATUS_UNSPECIFIED
}

func (x *BubbleRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BubbleRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Get bubble requests request
type GetBubbleRequestsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status        BubbleRequestStatus    `protobuf:"varint,3,opt,name=status,proto3,enum=hopen.bubble.v1.BubbleRequestStatus" json:"status,omitempty"`
	Page          int32                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBubbleRequestsRequest) Reset() {
	*x = GetBubbleRequestsRequest{}
	mi := &file_bubble_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleRequestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleRequestsRequest) ProtoMessage() {}

func (x *GetBubbleRequestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleRequestsRequest.ProtoReflect.Descriptor instead.
func (*GetBubbleRequestsRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{20}
}

func (x *GetBubbleRequestsRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *GetBubbleRequestsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetBubbleRequestsRequest) GetStatus() BubbleRequestStatus {
	if x != nil {
		return x.Status
	}
	return BubbleRequestStatus_BUBBLE_REQUEST_STATUS_UNSPECIFIED
}

func (x *GetBubbleRequestsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetBubbleRequestsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get bubble requests response
type GetBubbleRequestsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Requests      []*BubbleRequest       `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBubbleRequestsResponse) Reset() {
	*x = GetBubbleRequestsResponse{}
	mi := &file_bubble_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleRequestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleRequestsResponse) ProtoMessage() {}

func (x *GetBubbleRequestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleRequestsResponse.ProtoReflect.Descriptor instead.
func (*GetBubbleRequestsResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{21}
}

func (x *GetBubbleRequestsResponse) GetRequests() []*BubbleRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

func (x *GetBubbleRequestsResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetBubbleRequestsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Accept bubble request request
type AcceptBubbleRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptBubbleRequestRequest) Reset() {
	*x = AcceptBubbleRequestRequest{}
	mi := &file_bubble_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptBubbleRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBubbleRequestRequest) ProtoMessage() {}

func (x *AcceptBubbleRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBubbleRequestRequest.ProtoReflect.Descriptor instead.
func (*AcceptBubbleRequestRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{22}
}

func (x *AcceptBubbleRequestRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AcceptBubbleRequestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Accept bubble request response
type AcceptBubbleRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptBubbleRequestResponse) Reset() {
	*x = AcceptBubbleRequestResponse{}
	mi := &file_bubble_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptBubbleRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBubbleRequestResponse) ProtoMessage() {}

func (x *AcceptBubbleRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBubbleRequestResponse.ProtoReflect.Descriptor instead.
func (*AcceptBubbleRequestResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{23}
}

func (x *AcceptBubbleRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Reject bubble request request
type RejectBubbleRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectBubbleRequestRequest) Reset() {
	*x = RejectBubbleRequestRequest{}
	mi := &file_bubble_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectBubbleRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectBubbleRequestRequest) ProtoMessage() {}

func (x *RejectBubbleRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectBubbleRequestRequest.ProtoReflect.Descriptor instead.
func (*RejectBubbleRequestRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{24}
}

func (x *RejectBubbleRequestRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RejectBubbleRequestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RejectBubbleRequestRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Reject bubble request response
type RejectBubbleRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectBubbleRequestResponse) Reset() {
	*x = RejectBubbleRequestResponse{}
	mi := &file_bubble_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectBubbleRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectBubbleRequestResponse) ProtoMessage() {}

func (x *RejectBubbleRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectBubbleRequestResponse.ProtoReflect.Descriptor instead.
func (*RejectBubbleRequestResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{25}
}

func (x *RejectBubbleRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Send bubble invite request
type SendBubbleInviteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	InviterId     string                 `protobuf:"bytes,2,opt,name=inviter_id,json=inviterId,proto3" json:"inviter_id,omitempty"`
	InviteeIds    []string               `protobuf:"bytes,3,rep,name=invitee_ids,json=inviteeIds,proto3" json:"invitee_ids,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendBubbleInviteRequest) Reset() {
	*x = SendBubbleInviteRequest{}
	mi := &file_bubble_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendBubbleInviteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBubbleInviteRequest) ProtoMessage() {}

func (x *SendBubbleInviteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBubbleInviteRequest.ProtoReflect.Descriptor instead.
func (*SendBubbleInviteRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{26}
}

func (x *SendBubbleInviteRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *SendBubbleInviteRequest) GetInviterId() string {
	if x != nil {
		return x.InviterId
	}
	return ""
}

func (x *SendBubbleInviteRequest) GetInviteeIds() []string {
	if x != nil {
		return x.InviteeIds
	}
	return nil
}

func (x *SendBubbleInviteRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Send bubble invite response
type SendBubbleInviteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestIds    []string               `protobuf:"bytes,1,rep,name=request_ids,json=requestIds,proto3" json:"request_ids,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendBubbleInviteResponse) Reset() {
	*x = SendBubbleInviteResponse{}
	mi := &file_bubble_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendBubbleInviteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendBubbleInviteResponse) ProtoMessage() {}

func (x *SendBubbleInviteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendBubbleInviteResponse.ProtoReflect.Descriptor instead.
func (*SendBubbleInviteResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{27}
}

func (x *SendBubbleInviteResponse) GetRequestIds() []string {
	if x != nil {
		return x.RequestIds
	}
	return nil
}

func (x *SendBubbleInviteResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Bubble analytics
type BubbleAnalytics struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BubbleId       string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	TotalMembers   int32                  `protobuf:"varint,2,opt,name=total_members,json=totalMembers,proto3" json:"total_members,omitempty"`
	ActiveMembers  int32                  `protobuf:"varint,3,opt,name=active_members,json=activeMembers,proto3" json:"active_members,omitempty"`
	TotalMessages  int32                  `protobuf:"varint,4,opt,name=total_messages,json=totalMessages,proto3" json:"total_messages,omitempty"`
	TotalCalls     int32                  `protobuf:"varint,5,opt,name=total_calls,json=totalCalls,proto3" json:"total_calls,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LastActivityAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_activity_at,json=lastActivityAt,proto3" json:"last_activity_at,omitempty"`
	MemberActivity map[string]int32       `protobuf:"bytes,8,rep,name=member_activity,json=memberActivity,proto3" json:"member_activity,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BubbleAnalytics) Reset() {
	*x = BubbleAnalytics{}
	mi := &file_bubble_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BubbleAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BubbleAnalytics) ProtoMessage() {}

func (x *BubbleAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BubbleAnalytics.ProtoReflect.Descriptor instead.
func (*BubbleAnalytics) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{28}
}

func (x *BubbleAnalytics) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *BubbleAnalytics) GetTotalMembers() int32 {
	if x != nil {
		return x.TotalMembers
	}
	return 0
}

func (x *BubbleAnalytics) GetActiveMembers() int32 {
	if x != nil {
		return x.ActiveMembers
	}
	return 0
}

func (x *BubbleAnalytics) GetTotalMessages() int32 {
	if x != nil {
		return x.TotalMessages
	}
	return 0
}

func (x *BubbleAnalytics) GetTotalCalls() int32 {
	if x != nil {
		return x.TotalCalls
	}
	return 0
}

func (x *BubbleAnalytics) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BubbleAnalytics) GetLastActivityAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActivityAt
	}
	return nil
}

func (x *BubbleAnalytics) GetMemberActivity() map[string]int32 {
	if x != nil {
		return x.MemberActivity
	}
	return nil
}

// Get bubble analytics request
type GetBubbleAnalyticsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBubbleAnalyticsRequest) Reset() {
	*x = GetBubbleAnalyticsRequest{}
	mi := &file_bubble_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleAnalyticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleAnalyticsRequest) ProtoMessage() {}

func (x *GetBubbleAnalyticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleAnalyticsRequest.ProtoReflect.Descriptor instead.
func (*GetBubbleAnalyticsRequest) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{29}
}

func (x *GetBubbleAnalyticsRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

// Get bubble analytics response
type GetBubbleAnalyticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Analytics     *BubbleAnalytics       `protobuf:"bytes,1,opt,name=analytics,proto3" json:"analytics,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBubbleAnalyticsResponse) Reset() {
	*x = GetBubbleAnalyticsResponse{}
	mi := &file_bubble_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBubbleAnalyticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBubbleAnalyticsResponse) ProtoMessage() {}

func (x *GetBubbleAnalyticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bubble_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBubbleAnalyticsResponse.ProtoReflect.Descriptor instead.
func (*GetBubbleAnalyticsResponse) Descriptor() ([]byte, []int) {
	return file_bubble_proto_rawDescGZIP(), []int{30}
}

func (x *GetBubbleAnalyticsResponse) GetAnalytics() *BubbleAnalytics {
	if x != nil {
		return x.Analytics
	}
	return nil
}

func (x *GetBubbleAnalyticsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

var File_bubble_proto protoreflect.FileDescriptor

const file_bubble_proto_rawDesc = "" +
	"\n" +
	"\fbubble.proto\x12\x0fhopen.bubble.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"\xa5\x01\n" +
	"\x13CreateBubbleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"creator_id\x18\x03 \x01(\tR\tcreatorId\x129\n" +
	"\n" +
	"expires_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\"\x88\x01\n" +
	"\x14CreateBubbleResponse\x12/\n" +
	"\x06bubble\x18\x01 \x01(\v2\x17.hopen.common.v1.BubbleR\x06bubble\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"/\n" +
	"\x10GetBubbleRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\"\x85\x01\n" +
	"\x11GetBubbleResponse\x12/\n" +
	"\x06bubble\x18\x01 \x01(\v2\x17.hopen.common.v1.BubbleR\x06bubble\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xda\x01\n" +
	"\x13UpdateBubbleRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12%\n" +
	"\vdescription\x18\x03 \x01(\tH\x01R\vdescription\x88\x01\x01\x12>\n" +
	"\n" +
	"expires_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampH\x02R\texpiresAt\x88\x01\x01B\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\r\n" +
	"\v_expires_at\"\x88\x01\n" +
	"\x14UpdateBubbleResponse\x12/\n" +
	"\x06bubble\x18\x01 \x01(\v2\x17.hopen.common.v1.BubbleR\x06bubble\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"K\n" +
	"\x13DeleteBubbleRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"W\n" +
	"\x14DeleteBubbleResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"I\n" +
	"\x11JoinBubbleRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"U\n" +
	"\x12JoinBubbleResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"J\n" +
	"\x12LeaveBubbleRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"V\n" +
	"\x13LeaveBubbleResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"f\n" +
	"\x11KickMemberRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1b\n" +
	"\tmember_id\x18\x03 \x01(\tR\bmemberId\"U\n" +
	"\x12KickMemberResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xfa\x01\n" +
	"\fBubbleMember\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12)\n" +
	"\x04user\x18\x03 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x127\n" +
	"\tjoined_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\bjoinedAt\x123\n" +
	"\aleft_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x06leftAt\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\"\x92\x01\n" +
	"\x17GetBubbleMembersRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12)\n" +
	"\x10include_inactive\x18\x04 \x01(\bR\x0fincludeInactive\"\xd1\x01\n" +
	"\x18GetBubbleMembersResponse\x127\n" +
	"\amembers\x18\x01 \x03(\v2\x1d.hopen.bubble.v1.BubbleMemberR\amembers\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x8a\x01\n" +
	"\x15GetUserBubblesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12'\n" +
	"\x0finclude_expired\x18\x04 \x01(\bR\x0eincludeExpired\"\xc9\x01\n" +
	"\x16GetUserBubblesResponse\x121\n" +
	"\abubbles\x18\x01 \x03(\v2\x17.hopen.common.v1.BubbleR\abubbles\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xe4\x02\n" +
	"\rBubbleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1b\n" +
	"\tbubble_id\x18\x02 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12!\n" +
	"\frequester_id\x18\x04 \x01(\tR\vrequesterId\x126\n" +
	"\x04type\x18\x05 \x01(\x0e2\".hopen.bubble.v1.BubbleRequestTypeR\x04type\x12<\n" +
	"\x06status\x18\x06 \x01(\x0e2$.hopen.bubble.v1.BubbleRequestStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xbf\x01\n" +
	"\x18GetBubbleRequestsRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12<\n" +
	"\x06status\x18\x03 \x01(\x0e2$.hopen.bubble.v1.BubbleRequestStatusR\x06status\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x05 \x01(\x05R\bpageSize\"\xd5\x01\n" +
	"\x19GetBubbleRequestsResponse\x12:\n" +
	"\brequests\x18\x01 \x03(\v2\x1e.hopen.bubble.v1.BubbleRequestR\brequests\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"T\n" +
	"\x1aAcceptBubbleRequestRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"^\n" +
	"\x1bAcceptBubbleRequestResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"l\n" +
	"\x1aRejectBubbleRequestRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"^\n" +
	"\x1bRejectBubbleRequestResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x90\x01\n" +
	"\x17SendBubbleInviteRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x1d\n" +
	"\n" +
	"inviter_id\x18\x02 \x01(\tR\tinviterId\x12\x1f\n" +
	"\vinvitee_ids\x18\x03 \x03(\tR\n" +
	"inviteeIds\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\"|\n" +
	"\x18SendBubbleInviteResponse\x12\x1f\n" +
	"\vrequest_ids\x18\x01 \x03(\tR\n" +
	"requestIds\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xe5\x03\n" +
	"\x0fBubbleAnalytics\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12#\n" +
	"\rtotal_members\x18\x02 \x01(\x05R\ftotalMembers\x12%\n" +
	"\x0eactive_members\x18\x03 \x01(\x05R\ractiveMembers\x12%\n" +
	"\x0etotal_messages\x18\x04 \x01(\x05R\rtotalMessages\x12\x1f\n" +
	"\vtotal_calls\x18\x05 \x01(\x05R\n" +
	"totalCalls\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12D\n" +
	"\x10last_activity_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x0elastActivityAt\x12]\n" +
	"\x0fmember_activity\x18\b \x03(\v24.hopen.bubble.v1.BubbleAnalytics.MemberActivityEntryR\x0ememberActivity\x1aA\n" +
	"\x13MemberActivityEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"8\n" +
	"\x19GetBubbleAnalyticsRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\"\x9d\x01\n" +
	"\x1aGetBubbleAnalyticsResponse\x12>\n" +
	"\tanalytics\x18\x01 \x01(\v2 .hopen.bubble.v1.BubbleAnalyticsR\tanalytics\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse*v\n" +
	"\x11BubbleRequestType\x12#\n" +
	"\x1fBUBBLE_REQUEST_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18BUBBLE_REQUEST_TYPE_JOIN\x10\x01\x12\x1e\n" +
	"\x1aBUBBLE_REQUEST_TYPE_INVITE\x10\x02*\xcc\x01\n" +
	"\x13BubbleRequestStatus\x12%\n" +
	"!BUBBLE_REQUEST_STATUS_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dBUBBLE_REQUEST_STATUS_PENDING\x10\x01\x12\"\n" +
	"\x1eBUBBLE_REQUEST_STATUS_ACCEPTED\x10\x02\x12\"\n" +
	"\x1eBUBBLE_REQUEST_STATUS_REJECTED\x10\x03\x12#\n" +
	"\x1fBUBBLE_REQUEST_STATUS_CANCELLED\x10\x042\xf6\n" +
	"\n" +
	"\rBubbleService\x12[\n" +
	"\fCreateBubble\x12$.hopen.bubble.v1.CreateBubbleRequest\x1a%.hopen.bubble.v1.CreateBubbleResponse\x12R\n" +
	"\tGetBubble\x12!.hopen.bubble.v1.GetBubbleRequest\x1a\".hopen.bubble.v1.GetBubbleResponse\x12[\n" +
	"\fUpdateBubble\x12$.hopen.bubble.v1.UpdateBubbleRequest\x1a%.hopen.bubble.v1.UpdateBubbleResponse\x12[\n" +
	"\fDeleteBubble\x12$.hopen.bubble.v1.DeleteBubbleRequest\x1a%.hopen.bubble.v1.DeleteBubbleResponse\x12U\n" +
	"\n" +
	"JoinBubble\x12\".hopen.bubble.v1.JoinBubbleRequest\x1a#.hopen.bubble.v1.JoinBubbleResponse\x12X\n" +
	"\vLeaveBubble\x12#.hopen.bubble.v1.LeaveBubbleRequest\x1a$.hopen.bubble.v1.LeaveBubbleResponse\x12U\n" +
	"\n" +
	"KickMember\x12\".hopen.bubble.v1.KickMemberRequest\x1a#.hopen.bubble.v1.KickMemberResponse\x12g\n" +
	"\x10GetBubbleMembers\x12(.hopen.bubble.v1.GetBubbleMembersRequest\x1a).hopen.bubble.v1.GetBubbleMembersResponse\x12a\n" +
	"\x0eGetUserBubbles\x12&.hopen.bubble.v1.GetUserBubblesRequest\x1a'.hopen.bubble.v1.GetUserBubblesResponse\x12j\n" +
	"\x11GetBubbleRequests\x12).hopen.bubble.v1.GetBubbleRequestsRequest\x1a*.hopen.bubble.v1.GetBubbleRequestsResponse\x12p\n" +
	"\x13AcceptBubbleRequest\x12+.hopen.bubble.v1.AcceptBubbleRequestRequest\x1a,.hopen.bubble.v1.AcceptBubbleRequestResponse\x12p\n" +
	"\x13RejectBubbleRequest\x12+.hopen.bubble.v1.RejectBubbleRequestRequest\x1a,.hopen.bubble.v1.RejectBubbleRequestResponse\x12g\n" +
	"\x10SendBubbleInvite\x12(.hopen.bubble.v1.SendBubbleInviteRequest\x1a).hopen.bubble.v1.SendBubbleInviteResponse\x12m\n" +
	"\x12GetBubbleAnalytics\x12*.hopen.bubble.v1.GetBubbleAnalyticsRequest\x1a+.hopen.bubble.v1.GetBubbleAnalyticsResponseB>\n" +
	"\x13com.hopen.bubble.v1P\x01Z%hopenbackend/protos/gen/bubble;bubbleb\x06proto3"

var (
	file_bubble_proto_rawDescOnce sync.Once
	file_bubble_proto_rawDescData []byte
)

func file_bubble_proto_rawDescGZIP() []byte {
	file_bubble_proto_rawDescOnce.Do(func() {
		file_bubble_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_bubble_proto_rawDesc), len(file_bubble_proto_rawDesc)))
	})
	return file_bubble_proto_rawDescData
}

var file_bubble_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_bubble_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_bubble_proto_goTypes = []any{
	(BubbleRequestType)(0),              // 0: hopen.bubble.v1.BubbleRequestType
	(BubbleRequestStatus)(0),            // 1: hopen.bubble.v1.BubbleRequestStatus
	(*CreateBubbleRequest)(nil),         // 2: hopen.bubble.v1.CreateBubbleRequest
	(*CreateBubbleResponse)(nil),        // 3: hopen.bubble.v1.CreateBubbleResponse
	(*GetBubbleRequest)(nil),            // 4: hopen.bubble.v1.GetBubbleRequest
	(*GetBubbleResponse)(nil),           // 5: hopen.bubble.v1.GetBubbleResponse
	(*UpdateBubbleRequest)(nil),         // 6: hopen.bubble.v1.UpdateBubbleRequest
	(*UpdateBubbleResponse)(nil),        // 7: hopen.bubble.v1.UpdateBubbleResponse
	(*DeleteBubbleRequest)(nil),         // 8: hopen.bubble.v1.DeleteBubbleRequest
	(*DeleteBubbleResponse)(nil),        // 9: hopen.bubble.v1.DeleteBubbleResponse
	(*JoinBubbleRequest)(nil),           // 10: hopen.bubble.v1.JoinBubbleRequest
	(*JoinBubbleResponse)(nil),          // 11: hopen.bubble.v1.JoinBubbleResponse
	(*LeaveBubbleRequest)(nil),          // 12: hopen.bubble.v1.LeaveBubbleRequest
	(*LeaveBubbleResponse)(nil),         // 13: hopen.bubble.v1.LeaveBubbleResponse
	(*KickMemberRequest)(nil),           // 14: hopen.bubble.v1.KickMemberRequest
	(*KickMemberResponse)(nil),          // 15: hopen.bubble.v1.KickMemberResponse
	(*BubbleMember)(nil),                // 16: hopen.bubble.v1.BubbleMember
	(*GetBubbleMembersRequest)(nil),     // 17: hopen.bubble.v1.GetBubbleMembersRequest
	(*GetBubbleMembersResponse)(nil),    // 18: hopen.bubble.v1.GetBubbleMembersResponse
	(*GetUserBubblesRequest)(nil),       // 19: hopen.bubble.v1.GetUserBubblesRequest
	(*GetUserBubblesResponse)(nil),      // 20: hopen.bubble.v1.GetUserBubblesResponse
	(*BubbleRequest)(nil),               // 21: hopen.bubble.v1.BubbleRequest
	(*GetBubbleRequestsRequest)(nil),    // 22: hopen.bubble.v1.GetBubbleRequestsRequest
	(*GetBubbleRequestsResponse)(nil),   // 23: hopen.bubble.v1.GetBubbleRequestsResponse
	(*AcceptBubbleRequestRequest)(nil),  // 24: hopen.bubble.v1.AcceptBubbleRequestRequest
	(*AcceptBubbleRequestResponse)(nil), // 25: hopen.bubble.v1.AcceptBubbleRequestResponse
	(*RejectBubbleRequestRequest)(nil),  // 26: hopen.bubble.v1.RejectBubbleRequestRequest
	(*RejectBubbleRequestResponse)(nil), // 27: hopen.bubble.v1.RejectBubbleRequestResponse
	(*SendBubbleInviteRequest)(nil),     // 28: hopen.bubble.v1.SendBubbleInviteRequest
	(*SendBubbleInviteResponse)(nil),    // 29: hopen.bubble.v1.SendBubbleInviteResponse
	(*BubbleAnalytics)(nil),             // 30: hopen.bubble.v1.BubbleAnalytics
	(*GetBubbleAnalyticsRequest)(nil),   // 31: hopen.bubble.v1.GetBubbleAnalyticsRequest
	(*GetBubbleAnalyticsResponse)(nil),  // 32: hopen.bubble.v1.GetBubbleAnalyticsResponse
	nil,                                 // 33: hopen.bubble.v1.BubbleAnalytics.MemberActivityEntry
	(*timestamppb.Timestamp)(nil),       // 34: google.protobuf.Timestamp
	(*common.Bubble)(nil),               // 35: hopen.common.v1.Bubble
	(*common.ApiResponse)(nil),          // 36: hopen.common.v1.ApiResponse
	(*common.User)(nil),                 // 37: hopen.common.v1.User
	(*common.Pagination)(nil),           // 38: hopen.common.v1.Pagination
}
var file_bubble_proto_depIdxs = []int32{
	34, // 0: hopen.bubble.v1.CreateBubbleRequest.expires_at:type_name -> google.protobuf.Timestamp
	35, // 1: hopen.bubble.v1.CreateBubbleResponse.bubble:type_name -> hopen.common.v1.Bubble
	36, // 2: hopen.bubble.v1.CreateBubbleResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	35, // 3: hopen.bubble.v1.GetBubbleResponse.bubble:type_name -> hopen.common.v1.Bubble
	36, // 4: hopen.bubble.v1.GetBubbleResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	34, // 5: hopen.bubble.v1.UpdateBubbleRequest.expires_at:type_name -> google.protobuf.Timestamp
	35, // 6: hopen.bubble.v1.UpdateBubbleResponse.bubble:type_name -> hopen.common.v1.Bubble
	36, // 7: hopen.bubble.v1.UpdateBubbleResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	36, // 8: hopen.bubble.v1.DeleteBubbleResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	36, // 9: hopen.bubble.v1.JoinBubbleResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	36, // 10: hopen.bubble.v1.LeaveBubbleResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	36, // 11: hopen.bubble.v1.KickMemberResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	37, // 12: hopen.bubble.v1.BubbleMember.user:type_name -> hopen.common.v1.User
	34, // 13: hopen.bubble.v1.BubbleMember.joined_at:type_name -> google.protobuf.Timestamp
	34, // 14: hopen.bubble.v1.BubbleMember.left_at:type_name -> google.protobuf.Timestamp
	16, // 15: hopen.bubble.v1.GetBubbleMembersResponse.members:type_name -> hopen.bubble.v1.BubbleMember
	38, // 16: hopen.bubble.v1.GetBubbleMembersResponse.pagination:type_name -> hopen.common.v1.Pagination
	36, // 17: hopen.bubble.v1.GetBubbleMembersResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	35, // 18: hopen.bubble.v1.GetUserBubblesResponse.bubbles:type_name -> hopen.common.v1.Bubble
	38, // 19: hopen.bubble.v1.GetUserBubblesResponse.pagination:type_name -> hopen.common.v1.Pagination
	36, // 20: hopen.bubble.v1.GetUserBubblesResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 21: hopen.bubble.v1.BubbleRequest.type:type_name -> hopen.bubble.v1.BubbleRequestType
	1,  // 22: hopen.bubble.v1.BubbleRequest.status:type_name -> hopen.bubble.v1.BubbleRequestStatus
	34, // 23: hopen.bubble.v1.BubbleRequest.created_at:type_name -> google.protobuf.Timestamp
	34, // 24: hopen.bubble.v1.BubbleRequest.updated_at:type_name -> google.protobuf.Timestamp
	1,  // 25: hopen.bubble.v1.GetBubbleRequestsRequest.status:type_name -> hopen.bubble.v1.BubbleRequestStatus
	21, // 26: hopen.bubble.v1.GetBubbleRequestsResponse.requests:type_name -> hopen.bubble.v1.BubbleRequest
	38, // 27: hopen.bubble.v1.GetBubbleRequestsResponse.pagination:type_name -> hopen.common.v1.Pagination
	36, // 28: hopen.bubble.v1.GetBubbleRequestsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	36, // 29: hopen.bubble.v1.AcceptBubbleRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	36, // 30: hopen.bubble.v1.RejectBubbleRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	36, // 31: hopen.bubble.v1.SendBubbleInviteResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	34, // 32: hopen.bubble.v1.BubbleAnalytics.created_at:type_name -> google.protobuf.Timestamp
	34, // 33: hopen.bubble.v1.BubbleAnalytics.last_activity_at:type_name -> google.protobuf.Timestamp
	33, // 34: hopen.bubble.v1.BubbleAnalytics.member_activity:type_name -> hopen.bubble.v1.BubbleAnalytics.MemberActivityEntry
	30, // 35: hopen.bubble.v1.GetBubbleAnalyticsResponse.analytics:type_name -> hopen.bubble.v1.BubbleAnalytics
	36, // 36: hopen.bubble.v1.GetBubbleAnalyticsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	2,  // 37: hopen.bubble.v1.BubbleService.CreateBubble:input_type -> hopen.bubble.v1.CreateBubbleRequest
	4,  // 38: hopen.bubble.v1.BubbleService.GetBubble:input_type -> hopen.bubble.v1.GetBubbleRequest
	6,  // 39: hopen.bubble.v1.BubbleService.UpdateBubble:input_type -> hopen.bubble.v1.UpdateBubbleRequest
	8,  // 40: hopen.bubble.v1.BubbleService.DeleteBubble:input_type -> hopen.bubble.v1.DeleteBubbleRequest
	10, // 41: hopen.bubble.v1.BubbleService.JoinBubble:input_type -> hopen.bubble.v1.JoinBubbleRequest
	12, // 42: hopen.bubble.v1.BubbleService.LeaveBubble:input_type -> hopen.bubble.v1.LeaveBubbleRequest
	14, // 43: hopen.bubble.v1.BubbleService.KickMember:input_type -> hopen.bubble.v1.KickMemberRequest
	17, // 44: hopen.bubble.v1.BubbleService.GetBubbleMembers:input_type -> hopen.bubble.v1.GetBubbleMembersRequest
	19, // 45: hopen.bubble.v1.BubbleService.GetUserBubbles:input_type -> hopen.bubble.v1.GetUserBubblesRequest
	22, // 46: hopen.bubble.v1.BubbleService.GetBubbleRequests:input_type -> hopen.bubble.v1.GetBubbleRequestsRequest
	24, // 47: hopen.bubble.v1.BubbleService.AcceptBubbleRequest:input_type -> hopen.bubble.v1.AcceptBubbleRequestRequest
	26, // 48: hopen.bubble.v1.BubbleService.RejectBubbleRequest:input_type -> hopen.bubble.v1.RejectBubbleRequestRequest
	28, // 49: hopen.bubble.v1.BubbleService.SendBubbleInvite:input_type -> hopen.bubble.v1.SendBubbleInviteRequest
	31, // 50: hopen.bubble.v1.BubbleService.GetBubbleAnalytics:input_type -> hopen.bubble.v1.GetBubbleAnalyticsRequest
	3,  // 51: hopen.bubble.v1.BubbleService.CreateBubble:output_type -> hopen.bubble.v1.CreateBubbleResponse
	5,  // 52: hopen.bubble.v1.BubbleService.GetBubble:output_type -> hopen.bubble.v1.GetBubbleResponse
	7,  // 53: hopen.bubble.v1.BubbleService.UpdateBubble:output_type -> hopen.bubble.v1.UpdateBubbleResponse
	9,  // 54: hopen.bubble.v1.BubbleService.DeleteBubble:output_type -> hopen.bubble.v1.DeleteBubbleResponse
	11, // 55: hopen.bubble.v1.BubbleService.JoinBubble:output_type -> hopen.bubble.v1.JoinBubbleResponse
	13, // 56: hopen.bubble.v1.BubbleService.LeaveBubble:output_type -> hopen.bubble.v1.LeaveBubbleResponse
	15, // 57: hopen.bubble.v1.BubbleService.KickMember:output_type -> hopen.bubble.v1.KickMemberResponse
	18, // 58: hopen.bubble.v1.BubbleService.GetBubbleMembers:output_type -> hopen.bubble.v1.GetBubbleMembersResponse
	20, // 59: hopen.bubble.v1.BubbleService.GetUserBubbles:output_type -> hopen.bubble.v1.GetUserBubblesResponse
	23, // 60: hopen.bubble.v1.BubbleService.GetBubbleRequests:output_type -> hopen.bubble.v1.GetBubbleRequestsResponse
	25, // 61: hopen.bubble.v1.BubbleService.AcceptBubbleRequest:output_type -> hopen.bubble.v1.AcceptBubbleRequestResponse
	27, // 62: hopen.bubble.v1.BubbleService.RejectBubbleRequest:output_type -> hopen.bubble.v1.RejectBubbleRequestResponse
	29, // 63: hopen.bubble.v1.BubbleService.SendBubbleInvite:output_type -> hopen.bubble.v1.SendBubbleInviteResponse
	32, // 64: hopen.bubble.v1.BubbleService.GetBubbleAnalytics:output_type -> hopen.bubble.v1.GetBubbleAnalyticsResponse
	51, // [51:65] is the sub-list for method output_type
	37, // [37:51] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_bubble_proto_init() }
func file_bubble_proto_init() {
	if File_bubble_proto != nil {
		return
	}
	file_bubble_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_bubble_proto_rawDesc), len(file_bubble_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bubble_proto_goTypes,
		DependencyIndexes: file_bubble_proto_depIdxs,
		EnumInfos:         file_bubble_proto_enumTypes,
		MessageInfos:      file_bubble_proto_msgTypes,
	}.Build()
	File_bubble_proto = out.File
	file_bubble_proto_goTypes = nil
	file_bubble_proto_depIdxs = nil
}
