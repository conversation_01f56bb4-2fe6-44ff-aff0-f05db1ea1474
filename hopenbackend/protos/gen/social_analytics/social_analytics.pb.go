// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: social_analytics.proto

package socialanalytics

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get enhanced profile request
type GetEnhancedProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEnhancedProfileRequest) Reset() {
	*x = GetEnhancedProfileRequest{}
	mi := &file_social_analytics_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEnhancedProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnhancedProfileRequest) ProtoMessage() {}

func (x *GetEnhancedProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnhancedProfileRequest.ProtoReflect.Descriptor instead.
func (*GetEnhancedProfileRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{0}
}

func (x *GetEnhancedProfileRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get enhanced profile response
type GetEnhancedProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Profile       *EnhancedProfile       `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEnhancedProfileResponse) Reset() {
	*x = GetEnhancedProfileResponse{}
	mi := &file_social_analytics_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEnhancedProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnhancedProfileResponse) ProtoMessage() {}

func (x *GetEnhancedProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnhancedProfileResponse.ProtoReflect.Descriptor instead.
func (*GetEnhancedProfileResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{1}
}

func (x *GetEnhancedProfileResponse) GetProfile() *EnhancedProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *GetEnhancedProfileResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Enhanced profile with comprehensive relationship data
type EnhancedProfile struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username       string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Email          string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	FirstName      string                 `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName       string                 `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	AvatarUrl      *string                `protobuf:"bytes,6,opt,name=avatar_url,json=avatarUrl,proto3,oneof" json:"avatar_url,omitempty"`
	BubbleId       *string                `protobuf:"bytes,7,opt,name=bubble_id,json=bubbleId,proto3,oneof" json:"bubble_id,omitempty"`
	IsOnline       bool                   `protobuf:"varint,8,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	FriendIds      []string               `protobuf:"bytes,9,rep,name=friend_ids,json=friendIds,proto3" json:"friend_ids,omitempty"`
	ContactIds     []string               `protobuf:"bytes,10,rep,name=contact_ids,json=contactIds,proto3" json:"contact_ids,omitempty"`
	BlockedUserIds []string               `protobuf:"bytes,11,rep,name=blocked_user_ids,json=blockedUserIds,proto3" json:"blocked_user_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *EnhancedProfile) Reset() {
	*x = EnhancedProfile{}
	mi := &file_social_analytics_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnhancedProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnhancedProfile) ProtoMessage() {}

func (x *EnhancedProfile) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnhancedProfile.ProtoReflect.Descriptor instead.
func (*EnhancedProfile) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{2}
}

func (x *EnhancedProfile) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *EnhancedProfile) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *EnhancedProfile) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EnhancedProfile) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *EnhancedProfile) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *EnhancedProfile) GetAvatarUrl() string {
	if x != nil && x.AvatarUrl != nil {
		return *x.AvatarUrl
	}
	return ""
}

func (x *EnhancedProfile) GetBubbleId() string {
	if x != nil && x.BubbleId != nil {
		return *x.BubbleId
	}
	return ""
}

func (x *EnhancedProfile) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *EnhancedProfile) GetFriendIds() []string {
	if x != nil {
		return x.FriendIds
	}
	return nil
}

func (x *EnhancedProfile) GetContactIds() []string {
	if x != nil {
		return x.ContactIds
	}
	return nil
}

func (x *EnhancedProfile) GetBlockedUserIds() []string {
	if x != nil {
		return x.BlockedUserIds
	}
	return nil
}

// Get profile analytics request
type GetProfileAnalyticsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProfileAnalyticsRequest) Reset() {
	*x = GetProfileAnalyticsRequest{}
	mi := &file_social_analytics_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProfileAnalyticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileAnalyticsRequest) ProtoMessage() {}

func (x *GetProfileAnalyticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileAnalyticsRequest.ProtoReflect.Descriptor instead.
func (*GetProfileAnalyticsRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{3}
}

func (x *GetProfileAnalyticsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetProfileAnalyticsRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

// Get profile analytics response
type GetProfileAnalyticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Analytics     *ProfileAnalytics      `protobuf:"bytes,1,opt,name=analytics,proto3" json:"analytics,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProfileAnalyticsResponse) Reset() {
	*x = GetProfileAnalyticsResponse{}
	mi := &file_social_analytics_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProfileAnalyticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileAnalyticsResponse) ProtoMessage() {}

func (x *GetProfileAnalyticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileAnalyticsResponse.ProtoReflect.Descriptor instead.
func (*GetProfileAnalyticsResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{4}
}

func (x *GetProfileAnalyticsResponse) GetAnalytics() *ProfileAnalytics {
	if x != nil {
		return x.Analytics
	}
	return nil
}

func (x *GetProfileAnalyticsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Profile analytics with comprehensive data
type ProfileAnalytics struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	UserId             string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	MutualFriends      []string               `protobuf:"bytes,2,rep,name=mutual_friends,json=mutualFriends,proto3" json:"mutual_friends,omitempty"`
	MutualContacts     []string               `protobuf:"bytes,3,rep,name=mutual_contacts,json=mutualContacts,proto3" json:"mutual_contacts,omitempty"`
	CommonBubbles      []*CommonBubble        `protobuf:"bytes,4,rep,name=common_bubbles,json=commonBubbles,proto3" json:"common_bubbles,omitempty"`
	ConnectionStrength float32                `protobuf:"fixed32,5,opt,name=connection_strength,json=connectionStrength,proto3" json:"connection_strength,omitempty"`
	SocialScore        float32                `protobuf:"fixed32,6,opt,name=social_score,json=socialScore,proto3" json:"social_score,omitempty"`
	EngagementMetrics  *EngagementMetrics     `protobuf:"bytes,7,opt,name=engagement_metrics,json=engagementMetrics,proto3" json:"engagement_metrics,omitempty"`
	RecommendedActions []string               `protobuf:"bytes,8,rep,name=recommended_actions,json=recommendedActions,proto3" json:"recommended_actions,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ProfileAnalytics) Reset() {
	*x = ProfileAnalytics{}
	mi := &file_social_analytics_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileAnalytics) ProtoMessage() {}

func (x *ProfileAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileAnalytics.ProtoReflect.Descriptor instead.
func (*ProfileAnalytics) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{5}
}

func (x *ProfileAnalytics) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ProfileAnalytics) GetMutualFriends() []string {
	if x != nil {
		return x.MutualFriends
	}
	return nil
}

func (x *ProfileAnalytics) GetMutualContacts() []string {
	if x != nil {
		return x.MutualContacts
	}
	return nil
}

func (x *ProfileAnalytics) GetCommonBubbles() []*CommonBubble {
	if x != nil {
		return x.CommonBubbles
	}
	return nil
}

func (x *ProfileAnalytics) GetConnectionStrength() float32 {
	if x != nil {
		return x.ConnectionStrength
	}
	return 0
}

func (x *ProfileAnalytics) GetSocialScore() float32 {
	if x != nil {
		return x.SocialScore
	}
	return 0
}

func (x *ProfileAnalytics) GetEngagementMetrics() *EngagementMetrics {
	if x != nil {
		return x.EngagementMetrics
	}
	return nil
}

func (x *ProfileAnalytics) GetRecommendedActions() []string {
	if x != nil {
		return x.RecommendedActions
	}
	return nil
}

// Common bubble information
type CommonBubble struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	BubbleName    string                 `protobuf:"bytes,2,opt,name=bubble_name,json=bubbleName,proto3" json:"bubble_name,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	JoinedAt      *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	LeftAt        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=left_at,json=leftAt,proto3,oneof" json:"left_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonBubble) Reset() {
	*x = CommonBubble{}
	mi := &file_social_analytics_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonBubble) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonBubble) ProtoMessage() {}

func (x *CommonBubble) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonBubble.ProtoReflect.Descriptor instead.
func (*CommonBubble) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{6}
}

func (x *CommonBubble) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *CommonBubble) GetBubbleName() string {
	if x != nil {
		return x.BubbleName
	}
	return ""
}

func (x *CommonBubble) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CommonBubble) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

func (x *CommonBubble) GetLeftAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LeftAt
	}
	return nil
}

// Engagement metrics
type EngagementMetrics struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	TotalBubbles           int32                  `protobuf:"varint,1,opt,name=total_bubbles,json=totalBubbles,proto3" json:"total_bubbles,omitempty"`
	ActiveBubbles          int32                  `protobuf:"varint,2,opt,name=active_bubbles,json=activeBubbles,proto3" json:"active_bubbles,omitempty"`
	TotalFriends           int32                  `protobuf:"varint,3,opt,name=total_friends,json=totalFriends,proto3" json:"total_friends,omitempty"`
	TotalContacts          int32                  `protobuf:"varint,4,opt,name=total_contacts,json=totalContacts,proto3" json:"total_contacts,omitempty"`
	MessagesSent           int32                  `protobuf:"varint,5,opt,name=messages_sent,json=messagesSent,proto3" json:"messages_sent,omitempty"`
	MessagesReceived       int32                  `protobuf:"varint,6,opt,name=messages_received,json=messagesReceived,proto3" json:"messages_received,omitempty"`
	AvgResponseTimeMinutes float32                `protobuf:"fixed32,7,opt,name=avg_response_time_minutes,json=avgResponseTimeMinutes,proto3" json:"avg_response_time_minutes,omitempty"`
	LastActiveAt           string                 `protobuf:"bytes,8,opt,name=last_active_at,json=lastActiveAt,proto3" json:"last_active_at,omitempty"`
	EngagementScore        float32                `protobuf:"fixed32,9,opt,name=engagement_score,json=engagementScore,proto3" json:"engagement_score,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *EngagementMetrics) Reset() {
	*x = EngagementMetrics{}
	mi := &file_social_analytics_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EngagementMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngagementMetrics) ProtoMessage() {}

func (x *EngagementMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngagementMetrics.ProtoReflect.Descriptor instead.
func (*EngagementMetrics) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{7}
}

func (x *EngagementMetrics) GetTotalBubbles() int32 {
	if x != nil {
		return x.TotalBubbles
	}
	return 0
}

func (x *EngagementMetrics) GetActiveBubbles() int32 {
	if x != nil {
		return x.ActiveBubbles
	}
	return 0
}

func (x *EngagementMetrics) GetTotalFriends() int32 {
	if x != nil {
		return x.TotalFriends
	}
	return 0
}

func (x *EngagementMetrics) GetTotalContacts() int32 {
	if x != nil {
		return x.TotalContacts
	}
	return 0
}

func (x *EngagementMetrics) GetMessagesSent() int32 {
	if x != nil {
		return x.MessagesSent
	}
	return 0
}

func (x *EngagementMetrics) GetMessagesReceived() int32 {
	if x != nil {
		return x.MessagesReceived
	}
	return 0
}

func (x *EngagementMetrics) GetAvgResponseTimeMinutes() float32 {
	if x != nil {
		return x.AvgResponseTimeMinutes
	}
	return 0
}

func (x *EngagementMetrics) GetLastActiveAt() string {
	if x != nil {
		return x.LastActiveAt
	}
	return ""
}

func (x *EngagementMetrics) GetEngagementScore() float32 {
	if x != nil {
		return x.EngagementScore
	}
	return 0
}

// Get mutual friends request
type GetMutualFriendsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMutualFriendsRequest) Reset() {
	*x = GetMutualFriendsRequest{}
	mi := &file_social_analytics_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMutualFriendsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMutualFriendsRequest) ProtoMessage() {}

func (x *GetMutualFriendsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMutualFriendsRequest.ProtoReflect.Descriptor instead.
func (*GetMutualFriendsRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{8}
}

func (x *GetMutualFriendsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetMutualFriendsRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

// Get mutual friends response
type GetMutualFriendsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MutualFriends []string               `protobuf:"bytes,1,rep,name=mutual_friends,json=mutualFriends,proto3" json:"mutual_friends,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMutualFriendsResponse) Reset() {
	*x = GetMutualFriendsResponse{}
	mi := &file_social_analytics_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMutualFriendsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMutualFriendsResponse) ProtoMessage() {}

func (x *GetMutualFriendsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMutualFriendsResponse.ProtoReflect.Descriptor instead.
func (*GetMutualFriendsResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{9}
}

func (x *GetMutualFriendsResponse) GetMutualFriends() []string {
	if x != nil {
		return x.MutualFriends
	}
	return nil
}

func (x *GetMutualFriendsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get mutual contacts request
type GetMutualContactsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMutualContactsRequest) Reset() {
	*x = GetMutualContactsRequest{}
	mi := &file_social_analytics_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMutualContactsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMutualContactsRequest) ProtoMessage() {}

func (x *GetMutualContactsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMutualContactsRequest.ProtoReflect.Descriptor instead.
func (*GetMutualContactsRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{10}
}

func (x *GetMutualContactsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetMutualContactsRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

// Get mutual contacts response
type GetMutualContactsResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MutualContacts []string               `protobuf:"bytes,1,rep,name=mutual_contacts,json=mutualContacts,proto3" json:"mutual_contacts,omitempty"`
	ApiResponse    *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetMutualContactsResponse) Reset() {
	*x = GetMutualContactsResponse{}
	mi := &file_social_analytics_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMutualContactsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMutualContactsResponse) ProtoMessage() {}

func (x *GetMutualContactsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMutualContactsResponse.ProtoReflect.Descriptor instead.
func (*GetMutualContactsResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{11}
}

func (x *GetMutualContactsResponse) GetMutualContacts() []string {
	if x != nil {
		return x.MutualContacts
	}
	return nil
}

func (x *GetMutualContactsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get common bubbles request
type GetCommonBubblesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommonBubblesRequest) Reset() {
	*x = GetCommonBubblesRequest{}
	mi := &file_social_analytics_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommonBubblesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommonBubblesRequest) ProtoMessage() {}

func (x *GetCommonBubblesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommonBubblesRequest.ProtoReflect.Descriptor instead.
func (*GetCommonBubblesRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{12}
}

func (x *GetCommonBubblesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetCommonBubblesRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

// Get common bubbles response
type GetCommonBubblesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommonBubbles []*CommonBubble        `protobuf:"bytes,1,rep,name=common_bubbles,json=commonBubbles,proto3" json:"common_bubbles,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommonBubblesResponse) Reset() {
	*x = GetCommonBubblesResponse{}
	mi := &file_social_analytics_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommonBubblesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommonBubblesResponse) ProtoMessage() {}

func (x *GetCommonBubblesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommonBubblesResponse.ProtoReflect.Descriptor instead.
func (*GetCommonBubblesResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{13}
}

func (x *GetCommonBubblesResponse) GetCommonBubbles() []*CommonBubble {
	if x != nil {
		return x.CommonBubbles
	}
	return nil
}

func (x *GetCommonBubblesResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get connection strength request
type GetConnectionStrengthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConnectionStrengthRequest) Reset() {
	*x = GetConnectionStrengthRequest{}
	mi := &file_social_analytics_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConnectionStrengthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectionStrengthRequest) ProtoMessage() {}

func (x *GetConnectionStrengthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectionStrengthRequest.ProtoReflect.Descriptor instead.
func (*GetConnectionStrengthRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{14}
}

func (x *GetConnectionStrengthRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetConnectionStrengthRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

// Get connection strength response
type GetConnectionStrengthResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ConnectionStrength *ConnectionStrength    `protobuf:"bytes,1,opt,name=connection_strength,json=connectionStrength,proto3" json:"connection_strength,omitempty"`
	ApiResponse        *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetConnectionStrengthResponse) Reset() {
	*x = GetConnectionStrengthResponse{}
	mi := &file_social_analytics_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConnectionStrengthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectionStrengthResponse) ProtoMessage() {}

func (x *GetConnectionStrengthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectionStrengthResponse.ProtoReflect.Descriptor instead.
func (*GetConnectionStrengthResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{15}
}

func (x *GetConnectionStrengthResponse) GetConnectionStrength() *ConnectionStrength {
	if x != nil {
		return x.ConnectionStrength
	}
	return nil
}

func (x *GetConnectionStrengthResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Connection strength information
type ConnectionStrength struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	UserId              string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId        string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	Strength            float32                `protobuf:"fixed32,3,opt,name=strength,proto3" json:"strength,omitempty"`
	MutualFriendsCount  int32                  `protobuf:"varint,4,opt,name=mutual_friends_count,json=mutualFriendsCount,proto3" json:"mutual_friends_count,omitempty"`
	MutualContactsCount int32                  `protobuf:"varint,5,opt,name=mutual_contacts_count,json=mutualContactsCount,proto3" json:"mutual_contacts_count,omitempty"`
	CommonBubblesCount  int32                  `protobuf:"varint,6,opt,name=common_bubbles_count,json=commonBubblesCount,proto3" json:"common_bubbles_count,omitempty"`
	InteractionScore    float32                `protobuf:"fixed32,7,opt,name=interaction_score,json=interactionScore,proto3" json:"interaction_score,omitempty"`
	Factors             []string               `protobuf:"bytes,8,rep,name=factors,proto3" json:"factors,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ConnectionStrength) Reset() {
	*x = ConnectionStrength{}
	mi := &file_social_analytics_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionStrength) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionStrength) ProtoMessage() {}

func (x *ConnectionStrength) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionStrength.ProtoReflect.Descriptor instead.
func (*ConnectionStrength) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{16}
}

func (x *ConnectionStrength) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ConnectionStrength) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

func (x *ConnectionStrength) GetStrength() float32 {
	if x != nil {
		return x.Strength
	}
	return 0
}

func (x *ConnectionStrength) GetMutualFriendsCount() int32 {
	if x != nil {
		return x.MutualFriendsCount
	}
	return 0
}

func (x *ConnectionStrength) GetMutualContactsCount() int32 {
	if x != nil {
		return x.MutualContactsCount
	}
	return 0
}

func (x *ConnectionStrength) GetCommonBubblesCount() int32 {
	if x != nil {
		return x.CommonBubblesCount
	}
	return 0
}

func (x *ConnectionStrength) GetInteractionScore() float32 {
	if x != nil {
		return x.InteractionScore
	}
	return 0
}

func (x *ConnectionStrength) GetFactors() []string {
	if x != nil {
		return x.Factors
	}
	return nil
}

// Get social graph request
type GetSocialGraphRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Depth         int32                  `protobuf:"varint,2,opt,name=depth,proto3" json:"depth,omitempty"`
	Limit         int32                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSocialGraphRequest) Reset() {
	*x = GetSocialGraphRequest{}
	mi := &file_social_analytics_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSocialGraphRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSocialGraphRequest) ProtoMessage() {}

func (x *GetSocialGraphRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSocialGraphRequest.ProtoReflect.Descriptor instead.
func (*GetSocialGraphRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{17}
}

func (x *GetSocialGraphRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetSocialGraphRequest) GetDepth() int32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

func (x *GetSocialGraphRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// Get social graph response
type GetSocialGraphResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SocialGraph   *SocialGraph           `protobuf:"bytes,1,opt,name=social_graph,json=socialGraph,proto3" json:"social_graph,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSocialGraphResponse) Reset() {
	*x = GetSocialGraphResponse{}
	mi := &file_social_analytics_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSocialGraphResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSocialGraphResponse) ProtoMessage() {}

func (x *GetSocialGraphResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSocialGraphResponse.ProtoReflect.Descriptor instead.
func (*GetSocialGraphResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{18}
}

func (x *GetSocialGraphResponse) GetSocialGraph() *SocialGraph {
	if x != nil {
		return x.SocialGraph
	}
	return nil
}

func (x *GetSocialGraphResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Social graph data
type SocialGraph struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Nodes         []*SocialNode          `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Edges         []*SocialEdge          `protobuf:"bytes,3,rep,name=edges,proto3" json:"edges,omitempty"`
	Clusters      []*SocialCluster       `protobuf:"bytes,4,rep,name=clusters,proto3" json:"clusters,omitempty"`
	Metrics       *SocialGraphMetrics    `protobuf:"bytes,5,opt,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SocialGraph) Reset() {
	*x = SocialGraph{}
	mi := &file_social_analytics_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocialGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialGraph) ProtoMessage() {}

func (x *SocialGraph) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialGraph.ProtoReflect.Descriptor instead.
func (*SocialGraph) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{19}
}

func (x *SocialGraph) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SocialGraph) GetNodes() []*SocialNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *SocialGraph) GetEdges() []*SocialEdge {
	if x != nil {
		return x.Edges
	}
	return nil
}

func (x *SocialGraph) GetClusters() []*SocialCluster {
	if x != nil {
		return x.Clusters
	}
	return nil
}

func (x *SocialGraph) GetMetrics() *SocialGraphMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

// Social node
type SocialNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	NodeType      string                 `protobuf:"bytes,2,opt,name=node_type,json=nodeType,proto3" json:"node_type,omitempty"`
	Centrality    float32                `protobuf:"fixed32,3,opt,name=centrality,proto3" json:"centrality,omitempty"`
	ClusterId     string                 `protobuf:"bytes,4,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SocialNode) Reset() {
	*x = SocialNode{}
	mi := &file_social_analytics_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocialNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialNode) ProtoMessage() {}

func (x *SocialNode) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialNode.ProtoReflect.Descriptor instead.
func (*SocialNode) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{20}
}

func (x *SocialNode) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SocialNode) GetNodeType() string {
	if x != nil {
		return x.NodeType
	}
	return ""
}

func (x *SocialNode) GetCentrality() float32 {
	if x != nil {
		return x.Centrality
	}
	return 0
}

func (x *SocialNode) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

// Social edge
type SocialEdge struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FromUserId    string                 `protobuf:"bytes,1,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId      string                 `protobuf:"bytes,2,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	EdgeType      string                 `protobuf:"bytes,3,opt,name=edge_type,json=edgeType,proto3" json:"edge_type,omitempty"`
	Weight        float32                `protobuf:"fixed32,4,opt,name=weight,proto3" json:"weight,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SocialEdge) Reset() {
	*x = SocialEdge{}
	mi := &file_social_analytics_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocialEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialEdge) ProtoMessage() {}

func (x *SocialEdge) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialEdge.ProtoReflect.Descriptor instead.
func (*SocialEdge) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{21}
}

func (x *SocialEdge) GetFromUserId() string {
	if x != nil {
		return x.FromUserId
	}
	return ""
}

func (x *SocialEdge) GetToUserId() string {
	if x != nil {
		return x.ToUserId
	}
	return ""
}

func (x *SocialEdge) GetEdgeType() string {
	if x != nil {
		return x.EdgeType
	}
	return ""
}

func (x *SocialEdge) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *SocialEdge) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

// Social cluster
type SocialCluster struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClusterId     string                 `protobuf:"bytes,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Members       []string               `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`
	Cohesion      float32                `protobuf:"fixed32,3,opt,name=cohesion,proto3" json:"cohesion,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SocialCluster) Reset() {
	*x = SocialCluster{}
	mi := &file_social_analytics_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocialCluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialCluster) ProtoMessage() {}

func (x *SocialCluster) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialCluster.ProtoReflect.Descriptor instead.
func (*SocialCluster) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{22}
}

func (x *SocialCluster) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SocialCluster) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *SocialCluster) GetCohesion() float32 {
	if x != nil {
		return x.Cohesion
	}
	return 0
}

func (x *SocialCluster) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// Social graph metrics
type SocialGraphMetrics struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	TotalNodes            int32                  `protobuf:"varint,1,opt,name=total_nodes,json=totalNodes,proto3" json:"total_nodes,omitempty"`
	TotalEdges            int32                  `protobuf:"varint,2,opt,name=total_edges,json=totalEdges,proto3" json:"total_edges,omitempty"`
	Density               float32                `protobuf:"fixed32,3,opt,name=density,proto3" json:"density,omitempty"`
	ClusteringCoefficient float32                `protobuf:"fixed32,4,opt,name=clustering_coefficient,json=clusteringCoefficient,proto3" json:"clustering_coefficient,omitempty"`
	AvgPathLength         float32                `protobuf:"fixed32,5,opt,name=avg_path_length,json=avgPathLength,proto3" json:"avg_path_length,omitempty"`
	CentralityScore       float32                `protobuf:"fixed32,6,opt,name=centrality_score,json=centralityScore,proto3" json:"centrality_score,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *SocialGraphMetrics) Reset() {
	*x = SocialGraphMetrics{}
	mi := &file_social_analytics_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocialGraphMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialGraphMetrics) ProtoMessage() {}

func (x *SocialGraphMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialGraphMetrics.ProtoReflect.Descriptor instead.
func (*SocialGraphMetrics) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{23}
}

func (x *SocialGraphMetrics) GetTotalNodes() int32 {
	if x != nil {
		return x.TotalNodes
	}
	return 0
}

func (x *SocialGraphMetrics) GetTotalEdges() int32 {
	if x != nil {
		return x.TotalEdges
	}
	return 0
}

func (x *SocialGraphMetrics) GetDensity() float32 {
	if x != nil {
		return x.Density
	}
	return 0
}

func (x *SocialGraphMetrics) GetClusteringCoefficient() float32 {
	if x != nil {
		return x.ClusteringCoefficient
	}
	return 0
}

func (x *SocialGraphMetrics) GetAvgPathLength() float32 {
	if x != nil {
		return x.AvgPathLength
	}
	return 0
}

func (x *SocialGraphMetrics) GetCentralityScore() float32 {
	if x != nil {
		return x.CentralityScore
	}
	return 0
}

// Get engagement metrics request
type GetEngagementMetricsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEngagementMetricsRequest) Reset() {
	*x = GetEngagementMetricsRequest{}
	mi := &file_social_analytics_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEngagementMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEngagementMetricsRequest) ProtoMessage() {}

func (x *GetEngagementMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEngagementMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetEngagementMetricsRequest) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{24}
}

func (x *GetEngagementMetricsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get engagement metrics response
type GetEngagementMetricsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	EngagementMetrics *EngagementMetrics     `protobuf:"bytes,1,opt,name=engagement_metrics,json=engagementMetrics,proto3" json:"engagement_metrics,omitempty"`
	ApiResponse       *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetEngagementMetricsResponse) Reset() {
	*x = GetEngagementMetricsResponse{}
	mi := &file_social_analytics_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEngagementMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEngagementMetricsResponse) ProtoMessage() {}

func (x *GetEngagementMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_social_analytics_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEngagementMetricsResponse.ProtoReflect.Descriptor instead.
func (*GetEngagementMetricsResponse) Descriptor() ([]byte, []int) {
	return file_social_analytics_proto_rawDescGZIP(), []int{25}
}

func (x *GetEngagementMetricsResponse) GetEngagementMetrics() *EngagementMetrics {
	if x != nil {
		return x.EngagementMetrics
	}
	return nil
}

func (x *GetEngagementMetricsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

var File_social_analytics_proto protoreflect.FileDescriptor

const file_social_analytics_proto_rawDesc = "" +
	"\n" +
	"\x16social_analytics.proto\x12\x19hopen.social_analytics.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"4\n" +
	"\x19GetEnhancedProfileRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\xa3\x01\n" +
	"\x1aGetEnhancedProfileResponse\x12D\n" +
	"\aprofile\x18\x01 \x01(\v2*.hopen.social_analytics.v1.EnhancedProfileR\aprofile\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x82\x03\n" +
	"\x0fEnhancedProfile\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"first_name\x18\x04 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x05 \x01(\tR\blastName\x12\"\n" +
	"\n" +
	"avatar_url\x18\x06 \x01(\tH\x00R\tavatarUrl\x88\x01\x01\x12 \n" +
	"\tbubble_id\x18\a \x01(\tH\x01R\bbubbleId\x88\x01\x01\x12\x1b\n" +
	"\tis_online\x18\b \x01(\bR\bisOnline\x12\x1d\n" +
	"\n" +
	"friend_ids\x18\t \x03(\tR\tfriendIds\x12\x1f\n" +
	"\vcontact_ids\x18\n" +
	" \x03(\tR\n" +
	"contactIds\x12(\n" +
	"\x10blocked_user_ids\x18\v \x03(\tR\x0eblockedUserIdsB\r\n" +
	"\v_avatar_urlB\f\n" +
	"\n" +
	"_bubble_id\"[\n" +
	"\x1aGetProfileAnalyticsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\"\xa9\x01\n" +
	"\x1bGetProfileAnalyticsResponse\x12I\n" +
	"\tanalytics\x18\x01 \x01(\v2+.hopen.social_analytics.v1.ProfileAnalyticsR\tanalytics\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xad\x03\n" +
	"\x10ProfileAnalytics\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12%\n" +
	"\x0emutual_friends\x18\x02 \x03(\tR\rmutualFriends\x12'\n" +
	"\x0fmutual_contacts\x18\x03 \x03(\tR\x0emutualContacts\x12N\n" +
	"\x0ecommon_bubbles\x18\x04 \x03(\v2'.hopen.social_analytics.v1.CommonBubbleR\rcommonBubbles\x12/\n" +
	"\x13connection_strength\x18\x05 \x01(\x02R\x12connectionStrength\x12!\n" +
	"\fsocial_score\x18\x06 \x01(\x02R\vsocialScore\x12[\n" +
	"\x12engagement_metrics\x18\a \x01(\v2,.hopen.social_analytics.v1.EngagementMetricsR\x11engagementMetrics\x12/\n" +
	"\x13recommended_actions\x18\b \x03(\tR\x12recommendedActions\"\xe3\x01\n" +
	"\fCommonBubble\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x1f\n" +
	"\vbubble_name\x18\x02 \x01(\tR\n" +
	"bubbleName\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x127\n" +
	"\tjoined_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\bjoinedAt\x128\n" +
	"\aleft_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\x06leftAt\x88\x01\x01B\n" +
	"\n" +
	"\b_left_at\"\x89\x03\n" +
	"\x11EngagementMetrics\x12#\n" +
	"\rtotal_bubbles\x18\x01 \x01(\x05R\ftotalBubbles\x12%\n" +
	"\x0eactive_bubbles\x18\x02 \x01(\x05R\ractiveBubbles\x12#\n" +
	"\rtotal_friends\x18\x03 \x01(\x05R\ftotalFriends\x12%\n" +
	"\x0etotal_contacts\x18\x04 \x01(\x05R\rtotalContacts\x12#\n" +
	"\rmessages_sent\x18\x05 \x01(\x05R\fmessagesSent\x12+\n" +
	"\x11messages_received\x18\x06 \x01(\x05R\x10messagesReceived\x129\n" +
	"\x19avg_response_time_minutes\x18\a \x01(\x02R\x16avgResponseTimeMinutes\x12$\n" +
	"\x0elast_active_at\x18\b \x01(\tR\flastActiveAt\x12)\n" +
	"\x10engagement_score\x18\t \x01(\x02R\x0fengagementScore\"X\n" +
	"\x17GetMutualFriendsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\"\x82\x01\n" +
	"\x18GetMutualFriendsResponse\x12%\n" +
	"\x0emutual_friends\x18\x01 \x03(\tR\rmutualFriends\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"Y\n" +
	"\x18GetMutualContactsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\"\x85\x01\n" +
	"\x19GetMutualContactsResponse\x12'\n" +
	"\x0fmutual_contacts\x18\x01 \x03(\tR\x0emutualContacts\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"X\n" +
	"\x17GetCommonBubblesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\"\xab\x01\n" +
	"\x18GetCommonBubblesResponse\x12N\n" +
	"\x0ecommon_bubbles\x18\x01 \x03(\v2'.hopen.social_analytics.v1.CommonBubbleR\rcommonBubbles\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"]\n" +
	"\x1cGetConnectionStrengthRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\"\xc0\x01\n" +
	"\x1dGetConnectionStrengthResponse\x12^\n" +
	"\x13connection_strength\x18\x01 \x01(\v2-.hopen.social_analytics.v1.ConnectionStrengthR\x12connectionStrength\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xce\x02\n" +
	"\x12ConnectionStrength\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\x12\x1a\n" +
	"\bstrength\x18\x03 \x01(\x02R\bstrength\x120\n" +
	"\x14mutual_friends_count\x18\x04 \x01(\x05R\x12mutualFriendsCount\x122\n" +
	"\x15mutual_contacts_count\x18\x05 \x01(\x05R\x13mutualContactsCount\x120\n" +
	"\x14common_bubbles_count\x18\x06 \x01(\x05R\x12commonBubblesCount\x12+\n" +
	"\x11interaction_score\x18\a \x01(\x02R\x10interactionScore\x12\x18\n" +
	"\afactors\x18\b \x03(\tR\afactors\"\\\n" +
	"\x15GetSocialGraphRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x14\n" +
	"\x05depth\x18\x02 \x01(\x05R\x05depth\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x05R\x05limit\"\xa4\x01\n" +
	"\x16GetSocialGraphResponse\x12I\n" +
	"\fsocial_graph\x18\x01 \x01(\v2&.hopen.social_analytics.v1.SocialGraphR\vsocialGraph\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xaf\x02\n" +
	"\vSocialGraph\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12;\n" +
	"\x05nodes\x18\x02 \x03(\v2%.hopen.social_analytics.v1.SocialNodeR\x05nodes\x12;\n" +
	"\x05edges\x18\x03 \x03(\v2%.hopen.social_analytics.v1.SocialEdgeR\x05edges\x12D\n" +
	"\bclusters\x18\x04 \x03(\v2(.hopen.social_analytics.v1.SocialClusterR\bclusters\x12G\n" +
	"\ametrics\x18\x05 \x01(\v2-.hopen.social_analytics.v1.SocialGraphMetricsR\ametrics\"\x81\x01\n" +
	"\n" +
	"SocialNode\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tnode_type\x18\x02 \x01(\tR\bnodeType\x12\x1e\n" +
	"\n" +
	"centrality\x18\x03 \x01(\x02R\n" +
	"centrality\x12\x1d\n" +
	"\n" +
	"cluster_id\x18\x04 \x01(\tR\tclusterId\"\xa0\x01\n" +
	"\n" +
	"SocialEdge\x12 \n" +
	"\ffrom_user_id\x18\x01 \x01(\tR\n" +
	"fromUserId\x12\x1c\n" +
	"\n" +
	"to_user_id\x18\x02 \x01(\tR\btoUserId\x12\x1b\n" +
	"\tedge_type\x18\x03 \x01(\tR\bedgeType\x12\x16\n" +
	"\x06weight\x18\x04 \x01(\x02R\x06weight\x12\x1d\n" +
	"\n" +
	"created_at\x18\x05 \x01(\tR\tcreatedAt\"\x86\x01\n" +
	"\rSocialCluster\x12\x1d\n" +
	"\n" +
	"cluster_id\x18\x01 \x01(\tR\tclusterId\x12\x18\n" +
	"\amembers\x18\x02 \x03(\tR\amembers\x12\x1a\n" +
	"\bcohesion\x18\x03 \x01(\x02R\bcohesion\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"\xfa\x01\n" +
	"\x12SocialGraphMetrics\x12\x1f\n" +
	"\vtotal_nodes\x18\x01 \x01(\x05R\n" +
	"totalNodes\x12\x1f\n" +
	"\vtotal_edges\x18\x02 \x01(\x05R\n" +
	"totalEdges\x12\x18\n" +
	"\adensity\x18\x03 \x01(\x02R\adensity\x125\n" +
	"\x16clustering_coefficient\x18\x04 \x01(\x02R\x15clusteringCoefficient\x12&\n" +
	"\x0favg_path_length\x18\x05 \x01(\x02R\ravgPathLength\x12)\n" +
	"\x10centrality_score\x18\x06 \x01(\x02R\x0fcentralityScore\"6\n" +
	"\x1bGetEngagementMetricsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\xbc\x01\n" +
	"\x1cGetEngagementMetricsResponse\x12[\n" +
	"\x12engagement_metrics\x18\x01 \x01(\v2,.hopen.social_analytics.v1.EngagementMetricsR\x11engagementMetrics\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse2\xab\b\n" +
	"\x16SocialAnalyticsService\x12\x81\x01\n" +
	"\x12GetEnhancedProfile\x124.hopen.social_analytics.v1.GetEnhancedProfileRequest\x1a5.hopen.social_analytics.v1.GetEnhancedProfileResponse\x12\x84\x01\n" +
	"\x13GetProfileAnalytics\x125.hopen.social_analytics.v1.GetProfileAnalyticsRequest\x1a6.hopen.social_analytics.v1.GetProfileAnalyticsResponse\x12{\n" +
	"\x10GetMutualFriends\x122.hopen.social_analytics.v1.GetMutualFriendsRequest\x1a3.hopen.social_analytics.v1.GetMutualFriendsResponse\x12~\n" +
	"\x11GetMutualContacts\x123.hopen.social_analytics.v1.GetMutualContactsRequest\x1a4.hopen.social_analytics.v1.GetMutualContactsResponse\x12{\n" +
	"\x10GetCommonBubbles\x122.hopen.social_analytics.v1.GetCommonBubblesRequest\x1a3.hopen.social_analytics.v1.GetCommonBubblesResponse\x12\x8a\x01\n" +
	"\x15GetConnectionStrength\x127.hopen.social_analytics.v1.GetConnectionStrengthRequest\x1a8.hopen.social_analytics.v1.GetConnectionStrengthResponse\x12u\n" +
	"\x0eGetSocialGraph\x120.hopen.social_analytics.v1.GetSocialGraphRequest\x1a1.hopen.social_analytics.v1.GetSocialGraphResponse\x12\x87\x01\n" +
	"\x14GetEngagementMetrics\x126.hopen.social_analytics.v1.GetEngagementMetricsRequest\x1a7.hopen.social_analytics.v1.GetEngagementMetricsResponseB[\n" +
	"\x1dcom.hopen.social_analytics.v1P\x01Z8hopenbackend/protos/gen/social_analytics;socialanalyticsb\x06proto3"

var (
	file_social_analytics_proto_rawDescOnce sync.Once
	file_social_analytics_proto_rawDescData []byte
)

func file_social_analytics_proto_rawDescGZIP() []byte {
	file_social_analytics_proto_rawDescOnce.Do(func() {
		file_social_analytics_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_social_analytics_proto_rawDesc), len(file_social_analytics_proto_rawDesc)))
	})
	return file_social_analytics_proto_rawDescData
}

var file_social_analytics_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_social_analytics_proto_goTypes = []any{
	(*GetEnhancedProfileRequest)(nil),     // 0: hopen.social_analytics.v1.GetEnhancedProfileRequest
	(*GetEnhancedProfileResponse)(nil),    // 1: hopen.social_analytics.v1.GetEnhancedProfileResponse
	(*EnhancedProfile)(nil),               // 2: hopen.social_analytics.v1.EnhancedProfile
	(*GetProfileAnalyticsRequest)(nil),    // 3: hopen.social_analytics.v1.GetProfileAnalyticsRequest
	(*GetProfileAnalyticsResponse)(nil),   // 4: hopen.social_analytics.v1.GetProfileAnalyticsResponse
	(*ProfileAnalytics)(nil),              // 5: hopen.social_analytics.v1.ProfileAnalytics
	(*CommonBubble)(nil),                  // 6: hopen.social_analytics.v1.CommonBubble
	(*EngagementMetrics)(nil),             // 7: hopen.social_analytics.v1.EngagementMetrics
	(*GetMutualFriendsRequest)(nil),       // 8: hopen.social_analytics.v1.GetMutualFriendsRequest
	(*GetMutualFriendsResponse)(nil),      // 9: hopen.social_analytics.v1.GetMutualFriendsResponse
	(*GetMutualContactsRequest)(nil),      // 10: hopen.social_analytics.v1.GetMutualContactsRequest
	(*GetMutualContactsResponse)(nil),     // 11: hopen.social_analytics.v1.GetMutualContactsResponse
	(*GetCommonBubblesRequest)(nil),       // 12: hopen.social_analytics.v1.GetCommonBubblesRequest
	(*GetCommonBubblesResponse)(nil),      // 13: hopen.social_analytics.v1.GetCommonBubblesResponse
	(*GetConnectionStrengthRequest)(nil),  // 14: hopen.social_analytics.v1.GetConnectionStrengthRequest
	(*GetConnectionStrengthResponse)(nil), // 15: hopen.social_analytics.v1.GetConnectionStrengthResponse
	(*ConnectionStrength)(nil),            // 16: hopen.social_analytics.v1.ConnectionStrength
	(*GetSocialGraphRequest)(nil),         // 17: hopen.social_analytics.v1.GetSocialGraphRequest
	(*GetSocialGraphResponse)(nil),        // 18: hopen.social_analytics.v1.GetSocialGraphResponse
	(*SocialGraph)(nil),                   // 19: hopen.social_analytics.v1.SocialGraph
	(*SocialNode)(nil),                    // 20: hopen.social_analytics.v1.SocialNode
	(*SocialEdge)(nil),                    // 21: hopen.social_analytics.v1.SocialEdge
	(*SocialCluster)(nil),                 // 22: hopen.social_analytics.v1.SocialCluster
	(*SocialGraphMetrics)(nil),            // 23: hopen.social_analytics.v1.SocialGraphMetrics
	(*GetEngagementMetricsRequest)(nil),   // 24: hopen.social_analytics.v1.GetEngagementMetricsRequest
	(*GetEngagementMetricsResponse)(nil),  // 25: hopen.social_analytics.v1.GetEngagementMetricsResponse
	(*common.ApiResponse)(nil),            // 26: hopen.common.v1.ApiResponse
	(*timestamppb.Timestamp)(nil),         // 27: google.protobuf.Timestamp
}
var file_social_analytics_proto_depIdxs = []int32{
	2,  // 0: hopen.social_analytics.v1.GetEnhancedProfileResponse.profile:type_name -> hopen.social_analytics.v1.EnhancedProfile
	26, // 1: hopen.social_analytics.v1.GetEnhancedProfileResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	5,  // 2: hopen.social_analytics.v1.GetProfileAnalyticsResponse.analytics:type_name -> hopen.social_analytics.v1.ProfileAnalytics
	26, // 3: hopen.social_analytics.v1.GetProfileAnalyticsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	6,  // 4: hopen.social_analytics.v1.ProfileAnalytics.common_bubbles:type_name -> hopen.social_analytics.v1.CommonBubble
	7,  // 5: hopen.social_analytics.v1.ProfileAnalytics.engagement_metrics:type_name -> hopen.social_analytics.v1.EngagementMetrics
	27, // 6: hopen.social_analytics.v1.CommonBubble.joined_at:type_name -> google.protobuf.Timestamp
	27, // 7: hopen.social_analytics.v1.CommonBubble.left_at:type_name -> google.protobuf.Timestamp
	26, // 8: hopen.social_analytics.v1.GetMutualFriendsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 9: hopen.social_analytics.v1.GetMutualContactsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	6,  // 10: hopen.social_analytics.v1.GetCommonBubblesResponse.common_bubbles:type_name -> hopen.social_analytics.v1.CommonBubble
	26, // 11: hopen.social_analytics.v1.GetCommonBubblesResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	16, // 12: hopen.social_analytics.v1.GetConnectionStrengthResponse.connection_strength:type_name -> hopen.social_analytics.v1.ConnectionStrength
	26, // 13: hopen.social_analytics.v1.GetConnectionStrengthResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	19, // 14: hopen.social_analytics.v1.GetSocialGraphResponse.social_graph:type_name -> hopen.social_analytics.v1.SocialGraph
	26, // 15: hopen.social_analytics.v1.GetSocialGraphResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	20, // 16: hopen.social_analytics.v1.SocialGraph.nodes:type_name -> hopen.social_analytics.v1.SocialNode
	21, // 17: hopen.social_analytics.v1.SocialGraph.edges:type_name -> hopen.social_analytics.v1.SocialEdge
	22, // 18: hopen.social_analytics.v1.SocialGraph.clusters:type_name -> hopen.social_analytics.v1.SocialCluster
	23, // 19: hopen.social_analytics.v1.SocialGraph.metrics:type_name -> hopen.social_analytics.v1.SocialGraphMetrics
	7,  // 20: hopen.social_analytics.v1.GetEngagementMetricsResponse.engagement_metrics:type_name -> hopen.social_analytics.v1.EngagementMetrics
	26, // 21: hopen.social_analytics.v1.GetEngagementMetricsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 22: hopen.social_analytics.v1.SocialAnalyticsService.GetEnhancedProfile:input_type -> hopen.social_analytics.v1.GetEnhancedProfileRequest
	3,  // 23: hopen.social_analytics.v1.SocialAnalyticsService.GetProfileAnalytics:input_type -> hopen.social_analytics.v1.GetProfileAnalyticsRequest
	8,  // 24: hopen.social_analytics.v1.SocialAnalyticsService.GetMutualFriends:input_type -> hopen.social_analytics.v1.GetMutualFriendsRequest
	10, // 25: hopen.social_analytics.v1.SocialAnalyticsService.GetMutualContacts:input_type -> hopen.social_analytics.v1.GetMutualContactsRequest
	12, // 26: hopen.social_analytics.v1.SocialAnalyticsService.GetCommonBubbles:input_type -> hopen.social_analytics.v1.GetCommonBubblesRequest
	14, // 27: hopen.social_analytics.v1.SocialAnalyticsService.GetConnectionStrength:input_type -> hopen.social_analytics.v1.GetConnectionStrengthRequest
	17, // 28: hopen.social_analytics.v1.SocialAnalyticsService.GetSocialGraph:input_type -> hopen.social_analytics.v1.GetSocialGraphRequest
	24, // 29: hopen.social_analytics.v1.SocialAnalyticsService.GetEngagementMetrics:input_type -> hopen.social_analytics.v1.GetEngagementMetricsRequest
	1,  // 30: hopen.social_analytics.v1.SocialAnalyticsService.GetEnhancedProfile:output_type -> hopen.social_analytics.v1.GetEnhancedProfileResponse
	4,  // 31: hopen.social_analytics.v1.SocialAnalyticsService.GetProfileAnalytics:output_type -> hopen.social_analytics.v1.GetProfileAnalyticsResponse
	9,  // 32: hopen.social_analytics.v1.SocialAnalyticsService.GetMutualFriends:output_type -> hopen.social_analytics.v1.GetMutualFriendsResponse
	11, // 33: hopen.social_analytics.v1.SocialAnalyticsService.GetMutualContacts:output_type -> hopen.social_analytics.v1.GetMutualContactsResponse
	13, // 34: hopen.social_analytics.v1.SocialAnalyticsService.GetCommonBubbles:output_type -> hopen.social_analytics.v1.GetCommonBubblesResponse
	15, // 35: hopen.social_analytics.v1.SocialAnalyticsService.GetConnectionStrength:output_type -> hopen.social_analytics.v1.GetConnectionStrengthResponse
	18, // 36: hopen.social_analytics.v1.SocialAnalyticsService.GetSocialGraph:output_type -> hopen.social_analytics.v1.GetSocialGraphResponse
	25, // 37: hopen.social_analytics.v1.SocialAnalyticsService.GetEngagementMetrics:output_type -> hopen.social_analytics.v1.GetEngagementMetricsResponse
	30, // [30:38] is the sub-list for method output_type
	22, // [22:30] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_social_analytics_proto_init() }
func file_social_analytics_proto_init() {
	if File_social_analytics_proto != nil {
		return
	}
	file_social_analytics_proto_msgTypes[2].OneofWrappers = []any{}
	file_social_analytics_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_social_analytics_proto_rawDesc), len(file_social_analytics_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_social_analytics_proto_goTypes,
		DependencyIndexes: file_social_analytics_proto_depIdxs,
		MessageInfos:      file_social_analytics_proto_msgTypes,
	}.Build()
	File_social_analytics_proto = out.File
	file_social_analytics_proto_goTypes = nil
	file_social_analytics_proto_depIdxs = nil
}
