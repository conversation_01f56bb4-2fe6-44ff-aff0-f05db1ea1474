// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: social_analytics.proto

package socialanalytics

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SocialAnalyticsService_GetEnhancedProfile_FullMethodName    = "/hopen.social_analytics.v1.SocialAnalyticsService/GetEnhancedProfile"
	SocialAnalyticsService_GetProfileAnalytics_FullMethodName   = "/hopen.social_analytics.v1.SocialAnalyticsService/GetProfileAnalytics"
	SocialAnalyticsService_GetMutualFriends_FullMethodName      = "/hopen.social_analytics.v1.SocialAnalyticsService/GetMutualFriends"
	SocialAnalyticsService_GetMutualContacts_FullMethodName     = "/hopen.social_analytics.v1.SocialAnalyticsService/GetMutualContacts"
	SocialAnalyticsService_GetCommonBubbles_FullMethodName      = "/hopen.social_analytics.v1.SocialAnalyticsService/GetCommonBubbles"
	SocialAnalyticsService_GetConnectionStrength_FullMethodName = "/hopen.social_analytics.v1.SocialAnalyticsService/GetConnectionStrength"
	SocialAnalyticsService_GetSocialGraph_FullMethodName        = "/hopen.social_analytics.v1.SocialAnalyticsService/GetSocialGraph"
	SocialAnalyticsService_GetEngagementMetrics_FullMethodName  = "/hopen.social_analytics.v1.SocialAnalyticsService/GetEngagementMetrics"
)

// SocialAnalyticsServiceClient is the client API for SocialAnalyticsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Social analytics service for social graph analysis
type SocialAnalyticsServiceClient interface {
	// Get enhanced profile with comprehensive relationship data
	GetEnhancedProfile(ctx context.Context, in *GetEnhancedProfileRequest, opts ...grpc.CallOption) (*GetEnhancedProfileResponse, error)
	// Get comprehensive profile analytics including mutual connections
	GetProfileAnalytics(ctx context.Context, in *GetProfileAnalyticsRequest, opts ...grpc.CallOption) (*GetProfileAnalyticsResponse, error)
	// Get mutual friends between users
	GetMutualFriends(ctx context.Context, in *GetMutualFriendsRequest, opts ...grpc.CallOption) (*GetMutualFriendsResponse, error)
	// Get mutual contacts between users
	GetMutualContacts(ctx context.Context, in *GetMutualContactsRequest, opts ...grpc.CallOption) (*GetMutualContactsResponse, error)
	// Get common bubbles between users
	GetCommonBubbles(ctx context.Context, in *GetCommonBubblesRequest, opts ...grpc.CallOption) (*GetCommonBubblesResponse, error)
	// Get connection strength between users
	GetConnectionStrength(ctx context.Context, in *GetConnectionStrengthRequest, opts ...grpc.CallOption) (*GetConnectionStrengthResponse, error)
	// Get social graph data
	GetSocialGraph(ctx context.Context, in *GetSocialGraphRequest, opts ...grpc.CallOption) (*GetSocialGraphResponse, error)
	// Get engagement metrics
	GetEngagementMetrics(ctx context.Context, in *GetEngagementMetricsRequest, opts ...grpc.CallOption) (*GetEngagementMetricsResponse, error)
}

type socialAnalyticsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSocialAnalyticsServiceClient(cc grpc.ClientConnInterface) SocialAnalyticsServiceClient {
	return &socialAnalyticsServiceClient{cc}
}

func (c *socialAnalyticsServiceClient) GetEnhancedProfile(ctx context.Context, in *GetEnhancedProfileRequest, opts ...grpc.CallOption) (*GetEnhancedProfileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEnhancedProfileResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetEnhancedProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *socialAnalyticsServiceClient) GetProfileAnalytics(ctx context.Context, in *GetProfileAnalyticsRequest, opts ...grpc.CallOption) (*GetProfileAnalyticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProfileAnalyticsResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetProfileAnalytics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *socialAnalyticsServiceClient) GetMutualFriends(ctx context.Context, in *GetMutualFriendsRequest, opts ...grpc.CallOption) (*GetMutualFriendsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMutualFriendsResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetMutualFriends_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *socialAnalyticsServiceClient) GetMutualContacts(ctx context.Context, in *GetMutualContactsRequest, opts ...grpc.CallOption) (*GetMutualContactsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMutualContactsResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetMutualContacts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *socialAnalyticsServiceClient) GetCommonBubbles(ctx context.Context, in *GetCommonBubblesRequest, opts ...grpc.CallOption) (*GetCommonBubblesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCommonBubblesResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetCommonBubbles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *socialAnalyticsServiceClient) GetConnectionStrength(ctx context.Context, in *GetConnectionStrengthRequest, opts ...grpc.CallOption) (*GetConnectionStrengthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConnectionStrengthResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetConnectionStrength_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *socialAnalyticsServiceClient) GetSocialGraph(ctx context.Context, in *GetSocialGraphRequest, opts ...grpc.CallOption) (*GetSocialGraphResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSocialGraphResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetSocialGraph_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *socialAnalyticsServiceClient) GetEngagementMetrics(ctx context.Context, in *GetEngagementMetricsRequest, opts ...grpc.CallOption) (*GetEngagementMetricsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEngagementMetricsResponse)
	err := c.cc.Invoke(ctx, SocialAnalyticsService_GetEngagementMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SocialAnalyticsServiceServer is the server API for SocialAnalyticsService service.
// All implementations must embed UnimplementedSocialAnalyticsServiceServer
// for forward compatibility.
//
// Social analytics service for social graph analysis
type SocialAnalyticsServiceServer interface {
	// Get enhanced profile with comprehensive relationship data
	GetEnhancedProfile(context.Context, *GetEnhancedProfileRequest) (*GetEnhancedProfileResponse, error)
	// Get comprehensive profile analytics including mutual connections
	GetProfileAnalytics(context.Context, *GetProfileAnalyticsRequest) (*GetProfileAnalyticsResponse, error)
	// Get mutual friends between users
	GetMutualFriends(context.Context, *GetMutualFriendsRequest) (*GetMutualFriendsResponse, error)
	// Get mutual contacts between users
	GetMutualContacts(context.Context, *GetMutualContactsRequest) (*GetMutualContactsResponse, error)
	// Get common bubbles between users
	GetCommonBubbles(context.Context, *GetCommonBubblesRequest) (*GetCommonBubblesResponse, error)
	// Get connection strength between users
	GetConnectionStrength(context.Context, *GetConnectionStrengthRequest) (*GetConnectionStrengthResponse, error)
	// Get social graph data
	GetSocialGraph(context.Context, *GetSocialGraphRequest) (*GetSocialGraphResponse, error)
	// Get engagement metrics
	GetEngagementMetrics(context.Context, *GetEngagementMetricsRequest) (*GetEngagementMetricsResponse, error)
	mustEmbedUnimplementedSocialAnalyticsServiceServer()
}

// UnimplementedSocialAnalyticsServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSocialAnalyticsServiceServer struct{}

func (UnimplementedSocialAnalyticsServiceServer) GetEnhancedProfile(context.Context, *GetEnhancedProfileRequest) (*GetEnhancedProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnhancedProfile not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) GetProfileAnalytics(context.Context, *GetProfileAnalyticsRequest) (*GetProfileAnalyticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfileAnalytics not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) GetMutualFriends(context.Context, *GetMutualFriendsRequest) (*GetMutualFriendsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMutualFriends not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) GetMutualContacts(context.Context, *GetMutualContactsRequest) (*GetMutualContactsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMutualContacts not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) GetCommonBubbles(context.Context, *GetCommonBubblesRequest) (*GetCommonBubblesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommonBubbles not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) GetConnectionStrength(context.Context, *GetConnectionStrengthRequest) (*GetConnectionStrengthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConnectionStrength not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) GetSocialGraph(context.Context, *GetSocialGraphRequest) (*GetSocialGraphResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSocialGraph not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) GetEngagementMetrics(context.Context, *GetEngagementMetricsRequest) (*GetEngagementMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEngagementMetrics not implemented")
}
func (UnimplementedSocialAnalyticsServiceServer) mustEmbedUnimplementedSocialAnalyticsServiceServer() {
}
func (UnimplementedSocialAnalyticsServiceServer) testEmbeddedByValue() {}

// UnsafeSocialAnalyticsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SocialAnalyticsServiceServer will
// result in compilation errors.
type UnsafeSocialAnalyticsServiceServer interface {
	mustEmbedUnimplementedSocialAnalyticsServiceServer()
}

func RegisterSocialAnalyticsServiceServer(s grpc.ServiceRegistrar, srv SocialAnalyticsServiceServer) {
	// If the following call pancis, it indicates UnimplementedSocialAnalyticsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SocialAnalyticsService_ServiceDesc, srv)
}

func _SocialAnalyticsService_GetEnhancedProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnhancedProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetEnhancedProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetEnhancedProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetEnhancedProfile(ctx, req.(*GetEnhancedProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SocialAnalyticsService_GetProfileAnalytics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfileAnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetProfileAnalytics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetProfileAnalytics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetProfileAnalytics(ctx, req.(*GetProfileAnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SocialAnalyticsService_GetMutualFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMutualFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetMutualFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetMutualFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetMutualFriends(ctx, req.(*GetMutualFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SocialAnalyticsService_GetMutualContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMutualContactsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetMutualContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetMutualContacts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetMutualContacts(ctx, req.(*GetMutualContactsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SocialAnalyticsService_GetCommonBubbles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommonBubblesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetCommonBubbles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetCommonBubbles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetCommonBubbles(ctx, req.(*GetCommonBubblesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SocialAnalyticsService_GetConnectionStrength_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConnectionStrengthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetConnectionStrength(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetConnectionStrength_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetConnectionStrength(ctx, req.(*GetConnectionStrengthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SocialAnalyticsService_GetSocialGraph_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSocialGraphRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetSocialGraph(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetSocialGraph_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetSocialGraph(ctx, req.(*GetSocialGraphRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SocialAnalyticsService_GetEngagementMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEngagementMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SocialAnalyticsServiceServer).GetEngagementMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SocialAnalyticsService_GetEngagementMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SocialAnalyticsServiceServer).GetEngagementMetrics(ctx, req.(*GetEngagementMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SocialAnalyticsService_ServiceDesc is the grpc.ServiceDesc for SocialAnalyticsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SocialAnalyticsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.social_analytics.v1.SocialAnalyticsService",
	HandlerType: (*SocialAnalyticsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEnhancedProfile",
			Handler:    _SocialAnalyticsService_GetEnhancedProfile_Handler,
		},
		{
			MethodName: "GetProfileAnalytics",
			Handler:    _SocialAnalyticsService_GetProfileAnalytics_Handler,
		},
		{
			MethodName: "GetMutualFriends",
			Handler:    _SocialAnalyticsService_GetMutualFriends_Handler,
		},
		{
			MethodName: "GetMutualContacts",
			Handler:    _SocialAnalyticsService_GetMutualContacts_Handler,
		},
		{
			MethodName: "GetCommonBubbles",
			Handler:    _SocialAnalyticsService_GetCommonBubbles_Handler,
		},
		{
			MethodName: "GetConnectionStrength",
			Handler:    _SocialAnalyticsService_GetConnectionStrength_Handler,
		},
		{
			MethodName: "GetSocialGraph",
			Handler:    _SocialAnalyticsService_GetSocialGraph_Handler,
		},
		{
			MethodName: "GetEngagementMetrics",
			Handler:    _SocialAnalyticsService_GetEngagementMetrics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "social_analytics.proto",
}
