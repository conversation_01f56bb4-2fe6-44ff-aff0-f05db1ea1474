// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: friendship.proto

package friendship

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get friend requests request
type GetFriendRequestsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"` // pending, accepted, declined
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFriendRequestsRequest) Reset() {
	*x = GetFriendRequestsRequest{}
	mi := &file_friendship_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendRequestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendRequestsRequest) ProtoMessage() {}

func (x *GetFriendRequestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendRequestsRequest.ProtoReflect.Descriptor instead.
func (*GetFriendRequestsRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{0}
}

func (x *GetFriendRequestsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetFriendRequestsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetFriendRequestsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetFriendRequestsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get friend requests response
type GetFriendRequestsResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FriendRequests []*FriendRequest       `protobuf:"bytes,1,rep,name=friend_requests,json=friendRequests,proto3" json:"friend_requests,omitempty"`
	Pagination     *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse    *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetFriendRequestsResponse) Reset() {
	*x = GetFriendRequestsResponse{}
	mi := &file_friendship_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendRequestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendRequestsResponse) ProtoMessage() {}

func (x *GetFriendRequestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendRequestsResponse.ProtoReflect.Descriptor instead.
func (*GetFriendRequestsResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{1}
}

func (x *GetFriendRequestsResponse) GetFriendRequests() []*FriendRequest {
	if x != nil {
		return x.FriendRequests
	}
	return nil
}

func (x *GetFriendRequestsResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetFriendRequestsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Friend request
type FriendRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Id            string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FromUserId    string                  `protobuf:"bytes,2,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId      string                  `protobuf:"bytes,3,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	FromUser      *common.User            `protobuf:"bytes,4,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Message       string                  `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Status        common.FriendshipStatus `protobuf:"varint,6,opt,name=status,proto3,enum=hopen.common.v1.FriendshipStatus" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp  `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FriendRequest) Reset() {
	*x = FriendRequest{}
	mi := &file_friendship_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendRequest) ProtoMessage() {}

func (x *FriendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendRequest.ProtoReflect.Descriptor instead.
func (*FriendRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{2}
}

func (x *FriendRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FriendRequest) GetFromUserId() string {
	if x != nil {
		return x.FromUserId
	}
	return ""
}

func (x *FriendRequest) GetToUserId() string {
	if x != nil {
		return x.ToUserId
	}
	return ""
}

func (x *FriendRequest) GetFromUser() *common.User {
	if x != nil {
		return x.FromUser
	}
	return nil
}

func (x *FriendRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *FriendRequest) GetStatus() common.FriendshipStatus {
	if x != nil {
		return x.Status
	}
	return common.FriendshipStatus(0)
}

func (x *FriendRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FriendRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Accept friend request request
type AcceptFriendRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RequestId     string                 `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptFriendRequestRequest) Reset() {
	*x = AcceptFriendRequestRequest{}
	mi := &file_friendship_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptFriendRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptFriendRequestRequest) ProtoMessage() {}

func (x *AcceptFriendRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptFriendRequestRequest.ProtoReflect.Descriptor instead.
func (*AcceptFriendRequestRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{3}
}

func (x *AcceptFriendRequestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AcceptFriendRequestRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// Accept friend request response
type AcceptFriendRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FriendshipId  string                 `protobuf:"bytes,1,opt,name=friendship_id,json=friendshipId,proto3" json:"friendship_id,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptFriendRequestResponse) Reset() {
	*x = AcceptFriendRequestResponse{}
	mi := &file_friendship_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptFriendRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptFriendRequestResponse) ProtoMessage() {}

func (x *AcceptFriendRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptFriendRequestResponse.ProtoReflect.Descriptor instead.
func (*AcceptFriendRequestResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{4}
}

func (x *AcceptFriendRequestResponse) GetFriendshipId() string {
	if x != nil {
		return x.FriendshipId
	}
	return ""
}

func (x *AcceptFriendRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Decline friend request request
type DeclineFriendRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RequestId     string                 `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeclineFriendRequestRequest) Reset() {
	*x = DeclineFriendRequestRequest{}
	mi := &file_friendship_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeclineFriendRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineFriendRequestRequest) ProtoMessage() {}

func (x *DeclineFriendRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineFriendRequestRequest.ProtoReflect.Descriptor instead.
func (*DeclineFriendRequestRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{5}
}

func (x *DeclineFriendRequestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeclineFriendRequestRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DeclineFriendRequestRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Decline friend request response
type DeclineFriendRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeclineFriendRequestResponse) Reset() {
	*x = DeclineFriendRequestResponse{}
	mi := &file_friendship_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeclineFriendRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineFriendRequestResponse) ProtoMessage() {}

func (x *DeclineFriendRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineFriendRequestResponse.ProtoReflect.Descriptor instead.
func (*DeclineFriendRequestResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{6}
}

func (x *DeclineFriendRequestResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeclineFriendRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get friends request
type GetFriendsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFriendsRequest) Reset() {
	*x = GetFriendsRequest{}
	mi := &file_friendship_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendsRequest) ProtoMessage() {}

func (x *GetFriendsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendsRequest.ProtoReflect.Descriptor instead.
func (*GetFriendsRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{7}
}

func (x *GetFriendsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetFriendsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetFriendsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get friends response
type GetFriendsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Friends       []*common.User         `protobuf:"bytes,1,rep,name=friends,proto3" json:"friends,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFriendsResponse) Reset() {
	*x = GetFriendsResponse{}
	mi := &file_friendship_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendsResponse) ProtoMessage() {}

func (x *GetFriendsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendsResponse.ProtoReflect.Descriptor instead.
func (*GetFriendsResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{8}
}

func (x *GetFriendsResponse) GetFriends() []*common.User {
	if x != nil {
		return x.Friends
	}
	return nil
}

func (x *GetFriendsResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetFriendsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Remove friend request
type RemoveFriendRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FriendId      string                 `protobuf:"bytes,2,opt,name=friend_id,json=friendId,proto3" json:"friend_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveFriendRequest) Reset() {
	*x = RemoveFriendRequest{}
	mi := &file_friendship_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveFriendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFriendRequest) ProtoMessage() {}

func (x *RemoveFriendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFriendRequest.ProtoReflect.Descriptor instead.
func (*RemoveFriendRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{9}
}

func (x *RemoveFriendRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RemoveFriendRequest) GetFriendId() string {
	if x != nil {
		return x.FriendId
	}
	return ""
}

// Remove friend response
type RemoveFriendResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveFriendResponse) Reset() {
	*x = RemoveFriendResponse{}
	mi := &file_friendship_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveFriendResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFriendResponse) ProtoMessage() {}

func (x *RemoveFriendResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFriendResponse.ProtoReflect.Descriptor instead.
func (*RemoveFriendResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{10}
}

func (x *RemoveFriendResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RemoveFriendResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Block user request
type BlockUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BlockedUserId string                 `protobuf:"bytes,2,opt,name=blocked_user_id,json=blockedUserId,proto3" json:"blocked_user_id,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserRequest) Reset() {
	*x = BlockUserRequest{}
	mi := &file_friendship_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserRequest) ProtoMessage() {}

func (x *BlockUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserRequest.ProtoReflect.Descriptor instead.
func (*BlockUserRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{11}
}

func (x *BlockUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BlockUserRequest) GetBlockedUserId() string {
	if x != nil {
		return x.BlockedUserId
	}
	return ""
}

func (x *BlockUserRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Block user response
type BlockUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserResponse) Reset() {
	*x = BlockUserResponse{}
	mi := &file_friendship_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserResponse) ProtoMessage() {}

func (x *BlockUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserResponse.ProtoReflect.Descriptor instead.
func (*BlockUserResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{12}
}

func (x *BlockUserResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *BlockUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Unblock user request
type UnblockUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BlockedUserId string                 `protobuf:"bytes,2,opt,name=blocked_user_id,json=blockedUserId,proto3" json:"blocked_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockUserRequest) Reset() {
	*x = UnblockUserRequest{}
	mi := &file_friendship_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockUserRequest) ProtoMessage() {}

func (x *UnblockUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockUserRequest.ProtoReflect.Descriptor instead.
func (*UnblockUserRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{13}
}

func (x *UnblockUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UnblockUserRequest) GetBlockedUserId() string {
	if x != nil {
		return x.BlockedUserId
	}
	return ""
}

// Unblock user response
type UnblockUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockUserResponse) Reset() {
	*x = UnblockUserResponse{}
	mi := &file_friendship_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockUserResponse) ProtoMessage() {}

func (x *UnblockUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockUserResponse.ProtoReflect.Descriptor instead.
func (*UnblockUserResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{14}
}

func (x *UnblockUserResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UnblockUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get blocked users request
type GetBlockedUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockedUsersRequest) Reset() {
	*x = GetBlockedUsersRequest{}
	mi := &file_friendship_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockedUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockedUsersRequest) ProtoMessage() {}

func (x *GetBlockedUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockedUsersRequest.ProtoReflect.Descriptor instead.
func (*GetBlockedUsersRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{15}
}

func (x *GetBlockedUsersRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetBlockedUsersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetBlockedUsersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get blocked users response
type GetBlockedUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BlockedUsers  []*common.User         `protobuf:"bytes,1,rep,name=blocked_users,json=blockedUsers,proto3" json:"blocked_users,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockedUsersResponse) Reset() {
	*x = GetBlockedUsersResponse{}
	mi := &file_friendship_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockedUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockedUsersResponse) ProtoMessage() {}

func (x *GetBlockedUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockedUsersResponse.ProtoReflect.Descriptor instead.
func (*GetBlockedUsersResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{16}
}

func (x *GetBlockedUsersResponse) GetBlockedUsers() []*common.User {
	if x != nil {
		return x.BlockedUsers
	}
	return nil
}

func (x *GetBlockedUsersResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetBlockedUsersResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Check friendship status request
type CheckFriendshipStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckFriendshipStatusRequest) Reset() {
	*x = CheckFriendshipStatusRequest{}
	mi := &file_friendship_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckFriendshipStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFriendshipStatusRequest) ProtoMessage() {}

func (x *CheckFriendshipStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFriendshipStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckFriendshipStatusRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{17}
}

func (x *CheckFriendshipStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CheckFriendshipStatusRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

// Check friendship status response
type CheckFriendshipStatusResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Status        common.FriendshipStatus `protobuf:"varint,1,opt,name=status,proto3,enum=hopen.common.v1.FriendshipStatus" json:"status,omitempty"`
	FriendshipId  string                  `protobuf:"bytes,2,opt,name=friendship_id,json=friendshipId,proto3" json:"friendship_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp  `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ApiResponse   *common.ApiResponse     `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckFriendshipStatusResponse) Reset() {
	*x = CheckFriendshipStatusResponse{}
	mi := &file_friendship_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckFriendshipStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFriendshipStatusResponse) ProtoMessage() {}

func (x *CheckFriendshipStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFriendshipStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckFriendshipStatusResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{18}
}

func (x *CheckFriendshipStatusResponse) GetStatus() common.FriendshipStatus {
	if x != nil {
		return x.Status
	}
	return common.FriendshipStatus(0)
}

func (x *CheckFriendshipStatusResponse) GetFriendshipId() string {
	if x != nil {
		return x.FriendshipId
	}
	return ""
}

func (x *CheckFriendshipStatusResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CheckFriendshipStatusResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get mutual friends request
type GetMutualFriendsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,2,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	Limit         int32                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMutualFriendsRequest) Reset() {
	*x = GetMutualFriendsRequest{}
	mi := &file_friendship_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMutualFriendsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMutualFriendsRequest) ProtoMessage() {}

func (x *GetMutualFriendsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMutualFriendsRequest.ProtoReflect.Descriptor instead.
func (*GetMutualFriendsRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{19}
}

func (x *GetMutualFriendsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetMutualFriendsRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

func (x *GetMutualFriendsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// Get mutual friends response
type GetMutualFriendsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MutualFriends []*common.User         `protobuf:"bytes,1,rep,name=mutual_friends,json=mutualFriends,proto3" json:"mutual_friends,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMutualFriendsResponse) Reset() {
	*x = GetMutualFriendsResponse{}
	mi := &file_friendship_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMutualFriendsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMutualFriendsResponse) ProtoMessage() {}

func (x *GetMutualFriendsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMutualFriendsResponse.ProtoReflect.Descriptor instead.
func (*GetMutualFriendsResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{20}
}

func (x *GetMutualFriendsResponse) GetMutualFriends() []*common.User {
	if x != nil {
		return x.MutualFriends
	}
	return nil
}

func (x *GetMutualFriendsResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetMutualFriendsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get friendship suggestions request
type GetFriendshipSuggestionsRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Limit          int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	ExcludeUserIds []string               `protobuf:"bytes,3,rep,name=exclude_user_ids,json=excludeUserIds,proto3" json:"exclude_user_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetFriendshipSuggestionsRequest) Reset() {
	*x = GetFriendshipSuggestionsRequest{}
	mi := &file_friendship_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendshipSuggestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendshipSuggestionsRequest) ProtoMessage() {}

func (x *GetFriendshipSuggestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendshipSuggestionsRequest.ProtoReflect.Descriptor instead.
func (*GetFriendshipSuggestionsRequest) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{21}
}

func (x *GetFriendshipSuggestionsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetFriendshipSuggestionsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetFriendshipSuggestionsRequest) GetExcludeUserIds() []string {
	if x != nil {
		return x.ExcludeUserIds
	}
	return nil
}

// Get friendship suggestions response
type GetFriendshipSuggestionsResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Suggestions   []*FriendshipSuggestion `protobuf:"bytes,1,rep,name=suggestions,proto3" json:"suggestions,omitempty"`
	ApiResponse   *common.ApiResponse     `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFriendshipSuggestionsResponse) Reset() {
	*x = GetFriendshipSuggestionsResponse{}
	mi := &file_friendship_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendshipSuggestionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendshipSuggestionsResponse) ProtoMessage() {}

func (x *GetFriendshipSuggestionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendshipSuggestionsResponse.ProtoReflect.Descriptor instead.
func (*GetFriendshipSuggestionsResponse) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{22}
}

func (x *GetFriendshipSuggestionsResponse) GetSuggestions() []*FriendshipSuggestion {
	if x != nil {
		return x.Suggestions
	}
	return nil
}

func (x *GetFriendshipSuggestionsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Friendship suggestion
type FriendshipSuggestion struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	User                *common.User           `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Score               float32                `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	Reasons             []string               `protobuf:"bytes,3,rep,name=reasons,proto3" json:"reasons,omitempty"`
	MutualFriendsCount  int32                  `protobuf:"varint,4,opt,name=mutual_friends_count,json=mutualFriendsCount,proto3" json:"mutual_friends_count,omitempty"`
	MutualContactsCount int32                  `protobuf:"varint,5,opt,name=mutual_contacts_count,json=mutualContactsCount,proto3" json:"mutual_contacts_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *FriendshipSuggestion) Reset() {
	*x = FriendshipSuggestion{}
	mi := &file_friendship_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendshipSuggestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendshipSuggestion) ProtoMessage() {}

func (x *FriendshipSuggestion) ProtoReflect() protoreflect.Message {
	mi := &file_friendship_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendshipSuggestion.ProtoReflect.Descriptor instead.
func (*FriendshipSuggestion) Descriptor() ([]byte, []int) {
	return file_friendship_proto_rawDescGZIP(), []int{23}
}

func (x *FriendshipSuggestion) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *FriendshipSuggestion) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *FriendshipSuggestion) GetReasons() []string {
	if x != nil {
		return x.Reasons
	}
	return nil
}

func (x *FriendshipSuggestion) GetMutualFriendsCount() int32 {
	if x != nil {
		return x.MutualFriendsCount
	}
	return 0
}

func (x *FriendshipSuggestion) GetMutualContactsCount() int32 {
	if x != nil {
		return x.MutualContactsCount
	}
	return 0
}

var File_friendship_proto protoreflect.FileDescriptor

const file_friendship_proto_rawDesc = "" +
	"\n" +
	"\x10friendship.proto\x12\x13hopen.friendship.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"|\n" +
	"\x18GetFriendRequestsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xe6\x01\n" +
	"\x19GetFriendRequestsResponse\x12K\n" +
	"\x0ffriend_requests\x18\x01 \x03(\v2\".hopen.friendship.v1.FriendRequestR\x0efriendRequests\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xde\x02\n" +
	"\rFriendRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12 \n" +
	"\ffrom_user_id\x18\x02 \x01(\tR\n" +
	"fromUserId\x12\x1c\n" +
	"\n" +
	"to_user_id\x18\x03 \x01(\tR\btoUserId\x122\n" +
	"\tfrom_user\x18\x04 \x01(\v2\x15.hopen.common.v1.UserR\bfromUser\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x129\n" +
	"\x06status\x18\x06 \x01(\x0e2!.hopen.common.v1.FriendshipStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"T\n" +
	"\x1aAcceptFriendRequestRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\"\x83\x01\n" +
	"\x1bAcceptFriendRequestResponse\x12#\n" +
	"\rfriendship_id\x18\x01 \x01(\tR\ffriendshipId\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"m\n" +
	"\x1bDeclineFriendRequestRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"y\n" +
	"\x1cDeclineFriendRequestResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"]\n" +
	"\x11GetFriendsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"\xc3\x01\n" +
	"\x12GetFriendsResponse\x12/\n" +
	"\afriends\x18\x01 \x03(\v2\x15.hopen.common.v1.UserR\afriends\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"K\n" +
	"\x13RemoveFriendRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfriend_id\x18\x02 \x01(\tR\bfriendId\"q\n" +
	"\x14RemoveFriendResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"k\n" +
	"\x10BlockUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12&\n" +
	"\x0fblocked_user_id\x18\x02 \x01(\tR\rblockedUserId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"n\n" +
	"\x11BlockUserResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"U\n" +
	"\x12UnblockUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12&\n" +
	"\x0fblocked_user_id\x18\x02 \x01(\tR\rblockedUserId\"p\n" +
	"\x13UnblockUserResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"b\n" +
	"\x16GetBlockedUsersRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"\xd3\x01\n" +
	"\x17GetBlockedUsersResponse\x12:\n" +
	"\rblocked_users\x18\x01 \x03(\v2\x15.hopen.common.v1.UserR\fblockedUsers\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"]\n" +
	"\x1cCheckFriendshipStatusRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\"\xfb\x01\n" +
	"\x1dCheckFriendshipStatusResponse\x129\n" +
	"\x06status\x18\x01 \x01(\x0e2!.hopen.common.v1.FriendshipStatusR\x06status\x12#\n" +
	"\rfriendship_id\x18\x02 \x01(\tR\ffriendshipId\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"n\n" +
	"\x17GetMutualFriendsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\x0etarget_user_id\x18\x02 \x01(\tR\ftargetUserId\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x05R\x05limit\"\xaf\x01\n" +
	"\x18GetMutualFriendsResponse\x12<\n" +
	"\x0emutual_friends\x18\x01 \x03(\v2\x15.hopen.common.v1.UserR\rmutualFriends\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"z\n" +
	"\x1fGetFriendshipSuggestionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12(\n" +
	"\x10exclude_user_ids\x18\x03 \x03(\tR\x0eexcludeUserIds\"\xb0\x01\n" +
	" GetFriendshipSuggestionsResponse\x12K\n" +
	"\vsuggestions\x18\x01 \x03(\v2).hopen.friendship.v1.FriendshipSuggestionR\vsuggestions\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xd7\x01\n" +
	"\x14FriendshipSuggestion\x12)\n" +
	"\x04user\x18\x01 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x12\x14\n" +
	"\x05score\x18\x02 \x01(\x02R\x05score\x12\x18\n" +
	"\areasons\x18\x03 \x03(\tR\areasons\x120\n" +
	"\x14mutual_friends_count\x18\x04 \x01(\x05R\x12mutualFriendsCount\x122\n" +
	"\x15mutual_contacts_count\x18\x05 \x01(\x05R\x13mutualContactsCount2\xe9\t\n" +
	"\x11FriendshipService\x12r\n" +
	"\x11GetFriendRequests\x12-.hopen.friendship.v1.GetFriendRequestsRequest\x1a..hopen.friendship.v1.GetFriendRequestsResponse\x12x\n" +
	"\x13AcceptFriendRequest\x12/.hopen.friendship.v1.AcceptFriendRequestRequest\x1a0.hopen.friendship.v1.AcceptFriendRequestResponse\x12{\n" +
	"\x14DeclineFriendRequest\x120.hopen.friendship.v1.DeclineFriendRequestRequest\x1a1.hopen.friendship.v1.DeclineFriendRequestResponse\x12]\n" +
	"\n" +
	"GetFriends\x12&.hopen.friendship.v1.GetFriendsRequest\x1a'.hopen.friendship.v1.GetFriendsResponse\x12c\n" +
	"\fRemoveFriend\x12(.hopen.friendship.v1.RemoveFriendRequest\x1a).hopen.friendship.v1.RemoveFriendResponse\x12Z\n" +
	"\tBlockUser\x12%.hopen.friendship.v1.BlockUserRequest\x1a&.hopen.friendship.v1.BlockUserResponse\x12`\n" +
	"\vUnblockUser\x12'.hopen.friendship.v1.UnblockUserRequest\x1a(.hopen.friendship.v1.UnblockUserResponse\x12l\n" +
	"\x0fGetBlockedUsers\x12+.hopen.friendship.v1.GetBlockedUsersRequest\x1a,.hopen.friendship.v1.GetBlockedUsersResponse\x12~\n" +
	"\x15CheckFriendshipStatus\x121.hopen.friendship.v1.CheckFriendshipStatusRequest\x1a2.hopen.friendship.v1.CheckFriendshipStatusResponse\x12o\n" +
	"\x10GetMutualFriends\x12,.hopen.friendship.v1.GetMutualFriendsRequest\x1a-.hopen.friendship.v1.GetMutualFriendsResponse\x12\x87\x01\n" +
	"\x18GetFriendshipSuggestions\x124.hopen.friendship.v1.GetFriendshipSuggestionsRequest\x1a5.hopen.friendship.v1.GetFriendshipSuggestionsResponseBJ\n" +
	"\x17com.hopen.friendship.v1P\x01Z-hopenbackend/protos/gen/friendship;friendshipb\x06proto3"

var (
	file_friendship_proto_rawDescOnce sync.Once
	file_friendship_proto_rawDescData []byte
)

func file_friendship_proto_rawDescGZIP() []byte {
	file_friendship_proto_rawDescOnce.Do(func() {
		file_friendship_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_friendship_proto_rawDesc), len(file_friendship_proto_rawDesc)))
	})
	return file_friendship_proto_rawDescData
}

var file_friendship_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_friendship_proto_goTypes = []any{
	(*GetFriendRequestsRequest)(nil),         // 0: hopen.friendship.v1.GetFriendRequestsRequest
	(*GetFriendRequestsResponse)(nil),        // 1: hopen.friendship.v1.GetFriendRequestsResponse
	(*FriendRequest)(nil),                    // 2: hopen.friendship.v1.FriendRequest
	(*AcceptFriendRequestRequest)(nil),       // 3: hopen.friendship.v1.AcceptFriendRequestRequest
	(*AcceptFriendRequestResponse)(nil),      // 4: hopen.friendship.v1.AcceptFriendRequestResponse
	(*DeclineFriendRequestRequest)(nil),      // 5: hopen.friendship.v1.DeclineFriendRequestRequest
	(*DeclineFriendRequestResponse)(nil),     // 6: hopen.friendship.v1.DeclineFriendRequestResponse
	(*GetFriendsRequest)(nil),                // 7: hopen.friendship.v1.GetFriendsRequest
	(*GetFriendsResponse)(nil),               // 8: hopen.friendship.v1.GetFriendsResponse
	(*RemoveFriendRequest)(nil),              // 9: hopen.friendship.v1.RemoveFriendRequest
	(*RemoveFriendResponse)(nil),             // 10: hopen.friendship.v1.RemoveFriendResponse
	(*BlockUserRequest)(nil),                 // 11: hopen.friendship.v1.BlockUserRequest
	(*BlockUserResponse)(nil),                // 12: hopen.friendship.v1.BlockUserResponse
	(*UnblockUserRequest)(nil),               // 13: hopen.friendship.v1.UnblockUserRequest
	(*UnblockUserResponse)(nil),              // 14: hopen.friendship.v1.UnblockUserResponse
	(*GetBlockedUsersRequest)(nil),           // 15: hopen.friendship.v1.GetBlockedUsersRequest
	(*GetBlockedUsersResponse)(nil),          // 16: hopen.friendship.v1.GetBlockedUsersResponse
	(*CheckFriendshipStatusRequest)(nil),     // 17: hopen.friendship.v1.CheckFriendshipStatusRequest
	(*CheckFriendshipStatusResponse)(nil),    // 18: hopen.friendship.v1.CheckFriendshipStatusResponse
	(*GetMutualFriendsRequest)(nil),          // 19: hopen.friendship.v1.GetMutualFriendsRequest
	(*GetMutualFriendsResponse)(nil),         // 20: hopen.friendship.v1.GetMutualFriendsResponse
	(*GetFriendshipSuggestionsRequest)(nil),  // 21: hopen.friendship.v1.GetFriendshipSuggestionsRequest
	(*GetFriendshipSuggestionsResponse)(nil), // 22: hopen.friendship.v1.GetFriendshipSuggestionsResponse
	(*FriendshipSuggestion)(nil),             // 23: hopen.friendship.v1.FriendshipSuggestion
	(*common.Pagination)(nil),                // 24: hopen.common.v1.Pagination
	(*common.ApiResponse)(nil),               // 25: hopen.common.v1.ApiResponse
	(*common.User)(nil),                      // 26: hopen.common.v1.User
	(common.FriendshipStatus)(0),             // 27: hopen.common.v1.FriendshipStatus
	(*timestamppb.Timestamp)(nil),            // 28: google.protobuf.Timestamp
}
var file_friendship_proto_depIdxs = []int32{
	2,  // 0: hopen.friendship.v1.GetFriendRequestsResponse.friend_requests:type_name -> hopen.friendship.v1.FriendRequest
	24, // 1: hopen.friendship.v1.GetFriendRequestsResponse.pagination:type_name -> hopen.common.v1.Pagination
	25, // 2: hopen.friendship.v1.GetFriendRequestsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 3: hopen.friendship.v1.FriendRequest.from_user:type_name -> hopen.common.v1.User
	27, // 4: hopen.friendship.v1.FriendRequest.status:type_name -> hopen.common.v1.FriendshipStatus
	28, // 5: hopen.friendship.v1.FriendRequest.created_at:type_name -> google.protobuf.Timestamp
	28, // 6: hopen.friendship.v1.FriendRequest.updated_at:type_name -> google.protobuf.Timestamp
	25, // 7: hopen.friendship.v1.AcceptFriendRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 8: hopen.friendship.v1.DeclineFriendRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 9: hopen.friendship.v1.GetFriendsResponse.friends:type_name -> hopen.common.v1.User
	24, // 10: hopen.friendship.v1.GetFriendsResponse.pagination:type_name -> hopen.common.v1.Pagination
	25, // 11: hopen.friendship.v1.GetFriendsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 12: hopen.friendship.v1.RemoveFriendResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 13: hopen.friendship.v1.BlockUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 14: hopen.friendship.v1.UnblockUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 15: hopen.friendship.v1.GetBlockedUsersResponse.blocked_users:type_name -> hopen.common.v1.User
	24, // 16: hopen.friendship.v1.GetBlockedUsersResponse.pagination:type_name -> hopen.common.v1.Pagination
	25, // 17: hopen.friendship.v1.GetBlockedUsersResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 18: hopen.friendship.v1.CheckFriendshipStatusResponse.status:type_name -> hopen.common.v1.FriendshipStatus
	28, // 19: hopen.friendship.v1.CheckFriendshipStatusResponse.created_at:type_name -> google.protobuf.Timestamp
	25, // 20: hopen.friendship.v1.CheckFriendshipStatusResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 21: hopen.friendship.v1.GetMutualFriendsResponse.mutual_friends:type_name -> hopen.common.v1.User
	25, // 22: hopen.friendship.v1.GetMutualFriendsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	23, // 23: hopen.friendship.v1.GetFriendshipSuggestionsResponse.suggestions:type_name -> hopen.friendship.v1.FriendshipSuggestion
	25, // 24: hopen.friendship.v1.GetFriendshipSuggestionsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 25: hopen.friendship.v1.FriendshipSuggestion.user:type_name -> hopen.common.v1.User
	0,  // 26: hopen.friendship.v1.FriendshipService.GetFriendRequests:input_type -> hopen.friendship.v1.GetFriendRequestsRequest
	3,  // 27: hopen.friendship.v1.FriendshipService.AcceptFriendRequest:input_type -> hopen.friendship.v1.AcceptFriendRequestRequest
	5,  // 28: hopen.friendship.v1.FriendshipService.DeclineFriendRequest:input_type -> hopen.friendship.v1.DeclineFriendRequestRequest
	7,  // 29: hopen.friendship.v1.FriendshipService.GetFriends:input_type -> hopen.friendship.v1.GetFriendsRequest
	9,  // 30: hopen.friendship.v1.FriendshipService.RemoveFriend:input_type -> hopen.friendship.v1.RemoveFriendRequest
	11, // 31: hopen.friendship.v1.FriendshipService.BlockUser:input_type -> hopen.friendship.v1.BlockUserRequest
	13, // 32: hopen.friendship.v1.FriendshipService.UnblockUser:input_type -> hopen.friendship.v1.UnblockUserRequest
	15, // 33: hopen.friendship.v1.FriendshipService.GetBlockedUsers:input_type -> hopen.friendship.v1.GetBlockedUsersRequest
	17, // 34: hopen.friendship.v1.FriendshipService.CheckFriendshipStatus:input_type -> hopen.friendship.v1.CheckFriendshipStatusRequest
	19, // 35: hopen.friendship.v1.FriendshipService.GetMutualFriends:input_type -> hopen.friendship.v1.GetMutualFriendsRequest
	21, // 36: hopen.friendship.v1.FriendshipService.GetFriendshipSuggestions:input_type -> hopen.friendship.v1.GetFriendshipSuggestionsRequest
	1,  // 37: hopen.friendship.v1.FriendshipService.GetFriendRequests:output_type -> hopen.friendship.v1.GetFriendRequestsResponse
	4,  // 38: hopen.friendship.v1.FriendshipService.AcceptFriendRequest:output_type -> hopen.friendship.v1.AcceptFriendRequestResponse
	6,  // 39: hopen.friendship.v1.FriendshipService.DeclineFriendRequest:output_type -> hopen.friendship.v1.DeclineFriendRequestResponse
	8,  // 40: hopen.friendship.v1.FriendshipService.GetFriends:output_type -> hopen.friendship.v1.GetFriendsResponse
	10, // 41: hopen.friendship.v1.FriendshipService.RemoveFriend:output_type -> hopen.friendship.v1.RemoveFriendResponse
	12, // 42: hopen.friendship.v1.FriendshipService.BlockUser:output_type -> hopen.friendship.v1.BlockUserResponse
	14, // 43: hopen.friendship.v1.FriendshipService.UnblockUser:output_type -> hopen.friendship.v1.UnblockUserResponse
	16, // 44: hopen.friendship.v1.FriendshipService.GetBlockedUsers:output_type -> hopen.friendship.v1.GetBlockedUsersResponse
	18, // 45: hopen.friendship.v1.FriendshipService.CheckFriendshipStatus:output_type -> hopen.friendship.v1.CheckFriendshipStatusResponse
	20, // 46: hopen.friendship.v1.FriendshipService.GetMutualFriends:output_type -> hopen.friendship.v1.GetMutualFriendsResponse
	22, // 47: hopen.friendship.v1.FriendshipService.GetFriendshipSuggestions:output_type -> hopen.friendship.v1.GetFriendshipSuggestionsResponse
	37, // [37:48] is the sub-list for method output_type
	26, // [26:37] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_friendship_proto_init() }
func file_friendship_proto_init() {
	if File_friendship_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_friendship_proto_rawDesc), len(file_friendship_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_friendship_proto_goTypes,
		DependencyIndexes: file_friendship_proto_depIdxs,
		MessageInfos:      file_friendship_proto_msgTypes,
	}.Build()
	File_friendship_proto = out.File
	file_friendship_proto_goTypes = nil
	file_friendship_proto_depIdxs = nil
}
