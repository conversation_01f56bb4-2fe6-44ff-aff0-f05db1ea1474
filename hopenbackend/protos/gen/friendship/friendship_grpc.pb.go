// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: friendship.proto

package friendship

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FriendshipService_GetFriendRequests_FullMethodName        = "/hopen.friendship.v1.FriendshipService/GetFriendRequests"
	FriendshipService_AcceptFriendRequest_FullMethodName      = "/hopen.friendship.v1.FriendshipService/AcceptFriendRequest"
	FriendshipService_DeclineFriendRequest_FullMethodName     = "/hopen.friendship.v1.FriendshipService/DeclineFriendRequest"
	FriendshipService_GetFriends_FullMethodName               = "/hopen.friendship.v1.FriendshipService/GetFriends"
	FriendshipService_RemoveFriend_FullMethodName             = "/hopen.friendship.v1.FriendshipService/RemoveFriend"
	FriendshipService_BlockUser_FullMethodName                = "/hopen.friendship.v1.FriendshipService/BlockUser"
	FriendshipService_UnblockUser_FullMethodName              = "/hopen.friendship.v1.FriendshipService/UnblockUser"
	FriendshipService_GetBlockedUsers_FullMethodName          = "/hopen.friendship.v1.FriendshipService/GetBlockedUsers"
	FriendshipService_CheckFriendshipStatus_FullMethodName    = "/hopen.friendship.v1.FriendshipService/CheckFriendshipStatus"
	FriendshipService_GetMutualFriends_FullMethodName         = "/hopen.friendship.v1.FriendshipService/GetMutualFriends"
	FriendshipService_GetFriendshipSuggestions_FullMethodName = "/hopen.friendship.v1.FriendshipService/GetFriendshipSuggestions"
)

// FriendshipServiceClient is the client API for FriendshipService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Friendship service for friend request and friendship management
type FriendshipServiceClient interface {
	// Get friend requests for a user
	GetFriendRequests(ctx context.Context, in *GetFriendRequestsRequest, opts ...grpc.CallOption) (*GetFriendRequestsResponse, error)
	// Accept friend request
	AcceptFriendRequest(ctx context.Context, in *AcceptFriendRequestRequest, opts ...grpc.CallOption) (*AcceptFriendRequestResponse, error)
	// Decline friend request
	DeclineFriendRequest(ctx context.Context, in *DeclineFriendRequestRequest, opts ...grpc.CallOption) (*DeclineFriendRequestResponse, error)
	// Get user's friends
	GetFriends(ctx context.Context, in *GetFriendsRequest, opts ...grpc.CallOption) (*GetFriendsResponse, error)
	// Remove friend
	RemoveFriend(ctx context.Context, in *RemoveFriendRequest, opts ...grpc.CallOption) (*RemoveFriendResponse, error)
	// Block user
	BlockUser(ctx context.Context, in *BlockUserRequest, opts ...grpc.CallOption) (*BlockUserResponse, error)
	// Unblock user
	UnblockUser(ctx context.Context, in *UnblockUserRequest, opts ...grpc.CallOption) (*UnblockUserResponse, error)
	// Get blocked users
	GetBlockedUsers(ctx context.Context, in *GetBlockedUsersRequest, opts ...grpc.CallOption) (*GetBlockedUsersResponse, error)
	// Check friendship status
	CheckFriendshipStatus(ctx context.Context, in *CheckFriendshipStatusRequest, opts ...grpc.CallOption) (*CheckFriendshipStatusResponse, error)
	// Get mutual friends
	GetMutualFriends(ctx context.Context, in *GetMutualFriendsRequest, opts ...grpc.CallOption) (*GetMutualFriendsResponse, error)
	// Get friendship suggestions
	GetFriendshipSuggestions(ctx context.Context, in *GetFriendshipSuggestionsRequest, opts ...grpc.CallOption) (*GetFriendshipSuggestionsResponse, error)
}

type friendshipServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFriendshipServiceClient(cc grpc.ClientConnInterface) FriendshipServiceClient {
	return &friendshipServiceClient{cc}
}

func (c *friendshipServiceClient) GetFriendRequests(ctx context.Context, in *GetFriendRequestsRequest, opts ...grpc.CallOption) (*GetFriendRequestsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFriendRequestsResponse)
	err := c.cc.Invoke(ctx, FriendshipService_GetFriendRequests_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) AcceptFriendRequest(ctx context.Context, in *AcceptFriendRequestRequest, opts ...grpc.CallOption) (*AcceptFriendRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcceptFriendRequestResponse)
	err := c.cc.Invoke(ctx, FriendshipService_AcceptFriendRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) DeclineFriendRequest(ctx context.Context, in *DeclineFriendRequestRequest, opts ...grpc.CallOption) (*DeclineFriendRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeclineFriendRequestResponse)
	err := c.cc.Invoke(ctx, FriendshipService_DeclineFriendRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) GetFriends(ctx context.Context, in *GetFriendsRequest, opts ...grpc.CallOption) (*GetFriendsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFriendsResponse)
	err := c.cc.Invoke(ctx, FriendshipService_GetFriends_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) RemoveFriend(ctx context.Context, in *RemoveFriendRequest, opts ...grpc.CallOption) (*RemoveFriendResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveFriendResponse)
	err := c.cc.Invoke(ctx, FriendshipService_RemoveFriend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) BlockUser(ctx context.Context, in *BlockUserRequest, opts ...grpc.CallOption) (*BlockUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockUserResponse)
	err := c.cc.Invoke(ctx, FriendshipService_BlockUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) UnblockUser(ctx context.Context, in *UnblockUserRequest, opts ...grpc.CallOption) (*UnblockUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnblockUserResponse)
	err := c.cc.Invoke(ctx, FriendshipService_UnblockUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) GetBlockedUsers(ctx context.Context, in *GetBlockedUsersRequest, opts ...grpc.CallOption) (*GetBlockedUsersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlockedUsersResponse)
	err := c.cc.Invoke(ctx, FriendshipService_GetBlockedUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) CheckFriendshipStatus(ctx context.Context, in *CheckFriendshipStatusRequest, opts ...grpc.CallOption) (*CheckFriendshipStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckFriendshipStatusResponse)
	err := c.cc.Invoke(ctx, FriendshipService_CheckFriendshipStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) GetMutualFriends(ctx context.Context, in *GetMutualFriendsRequest, opts ...grpc.CallOption) (*GetMutualFriendsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMutualFriendsResponse)
	err := c.cc.Invoke(ctx, FriendshipService_GetMutualFriends_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendshipServiceClient) GetFriendshipSuggestions(ctx context.Context, in *GetFriendshipSuggestionsRequest, opts ...grpc.CallOption) (*GetFriendshipSuggestionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFriendshipSuggestionsResponse)
	err := c.cc.Invoke(ctx, FriendshipService_GetFriendshipSuggestions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FriendshipServiceServer is the server API for FriendshipService service.
// All implementations must embed UnimplementedFriendshipServiceServer
// for forward compatibility.
//
// Friendship service for friend request and friendship management
type FriendshipServiceServer interface {
	// Get friend requests for a user
	GetFriendRequests(context.Context, *GetFriendRequestsRequest) (*GetFriendRequestsResponse, error)
	// Accept friend request
	AcceptFriendRequest(context.Context, *AcceptFriendRequestRequest) (*AcceptFriendRequestResponse, error)
	// Decline friend request
	DeclineFriendRequest(context.Context, *DeclineFriendRequestRequest) (*DeclineFriendRequestResponse, error)
	// Get user's friends
	GetFriends(context.Context, *GetFriendsRequest) (*GetFriendsResponse, error)
	// Remove friend
	RemoveFriend(context.Context, *RemoveFriendRequest) (*RemoveFriendResponse, error)
	// Block user
	BlockUser(context.Context, *BlockUserRequest) (*BlockUserResponse, error)
	// Unblock user
	UnblockUser(context.Context, *UnblockUserRequest) (*UnblockUserResponse, error)
	// Get blocked users
	GetBlockedUsers(context.Context, *GetBlockedUsersRequest) (*GetBlockedUsersResponse, error)
	// Check friendship status
	CheckFriendshipStatus(context.Context, *CheckFriendshipStatusRequest) (*CheckFriendshipStatusResponse, error)
	// Get mutual friends
	GetMutualFriends(context.Context, *GetMutualFriendsRequest) (*GetMutualFriendsResponse, error)
	// Get friendship suggestions
	GetFriendshipSuggestions(context.Context, *GetFriendshipSuggestionsRequest) (*GetFriendshipSuggestionsResponse, error)
	mustEmbedUnimplementedFriendshipServiceServer()
}

// UnimplementedFriendshipServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFriendshipServiceServer struct{}

func (UnimplementedFriendshipServiceServer) GetFriendRequests(context.Context, *GetFriendRequestsRequest) (*GetFriendRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFriendRequests not implemented")
}
func (UnimplementedFriendshipServiceServer) AcceptFriendRequest(context.Context, *AcceptFriendRequestRequest) (*AcceptFriendRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptFriendRequest not implemented")
}
func (UnimplementedFriendshipServiceServer) DeclineFriendRequest(context.Context, *DeclineFriendRequestRequest) (*DeclineFriendRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeclineFriendRequest not implemented")
}
func (UnimplementedFriendshipServiceServer) GetFriends(context.Context, *GetFriendsRequest) (*GetFriendsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFriends not implemented")
}
func (UnimplementedFriendshipServiceServer) RemoveFriend(context.Context, *RemoveFriendRequest) (*RemoveFriendResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveFriend not implemented")
}
func (UnimplementedFriendshipServiceServer) BlockUser(context.Context, *BlockUserRequest) (*BlockUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlockUser not implemented")
}
func (UnimplementedFriendshipServiceServer) UnblockUser(context.Context, *UnblockUserRequest) (*UnblockUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnblockUser not implemented")
}
func (UnimplementedFriendshipServiceServer) GetBlockedUsers(context.Context, *GetBlockedUsersRequest) (*GetBlockedUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlockedUsers not implemented")
}
func (UnimplementedFriendshipServiceServer) CheckFriendshipStatus(context.Context, *CheckFriendshipStatusRequest) (*CheckFriendshipStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckFriendshipStatus not implemented")
}
func (UnimplementedFriendshipServiceServer) GetMutualFriends(context.Context, *GetMutualFriendsRequest) (*GetMutualFriendsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMutualFriends not implemented")
}
func (UnimplementedFriendshipServiceServer) GetFriendshipSuggestions(context.Context, *GetFriendshipSuggestionsRequest) (*GetFriendshipSuggestionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFriendshipSuggestions not implemented")
}
func (UnimplementedFriendshipServiceServer) mustEmbedUnimplementedFriendshipServiceServer() {}
func (UnimplementedFriendshipServiceServer) testEmbeddedByValue()                           {}

// UnsafeFriendshipServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FriendshipServiceServer will
// result in compilation errors.
type UnsafeFriendshipServiceServer interface {
	mustEmbedUnimplementedFriendshipServiceServer()
}

func RegisterFriendshipServiceServer(s grpc.ServiceRegistrar, srv FriendshipServiceServer) {
	// If the following call pancis, it indicates UnimplementedFriendshipServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FriendshipService_ServiceDesc, srv)
}

func _FriendshipService_GetFriendRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).GetFriendRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_GetFriendRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).GetFriendRequests(ctx, req.(*GetFriendRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_AcceptFriendRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptFriendRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).AcceptFriendRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_AcceptFriendRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).AcceptFriendRequest(ctx, req.(*AcceptFriendRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_DeclineFriendRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeclineFriendRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).DeclineFriendRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_DeclineFriendRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).DeclineFriendRequest(ctx, req.(*DeclineFriendRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_GetFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).GetFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_GetFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).GetFriends(ctx, req.(*GetFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_RemoveFriend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveFriendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).RemoveFriend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_RemoveFriend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).RemoveFriend(ctx, req.(*RemoveFriendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_BlockUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).BlockUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_BlockUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).BlockUser(ctx, req.(*BlockUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_UnblockUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnblockUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).UnblockUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_UnblockUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).UnblockUser(ctx, req.(*UnblockUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_GetBlockedUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlockedUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).GetBlockedUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_GetBlockedUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).GetBlockedUsers(ctx, req.(*GetBlockedUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_CheckFriendshipStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFriendshipStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).CheckFriendshipStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_CheckFriendshipStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).CheckFriendshipStatus(ctx, req.(*CheckFriendshipStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_GetMutualFriends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMutualFriendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).GetMutualFriends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_GetMutualFriends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).GetMutualFriends(ctx, req.(*GetMutualFriendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FriendshipService_GetFriendshipSuggestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendshipSuggestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendshipServiceServer).GetFriendshipSuggestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FriendshipService_GetFriendshipSuggestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendshipServiceServer).GetFriendshipSuggestions(ctx, req.(*GetFriendshipSuggestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FriendshipService_ServiceDesc is the grpc.ServiceDesc for FriendshipService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FriendshipService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.friendship.v1.FriendshipService",
	HandlerType: (*FriendshipServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFriendRequests",
			Handler:    _FriendshipService_GetFriendRequests_Handler,
		},
		{
			MethodName: "AcceptFriendRequest",
			Handler:    _FriendshipService_AcceptFriendRequest_Handler,
		},
		{
			MethodName: "DeclineFriendRequest",
			Handler:    _FriendshipService_DeclineFriendRequest_Handler,
		},
		{
			MethodName: "GetFriends",
			Handler:    _FriendshipService_GetFriends_Handler,
		},
		{
			MethodName: "RemoveFriend",
			Handler:    _FriendshipService_RemoveFriend_Handler,
		},
		{
			MethodName: "BlockUser",
			Handler:    _FriendshipService_BlockUser_Handler,
		},
		{
			MethodName: "UnblockUser",
			Handler:    _FriendshipService_UnblockUser_Handler,
		},
		{
			MethodName: "GetBlockedUsers",
			Handler:    _FriendshipService_GetBlockedUsers_Handler,
		},
		{
			MethodName: "CheckFriendshipStatus",
			Handler:    _FriendshipService_CheckFriendshipStatus_Handler,
		},
		{
			MethodName: "GetMutualFriends",
			Handler:    _FriendshipService_GetMutualFriends_Handler,
		},
		{
			MethodName: "GetFriendshipSuggestions",
			Handler:    _FriendshipService_GetFriendshipSuggestions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "friendship.proto",
}
