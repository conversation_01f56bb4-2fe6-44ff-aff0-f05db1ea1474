// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: call.proto

package call

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CallService_StartCall_FullMethodName             = "/hopen.call.v1.CallService/StartCall"
	CallService_JoinCall_FullMethodName              = "/hopen.call.v1.CallService/JoinCall"
	CallService_LeaveCall_FullMethodName             = "/hopen.call.v1.CallService/LeaveCall"
	CallService_EndCall_FullMethodName               = "/hopen.call.v1.CallService/EndCall"
	CallService_GetCall_FullMethodName               = "/hopen.call.v1.CallService/GetCall"
	CallService_UpdateCallMedia_FullMethodName       = "/hopen.call.v1.CallService/UpdateCallMedia"
	CallService_GetCallParticipants_FullMethodName   = "/hopen.call.v1.CallService/GetCallParticipants"
	CallService_UpdateParticipantMute_FullMethodName = "/hopen.call.v1.CallService/UpdateParticipantMute"
	CallService_GetCallHistory_FullMethodName        = "/hopen.call.v1.CallService/GetCallHistory"
	CallService_GetCallStats_FullMethodName          = "/hopen.call.v1.CallService/GetCallStats"
)

// CallServiceClient is the client API for CallService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Call service for WebRTC call management
type CallServiceClient interface {
	// Start a call
	StartCall(ctx context.Context, in *StartCallRequest, opts ...grpc.CallOption) (*StartCallResponse, error)
	// Join a call
	JoinCall(ctx context.Context, in *JoinCallRequest, opts ...grpc.CallOption) (*JoinCallResponse, error)
	// Leave a call
	LeaveCall(ctx context.Context, in *LeaveCallRequest, opts ...grpc.CallOption) (*LeaveCallResponse, error)
	// End a call
	EndCall(ctx context.Context, in *EndCallRequest, opts ...grpc.CallOption) (*EndCallResponse, error)
	// Get call information
	GetCall(ctx context.Context, in *GetCallRequest, opts ...grpc.CallOption) (*GetCallResponse, error)
	// Update call media state
	UpdateCallMedia(ctx context.Context, in *UpdateCallMediaRequest, opts ...grpc.CallOption) (*UpdateCallMediaResponse, error)
	// Get call participants
	GetCallParticipants(ctx context.Context, in *GetCallParticipantsRequest, opts ...grpc.CallOption) (*GetCallParticipantsResponse, error)
	// Mute/unmute participant
	UpdateParticipantMute(ctx context.Context, in *UpdateParticipantMuteRequest, opts ...grpc.CallOption) (*UpdateParticipantMuteResponse, error)
	// Get call history
	GetCallHistory(ctx context.Context, in *GetCallHistoryRequest, opts ...grpc.CallOption) (*GetCallHistoryResponse, error)
	// Get call statistics
	GetCallStats(ctx context.Context, in *GetCallStatsRequest, opts ...grpc.CallOption) (*GetCallStatsResponse, error)
}

type callServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCallServiceClient(cc grpc.ClientConnInterface) CallServiceClient {
	return &callServiceClient{cc}
}

func (c *callServiceClient) StartCall(ctx context.Context, in *StartCallRequest, opts ...grpc.CallOption) (*StartCallResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartCallResponse)
	err := c.cc.Invoke(ctx, CallService_StartCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) JoinCall(ctx context.Context, in *JoinCallRequest, opts ...grpc.CallOption) (*JoinCallResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JoinCallResponse)
	err := c.cc.Invoke(ctx, CallService_JoinCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) LeaveCall(ctx context.Context, in *LeaveCallRequest, opts ...grpc.CallOption) (*LeaveCallResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeaveCallResponse)
	err := c.cc.Invoke(ctx, CallService_LeaveCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) EndCall(ctx context.Context, in *EndCallRequest, opts ...grpc.CallOption) (*EndCallResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EndCallResponse)
	err := c.cc.Invoke(ctx, CallService_EndCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) GetCall(ctx context.Context, in *GetCallRequest, opts ...grpc.CallOption) (*GetCallResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCallResponse)
	err := c.cc.Invoke(ctx, CallService_GetCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) UpdateCallMedia(ctx context.Context, in *UpdateCallMediaRequest, opts ...grpc.CallOption) (*UpdateCallMediaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCallMediaResponse)
	err := c.cc.Invoke(ctx, CallService_UpdateCallMedia_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) GetCallParticipants(ctx context.Context, in *GetCallParticipantsRequest, opts ...grpc.CallOption) (*GetCallParticipantsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCallParticipantsResponse)
	err := c.cc.Invoke(ctx, CallService_GetCallParticipants_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) UpdateParticipantMute(ctx context.Context, in *UpdateParticipantMuteRequest, opts ...grpc.CallOption) (*UpdateParticipantMuteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateParticipantMuteResponse)
	err := c.cc.Invoke(ctx, CallService_UpdateParticipantMute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) GetCallHistory(ctx context.Context, in *GetCallHistoryRequest, opts ...grpc.CallOption) (*GetCallHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCallHistoryResponse)
	err := c.cc.Invoke(ctx, CallService_GetCallHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callServiceClient) GetCallStats(ctx context.Context, in *GetCallStatsRequest, opts ...grpc.CallOption) (*GetCallStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCallStatsResponse)
	err := c.cc.Invoke(ctx, CallService_GetCallStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CallServiceServer is the server API for CallService service.
// All implementations must embed UnimplementedCallServiceServer
// for forward compatibility.
//
// Call service for WebRTC call management
type CallServiceServer interface {
	// Start a call
	StartCall(context.Context, *StartCallRequest) (*StartCallResponse, error)
	// Join a call
	JoinCall(context.Context, *JoinCallRequest) (*JoinCallResponse, error)
	// Leave a call
	LeaveCall(context.Context, *LeaveCallRequest) (*LeaveCallResponse, error)
	// End a call
	EndCall(context.Context, *EndCallRequest) (*EndCallResponse, error)
	// Get call information
	GetCall(context.Context, *GetCallRequest) (*GetCallResponse, error)
	// Update call media state
	UpdateCallMedia(context.Context, *UpdateCallMediaRequest) (*UpdateCallMediaResponse, error)
	// Get call participants
	GetCallParticipants(context.Context, *GetCallParticipantsRequest) (*GetCallParticipantsResponse, error)
	// Mute/unmute participant
	UpdateParticipantMute(context.Context, *UpdateParticipantMuteRequest) (*UpdateParticipantMuteResponse, error)
	// Get call history
	GetCallHistory(context.Context, *GetCallHistoryRequest) (*GetCallHistoryResponse, error)
	// Get call statistics
	GetCallStats(context.Context, *GetCallStatsRequest) (*GetCallStatsResponse, error)
	mustEmbedUnimplementedCallServiceServer()
}

// UnimplementedCallServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCallServiceServer struct{}

func (UnimplementedCallServiceServer) StartCall(context.Context, *StartCallRequest) (*StartCallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCall not implemented")
}
func (UnimplementedCallServiceServer) JoinCall(context.Context, *JoinCallRequest) (*JoinCallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinCall not implemented")
}
func (UnimplementedCallServiceServer) LeaveCall(context.Context, *LeaveCallRequest) (*LeaveCallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveCall not implemented")
}
func (UnimplementedCallServiceServer) EndCall(context.Context, *EndCallRequest) (*EndCallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EndCall not implemented")
}
func (UnimplementedCallServiceServer) GetCall(context.Context, *GetCallRequest) (*GetCallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCall not implemented")
}
func (UnimplementedCallServiceServer) UpdateCallMedia(context.Context, *UpdateCallMediaRequest) (*UpdateCallMediaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCallMedia not implemented")
}
func (UnimplementedCallServiceServer) GetCallParticipants(context.Context, *GetCallParticipantsRequest) (*GetCallParticipantsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCallParticipants not implemented")
}
func (UnimplementedCallServiceServer) UpdateParticipantMute(context.Context, *UpdateParticipantMuteRequest) (*UpdateParticipantMuteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateParticipantMute not implemented")
}
func (UnimplementedCallServiceServer) GetCallHistory(context.Context, *GetCallHistoryRequest) (*GetCallHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCallHistory not implemented")
}
func (UnimplementedCallServiceServer) GetCallStats(context.Context, *GetCallStatsRequest) (*GetCallStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCallStats not implemented")
}
func (UnimplementedCallServiceServer) mustEmbedUnimplementedCallServiceServer() {}
func (UnimplementedCallServiceServer) testEmbeddedByValue()                     {}

// UnsafeCallServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CallServiceServer will
// result in compilation errors.
type UnsafeCallServiceServer interface {
	mustEmbedUnimplementedCallServiceServer()
}

func RegisterCallServiceServer(s grpc.ServiceRegistrar, srv CallServiceServer) {
	// If the following call pancis, it indicates UnimplementedCallServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CallService_ServiceDesc, srv)
}

func _CallService_StartCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartCallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).StartCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_StartCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).StartCall(ctx, req.(*StartCallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_JoinCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinCallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).JoinCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_JoinCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).JoinCall(ctx, req.(*JoinCallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_LeaveCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaveCallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).LeaveCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_LeaveCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).LeaveCall(ctx, req.(*LeaveCallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_EndCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EndCallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).EndCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_EndCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).EndCall(ctx, req.(*EndCallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_GetCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).GetCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_GetCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).GetCall(ctx, req.(*GetCallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_UpdateCallMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCallMediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).UpdateCallMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_UpdateCallMedia_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).UpdateCallMedia(ctx, req.(*UpdateCallMediaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_GetCallParticipants_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCallParticipantsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).GetCallParticipants(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_GetCallParticipants_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).GetCallParticipants(ctx, req.(*GetCallParticipantsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_UpdateParticipantMute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateParticipantMuteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).UpdateParticipantMute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_UpdateParticipantMute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).UpdateParticipantMute(ctx, req.(*UpdateParticipantMuteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_GetCallHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCallHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).GetCallHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_GetCallHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).GetCallHistory(ctx, req.(*GetCallHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallService_GetCallStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCallStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallServiceServer).GetCallStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallService_GetCallStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallServiceServer).GetCallStats(ctx, req.(*GetCallStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CallService_ServiceDesc is the grpc.ServiceDesc for CallService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CallService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.call.v1.CallService",
	HandlerType: (*CallServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartCall",
			Handler:    _CallService_StartCall_Handler,
		},
		{
			MethodName: "JoinCall",
			Handler:    _CallService_JoinCall_Handler,
		},
		{
			MethodName: "LeaveCall",
			Handler:    _CallService_LeaveCall_Handler,
		},
		{
			MethodName: "EndCall",
			Handler:    _CallService_EndCall_Handler,
		},
		{
			MethodName: "GetCall",
			Handler:    _CallService_GetCall_Handler,
		},
		{
			MethodName: "UpdateCallMedia",
			Handler:    _CallService_UpdateCallMedia_Handler,
		},
		{
			MethodName: "GetCallParticipants",
			Handler:    _CallService_GetCallParticipants_Handler,
		},
		{
			MethodName: "UpdateParticipantMute",
			Handler:    _CallService_UpdateParticipantMute_Handler,
		},
		{
			MethodName: "GetCallHistory",
			Handler:    _CallService_GetCallHistory_Handler,
		},
		{
			MethodName: "GetCallStats",
			Handler:    _CallService_GetCallStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "call.proto",
}
