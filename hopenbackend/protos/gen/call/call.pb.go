// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: call.proto

package call

import (
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Call types
type CallType int32

const (
	CallType_CALL_TYPE_UNSPECIFIED  CallType = 0
	CallType_CALL_TYPE_AUDIO        CallType = 1
	CallType_CALL_TYPE_VIDEO        CallType = 2
	CallType_CALL_TYPE_SCREEN_SHARE CallType = 3
)

// Enum value maps for CallType.
var (
	CallType_name = map[int32]string{
		0: "CALL_TYPE_UNSPECIFIED",
		1: "CALL_TYPE_AUDIO",
		2: "CALL_TYPE_VIDEO",
		3: "CALL_TYPE_SCREEN_SHARE",
	}
	CallType_value = map[string]int32{
		"CALL_TYPE_UNSPECIFIED":  0,
		"CALL_TYPE_AUDIO":        1,
		"CALL_TYPE_VIDEO":        2,
		"CALL_TYPE_SCREEN_SHARE": 3,
	}
)

func (x CallType) Enum() *CallType {
	p := new(CallType)
	*p = x
	return p
}

func (x CallType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallType) Descriptor() protoreflect.EnumDescriptor {
	return file_call_proto_enumTypes[0].Descriptor()
}

func (CallType) Type() protoreflect.EnumType {
	return &file_call_proto_enumTypes[0]
}

func (x CallType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallType.Descriptor instead.
func (CallType) EnumDescriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{0}
}

// Call status
type CallStatus int32

const (
	CallStatus_CALL_STATUS_UNSPECIFIED CallStatus = 0
	CallStatus_CALL_STATUS_INITIATING  CallStatus = 1
	CallStatus_CALL_STATUS_RINGING     CallStatus = 2
	CallStatus_CALL_STATUS_CONNECTED   CallStatus = 3
	CallStatus_CALL_STATUS_ENDED       CallStatus = 4
	CallStatus_CALL_STATUS_FAILED      CallStatus = 5
	CallStatus_CALL_STATUS_MISSED      CallStatus = 6
)

// Enum value maps for CallStatus.
var (
	CallStatus_name = map[int32]string{
		0: "CALL_STATUS_UNSPECIFIED",
		1: "CALL_STATUS_INITIATING",
		2: "CALL_STATUS_RINGING",
		3: "CALL_STATUS_CONNECTED",
		4: "CALL_STATUS_ENDED",
		5: "CALL_STATUS_FAILED",
		6: "CALL_STATUS_MISSED",
	}
	CallStatus_value = map[string]int32{
		"CALL_STATUS_UNSPECIFIED": 0,
		"CALL_STATUS_INITIATING":  1,
		"CALL_STATUS_RINGING":     2,
		"CALL_STATUS_CONNECTED":   3,
		"CALL_STATUS_ENDED":       4,
		"CALL_STATUS_FAILED":      5,
		"CALL_STATUS_MISSED":      6,
	}
)

func (x CallStatus) Enum() *CallStatus {
	p := new(CallStatus)
	*p = x
	return p
}

func (x CallStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_call_proto_enumTypes[1].Descriptor()
}

func (CallStatus) Type() protoreflect.EnumType {
	return &file_call_proto_enumTypes[1]
}

func (x CallStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallStatus.Descriptor instead.
func (CallStatus) EnumDescriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{1}
}

// Participant role
type ParticipantRole int32

const (
	ParticipantRole_PARTICIPANT_ROLE_UNSPECIFIED ParticipantRole = 0
	ParticipantRole_PARTICIPANT_ROLE_INITIATOR   ParticipantRole = 1
	ParticipantRole_PARTICIPANT_ROLE_PARTICIPANT ParticipantRole = 2
	ParticipantRole_PARTICIPANT_ROLE_MODERATOR   ParticipantRole = 3
)

// Enum value maps for ParticipantRole.
var (
	ParticipantRole_name = map[int32]string{
		0: "PARTICIPANT_ROLE_UNSPECIFIED",
		1: "PARTICIPANT_ROLE_INITIATOR",
		2: "PARTICIPANT_ROLE_PARTICIPANT",
		3: "PARTICIPANT_ROLE_MODERATOR",
	}
	ParticipantRole_value = map[string]int32{
		"PARTICIPANT_ROLE_UNSPECIFIED": 0,
		"PARTICIPANT_ROLE_INITIATOR":   1,
		"PARTICIPANT_ROLE_PARTICIPANT": 2,
		"PARTICIPANT_ROLE_MODERATOR":   3,
	}
)

func (x ParticipantRole) Enum() *ParticipantRole {
	p := new(ParticipantRole)
	*p = x
	return p
}

func (x ParticipantRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantRole) Descriptor() protoreflect.EnumDescriptor {
	return file_call_proto_enumTypes[2].Descriptor()
}

func (ParticipantRole) Type() protoreflect.EnumType {
	return &file_call_proto_enumTypes[2]
}

func (x ParticipantRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantRole.Descriptor instead.
func (ParticipantRole) EnumDescriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{2}
}

// Call end reason
type CallEndReason int32

const (
	CallEndReason_CALL_END_REASON_UNSPECIFIED   CallEndReason = 0
	CallEndReason_CALL_END_REASON_HANGUP        CallEndReason = 1
	CallEndReason_CALL_END_REASON_BUSY          CallEndReason = 2
	CallEndReason_CALL_END_REASON_NO_ANSWER     CallEndReason = 3
	CallEndReason_CALL_END_REASON_REJECTED      CallEndReason = 4
	CallEndReason_CALL_END_REASON_NETWORK_ERROR CallEndReason = 5
	CallEndReason_CALL_END_REASON_SYSTEM_ERROR  CallEndReason = 6
)

// Enum value maps for CallEndReason.
var (
	CallEndReason_name = map[int32]string{
		0: "CALL_END_REASON_UNSPECIFIED",
		1: "CALL_END_REASON_HANGUP",
		2: "CALL_END_REASON_BUSY",
		3: "CALL_END_REASON_NO_ANSWER",
		4: "CALL_END_REASON_REJECTED",
		5: "CALL_END_REASON_NETWORK_ERROR",
		6: "CALL_END_REASON_SYSTEM_ERROR",
	}
	CallEndReason_value = map[string]int32{
		"CALL_END_REASON_UNSPECIFIED":   0,
		"CALL_END_REASON_HANGUP":        1,
		"CALL_END_REASON_BUSY":          2,
		"CALL_END_REASON_NO_ANSWER":     3,
		"CALL_END_REASON_REJECTED":      4,
		"CALL_END_REASON_NETWORK_ERROR": 5,
		"CALL_END_REASON_SYSTEM_ERROR":  6,
	}
)

func (x CallEndReason) Enum() *CallEndReason {
	p := new(CallEndReason)
	*p = x
	return p
}

func (x CallEndReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallEndReason) Descriptor() protoreflect.EnumDescriptor {
	return file_call_proto_enumTypes[3].Descriptor()
}

func (CallEndReason) Type() protoreflect.EnumType {
	return &file_call_proto_enumTypes[3]
}

func (x CallEndReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallEndReason.Descriptor instead.
func (CallEndReason) EnumDescriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{3}
}

// Start call request
type StartCallRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BubbleId      string                 `protobuf:"bytes,2,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	CallType      CallType               `protobuf:"varint,3,opt,name=call_type,json=callType,proto3,enum=hopen.call.v1.CallType" json:"call_type,omitempty"`
	WithVideo     bool                   `protobuf:"varint,4,opt,name=with_video,json=withVideo,proto3" json:"with_video,omitempty"`
	WithAudio     bool                   `protobuf:"varint,5,opt,name=with_audio,json=withAudio,proto3" json:"with_audio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartCallRequest) Reset() {
	*x = StartCallRequest{}
	mi := &file_call_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCallRequest) ProtoMessage() {}

func (x *StartCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCallRequest.ProtoReflect.Descriptor instead.
func (*StartCallRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{0}
}

func (x *StartCallRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *StartCallRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *StartCallRequest) GetCallType() CallType {
	if x != nil {
		return x.CallType
	}
	return CallType_CALL_TYPE_UNSPECIFIED
}

func (x *StartCallRequest) GetWithVideo() bool {
	if x != nil {
		return x.WithVideo
	}
	return false
}

func (x *StartCallRequest) GetWithAudio() bool {
	if x != nil {
		return x.WithAudio
	}
	return false
}

// Start call response
type StartCallResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallId        string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	CallInfo      *CallInfo              `protobuf:"bytes,2,opt,name=call_info,json=callInfo,proto3" json:"call_info,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartCallResponse) Reset() {
	*x = StartCallResponse{}
	mi := &file_call_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCallResponse) ProtoMessage() {}

func (x *StartCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCallResponse.ProtoReflect.Descriptor instead.
func (*StartCallResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{1}
}

func (x *StartCallResponse) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *StartCallResponse) GetCallInfo() *CallInfo {
	if x != nil {
		return x.CallInfo
	}
	return nil
}

func (x *StartCallResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Join call request
type JoinCallRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CallId        string                 `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	WithVideo     bool                   `protobuf:"varint,3,opt,name=with_video,json=withVideo,proto3" json:"with_video,omitempty"`
	WithAudio     bool                   `protobuf:"varint,4,opt,name=with_audio,json=withAudio,proto3" json:"with_audio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinCallRequest) Reset() {
	*x = JoinCallRequest{}
	mi := &file_call_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinCallRequest) ProtoMessage() {}

func (x *JoinCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinCallRequest.ProtoReflect.Descriptor instead.
func (*JoinCallRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{2}
}

func (x *JoinCallRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *JoinCallRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *JoinCallRequest) GetWithVideo() bool {
	if x != nil {
		return x.WithVideo
	}
	return false
}

func (x *JoinCallRequest) GetWithAudio() bool {
	if x != nil {
		return x.WithAudio
	}
	return false
}

// Join call response
type JoinCallResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallInfo      *CallInfo              `protobuf:"bytes,1,opt,name=call_info,json=callInfo,proto3" json:"call_info,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	AccessToken   string                 `protobuf:"bytes,3,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	LivekitUrl    string                 `protobuf:"bytes,4,opt,name=livekit_url,json=livekitUrl,proto3" json:"livekit_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinCallResponse) Reset() {
	*x = JoinCallResponse{}
	mi := &file_call_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinCallResponse) ProtoMessage() {}

func (x *JoinCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinCallResponse.ProtoReflect.Descriptor instead.
func (*JoinCallResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{3}
}

func (x *JoinCallResponse) GetCallInfo() *CallInfo {
	if x != nil {
		return x.CallInfo
	}
	return nil
}

func (x *JoinCallResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

func (x *JoinCallResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *JoinCallResponse) GetLivekitUrl() string {
	if x != nil {
		return x.LivekitUrl
	}
	return ""
}

// Leave call request
type LeaveCallRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CallId        string                 `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveCallRequest) Reset() {
	*x = LeaveCallRequest{}
	mi := &file_call_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveCallRequest) ProtoMessage() {}

func (x *LeaveCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveCallRequest.ProtoReflect.Descriptor instead.
func (*LeaveCallRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{4}
}

func (x *LeaveCallRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *LeaveCallRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

// Leave call response
type LeaveCallResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveCallResponse) Reset() {
	*x = LeaveCallResponse{}
	mi := &file_call_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveCallResponse) ProtoMessage() {}

func (x *LeaveCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveCallResponse.ProtoReflect.Descriptor instead.
func (*LeaveCallResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{5}
}

func (x *LeaveCallResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *LeaveCallResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// End call request
type EndCallRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CallId        string                 `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndCallRequest) Reset() {
	*x = EndCallRequest{}
	mi := &file_call_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndCallRequest) ProtoMessage() {}

func (x *EndCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndCallRequest.ProtoReflect.Descriptor instead.
func (*EndCallRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{6}
}

func (x *EndCallRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *EndCallRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

// End call response
type EndCallResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndCallResponse) Reset() {
	*x = EndCallResponse{}
	mi := &file_call_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndCallResponse) ProtoMessage() {}

func (x *EndCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndCallResponse.ProtoReflect.Descriptor instead.
func (*EndCallResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{7}
}

func (x *EndCallResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *EndCallResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get call request
type GetCallRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallId        string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallRequest) Reset() {
	*x = GetCallRequest{}
	mi := &file_call_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallRequest) ProtoMessage() {}

func (x *GetCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallRequest.ProtoReflect.Descriptor instead.
func (*GetCallRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{8}
}

func (x *GetCallRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

// Get call response
type GetCallResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallInfo      *CallInfo              `protobuf:"bytes,1,opt,name=call_info,json=callInfo,proto3" json:"call_info,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallResponse) Reset() {
	*x = GetCallResponse{}
	mi := &file_call_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallResponse) ProtoMessage() {}

func (x *GetCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallResponse.ProtoReflect.Descriptor instead.
func (*GetCallResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{9}
}

func (x *GetCallResponse) GetCallInfo() *CallInfo {
	if x != nil {
		return x.CallInfo
	}
	return nil
}

func (x *GetCallResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update call media request
type UpdateCallMediaRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CallId          string                 `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	WithVideo       bool                   `protobuf:"varint,3,opt,name=with_video,json=withVideo,proto3" json:"with_video,omitempty"`
	WithAudio       bool                   `protobuf:"varint,4,opt,name=with_audio,json=withAudio,proto3" json:"with_audio,omitempty"`
	WithScreenShare bool                   `protobuf:"varint,5,opt,name=with_screen_share,json=withScreenShare,proto3" json:"with_screen_share,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateCallMediaRequest) Reset() {
	*x = UpdateCallMediaRequest{}
	mi := &file_call_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCallMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallMediaRequest) ProtoMessage() {}

func (x *UpdateCallMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallMediaRequest.ProtoReflect.Descriptor instead.
func (*UpdateCallMediaRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateCallMediaRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateCallMediaRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *UpdateCallMediaRequest) GetWithVideo() bool {
	if x != nil {
		return x.WithVideo
	}
	return false
}

func (x *UpdateCallMediaRequest) GetWithAudio() bool {
	if x != nil {
		return x.WithAudio
	}
	return false
}

func (x *UpdateCallMediaRequest) GetWithScreenShare() bool {
	if x != nil {
		return x.WithScreenShare
	}
	return false
}

// Update call media response
type UpdateCallMediaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCallMediaResponse) Reset() {
	*x = UpdateCallMediaResponse{}
	mi := &file_call_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCallMediaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallMediaResponse) ProtoMessage() {}

func (x *UpdateCallMediaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallMediaResponse.ProtoReflect.Descriptor instead.
func (*UpdateCallMediaResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateCallMediaResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateCallMediaResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get call participants request
type GetCallParticipantsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallId        string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallParticipantsRequest) Reset() {
	*x = GetCallParticipantsRequest{}
	mi := &file_call_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallParticipantsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallParticipantsRequest) ProtoMessage() {}

func (x *GetCallParticipantsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallParticipantsRequest.ProtoReflect.Descriptor instead.
func (*GetCallParticipantsRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{12}
}

func (x *GetCallParticipantsRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

// Get call participants response
type GetCallParticipantsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Participants  []*CallParticipant     `protobuf:"bytes,1,rep,name=participants,proto3" json:"participants,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallParticipantsResponse) Reset() {
	*x = GetCallParticipantsResponse{}
	mi := &file_call_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallParticipantsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallParticipantsResponse) ProtoMessage() {}

func (x *GetCallParticipantsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallParticipantsResponse.ProtoReflect.Descriptor instead.
func (*GetCallParticipantsResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{13}
}

func (x *GetCallParticipantsResponse) GetParticipants() []*CallParticipant {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *GetCallParticipantsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update participant mute request
type UpdateParticipantMuteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CallId        string                 `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	TargetUserId  string                 `protobuf:"bytes,3,opt,name=target_user_id,json=targetUserId,proto3" json:"target_user_id,omitempty"`
	IsMuted       bool                   `protobuf:"varint,4,opt,name=is_muted,json=isMuted,proto3" json:"is_muted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateParticipantMuteRequest) Reset() {
	*x = UpdateParticipantMuteRequest{}
	mi := &file_call_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateParticipantMuteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateParticipantMuteRequest) ProtoMessage() {}

func (x *UpdateParticipantMuteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateParticipantMuteRequest.ProtoReflect.Descriptor instead.
func (*UpdateParticipantMuteRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateParticipantMuteRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateParticipantMuteRequest) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *UpdateParticipantMuteRequest) GetTargetUserId() string {
	if x != nil {
		return x.TargetUserId
	}
	return ""
}

func (x *UpdateParticipantMuteRequest) GetIsMuted() bool {
	if x != nil {
		return x.IsMuted
	}
	return false
}

// Update participant mute response
type UpdateParticipantMuteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateParticipantMuteResponse) Reset() {
	*x = UpdateParticipantMuteResponse{}
	mi := &file_call_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateParticipantMuteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateParticipantMuteResponse) ProtoMessage() {}

func (x *UpdateParticipantMuteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateParticipantMuteResponse.ProtoReflect.Descriptor instead.
func (*UpdateParticipantMuteResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateParticipantMuteResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateParticipantMuteResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get call history request
type GetCallHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallHistoryRequest) Reset() {
	*x = GetCallHistoryRequest{}
	mi := &file_call_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallHistoryRequest) ProtoMessage() {}

func (x *GetCallHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetCallHistoryRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{16}
}

func (x *GetCallHistoryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetCallHistoryRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCallHistoryRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get call history response
type GetCallHistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallHistory   []*CallRecord          `protobuf:"bytes,1,rep,name=call_history,json=callHistory,proto3" json:"call_history,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallHistoryResponse) Reset() {
	*x = GetCallHistoryResponse{}
	mi := &file_call_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallHistoryResponse) ProtoMessage() {}

func (x *GetCallHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetCallHistoryResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{17}
}

func (x *GetCallHistoryResponse) GetCallHistory() []*CallRecord {
	if x != nil {
		return x.CallHistory
	}
	return nil
}

func (x *GetCallHistoryResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetCallHistoryResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get call stats request
type GetCallStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Period        string                 `protobuf:"bytes,2,opt,name=period,proto3" json:"period,omitempty"` // daily, weekly, monthly
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallStatsRequest) Reset() {
	*x = GetCallStatsRequest{}
	mi := &file_call_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallStatsRequest) ProtoMessage() {}

func (x *GetCallStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallStatsRequest.ProtoReflect.Descriptor instead.
func (*GetCallStatsRequest) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{18}
}

func (x *GetCallStatsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetCallStatsRequest) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

// Get call stats response
type GetCallStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *CallStats             `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCallStatsResponse) Reset() {
	*x = GetCallStatsResponse{}
	mi := &file_call_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCallStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallStatsResponse) ProtoMessage() {}

func (x *GetCallStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallStatsResponse.ProtoReflect.Descriptor instead.
func (*GetCallStatsResponse) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{19}
}

func (x *GetCallStatsResponse) GetStats() *CallStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetCallStatsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Call information
type CallInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallId        string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	BubbleId      string                 `protobuf:"bytes,2,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	InitiatorId   string                 `protobuf:"bytes,3,opt,name=initiator_id,json=initiatorId,proto3" json:"initiator_id,omitempty"`
	CallType      CallType               `protobuf:"varint,4,opt,name=call_type,json=callType,proto3,enum=hopen.call.v1.CallType" json:"call_type,omitempty"`
	Status        CallStatus             `protobuf:"varint,5,opt,name=status,proto3,enum=hopen.call.v1.CallStatus" json:"status,omitempty"`
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt       *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	Participants  []*CallParticipant     `protobuf:"bytes,8,rep,name=participants,proto3" json:"participants,omitempty"`
	MediaState    *CallMediaState        `protobuf:"bytes,9,opt,name=media_state,json=mediaState,proto3" json:"media_state,omitempty"`
	Settings      *CallSettings          `protobuf:"bytes,10,opt,name=settings,proto3" json:"settings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallInfo) Reset() {
	*x = CallInfo{}
	mi := &file_call_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallInfo) ProtoMessage() {}

func (x *CallInfo) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallInfo.ProtoReflect.Descriptor instead.
func (*CallInfo) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{20}
}

func (x *CallInfo) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *CallInfo) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *CallInfo) GetInitiatorId() string {
	if x != nil {
		return x.InitiatorId
	}
	return ""
}

func (x *CallInfo) GetCallType() CallType {
	if x != nil {
		return x.CallType
	}
	return CallType_CALL_TYPE_UNSPECIFIED
}

func (x *CallInfo) GetStatus() CallStatus {
	if x != nil {
		return x.Status
	}
	return CallStatus_CALL_STATUS_UNSPECIFIED
}

func (x *CallInfo) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *CallInfo) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *CallInfo) GetParticipants() []*CallParticipant {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *CallInfo) GetMediaState() *CallMediaState {
	if x != nil {
		return x.MediaState
	}
	return nil
}

func (x *CallInfo) GetSettings() *CallSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// Call participant
type CallParticipant struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	User            *common.User           `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	JoinedAt        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	LeftAt          *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=left_at,json=leftAt,proto3" json:"left_at,omitempty"`
	IsMuted         bool                   `protobuf:"varint,5,opt,name=is_muted,json=isMuted,proto3" json:"is_muted,omitempty"`
	HasVideo        bool                   `protobuf:"varint,6,opt,name=has_video,json=hasVideo,proto3" json:"has_video,omitempty"`
	HasAudio        bool                   `protobuf:"varint,7,opt,name=has_audio,json=hasAudio,proto3" json:"has_audio,omitempty"`
	IsScreenSharing bool                   `protobuf:"varint,8,opt,name=is_screen_sharing,json=isScreenSharing,proto3" json:"is_screen_sharing,omitempty"`
	IsPresent       bool                   `protobuf:"varint,9,opt,name=is_present,json=isPresent,proto3" json:"is_present,omitempty"`
	Role            ParticipantRole        `protobuf:"varint,10,opt,name=role,proto3,enum=hopen.call.v1.ParticipantRole" json:"role,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CallParticipant) Reset() {
	*x = CallParticipant{}
	mi := &file_call_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallParticipant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallParticipant) ProtoMessage() {}

func (x *CallParticipant) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallParticipant.ProtoReflect.Descriptor instead.
func (*CallParticipant) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{21}
}

func (x *CallParticipant) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CallParticipant) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *CallParticipant) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

func (x *CallParticipant) GetLeftAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LeftAt
	}
	return nil
}

func (x *CallParticipant) GetIsMuted() bool {
	if x != nil {
		return x.IsMuted
	}
	return false
}

func (x *CallParticipant) GetHasVideo() bool {
	if x != nil {
		return x.HasVideo
	}
	return false
}

func (x *CallParticipant) GetHasAudio() bool {
	if x != nil {
		return x.HasAudio
	}
	return false
}

func (x *CallParticipant) GetIsScreenSharing() bool {
	if x != nil {
		return x.IsScreenSharing
	}
	return false
}

func (x *CallParticipant) GetIsPresent() bool {
	if x != nil {
		return x.IsPresent
	}
	return false
}

func (x *CallParticipant) GetRole() ParticipantRole {
	if x != nil {
		return x.Role
	}
	return ParticipantRole_PARTICIPANT_ROLE_UNSPECIFIED
}

// Call media state
type CallMediaState struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	HasVideo          bool                   `protobuf:"varint,1,opt,name=has_video,json=hasVideo,proto3" json:"has_video,omitempty"`
	HasAudio          bool                   `protobuf:"varint,2,opt,name=has_audio,json=hasAudio,proto3" json:"has_audio,omitempty"`
	HasScreenShare    bool                   `protobuf:"varint,3,opt,name=has_screen_share,json=hasScreenShare,proto3" json:"has_screen_share,omitempty"`
	ActiveSpeaker     string                 `protobuf:"bytes,4,opt,name=active_speaker,json=activeSpeaker,proto3" json:"active_speaker,omitempty"`
	MutedParticipants []string               `protobuf:"bytes,5,rep,name=muted_participants,json=mutedParticipants,proto3" json:"muted_participants,omitempty"`
	VideoParticipants []string               `protobuf:"bytes,6,rep,name=video_participants,json=videoParticipants,proto3" json:"video_participants,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CallMediaState) Reset() {
	*x = CallMediaState{}
	mi := &file_call_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallMediaState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallMediaState) ProtoMessage() {}

func (x *CallMediaState) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallMediaState.ProtoReflect.Descriptor instead.
func (*CallMediaState) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{22}
}

func (x *CallMediaState) GetHasVideo() bool {
	if x != nil {
		return x.HasVideo
	}
	return false
}

func (x *CallMediaState) GetHasAudio() bool {
	if x != nil {
		return x.HasAudio
	}
	return false
}

func (x *CallMediaState) GetHasScreenShare() bool {
	if x != nil {
		return x.HasScreenShare
	}
	return false
}

func (x *CallMediaState) GetActiveSpeaker() string {
	if x != nil {
		return x.ActiveSpeaker
	}
	return ""
}

func (x *CallMediaState) GetMutedParticipants() []string {
	if x != nil {
		return x.MutedParticipants
	}
	return nil
}

func (x *CallMediaState) GetVideoParticipants() []string {
	if x != nil {
		return x.VideoParticipants
	}
	return nil
}

// Call settings
type CallSettings struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	MaxParticipants  int32                  `protobuf:"varint,1,opt,name=max_participants,json=maxParticipants,proto3" json:"max_participants,omitempty"`
	AllowScreenShare bool                   `protobuf:"varint,2,opt,name=allow_screen_share,json=allowScreenShare,proto3" json:"allow_screen_share,omitempty"`
	AllowRecording   bool                   `protobuf:"varint,3,opt,name=allow_recording,json=allowRecording,proto3" json:"allow_recording,omitempty"`
	Layout           string                 `protobuf:"bytes,4,opt,name=layout,proto3" json:"layout,omitempty"` // grid, spotlight, presentation
	AutoMuteOnJoin   bool                   `protobuf:"varint,5,opt,name=auto_mute_on_join,json=autoMuteOnJoin,proto3" json:"auto_mute_on_join,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CallSettings) Reset() {
	*x = CallSettings{}
	mi := &file_call_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallSettings) ProtoMessage() {}

func (x *CallSettings) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallSettings.ProtoReflect.Descriptor instead.
func (*CallSettings) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{23}
}

func (x *CallSettings) GetMaxParticipants() int32 {
	if x != nil {
		return x.MaxParticipants
	}
	return 0
}

func (x *CallSettings) GetAllowScreenShare() bool {
	if x != nil {
		return x.AllowScreenShare
	}
	return false
}

func (x *CallSettings) GetAllowRecording() bool {
	if x != nil {
		return x.AllowRecording
	}
	return false
}

func (x *CallSettings) GetLayout() string {
	if x != nil {
		return x.Layout
	}
	return ""
}

func (x *CallSettings) GetAutoMuteOnJoin() bool {
	if x != nil {
		return x.AutoMuteOnJoin
	}
	return false
}

// Call record for history
type CallRecord struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	CallId           string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	BubbleId         string                 `protobuf:"bytes,2,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	BubbleName       string                 `protobuf:"bytes,3,opt,name=bubble_name,json=bubbleName,proto3" json:"bubble_name,omitempty"`
	CallType         CallType               `protobuf:"varint,4,opt,name=call_type,json=callType,proto3,enum=hopen.call.v1.CallType" json:"call_type,omitempty"`
	StartedAt        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt          *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	DurationSeconds  int32                  `protobuf:"varint,7,opt,name=duration_seconds,json=durationSeconds,proto3" json:"duration_seconds,omitempty"`
	ParticipantCount int32                  `protobuf:"varint,8,opt,name=participant_count,json=participantCount,proto3" json:"participant_count,omitempty"`
	WasInitiator     bool                   `protobuf:"varint,9,opt,name=was_initiator,json=wasInitiator,proto3" json:"was_initiator,omitempty"`
	EndReason        CallEndReason          `protobuf:"varint,10,opt,name=end_reason,json=endReason,proto3,enum=hopen.call.v1.CallEndReason" json:"end_reason,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CallRecord) Reset() {
	*x = CallRecord{}
	mi := &file_call_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallRecord) ProtoMessage() {}

func (x *CallRecord) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallRecord.ProtoReflect.Descriptor instead.
func (*CallRecord) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{24}
}

func (x *CallRecord) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *CallRecord) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *CallRecord) GetBubbleName() string {
	if x != nil {
		return x.BubbleName
	}
	return ""
}

func (x *CallRecord) GetCallType() CallType {
	if x != nil {
		return x.CallType
	}
	return CallType_CALL_TYPE_UNSPECIFIED
}

func (x *CallRecord) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *CallRecord) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *CallRecord) GetDurationSeconds() int32 {
	if x != nil {
		return x.DurationSeconds
	}
	return 0
}

func (x *CallRecord) GetParticipantCount() int32 {
	if x != nil {
		return x.ParticipantCount
	}
	return 0
}

func (x *CallRecord) GetWasInitiator() bool {
	if x != nil {
		return x.WasInitiator
	}
	return false
}

func (x *CallRecord) GetEndReason() CallEndReason {
	if x != nil {
		return x.EndReason
	}
	return CallEndReason_CALL_END_REASON_UNSPECIFIED
}

// Call statistics
type CallStats struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	TotalCalls             int32                  `protobuf:"varint,1,opt,name=total_calls,json=totalCalls,proto3" json:"total_calls,omitempty"`
	TotalDurationMinutes   int32                  `protobuf:"varint,2,opt,name=total_duration_minutes,json=totalDurationMinutes,proto3" json:"total_duration_minutes,omitempty"`
	AverageDurationMinutes int32                  `protobuf:"varint,3,opt,name=average_duration_minutes,json=averageDurationMinutes,proto3" json:"average_duration_minutes,omitempty"`
	TotalParticipants      int32                  `protobuf:"varint,4,opt,name=total_participants,json=totalParticipants,proto3" json:"total_participants,omitempty"`
	CallsByType            map[string]int32       `protobuf:"bytes,5,rep,name=calls_by_type,json=callsByType,proto3" json:"calls_by_type,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	CallsByDay             map[string]int32       `protobuf:"bytes,6,rep,name=calls_by_day,json=callsByDay,proto3" json:"calls_by_day,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	SuccessRate            float32                `protobuf:"fixed32,7,opt,name=success_rate,json=successRate,proto3" json:"success_rate,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CallStats) Reset() {
	*x = CallStats{}
	mi := &file_call_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallStats) ProtoMessage() {}

func (x *CallStats) ProtoReflect() protoreflect.Message {
	mi := &file_call_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallStats.ProtoReflect.Descriptor instead.
func (*CallStats) Descriptor() ([]byte, []int) {
	return file_call_proto_rawDescGZIP(), []int{25}
}

func (x *CallStats) GetTotalCalls() int32 {
	if x != nil {
		return x.TotalCalls
	}
	return 0
}

func (x *CallStats) GetTotalDurationMinutes() int32 {
	if x != nil {
		return x.TotalDurationMinutes
	}
	return 0
}

func (x *CallStats) GetAverageDurationMinutes() int32 {
	if x != nil {
		return x.AverageDurationMinutes
	}
	return 0
}

func (x *CallStats) GetTotalParticipants() int32 {
	if x != nil {
		return x.TotalParticipants
	}
	return 0
}

func (x *CallStats) GetCallsByType() map[string]int32 {
	if x != nil {
		return x.CallsByType
	}
	return nil
}

func (x *CallStats) GetCallsByDay() map[string]int32 {
	if x != nil {
		return x.CallsByDay
	}
	return nil
}

func (x *CallStats) GetSuccessRate() float32 {
	if x != nil {
		return x.SuccessRate
	}
	return 0
}

var File_call_proto protoreflect.FileDescriptor

const file_call_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"call.proto\x12\rhopen.call.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"\xbc\x01\n" +
	"\x10StartCallRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tbubble_id\x18\x02 \x01(\tR\bbubbleId\x124\n" +
	"\tcall_type\x18\x03 \x01(\x0e2\x17.hopen.call.v1.CallTypeR\bcallType\x12\x1d\n" +
	"\n" +
	"with_video\x18\x04 \x01(\bR\twithVideo\x12\x1d\n" +
	"\n" +
	"with_audio\x18\x05 \x01(\bR\twithAudio\"\xa3\x01\n" +
	"\x11StartCallResponse\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\tR\x06callId\x124\n" +
	"\tcall_info\x18\x02 \x01(\v2\x17.hopen.call.v1.CallInfoR\bcallInfo\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x81\x01\n" +
	"\x0fJoinCallRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x17\n" +
	"\acall_id\x18\x02 \x01(\tR\x06callId\x12\x1d\n" +
	"\n" +
	"with_video\x18\x03 \x01(\bR\twithVideo\x12\x1d\n" +
	"\n" +
	"with_audio\x18\x04 \x01(\bR\twithAudio\"\x89\x01\n" +
	"\x10JoinCallResponse\x124\n" +
	"\tcall_info\x18\x01 \x01(\v2\x17.hopen.call.v1.CallInfoR\bcallInfo\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"D\n" +
	"\x10LeaveCallRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x17\n" +
	"\acall_id\x18\x02 \x01(\tR\x06callId\"n\n" +
	"\x11LeaveCallResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"B\n" +
	"\x0eEndCallRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x17\n" +
	"\acall_id\x18\x02 \x01(\tR\x06callId\"l\n" +
	"\x0fEndCallResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\")\n" +
	"\x0eGetCallRequest\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\tR\x06callId\"\x88\x01\n" +
	"\x0fGetCallResponse\x124\n" +
	"\tcall_info\x18\x01 \x01(\v2\x17.hopen.call.v1.CallInfoR\bcallInfo\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xb4\x01\n" +
	"\x16UpdateCallMediaRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x17\n" +
	"\acall_id\x18\x02 \x01(\tR\x06callId\x12\x1d\n" +
	"\n" +
	"with_video\x18\x03 \x01(\bR\twithVideo\x12\x1d\n" +
	"\n" +
	"with_audio\x18\x04 \x01(\bR\twithAudio\x12*\n" +
	"\x11with_screen_share\x18\x05 \x01(\bR\x0fwithScreenShare\"t\n" +
	"\x17UpdateCallMediaResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"5\n" +
	"\x1aGetCallParticipantsRequest\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\tR\x06callId\"\xa2\x01\n" +
	"\x1bGetCallParticipantsResponse\x12B\n" +
	"\fparticipants\x18\x01 \x03(\v2\x1e.hopen.call.v1.CallParticipantR\fparticipants\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x91\x01\n" +
	"\x1cUpdateParticipantMuteRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x17\n" +
	"\acall_id\x18\x02 \x01(\tR\x06callId\x12$\n" +
	"\x0etarget_user_id\x18\x03 \x01(\tR\ftargetUserId\x12\x19\n" +
	"\bis_muted\x18\x04 \x01(\bR\aisMuted\"z\n" +
	"\x1dUpdateParticipantMuteResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"a\n" +
	"\x15GetCallHistoryRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"\xd4\x01\n" +
	"\x16GetCallHistoryResponse\x12<\n" +
	"\fcall_history\x18\x01 \x03(\v2\x19.hopen.call.v1.CallRecordR\vcallHistory\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"F\n" +
	"\x13GetCallStatsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06period\x18\x02 \x01(\tR\x06period\"\x87\x01\n" +
	"\x14GetCallStatsResponse\x12.\n" +
	"\x05stats\x18\x01 \x01(\v2\x18.hopen.call.v1.CallStatsR\x05stats\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xfb\x03\n" +
	"\bCallInfo\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\tR\x06callId\x12\x1b\n" +
	"\tbubble_id\x18\x02 \x01(\tR\bbubbleId\x12!\n" +
	"\finitiator_id\x18\x03 \x01(\tR\vinitiatorId\x124\n" +
	"\tcall_type\x18\x04 \x01(\x0e2\x17.hopen.call.v1.CallTypeR\bcallType\x121\n" +
	"\x06status\x18\x05 \x01(\x0e2\x19.hopen.call.v1.CallStatusR\x06status\x129\n" +
	"\n" +
	"started_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x125\n" +
	"\bended_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\aendedAt\x12B\n" +
	"\fparticipants\x18\b \x03(\v2\x1e.hopen.call.v1.CallParticipantR\fparticipants\x12>\n" +
	"\vmedia_state\x18\t \x01(\v2\x1d.hopen.call.v1.CallMediaStateR\n" +
	"mediaState\x127\n" +
	"\bsettings\x18\n" +
	" \x01(\v2\x1b.hopen.call.v1.CallSettingsR\bsettings\"\x97\x03\n" +
	"\x0fCallParticipant\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12)\n" +
	"\x04user\x18\x02 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x127\n" +
	"\tjoined_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\bjoinedAt\x123\n" +
	"\aleft_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x06leftAt\x12\x19\n" +
	"\bis_muted\x18\x05 \x01(\bR\aisMuted\x12\x1b\n" +
	"\thas_video\x18\x06 \x01(\bR\bhasVideo\x12\x1b\n" +
	"\thas_audio\x18\a \x01(\bR\bhasAudio\x12*\n" +
	"\x11is_screen_sharing\x18\b \x01(\bR\x0fisScreenSharing\x12\x1d\n" +
	"\n" +
	"is_present\x18\t \x01(\bR\tisPresent\x122\n" +
	"\x04role\x18\n" +
	" \x01(\x0e2\x1e.hopen.call.v1.ParticipantRoleR\x04role\"\xf9\x01\n" +
	"\x0eCallMediaState\x12\x1b\n" +
	"\thas_video\x18\x01 \x01(\bR\bhasVideo\x12\x1b\n" +
	"\thas_audio\x18\x02 \x01(\bR\bhasAudio\x12(\n" +
	"\x10has_screen_share\x18\x03 \x01(\bR\x0ehasScreenShare\x12%\n" +
	"\x0eactive_speaker\x18\x04 \x01(\tR\ractiveSpeaker\x12-\n" +
	"\x12muted_participants\x18\x05 \x03(\tR\x11mutedParticipants\x12-\n" +
	"\x12video_participants\x18\x06 \x03(\tR\x11videoParticipants\"\xd3\x01\n" +
	"\fCallSettings\x12)\n" +
	"\x10max_participants\x18\x01 \x01(\x05R\x0fmaxParticipants\x12,\n" +
	"\x12allow_screen_share\x18\x02 \x01(\bR\x10allowScreenShare\x12'\n" +
	"\x0fallow_recording\x18\x03 \x01(\bR\x0eallowRecording\x12\x16\n" +
	"\x06layout\x18\x04 \x01(\tR\x06layout\x12)\n" +
	"\x11auto_mute_on_join\x18\x05 \x01(\bR\x0eautoMuteOnJoin\"\xc5\x03\n" +
	"\n" +
	"CallRecord\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\tR\x06callId\x12\x1b\n" +
	"\tbubble_id\x18\x02 \x01(\tR\bbubbleId\x12\x1f\n" +
	"\vbubble_name\x18\x03 \x01(\tR\n" +
	"bubbleName\x124\n" +
	"\tcall_type\x18\x04 \x01(\x0e2\x17.hopen.call.v1.CallTypeR\bcallType\x129\n" +
	"\n" +
	"started_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x125\n" +
	"\bended_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\aendedAt\x12)\n" +
	"\x10duration_seconds\x18\a \x01(\x05R\x0fdurationSeconds\x12+\n" +
	"\x11participant_count\x18\b \x01(\x05R\x10participantCount\x12#\n" +
	"\rwas_initiator\x18\t \x01(\bR\fwasInitiator\x12;\n" +
	"\n" +
	"end_reason\x18\n" +
	" \x01(\x0e2\x1c.hopen.call.v1.CallEndReasonR\tendReason\"\x88\x04\n" +
	"\tCallStats\x12\x1f\n" +
	"\vtotal_calls\x18\x01 \x01(\x05R\n" +
	"totalCalls\x124\n" +
	"\x16total_duration_minutes\x18\x02 \x01(\x05R\x14totalDurationMinutes\x128\n" +
	"\x18average_duration_minutes\x18\x03 \x01(\x05R\x16averageDurationMinutes\x12-\n" +
	"\x12total_participants\x18\x04 \x01(\x05R\x11totalParticipants\x12M\n" +
	"\rcalls_by_type\x18\x05 \x03(\v2).hopen.call.v1.CallStats.CallsByTypeEntryR\vcallsByType\x12J\n" +
	"\fcalls_by_day\x18\x06 \x03(\v2(.hopen.call.v1.CallStats.CallsByDayEntryR\n" +
	"callsByDay\x12!\n" +
	"\fsuccess_rate\x18\a \x01(\x02R\vsuccessRate\x1a>\n" +
	"\x10CallsByTypeEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a=\n" +
	"\x0fCallsByDayEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01*k\n" +
	"\bCallType\x12\x19\n" +
	"\x15CALL_TYPE_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fCALL_TYPE_AUDIO\x10\x01\x12\x13\n" +
	"\x0fCALL_TYPE_VIDEO\x10\x02\x12\x1a\n" +
	"\x16CALL_TYPE_SCREEN_SHARE\x10\x03*\xc0\x01\n" +
	"\n" +
	"CallStatus\x12\x1b\n" +
	"\x17CALL_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16CALL_STATUS_INITIATING\x10\x01\x12\x17\n" +
	"\x13CALL_STATUS_RINGING\x10\x02\x12\x19\n" +
	"\x15CALL_STATUS_CONNECTED\x10\x03\x12\x15\n" +
	"\x11CALL_STATUS_ENDED\x10\x04\x12\x16\n" +
	"\x12CALL_STATUS_FAILED\x10\x05\x12\x16\n" +
	"\x12CALL_STATUS_MISSED\x10\x06*\x95\x01\n" +
	"\x0fParticipantRole\x12 \n" +
	"\x1cPARTICIPANT_ROLE_UNSPECIFIED\x10\x00\x12\x1e\n" +
	"\x1aPARTICIPANT_ROLE_INITIATOR\x10\x01\x12 \n" +
	"\x1cPARTICIPANT_ROLE_PARTICIPANT\x10\x02\x12\x1e\n" +
	"\x1aPARTICIPANT_ROLE_MODERATOR\x10\x03*\xe8\x01\n" +
	"\rCallEndReason\x12\x1f\n" +
	"\x1bCALL_END_REASON_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16CALL_END_REASON_HANGUP\x10\x01\x12\x18\n" +
	"\x14CALL_END_REASON_BUSY\x10\x02\x12\x1d\n" +
	"\x19CALL_END_REASON_NO_ANSWER\x10\x03\x12\x1c\n" +
	"\x18CALL_END_REASON_REJECTED\x10\x04\x12!\n" +
	"\x1dCALL_END_REASON_NETWORK_ERROR\x10\x05\x12 \n" +
	"\x1cCALL_END_REASON_SYSTEM_ERROR\x10\x062\x8a\a\n" +
	"\vCallService\x12N\n" +
	"\tStartCall\x12\x1f.hopen.call.v1.StartCallRequest\x1a .hopen.call.v1.StartCallResponse\x12K\n" +
	"\bJoinCall\x12\x1e.hopen.call.v1.JoinCallRequest\x1a\x1f.hopen.call.v1.JoinCallResponse\x12N\n" +
	"\tLeaveCall\x12\x1f.hopen.call.v1.LeaveCallRequest\x1a .hopen.call.v1.LeaveCallResponse\x12H\n" +
	"\aEndCall\x12\x1d.hopen.call.v1.EndCallRequest\x1a\x1e.hopen.call.v1.EndCallResponse\x12H\n" +
	"\aGetCall\x12\x1d.hopen.call.v1.GetCallRequest\x1a\x1e.hopen.call.v1.GetCallResponse\x12`\n" +
	"\x0fUpdateCallMedia\x12%.hopen.call.v1.UpdateCallMediaRequest\x1a&.hopen.call.v1.UpdateCallMediaResponse\x12l\n" +
	"\x13GetCallParticipants\x12).hopen.call.v1.GetCallParticipantsRequest\x1a*.hopen.call.v1.GetCallParticipantsResponse\x12r\n" +
	"\x15UpdateParticipantMute\x12+.hopen.call.v1.UpdateParticipantMuteRequest\x1a,.hopen.call.v1.UpdateParticipantMuteResponse\x12]\n" +
	"\x0eGetCallHistory\x12$.hopen.call.v1.GetCallHistoryRequest\x1a%.hopen.call.v1.GetCallHistoryResponse\x12W\n" +
	"\fGetCallStats\x12\".hopen.call.v1.GetCallStatsRequest\x1a#.hopen.call.v1.GetCallStatsResponseB8\n" +
	"\x11com.hopen.call.v1P\x01Z!hopenbackend/protos/gen/call;callb\x06proto3"

var (
	file_call_proto_rawDescOnce sync.Once
	file_call_proto_rawDescData []byte
)

func file_call_proto_rawDescGZIP() []byte {
	file_call_proto_rawDescOnce.Do(func() {
		file_call_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_call_proto_rawDesc), len(file_call_proto_rawDesc)))
	})
	return file_call_proto_rawDescData
}

var file_call_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_call_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_call_proto_goTypes = []any{
	(CallType)(0),                         // 0: hopen.call.v1.CallType
	(CallStatus)(0),                       // 1: hopen.call.v1.CallStatus
	(ParticipantRole)(0),                  // 2: hopen.call.v1.ParticipantRole
	(CallEndReason)(0),                    // 3: hopen.call.v1.CallEndReason
	(*StartCallRequest)(nil),              // 4: hopen.call.v1.StartCallRequest
	(*StartCallResponse)(nil),             // 5: hopen.call.v1.StartCallResponse
	(*JoinCallRequest)(nil),               // 6: hopen.call.v1.JoinCallRequest
	(*JoinCallResponse)(nil),              // 7: hopen.call.v1.JoinCallResponse
	(*LeaveCallRequest)(nil),              // 8: hopen.call.v1.LeaveCallRequest
	(*LeaveCallResponse)(nil),             // 9: hopen.call.v1.LeaveCallResponse
	(*EndCallRequest)(nil),                // 10: hopen.call.v1.EndCallRequest
	(*EndCallResponse)(nil),               // 11: hopen.call.v1.EndCallResponse
	(*GetCallRequest)(nil),                // 12: hopen.call.v1.GetCallRequest
	(*GetCallResponse)(nil),               // 13: hopen.call.v1.GetCallResponse
	(*UpdateCallMediaRequest)(nil),        // 14: hopen.call.v1.UpdateCallMediaRequest
	(*UpdateCallMediaResponse)(nil),       // 15: hopen.call.v1.UpdateCallMediaResponse
	(*GetCallParticipantsRequest)(nil),    // 16: hopen.call.v1.GetCallParticipantsRequest
	(*GetCallParticipantsResponse)(nil),   // 17: hopen.call.v1.GetCallParticipantsResponse
	(*UpdateParticipantMuteRequest)(nil),  // 18: hopen.call.v1.UpdateParticipantMuteRequest
	(*UpdateParticipantMuteResponse)(nil), // 19: hopen.call.v1.UpdateParticipantMuteResponse
	(*GetCallHistoryRequest)(nil),         // 20: hopen.call.v1.GetCallHistoryRequest
	(*GetCallHistoryResponse)(nil),        // 21: hopen.call.v1.GetCallHistoryResponse
	(*GetCallStatsRequest)(nil),           // 22: hopen.call.v1.GetCallStatsRequest
	(*GetCallStatsResponse)(nil),          // 23: hopen.call.v1.GetCallStatsResponse
	(*CallInfo)(nil),                      // 24: hopen.call.v1.CallInfo
	(*CallParticipant)(nil),               // 25: hopen.call.v1.CallParticipant
	(*CallMediaState)(nil),                // 26: hopen.call.v1.CallMediaState
	(*CallSettings)(nil),                  // 27: hopen.call.v1.CallSettings
	(*CallRecord)(nil),                    // 28: hopen.call.v1.CallRecord
	(*CallStats)(nil),                     // 29: hopen.call.v1.CallStats
	nil,                                   // 30: hopen.call.v1.CallStats.CallsByTypeEntry
	nil,                                   // 31: hopen.call.v1.CallStats.CallsByDayEntry
	(*common.ApiResponse)(nil),            // 32: hopen.common.v1.ApiResponse
	(*common.Pagination)(nil),             // 33: hopen.common.v1.Pagination
	(*timestamppb.Timestamp)(nil),         // 34: google.protobuf.Timestamp
	(*common.User)(nil),                   // 35: hopen.common.v1.User
}
var file_call_proto_depIdxs = []int32{
	0,  // 0: hopen.call.v1.StartCallRequest.call_type:type_name -> hopen.call.v1.CallType
	24, // 1: hopen.call.v1.StartCallResponse.call_info:type_name -> hopen.call.v1.CallInfo
	32, // 2: hopen.call.v1.StartCallResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	24, // 3: hopen.call.v1.JoinCallResponse.call_info:type_name -> hopen.call.v1.CallInfo
	32, // 4: hopen.call.v1.JoinCallResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	32, // 5: hopen.call.v1.LeaveCallResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	32, // 6: hopen.call.v1.EndCallResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	24, // 7: hopen.call.v1.GetCallResponse.call_info:type_name -> hopen.call.v1.CallInfo
	32, // 8: hopen.call.v1.GetCallResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	32, // 9: hopen.call.v1.UpdateCallMediaResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 10: hopen.call.v1.GetCallParticipantsResponse.participants:type_name -> hopen.call.v1.CallParticipant
	32, // 11: hopen.call.v1.GetCallParticipantsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	32, // 12: hopen.call.v1.UpdateParticipantMuteResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	28, // 13: hopen.call.v1.GetCallHistoryResponse.call_history:type_name -> hopen.call.v1.CallRecord
	33, // 14: hopen.call.v1.GetCallHistoryResponse.pagination:type_name -> hopen.common.v1.Pagination
	32, // 15: hopen.call.v1.GetCallHistoryResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	29, // 16: hopen.call.v1.GetCallStatsResponse.stats:type_name -> hopen.call.v1.CallStats
	32, // 17: hopen.call.v1.GetCallStatsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 18: hopen.call.v1.CallInfo.call_type:type_name -> hopen.call.v1.CallType
	1,  // 19: hopen.call.v1.CallInfo.status:type_name -> hopen.call.v1.CallStatus
	34, // 20: hopen.call.v1.CallInfo.started_at:type_name -> google.protobuf.Timestamp
	34, // 21: hopen.call.v1.CallInfo.ended_at:type_name -> google.protobuf.Timestamp
	25, // 22: hopen.call.v1.CallInfo.participants:type_name -> hopen.call.v1.CallParticipant
	26, // 23: hopen.call.v1.CallInfo.media_state:type_name -> hopen.call.v1.CallMediaState
	27, // 24: hopen.call.v1.CallInfo.settings:type_name -> hopen.call.v1.CallSettings
	35, // 25: hopen.call.v1.CallParticipant.user:type_name -> hopen.common.v1.User
	34, // 26: hopen.call.v1.CallParticipant.joined_at:type_name -> google.protobuf.Timestamp
	34, // 27: hopen.call.v1.CallParticipant.left_at:type_name -> google.protobuf.Timestamp
	2,  // 28: hopen.call.v1.CallParticipant.role:type_name -> hopen.call.v1.ParticipantRole
	0,  // 29: hopen.call.v1.CallRecord.call_type:type_name -> hopen.call.v1.CallType
	34, // 30: hopen.call.v1.CallRecord.started_at:type_name -> google.protobuf.Timestamp
	34, // 31: hopen.call.v1.CallRecord.ended_at:type_name -> google.protobuf.Timestamp
	3,  // 32: hopen.call.v1.CallRecord.end_reason:type_name -> hopen.call.v1.CallEndReason
	30, // 33: hopen.call.v1.CallStats.calls_by_type:type_name -> hopen.call.v1.CallStats.CallsByTypeEntry
	31, // 34: hopen.call.v1.CallStats.calls_by_day:type_name -> hopen.call.v1.CallStats.CallsByDayEntry
	4,  // 35: hopen.call.v1.CallService.StartCall:input_type -> hopen.call.v1.StartCallRequest
	6,  // 36: hopen.call.v1.CallService.JoinCall:input_type -> hopen.call.v1.JoinCallRequest
	8,  // 37: hopen.call.v1.CallService.LeaveCall:input_type -> hopen.call.v1.LeaveCallRequest
	10, // 38: hopen.call.v1.CallService.EndCall:input_type -> hopen.call.v1.EndCallRequest
	12, // 39: hopen.call.v1.CallService.GetCall:input_type -> hopen.call.v1.GetCallRequest
	14, // 40: hopen.call.v1.CallService.UpdateCallMedia:input_type -> hopen.call.v1.UpdateCallMediaRequest
	16, // 41: hopen.call.v1.CallService.GetCallParticipants:input_type -> hopen.call.v1.GetCallParticipantsRequest
	18, // 42: hopen.call.v1.CallService.UpdateParticipantMute:input_type -> hopen.call.v1.UpdateParticipantMuteRequest
	20, // 43: hopen.call.v1.CallService.GetCallHistory:input_type -> hopen.call.v1.GetCallHistoryRequest
	22, // 44: hopen.call.v1.CallService.GetCallStats:input_type -> hopen.call.v1.GetCallStatsRequest
	5,  // 45: hopen.call.v1.CallService.StartCall:output_type -> hopen.call.v1.StartCallResponse
	7,  // 46: hopen.call.v1.CallService.JoinCall:output_type -> hopen.call.v1.JoinCallResponse
	9,  // 47: hopen.call.v1.CallService.LeaveCall:output_type -> hopen.call.v1.LeaveCallResponse
	11, // 48: hopen.call.v1.CallService.EndCall:output_type -> hopen.call.v1.EndCallResponse
	13, // 49: hopen.call.v1.CallService.GetCall:output_type -> hopen.call.v1.GetCallResponse
	15, // 50: hopen.call.v1.CallService.UpdateCallMedia:output_type -> hopen.call.v1.UpdateCallMediaResponse
	17, // 51: hopen.call.v1.CallService.GetCallParticipants:output_type -> hopen.call.v1.GetCallParticipantsResponse
	19, // 52: hopen.call.v1.CallService.UpdateParticipantMute:output_type -> hopen.call.v1.UpdateParticipantMuteResponse
	21, // 53: hopen.call.v1.CallService.GetCallHistory:output_type -> hopen.call.v1.GetCallHistoryResponse
	23, // 54: hopen.call.v1.CallService.GetCallStats:output_type -> hopen.call.v1.GetCallStatsResponse
	45, // [45:55] is the sub-list for method output_type
	35, // [35:45] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_call_proto_init() }
func file_call_proto_init() {
	if File_call_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_call_proto_rawDesc), len(file_call_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_call_proto_goTypes,
		DependencyIndexes: file_call_proto_depIdxs,
		EnumInfos:         file_call_proto_enumTypes,
		MessageInfos:      file_call_proto_msgTypes,
	}.Build()
	File_call_proto = out.File
	file_call_proto_goTypes = nil
	file_call_proto_depIdxs = nil
}
