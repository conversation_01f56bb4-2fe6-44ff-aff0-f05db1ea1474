// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: media.proto

package media

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// File status enum
type FileStatus int32

const (
	FileStatus_FILE_STATUS_UNSPECIFIED FileStatus = 0
	FileStatus_FILE_STATUS_UPLOADING   FileStatus = 1
	FileStatus_FILE_STATUS_PROCESSING  FileStatus = 2
	FileStatus_FILE_STATUS_READY       FileStatus = 3
	FileStatus_FILE_STATUS_FAILED      FileStatus = 4
	FileStatus_FILE_STATUS_DELETED     FileStatus = 5
)

// Enum value maps for FileStatus.
var (
	FileStatus_name = map[int32]string{
		0: "FILE_STATUS_UNSPECIFIED",
		1: "FILE_STATUS_UPLOADING",
		2: "FILE_STATUS_PROCESSING",
		3: "FILE_STATUS_READY",
		4: "FILE_STATUS_FAILED",
		5: "FILE_STATUS_DELETED",
	}
	FileStatus_value = map[string]int32{
		"FILE_STATUS_UNSPECIFIED": 0,
		"FILE_STATUS_UPLOADING":   1,
		"FILE_STATUS_PROCESSING":  2,
		"FILE_STATUS_READY":       3,
		"FILE_STATUS_FAILED":      4,
		"FILE_STATUS_DELETED":     5,
	}
)

func (x FileStatus) Enum() *FileStatus {
	p := new(FileStatus)
	*p = x
	return p
}

func (x FileStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_media_proto_enumTypes[0].Descriptor()
}

func (FileStatus) Type() protoreflect.EnumType {
	return &file_media_proto_enumTypes[0]
}

func (x FileStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileStatus.Descriptor instead.
func (FileStatus) EnumDescriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{0}
}

// Upload file request
type UploadFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileName      string                 `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType      string                 `protobuf:"bytes,3,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileSize      int64                  `protobuf:"varint,4,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	BubbleId      string                 `protobuf:"bytes,5,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	MessageId     string                 `protobuf:"bytes,6,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	FileData      []byte                 `protobuf:"bytes,7,opt,name=file_data,json=fileData,proto3" json:"file_data,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,8,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadFileRequest) Reset() {
	*x = UploadFileRequest{}
	mi := &file_media_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileRequest) ProtoMessage() {}

func (x *UploadFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileRequest.ProtoReflect.Descriptor instead.
func (*UploadFileRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{0}
}

func (x *UploadFileRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UploadFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadFileRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *UploadFileRequest) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *UploadFileRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *UploadFileRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *UploadFileRequest) GetFileData() []byte {
	if x != nil {
		return x.FileData
	}
	return nil
}

func (x *UploadFileRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Upload file response
type UploadFileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	FileInfo      *FileInfo              `protobuf:"bytes,2,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`
	UploadUrl     string                 `protobuf:"bytes,3,opt,name=upload_url,json=uploadUrl,proto3" json:"upload_url,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadFileResponse) Reset() {
	*x = UploadFileResponse{}
	mi := &file_media_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileResponse) ProtoMessage() {}

func (x *UploadFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileResponse.ProtoReflect.Descriptor instead.
func (*UploadFileResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{1}
}

func (x *UploadFileResponse) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *UploadFileResponse) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

func (x *UploadFileResponse) GetUploadUrl() string {
	if x != nil {
		return x.UploadUrl
	}
	return ""
}

func (x *UploadFileResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get file info request
type GetFileInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFileInfoRequest) Reset() {
	*x = GetFileInfoRequest{}
	mi := &file_media_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileInfoRequest) ProtoMessage() {}

func (x *GetFileInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileInfoRequest.ProtoReflect.Descriptor instead.
func (*GetFileInfoRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{2}
}

func (x *GetFileInfoRequest) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

// Get file info response
type GetFileInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileInfo      *FileInfo              `protobuf:"bytes,1,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFileInfoResponse) Reset() {
	*x = GetFileInfoResponse{}
	mi := &file_media_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileInfoResponse) ProtoMessage() {}

func (x *GetFileInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileInfoResponse.ProtoReflect.Descriptor instead.
func (*GetFileInfoResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{3}
}

func (x *GetFileInfoResponse) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

func (x *GetFileInfoResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Delete file request
type DeleteFileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileId        string                 `protobuf:"bytes,2,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFileRequest) Reset() {
	*x = DeleteFileRequest{}
	mi := &file_media_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFileRequest) ProtoMessage() {}

func (x *DeleteFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFileRequest.ProtoReflect.Descriptor instead.
func (*DeleteFileRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteFileRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeleteFileRequest) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

// Delete file response
type DeleteFileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFileResponse) Reset() {
	*x = DeleteFileResponse{}
	mi := &file_media_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFileResponse) ProtoMessage() {}

func (x *DeleteFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFileResponse.ProtoReflect.Descriptor instead.
func (*DeleteFileResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteFileResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteFileResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get file URL request
type GetFileUrlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	AccessType    string                 `protobuf:"bytes,2,opt,name=access_type,json=accessType,proto3" json:"access_type,omitempty"` // public, private, signed
	ExpirySeconds int32                  `protobuf:"varint,3,opt,name=expiry_seconds,json=expirySeconds,proto3" json:"expiry_seconds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFileUrlRequest) Reset() {
	*x = GetFileUrlRequest{}
	mi := &file_media_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileUrlRequest) ProtoMessage() {}

func (x *GetFileUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileUrlRequest.ProtoReflect.Descriptor instead.
func (*GetFileUrlRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{6}
}

func (x *GetFileUrlRequest) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *GetFileUrlRequest) GetAccessType() string {
	if x != nil {
		return x.AccessType
	}
	return ""
}

func (x *GetFileUrlRequest) GetExpirySeconds() int32 {
	if x != nil {
		return x.ExpirySeconds
	}
	return 0
}

// Get file URL response
type GetFileUrlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileUrl       string                 `protobuf:"bytes,1,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFileUrlResponse) Reset() {
	*x = GetFileUrlResponse{}
	mi := &file_media_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileUrlResponse) ProtoMessage() {}

func (x *GetFileUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileUrlResponse.ProtoReflect.Descriptor instead.
func (*GetFileUrlResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{7}
}

func (x *GetFileUrlResponse) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *GetFileUrlResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *GetFileUrlResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Upload profile picture request
type UploadProfilePictureRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileName          string                 `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType          string                 `protobuf:"bytes,3,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileSize          int64                  `protobuf:"varint,4,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	ImageData         []byte                 `protobuf:"bytes,5,opt,name=image_data,json=imageData,proto3" json:"image_data,omitempty"`
	GenerateThumbnail bool                   `protobuf:"varint,6,opt,name=generate_thumbnail,json=generateThumbnail,proto3" json:"generate_thumbnail,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UploadProfilePictureRequest) Reset() {
	*x = UploadProfilePictureRequest{}
	mi := &file_media_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadProfilePictureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadProfilePictureRequest) ProtoMessage() {}

func (x *UploadProfilePictureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadProfilePictureRequest.ProtoReflect.Descriptor instead.
func (*UploadProfilePictureRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{8}
}

func (x *UploadProfilePictureRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UploadProfilePictureRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadProfilePictureRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *UploadProfilePictureRequest) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *UploadProfilePictureRequest) GetImageData() []byte {
	if x != nil {
		return x.ImageData
	}
	return nil
}

func (x *UploadProfilePictureRequest) GetGenerateThumbnail() bool {
	if x != nil {
		return x.GenerateThumbnail
	}
	return false
}

// Upload profile picture response
type UploadProfilePictureResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	FileInfo      *FileInfo              `protobuf:"bytes,2,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`
	ThumbnailUrl  string                 `protobuf:"bytes,3,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadProfilePictureResponse) Reset() {
	*x = UploadProfilePictureResponse{}
	mi := &file_media_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadProfilePictureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadProfilePictureResponse) ProtoMessage() {}

func (x *UploadProfilePictureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadProfilePictureResponse.ProtoReflect.Descriptor instead.
func (*UploadProfilePictureResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{9}
}

func (x *UploadProfilePictureResponse) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *UploadProfilePictureResponse) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

func (x *UploadProfilePictureResponse) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *UploadProfilePictureResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get user media request
type GetUserMediaRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	MediaType     string                 `protobuf:"bytes,2,opt,name=media_type,json=mediaType,proto3" json:"media_type,omitempty"` // image, video, audio, document
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserMediaRequest) Reset() {
	*x = GetUserMediaRequest{}
	mi := &file_media_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserMediaRequest) ProtoMessage() {}

func (x *GetUserMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserMediaRequest.ProtoReflect.Descriptor instead.
func (*GetUserMediaRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{10}
}

func (x *GetUserMediaRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserMediaRequest) GetMediaType() string {
	if x != nil {
		return x.MediaType
	}
	return ""
}

func (x *GetUserMediaRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUserMediaRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get user media response
type GetUserMediaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MediaFiles    []*FileInfo            `protobuf:"bytes,1,rep,name=media_files,json=mediaFiles,proto3" json:"media_files,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserMediaResponse) Reset() {
	*x = GetUserMediaResponse{}
	mi := &file_media_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserMediaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserMediaResponse) ProtoMessage() {}

func (x *GetUserMediaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserMediaResponse.ProtoReflect.Descriptor instead.
func (*GetUserMediaResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserMediaResponse) GetMediaFiles() []*FileInfo {
	if x != nil {
		return x.MediaFiles
	}
	return nil
}

func (x *GetUserMediaResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetUserMediaResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Generate thumbnail request
type GenerateThumbnailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	Width         int32                  `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height        int32                  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Format        string                 `protobuf:"bytes,4,opt,name=format,proto3" json:"format,omitempty"` // jpeg, png, webp
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateThumbnailRequest) Reset() {
	*x = GenerateThumbnailRequest{}
	mi := &file_media_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateThumbnailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateThumbnailRequest) ProtoMessage() {}

func (x *GenerateThumbnailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateThumbnailRequest.ProtoReflect.Descriptor instead.
func (*GenerateThumbnailRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{12}
}

func (x *GenerateThumbnailRequest) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *GenerateThumbnailRequest) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *GenerateThumbnailRequest) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *GenerateThumbnailRequest) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

// Generate thumbnail response
type GenerateThumbnailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThumbnailId   string                 `protobuf:"bytes,1,opt,name=thumbnail_id,json=thumbnailId,proto3" json:"thumbnail_id,omitempty"`
	ThumbnailUrl  string                 `protobuf:"bytes,2,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateThumbnailResponse) Reset() {
	*x = GenerateThumbnailResponse{}
	mi := &file_media_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateThumbnailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateThumbnailResponse) ProtoMessage() {}

func (x *GenerateThumbnailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateThumbnailResponse.ProtoReflect.Descriptor instead.
func (*GenerateThumbnailResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{13}
}

func (x *GenerateThumbnailResponse) GetThumbnailId() string {
	if x != nil {
		return x.ThumbnailId
	}
	return ""
}

func (x *GenerateThumbnailResponse) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *GenerateThumbnailResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get media stats request
type GetMediaStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Period        string                 `protobuf:"bytes,2,opt,name=period,proto3" json:"period,omitempty"` // daily, weekly, monthly
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMediaStatsRequest) Reset() {
	*x = GetMediaStatsRequest{}
	mi := &file_media_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMediaStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaStatsRequest) ProtoMessage() {}

func (x *GetMediaStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaStatsRequest.ProtoReflect.Descriptor instead.
func (*GetMediaStatsRequest) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{14}
}

func (x *GetMediaStatsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetMediaStatsRequest) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

// Get media stats response
type GetMediaStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *MediaStats            `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMediaStatsResponse) Reset() {
	*x = GetMediaStatsResponse{}
	mi := &file_media_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMediaStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaStatsResponse) ProtoMessage() {}

func (x *GetMediaStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaStatsResponse.ProtoReflect.Descriptor instead.
func (*GetMediaStatsResponse) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{15}
}

func (x *GetMediaStatsResponse) GetStats() *MediaStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetMediaStatsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// File chunk for streaming upload
type FileChunk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ChunkIndex    int32                  `protobuf:"varint,3,opt,name=chunk_index,json=chunkIndex,proto3" json:"chunk_index,omitempty"`
	TotalChunks   int32                  `protobuf:"varint,4,opt,name=total_chunks,json=totalChunks,proto3" json:"total_chunks,omitempty"`
	ChunkData     []byte                 `protobuf:"bytes,5,opt,name=chunk_data,json=chunkData,proto3" json:"chunk_data,omitempty"`
	IsLastChunk   bool                   `protobuf:"varint,6,opt,name=is_last_chunk,json=isLastChunk,proto3" json:"is_last_chunk,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,7,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileChunk) Reset() {
	*x = FileChunk{}
	mi := &file_media_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileChunk) ProtoMessage() {}

func (x *FileChunk) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileChunk.ProtoReflect.Descriptor instead.
func (*FileChunk) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{16}
}

func (x *FileChunk) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *FileChunk) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *FileChunk) GetChunkIndex() int32 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *FileChunk) GetTotalChunks() int32 {
	if x != nil {
		return x.TotalChunks
	}
	return 0
}

func (x *FileChunk) GetChunkData() []byte {
	if x != nil {
		return x.ChunkData
	}
	return nil
}

func (x *FileChunk) GetIsLastChunk() bool {
	if x != nil {
		return x.IsLastChunk
	}
	return false
}

func (x *FileChunk) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// File information
type FileInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileName      string                 `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType      string                 `protobuf:"bytes,4,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	MimeType      string                 `protobuf:"bytes,5,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	FileSize      int64                  `protobuf:"varint,6,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileUrl       string                 `protobuf:"bytes,7,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	ThumbnailUrl  string                 `protobuf:"bytes,8,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"`
	BubbleId      string                 `protobuf:"bytes,9,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	MessageId     string                 `protobuf:"bytes,10,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	Status        FileStatus             `protobuf:"varint,11,opt,name=status,proto3,enum=hopen.media.v1.FileStatus" json:"status,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,12,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Dimensions    *FileDimensions        `protobuf:"bytes,15,opt,name=dimensions,proto3" json:"dimensions,omitempty"`
	Duration      *FileDuration          `protobuf:"bytes,16,opt,name=duration,proto3" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	mi := &file_media_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{17}
}

func (x *FileInfo) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *FileInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *FileInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileInfo) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *FileInfo) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileInfo) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *FileInfo) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *FileInfo) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *FileInfo) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *FileInfo) GetStatus() FileStatus {
	if x != nil {
		return x.Status
	}
	return FileStatus_FILE_STATUS_UNSPECIFIED
}

func (x *FileInfo) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *FileInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FileInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *FileInfo) GetDimensions() *FileDimensions {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *FileInfo) GetDuration() *FileDuration {
	if x != nil {
		return x.Duration
	}
	return nil
}

// File dimensions for images/videos
type FileDimensions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Width         int32                  `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Height        int32                  `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	AspectRatio   string                 `protobuf:"bytes,3,opt,name=aspect_ratio,json=aspectRatio,proto3" json:"aspect_ratio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileDimensions) Reset() {
	*x = FileDimensions{}
	mi := &file_media_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDimensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDimensions) ProtoMessage() {}

func (x *FileDimensions) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDimensions.ProtoReflect.Descriptor instead.
func (*FileDimensions) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{18}
}

func (x *FileDimensions) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *FileDimensions) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *FileDimensions) GetAspectRatio() string {
	if x != nil {
		return x.AspectRatio
	}
	return ""
}

// File duration for videos/audio
type FileDuration struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	DurationSeconds   float32                `protobuf:"fixed32,1,opt,name=duration_seconds,json=durationSeconds,proto3" json:"duration_seconds,omitempty"`
	FormattedDuration string                 `protobuf:"bytes,2,opt,name=formatted_duration,json=formattedDuration,proto3" json:"formatted_duration,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *FileDuration) Reset() {
	*x = FileDuration{}
	mi := &file_media_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDuration) ProtoMessage() {}

func (x *FileDuration) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDuration.ProtoReflect.Descriptor instead.
func (*FileDuration) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{19}
}

func (x *FileDuration) GetDurationSeconds() float32 {
	if x != nil {
		return x.DurationSeconds
	}
	return 0
}

func (x *FileDuration) GetFormattedDuration() string {
	if x != nil {
		return x.FormattedDuration
	}
	return ""
}

// Media statistics
type MediaStats struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TotalFiles      int32                  `protobuf:"varint,1,opt,name=total_files,json=totalFiles,proto3" json:"total_files,omitempty"`
	TotalSizeBytes  int64                  `protobuf:"varint,2,opt,name=total_size_bytes,json=totalSizeBytes,proto3" json:"total_size_bytes,omitempty"`
	FilesByType     map[string]int32       `protobuf:"bytes,3,rep,name=files_by_type,json=filesByType,proto3" json:"files_by_type,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	SizeByType      map[string]int64       `protobuf:"bytes,4,rep,name=size_by_type,json=sizeByType,proto3" json:"size_by_type,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	RecentUploads   []string               `protobuf:"bytes,5,rep,name=recent_uploads,json=recentUploads,proto3" json:"recent_uploads,omitempty"`
	AverageFileSize float32                `protobuf:"fixed32,6,opt,name=average_file_size,json=averageFileSize,proto3" json:"average_file_size,omitempty"`
	LastUpload      *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_upload,json=lastUpload,proto3" json:"last_upload,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MediaStats) Reset() {
	*x = MediaStats{}
	mi := &file_media_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaStats) ProtoMessage() {}

func (x *MediaStats) ProtoReflect() protoreflect.Message {
	mi := &file_media_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaStats.ProtoReflect.Descriptor instead.
func (*MediaStats) Descriptor() ([]byte, []int) {
	return file_media_proto_rawDescGZIP(), []int{20}
}

func (x *MediaStats) GetTotalFiles() int32 {
	if x != nil {
		return x.TotalFiles
	}
	return 0
}

func (x *MediaStats) GetTotalSizeBytes() int64 {
	if x != nil {
		return x.TotalSizeBytes
	}
	return 0
}

func (x *MediaStats) GetFilesByType() map[string]int32 {
	if x != nil {
		return x.FilesByType
	}
	return nil
}

func (x *MediaStats) GetSizeByType() map[string]int64 {
	if x != nil {
		return x.SizeByType
	}
	return nil
}

func (x *MediaStats) GetRecentUploads() []string {
	if x != nil {
		return x.RecentUploads
	}
	return nil
}

func (x *MediaStats) GetAverageFileSize() float32 {
	if x != nil {
		return x.AverageFileSize
	}
	return 0
}

func (x *MediaStats) GetLastUpload() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpload
	}
	return nil
}

var File_media_proto protoreflect.FileDescriptor

const file_media_proto_rawDesc = "" +
	"\n" +
	"\vmedia.proto\x12\x0ehopen.media.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"\xe6\x02\n" +
	"\x11UploadFileRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfile_name\x18\x02 \x01(\tR\bfileName\x12\x1b\n" +
	"\tfile_type\x18\x03 \x01(\tR\bfileType\x12\x1b\n" +
	"\tfile_size\x18\x04 \x01(\x03R\bfileSize\x12\x1b\n" +
	"\tbubble_id\x18\x05 \x01(\tR\bbubbleId\x12\x1d\n" +
	"\n" +
	"message_id\x18\x06 \x01(\tR\tmessageId\x12\x1b\n" +
	"\tfile_data\x18\a \x01(\fR\bfileData\x12K\n" +
	"\bmetadata\x18\b \x03(\v2/.hopen.media.v1.UploadFileRequest.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xc4\x01\n" +
	"\x12UploadFileResponse\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\tR\x06fileId\x125\n" +
	"\tfile_info\x18\x02 \x01(\v2\x18.hopen.media.v1.FileInfoR\bfileInfo\x12\x1d\n" +
	"\n" +
	"upload_url\x18\x03 \x01(\tR\tuploadUrl\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"-\n" +
	"\x12GetFileInfoRequest\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\tR\x06fileId\"\x8d\x01\n" +
	"\x13GetFileInfoResponse\x125\n" +
	"\tfile_info\x18\x01 \x01(\v2\x18.hopen.media.v1.FileInfoR\bfileInfo\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"E\n" +
	"\x11DeleteFileRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x17\n" +
	"\afile_id\x18\x02 \x01(\tR\x06fileId\"o\n" +
	"\x12DeleteFileResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"t\n" +
	"\x11GetFileUrlRequest\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\tR\x06fileId\x12\x1f\n" +
	"\vaccess_type\x18\x02 \x01(\tR\n" +
	"accessType\x12%\n" +
	"\x0eexpiry_seconds\x18\x03 \x01(\x05R\rexpirySeconds\"\xab\x01\n" +
	"\x12GetFileUrlResponse\x12\x19\n" +
	"\bfile_url\x18\x01 \x01(\tR\afileUrl\x129\n" +
	"\n" +
	"expires_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xdb\x01\n" +
	"\x1bUploadProfilePictureRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfile_name\x18\x02 \x01(\tR\bfileName\x12\x1b\n" +
	"\tfile_type\x18\x03 \x01(\tR\bfileType\x12\x1b\n" +
	"\tfile_size\x18\x04 \x01(\x03R\bfileSize\x12\x1d\n" +
	"\n" +
	"image_data\x18\x05 \x01(\fR\timageData\x12-\n" +
	"\x12generate_thumbnail\x18\x06 \x01(\bR\x11generateThumbnail\"\xd4\x01\n" +
	"\x1cUploadProfilePictureResponse\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\tR\x06fileId\x125\n" +
	"\tfile_info\x18\x02 \x01(\v2\x18.hopen.media.v1.FileInfoR\bfileInfo\x12#\n" +
	"\rthumbnail_url\x18\x03 \x01(\tR\fthumbnailUrl\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"~\n" +
	"\x13GetUserMediaRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"media_type\x18\x02 \x01(\tR\tmediaType\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xcf\x01\n" +
	"\x14GetUserMediaResponse\x129\n" +
	"\vmedia_files\x18\x01 \x03(\v2\x18.hopen.media.v1.FileInfoR\n" +
	"mediaFiles\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"y\n" +
	"\x18GenerateThumbnailRequest\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\tR\x06fileId\x12\x14\n" +
	"\x05width\x18\x02 \x01(\x05R\x05width\x12\x16\n" +
	"\x06height\x18\x03 \x01(\x05R\x06height\x12\x16\n" +
	"\x06format\x18\x04 \x01(\tR\x06format\"\xa4\x01\n" +
	"\x19GenerateThumbnailResponse\x12!\n" +
	"\fthumbnail_id\x18\x01 \x01(\tR\vthumbnailId\x12#\n" +
	"\rthumbnail_url\x18\x02 \x01(\tR\fthumbnailUrl\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"G\n" +
	"\x14GetMediaStatsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06period\x18\x02 \x01(\tR\x06period\"\x8a\x01\n" +
	"\x15GetMediaStatsResponse\x120\n" +
	"\x05stats\x18\x01 \x01(\v2\x1a.hopen.media.v1.MediaStatsR\x05stats\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xc6\x02\n" +
	"\tFileChunk\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\tR\x06fileId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1f\n" +
	"\vchunk_index\x18\x03 \x01(\x05R\n" +
	"chunkIndex\x12!\n" +
	"\ftotal_chunks\x18\x04 \x01(\x05R\vtotalChunks\x12\x1d\n" +
	"\n" +
	"chunk_data\x18\x05 \x01(\fR\tchunkData\x12\"\n" +
	"\ris_last_chunk\x18\x06 \x01(\bR\visLastChunk\x12C\n" +
	"\bmetadata\x18\a \x03(\v2'.hopen.media.v1.FileChunk.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xd1\x05\n" +
	"\bFileInfo\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\tR\x06fileId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfile_name\x18\x03 \x01(\tR\bfileName\x12\x1b\n" +
	"\tfile_type\x18\x04 \x01(\tR\bfileType\x12\x1b\n" +
	"\tmime_type\x18\x05 \x01(\tR\bmimeType\x12\x1b\n" +
	"\tfile_size\x18\x06 \x01(\x03R\bfileSize\x12\x19\n" +
	"\bfile_url\x18\a \x01(\tR\afileUrl\x12#\n" +
	"\rthumbnail_url\x18\b \x01(\tR\fthumbnailUrl\x12\x1b\n" +
	"\tbubble_id\x18\t \x01(\tR\bbubbleId\x12\x1d\n" +
	"\n" +
	"message_id\x18\n" +
	" \x01(\tR\tmessageId\x122\n" +
	"\x06status\x18\v \x01(\x0e2\x1a.hopen.media.v1.FileStatusR\x06status\x12B\n" +
	"\bmetadata\x18\f \x03(\v2&.hopen.media.v1.FileInfo.MetadataEntryR\bmetadata\x129\n" +
	"\n" +
	"created_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12>\n" +
	"\n" +
	"dimensions\x18\x0f \x01(\v2\x1e.hopen.media.v1.FileDimensionsR\n" +
	"dimensions\x128\n" +
	"\bduration\x18\x10 \x01(\v2\x1c.hopen.media.v1.FileDurationR\bduration\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"a\n" +
	"\x0eFileDimensions\x12\x14\n" +
	"\x05width\x18\x01 \x01(\x05R\x05width\x12\x16\n" +
	"\x06height\x18\x02 \x01(\x05R\x06height\x12!\n" +
	"\faspect_ratio\x18\x03 \x01(\tR\vaspectRatio\"h\n" +
	"\fFileDuration\x12)\n" +
	"\x10duration_seconds\x18\x01 \x01(\x02R\x0fdurationSeconds\x12-\n" +
	"\x12formatted_duration\x18\x02 \x01(\tR\x11formattedDuration\"\x85\x04\n" +
	"\n" +
	"MediaStats\x12\x1f\n" +
	"\vtotal_files\x18\x01 \x01(\x05R\n" +
	"totalFiles\x12(\n" +
	"\x10total_size_bytes\x18\x02 \x01(\x03R\x0etotalSizeBytes\x12O\n" +
	"\rfiles_by_type\x18\x03 \x03(\v2+.hopen.media.v1.MediaStats.FilesByTypeEntryR\vfilesByType\x12L\n" +
	"\fsize_by_type\x18\x04 \x03(\v2*.hopen.media.v1.MediaStats.SizeByTypeEntryR\n" +
	"sizeByType\x12%\n" +
	"\x0erecent_uploads\x18\x05 \x03(\tR\rrecentUploads\x12*\n" +
	"\x11average_file_size\x18\x06 \x01(\x02R\x0faverageFileSize\x12;\n" +
	"\vlast_upload\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastUpload\x1a>\n" +
	"\x10FilesByTypeEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a=\n" +
	"\x0fSizeByTypeEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x03R\x05value:\x028\x01*\xa8\x01\n" +
	"\n" +
	"FileStatus\x12\x1b\n" +
	"\x17FILE_STATUS_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15FILE_STATUS_UPLOADING\x10\x01\x12\x1a\n" +
	"\x16FILE_STATUS_PROCESSING\x10\x02\x12\x15\n" +
	"\x11FILE_STATUS_READY\x10\x03\x12\x16\n" +
	"\x12FILE_STATUS_FAILED\x10\x04\x12\x17\n" +
	"\x13FILE_STATUS_DELETED\x10\x052\xd0\x06\n" +
	"\fMediaService\x12S\n" +
	"\n" +
	"UploadFile\x12!.hopen.media.v1.UploadFileRequest\x1a\".hopen.media.v1.UploadFileResponse\x12V\n" +
	"\vGetFileInfo\x12\".hopen.media.v1.GetFileInfoRequest\x1a#.hopen.media.v1.GetFileInfoResponse\x12S\n" +
	"\n" +
	"DeleteFile\x12!.hopen.media.v1.DeleteFileRequest\x1a\".hopen.media.v1.DeleteFileResponse\x12S\n" +
	"\n" +
	"GetFileUrl\x12!.hopen.media.v1.GetFileUrlRequest\x1a\".hopen.media.v1.GetFileUrlResponse\x12q\n" +
	"\x14UploadProfilePicture\x12+.hopen.media.v1.UploadProfilePictureRequest\x1a,.hopen.media.v1.UploadProfilePictureResponse\x12Y\n" +
	"\fGetUserMedia\x12#.hopen.media.v1.GetUserMediaRequest\x1a$.hopen.media.v1.GetUserMediaResponse\x12h\n" +
	"\x11GenerateThumbnail\x12(.hopen.media.v1.GenerateThumbnailRequest\x1a).hopen.media.v1.GenerateThumbnailResponse\x12\\\n" +
	"\rGetMediaStats\x12$.hopen.media.v1.GetMediaStatsRequest\x1a%.hopen.media.v1.GetMediaStatsResponse\x12S\n" +
	"\x10StreamFileUpload\x12\x19.hopen.media.v1.FileChunk\x1a\".hopen.media.v1.UploadFileResponse(\x01B;\n" +
	"\x12com.hopen.media.v1P\x01Z#hopenbackend/protos/gen/media;mediab\x06proto3"

var (
	file_media_proto_rawDescOnce sync.Once
	file_media_proto_rawDescData []byte
)

func file_media_proto_rawDescGZIP() []byte {
	file_media_proto_rawDescOnce.Do(func() {
		file_media_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_media_proto_rawDesc), len(file_media_proto_rawDesc)))
	})
	return file_media_proto_rawDescData
}

var file_media_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_media_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_media_proto_goTypes = []any{
	(FileStatus)(0),                      // 0: hopen.media.v1.FileStatus
	(*UploadFileRequest)(nil),            // 1: hopen.media.v1.UploadFileRequest
	(*UploadFileResponse)(nil),           // 2: hopen.media.v1.UploadFileResponse
	(*GetFileInfoRequest)(nil),           // 3: hopen.media.v1.GetFileInfoRequest
	(*GetFileInfoResponse)(nil),          // 4: hopen.media.v1.GetFileInfoResponse
	(*DeleteFileRequest)(nil),            // 5: hopen.media.v1.DeleteFileRequest
	(*DeleteFileResponse)(nil),           // 6: hopen.media.v1.DeleteFileResponse
	(*GetFileUrlRequest)(nil),            // 7: hopen.media.v1.GetFileUrlRequest
	(*GetFileUrlResponse)(nil),           // 8: hopen.media.v1.GetFileUrlResponse
	(*UploadProfilePictureRequest)(nil),  // 9: hopen.media.v1.UploadProfilePictureRequest
	(*UploadProfilePictureResponse)(nil), // 10: hopen.media.v1.UploadProfilePictureResponse
	(*GetUserMediaRequest)(nil),          // 11: hopen.media.v1.GetUserMediaRequest
	(*GetUserMediaResponse)(nil),         // 12: hopen.media.v1.GetUserMediaResponse
	(*GenerateThumbnailRequest)(nil),     // 13: hopen.media.v1.GenerateThumbnailRequest
	(*GenerateThumbnailResponse)(nil),    // 14: hopen.media.v1.GenerateThumbnailResponse
	(*GetMediaStatsRequest)(nil),         // 15: hopen.media.v1.GetMediaStatsRequest
	(*GetMediaStatsResponse)(nil),        // 16: hopen.media.v1.GetMediaStatsResponse
	(*FileChunk)(nil),                    // 17: hopen.media.v1.FileChunk
	(*FileInfo)(nil),                     // 18: hopen.media.v1.FileInfo
	(*FileDimensions)(nil),               // 19: hopen.media.v1.FileDimensions
	(*FileDuration)(nil),                 // 20: hopen.media.v1.FileDuration
	(*MediaStats)(nil),                   // 21: hopen.media.v1.MediaStats
	nil,                                  // 22: hopen.media.v1.UploadFileRequest.MetadataEntry
	nil,                                  // 23: hopen.media.v1.FileChunk.MetadataEntry
	nil,                                  // 24: hopen.media.v1.FileInfo.MetadataEntry
	nil,                                  // 25: hopen.media.v1.MediaStats.FilesByTypeEntry
	nil,                                  // 26: hopen.media.v1.MediaStats.SizeByTypeEntry
	(*common.ApiResponse)(nil),           // 27: hopen.common.v1.ApiResponse
	(*timestamppb.Timestamp)(nil),        // 28: google.protobuf.Timestamp
	(*common.Pagination)(nil),            // 29: hopen.common.v1.Pagination
}
var file_media_proto_depIdxs = []int32{
	22, // 0: hopen.media.v1.UploadFileRequest.metadata:type_name -> hopen.media.v1.UploadFileRequest.MetadataEntry
	18, // 1: hopen.media.v1.UploadFileResponse.file_info:type_name -> hopen.media.v1.FileInfo
	27, // 2: hopen.media.v1.UploadFileResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	18, // 3: hopen.media.v1.GetFileInfoResponse.file_info:type_name -> hopen.media.v1.FileInfo
	27, // 4: hopen.media.v1.GetFileInfoResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 5: hopen.media.v1.DeleteFileResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	28, // 6: hopen.media.v1.GetFileUrlResponse.expires_at:type_name -> google.protobuf.Timestamp
	27, // 7: hopen.media.v1.GetFileUrlResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	18, // 8: hopen.media.v1.UploadProfilePictureResponse.file_info:type_name -> hopen.media.v1.FileInfo
	27, // 9: hopen.media.v1.UploadProfilePictureResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	18, // 10: hopen.media.v1.GetUserMediaResponse.media_files:type_name -> hopen.media.v1.FileInfo
	29, // 11: hopen.media.v1.GetUserMediaResponse.pagination:type_name -> hopen.common.v1.Pagination
	27, // 12: hopen.media.v1.GetUserMediaResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 13: hopen.media.v1.GenerateThumbnailResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	21, // 14: hopen.media.v1.GetMediaStatsResponse.stats:type_name -> hopen.media.v1.MediaStats
	27, // 15: hopen.media.v1.GetMediaStatsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	23, // 16: hopen.media.v1.FileChunk.metadata:type_name -> hopen.media.v1.FileChunk.MetadataEntry
	0,  // 17: hopen.media.v1.FileInfo.status:type_name -> hopen.media.v1.FileStatus
	24, // 18: hopen.media.v1.FileInfo.metadata:type_name -> hopen.media.v1.FileInfo.MetadataEntry
	28, // 19: hopen.media.v1.FileInfo.created_at:type_name -> google.protobuf.Timestamp
	28, // 20: hopen.media.v1.FileInfo.updated_at:type_name -> google.protobuf.Timestamp
	19, // 21: hopen.media.v1.FileInfo.dimensions:type_name -> hopen.media.v1.FileDimensions
	20, // 22: hopen.media.v1.FileInfo.duration:type_name -> hopen.media.v1.FileDuration
	25, // 23: hopen.media.v1.MediaStats.files_by_type:type_name -> hopen.media.v1.MediaStats.FilesByTypeEntry
	26, // 24: hopen.media.v1.MediaStats.size_by_type:type_name -> hopen.media.v1.MediaStats.SizeByTypeEntry
	28, // 25: hopen.media.v1.MediaStats.last_upload:type_name -> google.protobuf.Timestamp
	1,  // 26: hopen.media.v1.MediaService.UploadFile:input_type -> hopen.media.v1.UploadFileRequest
	3,  // 27: hopen.media.v1.MediaService.GetFileInfo:input_type -> hopen.media.v1.GetFileInfoRequest
	5,  // 28: hopen.media.v1.MediaService.DeleteFile:input_type -> hopen.media.v1.DeleteFileRequest
	7,  // 29: hopen.media.v1.MediaService.GetFileUrl:input_type -> hopen.media.v1.GetFileUrlRequest
	9,  // 30: hopen.media.v1.MediaService.UploadProfilePicture:input_type -> hopen.media.v1.UploadProfilePictureRequest
	11, // 31: hopen.media.v1.MediaService.GetUserMedia:input_type -> hopen.media.v1.GetUserMediaRequest
	13, // 32: hopen.media.v1.MediaService.GenerateThumbnail:input_type -> hopen.media.v1.GenerateThumbnailRequest
	15, // 33: hopen.media.v1.MediaService.GetMediaStats:input_type -> hopen.media.v1.GetMediaStatsRequest
	17, // 34: hopen.media.v1.MediaService.StreamFileUpload:input_type -> hopen.media.v1.FileChunk
	2,  // 35: hopen.media.v1.MediaService.UploadFile:output_type -> hopen.media.v1.UploadFileResponse
	4,  // 36: hopen.media.v1.MediaService.GetFileInfo:output_type -> hopen.media.v1.GetFileInfoResponse
	6,  // 37: hopen.media.v1.MediaService.DeleteFile:output_type -> hopen.media.v1.DeleteFileResponse
	8,  // 38: hopen.media.v1.MediaService.GetFileUrl:output_type -> hopen.media.v1.GetFileUrlResponse
	10, // 39: hopen.media.v1.MediaService.UploadProfilePicture:output_type -> hopen.media.v1.UploadProfilePictureResponse
	12, // 40: hopen.media.v1.MediaService.GetUserMedia:output_type -> hopen.media.v1.GetUserMediaResponse
	14, // 41: hopen.media.v1.MediaService.GenerateThumbnail:output_type -> hopen.media.v1.GenerateThumbnailResponse
	16, // 42: hopen.media.v1.MediaService.GetMediaStats:output_type -> hopen.media.v1.GetMediaStatsResponse
	2,  // 43: hopen.media.v1.MediaService.StreamFileUpload:output_type -> hopen.media.v1.UploadFileResponse
	35, // [35:44] is the sub-list for method output_type
	26, // [26:35] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_media_proto_init() }
func file_media_proto_init() {
	if File_media_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_media_proto_rawDesc), len(file_media_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_media_proto_goTypes,
		DependencyIndexes: file_media_proto_depIdxs,
		EnumInfos:         file_media_proto_enumTypes,
		MessageInfos:      file_media_proto_msgTypes,
	}.Build()
	File_media_proto = out.File
	file_media_proto_goTypes = nil
	file_media_proto_depIdxs = nil
}
