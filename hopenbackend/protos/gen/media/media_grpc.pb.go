// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: media.proto

package media

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MediaService_UploadFile_FullMethodName           = "/hopen.media.v1.MediaService/UploadFile"
	MediaService_GetFileInfo_FullMethodName          = "/hopen.media.v1.MediaService/GetFileInfo"
	MediaService_DeleteFile_FullMethodName           = "/hopen.media.v1.MediaService/DeleteFile"
	MediaService_GetFileUrl_FullMethodName           = "/hopen.media.v1.MediaService/GetFileUrl"
	MediaService_UploadProfilePicture_FullMethodName = "/hopen.media.v1.MediaService/UploadProfilePicture"
	MediaService_GetUserMedia_FullMethodName         = "/hopen.media.v1.MediaService/GetUserMedia"
	MediaService_GenerateThumbnail_FullMethodName    = "/hopen.media.v1.MediaService/GenerateThumbnail"
	MediaService_GetMediaStats_FullMethodName        = "/hopen.media.v1.MediaService/GetMediaStats"
	MediaService_StreamFileUpload_FullMethodName     = "/hopen.media.v1.MediaService/StreamFileUpload"
)

// MediaServiceClient is the client API for MediaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Media service for file upload and media management
type MediaServiceClient interface {
	// Upload file
	UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error)
	// Get file information
	GetFileInfo(ctx context.Context, in *GetFileInfoRequest, opts ...grpc.CallOption) (*GetFileInfoResponse, error)
	// Delete file
	DeleteFile(ctx context.Context, in *DeleteFileRequest, opts ...grpc.CallOption) (*DeleteFileResponse, error)
	// Get file URL
	GetFileUrl(ctx context.Context, in *GetFileUrlRequest, opts ...grpc.CallOption) (*GetFileUrlResponse, error)
	// Upload profile picture
	UploadProfilePicture(ctx context.Context, in *UploadProfilePictureRequest, opts ...grpc.CallOption) (*UploadProfilePictureResponse, error)
	// Get user media
	GetUserMedia(ctx context.Context, in *GetUserMediaRequest, opts ...grpc.CallOption) (*GetUserMediaResponse, error)
	// Generate thumbnail
	GenerateThumbnail(ctx context.Context, in *GenerateThumbnailRequest, opts ...grpc.CallOption) (*GenerateThumbnailResponse, error)
	// Get media statistics
	GetMediaStats(ctx context.Context, in *GetMediaStatsRequest, opts ...grpc.CallOption) (*GetMediaStatsResponse, error)
	// Stream file upload
	StreamFileUpload(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[FileChunk, UploadFileResponse], error)
}

type mediaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMediaServiceClient(cc grpc.ClientConnInterface) MediaServiceClient {
	return &mediaServiceClient{cc}
}

func (c *mediaServiceClient) UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadFileResponse)
	err := c.cc.Invoke(ctx, MediaService_UploadFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) GetFileInfo(ctx context.Context, in *GetFileInfoRequest, opts ...grpc.CallOption) (*GetFileInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFileInfoResponse)
	err := c.cc.Invoke(ctx, MediaService_GetFileInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) DeleteFile(ctx context.Context, in *DeleteFileRequest, opts ...grpc.CallOption) (*DeleteFileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteFileResponse)
	err := c.cc.Invoke(ctx, MediaService_DeleteFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) GetFileUrl(ctx context.Context, in *GetFileUrlRequest, opts ...grpc.CallOption) (*GetFileUrlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFileUrlResponse)
	err := c.cc.Invoke(ctx, MediaService_GetFileUrl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) UploadProfilePicture(ctx context.Context, in *UploadProfilePictureRequest, opts ...grpc.CallOption) (*UploadProfilePictureResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadProfilePictureResponse)
	err := c.cc.Invoke(ctx, MediaService_UploadProfilePicture_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) GetUserMedia(ctx context.Context, in *GetUserMediaRequest, opts ...grpc.CallOption) (*GetUserMediaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserMediaResponse)
	err := c.cc.Invoke(ctx, MediaService_GetUserMedia_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) GenerateThumbnail(ctx context.Context, in *GenerateThumbnailRequest, opts ...grpc.CallOption) (*GenerateThumbnailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateThumbnailResponse)
	err := c.cc.Invoke(ctx, MediaService_GenerateThumbnail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) GetMediaStats(ctx context.Context, in *GetMediaStatsRequest, opts ...grpc.CallOption) (*GetMediaStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMediaStatsResponse)
	err := c.cc.Invoke(ctx, MediaService_GetMediaStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mediaServiceClient) StreamFileUpload(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[FileChunk, UploadFileResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &MediaService_ServiceDesc.Streams[0], MediaService_StreamFileUpload_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[FileChunk, UploadFileResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type MediaService_StreamFileUploadClient = grpc.ClientStreamingClient[FileChunk, UploadFileResponse]

// MediaServiceServer is the server API for MediaService service.
// All implementations must embed UnimplementedMediaServiceServer
// for forward compatibility.
//
// Media service for file upload and media management
type MediaServiceServer interface {
	// Upload file
	UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error)
	// Get file information
	GetFileInfo(context.Context, *GetFileInfoRequest) (*GetFileInfoResponse, error)
	// Delete file
	DeleteFile(context.Context, *DeleteFileRequest) (*DeleteFileResponse, error)
	// Get file URL
	GetFileUrl(context.Context, *GetFileUrlRequest) (*GetFileUrlResponse, error)
	// Upload profile picture
	UploadProfilePicture(context.Context, *UploadProfilePictureRequest) (*UploadProfilePictureResponse, error)
	// Get user media
	GetUserMedia(context.Context, *GetUserMediaRequest) (*GetUserMediaResponse, error)
	// Generate thumbnail
	GenerateThumbnail(context.Context, *GenerateThumbnailRequest) (*GenerateThumbnailResponse, error)
	// Get media statistics
	GetMediaStats(context.Context, *GetMediaStatsRequest) (*GetMediaStatsResponse, error)
	// Stream file upload
	StreamFileUpload(grpc.ClientStreamingServer[FileChunk, UploadFileResponse]) error
	mustEmbedUnimplementedMediaServiceServer()
}

// UnimplementedMediaServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMediaServiceServer struct{}

func (UnimplementedMediaServiceServer) UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedMediaServiceServer) GetFileInfo(context.Context, *GetFileInfoRequest) (*GetFileInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileInfo not implemented")
}
func (UnimplementedMediaServiceServer) DeleteFile(context.Context, *DeleteFileRequest) (*DeleteFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFile not implemented")
}
func (UnimplementedMediaServiceServer) GetFileUrl(context.Context, *GetFileUrlRequest) (*GetFileUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileUrl not implemented")
}
func (UnimplementedMediaServiceServer) UploadProfilePicture(context.Context, *UploadProfilePictureRequest) (*UploadProfilePictureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadProfilePicture not implemented")
}
func (UnimplementedMediaServiceServer) GetUserMedia(context.Context, *GetUserMediaRequest) (*GetUserMediaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserMedia not implemented")
}
func (UnimplementedMediaServiceServer) GenerateThumbnail(context.Context, *GenerateThumbnailRequest) (*GenerateThumbnailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateThumbnail not implemented")
}
func (UnimplementedMediaServiceServer) GetMediaStats(context.Context, *GetMediaStatsRequest) (*GetMediaStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMediaStats not implemented")
}
func (UnimplementedMediaServiceServer) StreamFileUpload(grpc.ClientStreamingServer[FileChunk, UploadFileResponse]) error {
	return status.Errorf(codes.Unimplemented, "method StreamFileUpload not implemented")
}
func (UnimplementedMediaServiceServer) mustEmbedUnimplementedMediaServiceServer() {}
func (UnimplementedMediaServiceServer) testEmbeddedByValue()                      {}

// UnsafeMediaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MediaServiceServer will
// result in compilation errors.
type UnsafeMediaServiceServer interface {
	mustEmbedUnimplementedMediaServiceServer()
}

func RegisterMediaServiceServer(s grpc.ServiceRegistrar, srv MediaServiceServer) {
	// If the following call pancis, it indicates UnimplementedMediaServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MediaService_ServiceDesc, srv)
}

func _MediaService_UploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).UploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_UploadFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).UploadFile(ctx, req.(*UploadFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_GetFileInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).GetFileInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_GetFileInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).GetFileInfo(ctx, req.(*GetFileInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_DeleteFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).DeleteFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_DeleteFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).DeleteFile(ctx, req.(*DeleteFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_GetFileUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).GetFileUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_GetFileUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).GetFileUrl(ctx, req.(*GetFileUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_UploadProfilePicture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadProfilePictureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).UploadProfilePicture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_UploadProfilePicture_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).UploadProfilePicture(ctx, req.(*UploadProfilePictureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_GetUserMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).GetUserMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_GetUserMedia_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).GetUserMedia(ctx, req.(*GetUserMediaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_GenerateThumbnail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateThumbnailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).GenerateThumbnail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_GenerateThumbnail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).GenerateThumbnail(ctx, req.(*GenerateThumbnailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_GetMediaStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMediaStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MediaServiceServer).GetMediaStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MediaService_GetMediaStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MediaServiceServer).GetMediaStats(ctx, req.(*GetMediaStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MediaService_StreamFileUpload_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(MediaServiceServer).StreamFileUpload(&grpc.GenericServerStream[FileChunk, UploadFileResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type MediaService_StreamFileUploadServer = grpc.ClientStreamingServer[FileChunk, UploadFileResponse]

// MediaService_ServiceDesc is the grpc.ServiceDesc for MediaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MediaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.media.v1.MediaService",
	HandlerType: (*MediaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadFile",
			Handler:    _MediaService_UploadFile_Handler,
		},
		{
			MethodName: "GetFileInfo",
			Handler:    _MediaService_GetFileInfo_Handler,
		},
		{
			MethodName: "DeleteFile",
			Handler:    _MediaService_DeleteFile_Handler,
		},
		{
			MethodName: "GetFileUrl",
			Handler:    _MediaService_GetFileUrl_Handler,
		},
		{
			MethodName: "UploadProfilePicture",
			Handler:    _MediaService_UploadProfilePicture_Handler,
		},
		{
			MethodName: "GetUserMedia",
			Handler:    _MediaService_GetUserMedia_Handler,
		},
		{
			MethodName: "GenerateThumbnail",
			Handler:    _MediaService_GenerateThumbnail_Handler,
		},
		{
			MethodName: "GetMediaStats",
			Handler:    _MediaService_GetMediaStats_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamFileUpload",
			Handler:       _MediaService_StreamFileUpload_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "media.proto",
}
