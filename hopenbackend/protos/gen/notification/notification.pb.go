// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: notification.proto

package notification

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Register FCM token request
type RegisterFCMTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FcmToken      string                 `protobuf:"bytes,2,opt,name=fcm_token,json=fcmToken,proto3" json:"fcm_token,omitempty"`
	DeviceId      string                 `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform      string                 `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`
	AppVersion    string                 `protobuf:"bytes,5,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterFCMTokenRequest) Reset() {
	*x = RegisterFCMTokenRequest{}
	mi := &file_notification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterFCMTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterFCMTokenRequest) ProtoMessage() {}

func (x *RegisterFCMTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterFCMTokenRequest.ProtoReflect.Descriptor instead.
func (*RegisterFCMTokenRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterFCMTokenRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RegisterFCMTokenRequest) GetFcmToken() string {
	if x != nil {
		return x.FcmToken
	}
	return ""
}

func (x *RegisterFCMTokenRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *RegisterFCMTokenRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *RegisterFCMTokenRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

// Register FCM token response
type RegisterFCMTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterFCMTokenResponse) Reset() {
	*x = RegisterFCMTokenResponse{}
	mi := &file_notification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterFCMTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterFCMTokenResponse) ProtoMessage() {}

func (x *RegisterFCMTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterFCMTokenResponse.ProtoReflect.Descriptor instead.
func (*RegisterFCMTokenResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterFCMTokenResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Unregister FCM token request
type UnregisterFCMTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FcmToken      string                 `protobuf:"bytes,2,opt,name=fcm_token,json=fcmToken,proto3" json:"fcm_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnregisterFCMTokenRequest) Reset() {
	*x = UnregisterFCMTokenRequest{}
	mi := &file_notification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnregisterFCMTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnregisterFCMTokenRequest) ProtoMessage() {}

func (x *UnregisterFCMTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnregisterFCMTokenRequest.ProtoReflect.Descriptor instead.
func (*UnregisterFCMTokenRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{2}
}

func (x *UnregisterFCMTokenRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UnregisterFCMTokenRequest) GetFcmToken() string {
	if x != nil {
		return x.FcmToken
	}
	return ""
}

// Unregister FCM token response
type UnregisterFCMTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnregisterFCMTokenResponse) Reset() {
	*x = UnregisterFCMTokenResponse{}
	mi := &file_notification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnregisterFCMTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnregisterFCMTokenResponse) ProtoMessage() {}

func (x *UnregisterFCMTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnregisterFCMTokenResponse.ProtoReflect.Descriptor instead.
func (*UnregisterFCMTokenResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{3}
}

func (x *UnregisterFCMTokenResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Send push notification request
type SendPushNotificationRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	UserId        string                  `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Title         string                  `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Body          string                  `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	Data          map[string]string       `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Type          common.NotificationType `protobuf:"varint,5,opt,name=type,proto3,enum=hopen.common.v1.NotificationType" json:"type,omitempty"`
	ImageUrl      string                  `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Sound         string                  `protobuf:"bytes,7,opt,name=sound,proto3" json:"sound,omitempty"`
	Badge         int32                   `protobuf:"varint,8,opt,name=badge,proto3" json:"badge,omitempty"`
	PriorityHigh  bool                    `protobuf:"varint,9,opt,name=priority_high,json=priorityHigh,proto3" json:"priority_high,omitempty"`
	TtlSeconds    int32                   `protobuf:"varint,10,opt,name=ttl_seconds,json=ttlSeconds,proto3" json:"ttl_seconds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPushNotificationRequest) Reset() {
	*x = SendPushNotificationRequest{}
	mi := &file_notification_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPushNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationRequest) ProtoMessage() {}

func (x *SendPushNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationRequest.ProtoReflect.Descriptor instead.
func (*SendPushNotificationRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{4}
}

func (x *SendPushNotificationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SendPushNotificationRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SendPushNotificationRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *SendPushNotificationRequest) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SendPushNotificationRequest) GetType() common.NotificationType {
	if x != nil {
		return x.Type
	}
	return common.NotificationType(0)
}

func (x *SendPushNotificationRequest) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *SendPushNotificationRequest) GetSound() string {
	if x != nil {
		return x.Sound
	}
	return ""
}

func (x *SendPushNotificationRequest) GetBadge() int32 {
	if x != nil {
		return x.Badge
	}
	return 0
}

func (x *SendPushNotificationRequest) GetPriorityHigh() bool {
	if x != nil {
		return x.PriorityHigh
	}
	return false
}

func (x *SendPushNotificationRequest) GetTtlSeconds() int32 {
	if x != nil {
		return x.TtlSeconds
	}
	return 0
}

// Send push notification response
type SendPushNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageId     string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPushNotificationResponse) Reset() {
	*x = SendPushNotificationResponse{}
	mi := &file_notification_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPushNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationResponse) ProtoMessage() {}

func (x *SendPushNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationResponse.ProtoReflect.Descriptor instead.
func (*SendPushNotificationResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{5}
}

func (x *SendPushNotificationResponse) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *SendPushNotificationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SendPushNotificationResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *SendPushNotificationResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Send push notification to topic request
type SendPushNotificationToTopicRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Topic         string                  `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	Title         string                  `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Body          string                  `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	Data          map[string]string       `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Type          common.NotificationType `protobuf:"varint,5,opt,name=type,proto3,enum=hopen.common.v1.NotificationType" json:"type,omitempty"`
	ImageUrl      string                  `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Sound         string                  `protobuf:"bytes,7,opt,name=sound,proto3" json:"sound,omitempty"`
	Badge         int32                   `protobuf:"varint,8,opt,name=badge,proto3" json:"badge,omitempty"`
	PriorityHigh  bool                    `protobuf:"varint,9,opt,name=priority_high,json=priorityHigh,proto3" json:"priority_high,omitempty"`
	TtlSeconds    int32                   `protobuf:"varint,10,opt,name=ttl_seconds,json=ttlSeconds,proto3" json:"ttl_seconds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPushNotificationToTopicRequest) Reset() {
	*x = SendPushNotificationToTopicRequest{}
	mi := &file_notification_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPushNotificationToTopicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationToTopicRequest) ProtoMessage() {}

func (x *SendPushNotificationToTopicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationToTopicRequest.ProtoReflect.Descriptor instead.
func (*SendPushNotificationToTopicRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{6}
}

func (x *SendPushNotificationToTopicRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *SendPushNotificationToTopicRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SendPushNotificationToTopicRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *SendPushNotificationToTopicRequest) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SendPushNotificationToTopicRequest) GetType() common.NotificationType {
	if x != nil {
		return x.Type
	}
	return common.NotificationType(0)
}

func (x *SendPushNotificationToTopicRequest) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *SendPushNotificationToTopicRequest) GetSound() string {
	if x != nil {
		return x.Sound
	}
	return ""
}

func (x *SendPushNotificationToTopicRequest) GetBadge() int32 {
	if x != nil {
		return x.Badge
	}
	return 0
}

func (x *SendPushNotificationToTopicRequest) GetPriorityHigh() bool {
	if x != nil {
		return x.PriorityHigh
	}
	return false
}

func (x *SendPushNotificationToTopicRequest) GetTtlSeconds() int32 {
	if x != nil {
		return x.TtlSeconds
	}
	return 0
}

// Send push notification to topic response
type SendPushNotificationToTopicResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageId     string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	SuccessCount  int32                  `protobuf:"varint,2,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`
	FailureCount  int32                  `protobuf:"varint,3,opt,name=failure_count,json=failureCount,proto3" json:"failure_count,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPushNotificationToTopicResponse) Reset() {
	*x = SendPushNotificationToTopicResponse{}
	mi := &file_notification_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPushNotificationToTopicResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationToTopicResponse) ProtoMessage() {}

func (x *SendPushNotificationToTopicResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationToTopicResponse.ProtoReflect.Descriptor instead.
func (*SendPushNotificationToTopicResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{7}
}

func (x *SendPushNotificationToTopicResponse) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *SendPushNotificationToTopicResponse) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *SendPushNotificationToTopicResponse) GetFailureCount() int32 {
	if x != nil {
		return x.FailureCount
	}
	return 0
}

func (x *SendPushNotificationToTopicResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get user notifications request
type GetUserNotificationsRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	UserId        string                  `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IncludeRead   bool                    `protobuf:"varint,4,opt,name=include_read,json=includeRead,proto3" json:"include_read,omitempty"`
	Type          common.NotificationType `protobuf:"varint,5,opt,name=type,proto3,enum=hopen.common.v1.NotificationType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserNotificationsRequest) Reset() {
	*x = GetUserNotificationsRequest{}
	mi := &file_notification_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserNotificationsRequest) ProtoMessage() {}

func (x *GetUserNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserNotificationsRequest.ProtoReflect.Descriptor instead.
func (*GetUserNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserNotificationsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserNotificationsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUserNotificationsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetUserNotificationsRequest) GetIncludeRead() bool {
	if x != nil {
		return x.IncludeRead
	}
	return false
}

func (x *GetUserNotificationsRequest) GetType() common.NotificationType {
	if x != nil {
		return x.Type
	}
	return common.NotificationType(0)
}

// Get user notifications response
type GetUserNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notifications []*common.Notification `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserNotificationsResponse) Reset() {
	*x = GetUserNotificationsResponse{}
	mi := &file_notification_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserNotificationsResponse) ProtoMessage() {}

func (x *GetUserNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserNotificationsResponse.ProtoReflect.Descriptor instead.
func (*GetUserNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserNotificationsResponse) GetNotifications() []*common.Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *GetUserNotificationsResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetUserNotificationsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Mark notification as read request
type MarkNotificationAsReadRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	NotificationId string                 `protobuf:"bytes,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MarkNotificationAsReadRequest) Reset() {
	*x = MarkNotificationAsReadRequest{}
	mi := &file_notification_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationAsReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationAsReadRequest) ProtoMessage() {}

func (x *MarkNotificationAsReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationAsReadRequest.ProtoReflect.Descriptor instead.
func (*MarkNotificationAsReadRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{10}
}

func (x *MarkNotificationAsReadRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MarkNotificationAsReadRequest) GetNotificationId() string {
	if x != nil {
		return x.NotificationId
	}
	return ""
}

// Mark notification as read response
type MarkNotificationAsReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkNotificationAsReadResponse) Reset() {
	*x = MarkNotificationAsReadResponse{}
	mi := &file_notification_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkNotificationAsReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkNotificationAsReadResponse) ProtoMessage() {}

func (x *MarkNotificationAsReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkNotificationAsReadResponse.ProtoReflect.Descriptor instead.
func (*MarkNotificationAsReadResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{11}
}

func (x *MarkNotificationAsReadResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Mark all notifications as read request
type MarkAllNotificationsAsReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkAllNotificationsAsReadRequest) Reset() {
	*x = MarkAllNotificationsAsReadRequest{}
	mi := &file_notification_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAllNotificationsAsReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAllNotificationsAsReadRequest) ProtoMessage() {}

func (x *MarkAllNotificationsAsReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAllNotificationsAsReadRequest.ProtoReflect.Descriptor instead.
func (*MarkAllNotificationsAsReadRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{12}
}

func (x *MarkAllNotificationsAsReadRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Mark all notifications as read response
type MarkAllNotificationsAsReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UpdatedCount  int32                  `protobuf:"varint,1,opt,name=updated_count,json=updatedCount,proto3" json:"updated_count,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkAllNotificationsAsReadResponse) Reset() {
	*x = MarkAllNotificationsAsReadResponse{}
	mi := &file_notification_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAllNotificationsAsReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAllNotificationsAsReadResponse) ProtoMessage() {}

func (x *MarkAllNotificationsAsReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAllNotificationsAsReadResponse.ProtoReflect.Descriptor instead.
func (*MarkAllNotificationsAsReadResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{13}
}

func (x *MarkAllNotificationsAsReadResponse) GetUpdatedCount() int32 {
	if x != nil {
		return x.UpdatedCount
	}
	return 0
}

func (x *MarkAllNotificationsAsReadResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Delete notification request
type DeleteNotificationRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	NotificationId string                 `protobuf:"bytes,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DeleteNotificationRequest) Reset() {
	*x = DeleteNotificationRequest{}
	mi := &file_notification_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationRequest) ProtoMessage() {}

func (x *DeleteNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationRequest.ProtoReflect.Descriptor instead.
func (*DeleteNotificationRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteNotificationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeleteNotificationRequest) GetNotificationId() string {
	if x != nil {
		return x.NotificationId
	}
	return ""
}

// Delete notification response
type DeleteNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNotificationResponse) Reset() {
	*x = DeleteNotificationResponse{}
	mi := &file_notification_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationResponse) ProtoMessage() {}

func (x *DeleteNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationResponse.ProtoReflect.Descriptor instead.
func (*DeleteNotificationResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteNotificationResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Notification settings
type NotificationSettings struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	UserId                     string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PushEnabled                bool                   `protobuf:"varint,2,opt,name=push_enabled,json=pushEnabled,proto3" json:"push_enabled,omitempty"`
	ContactRequestsEnabled     bool                   `protobuf:"varint,3,opt,name=contact_requests_enabled,json=contactRequestsEnabled,proto3" json:"contact_requests_enabled,omitempty"`
	FriendRequestsEnabled      bool                   `protobuf:"varint,4,opt,name=friend_requests_enabled,json=friendRequestsEnabled,proto3" json:"friend_requests_enabled,omitempty"`
	BubbleInvitesEnabled       bool                   `protobuf:"varint,5,opt,name=bubble_invites_enabled,json=bubbleInvitesEnabled,proto3" json:"bubble_invites_enabled,omitempty"`
	MessagesEnabled            bool                   `protobuf:"varint,6,opt,name=messages_enabled,json=messagesEnabled,proto3" json:"messages_enabled,omitempty"`
	CallsEnabled               bool                   `protobuf:"varint,7,opt,name=calls_enabled,json=callsEnabled,proto3" json:"calls_enabled,omitempty"`
	SystemNotificationsEnabled bool                   `protobuf:"varint,8,opt,name=system_notifications_enabled,json=systemNotificationsEnabled,proto3" json:"system_notifications_enabled,omitempty"`
	SoundEnabled               bool                   `protobuf:"varint,9,opt,name=sound_enabled,json=soundEnabled,proto3" json:"sound_enabled,omitempty"`
	VibrationEnabled           bool                   `protobuf:"varint,10,opt,name=vibration_enabled,json=vibrationEnabled,proto3" json:"vibration_enabled,omitempty"`
	QuietHoursStart            string                 `protobuf:"bytes,11,opt,name=quiet_hours_start,json=quietHoursStart,proto3" json:"quiet_hours_start,omitempty"`
	QuietHoursEnd              string                 `protobuf:"bytes,12,opt,name=quiet_hours_end,json=quietHoursEnd,proto3" json:"quiet_hours_end,omitempty"`
	QuietHoursEnabled          bool                   `protobuf:"varint,13,opt,name=quiet_hours_enabled,json=quietHoursEnabled,proto3" json:"quiet_hours_enabled,omitempty"`
	UpdatedAt                  *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *NotificationSettings) Reset() {
	*x = NotificationSettings{}
	mi := &file_notification_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationSettings) ProtoMessage() {}

func (x *NotificationSettings) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationSettings.ProtoReflect.Descriptor instead.
func (*NotificationSettings) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{16}
}

func (x *NotificationSettings) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *NotificationSettings) GetPushEnabled() bool {
	if x != nil {
		return x.PushEnabled
	}
	return false
}

func (x *NotificationSettings) GetContactRequestsEnabled() bool {
	if x != nil {
		return x.ContactRequestsEnabled
	}
	return false
}

func (x *NotificationSettings) GetFriendRequestsEnabled() bool {
	if x != nil {
		return x.FriendRequestsEnabled
	}
	return false
}

func (x *NotificationSettings) GetBubbleInvitesEnabled() bool {
	if x != nil {
		return x.BubbleInvitesEnabled
	}
	return false
}

func (x *NotificationSettings) GetMessagesEnabled() bool {
	if x != nil {
		return x.MessagesEnabled
	}
	return false
}

func (x *NotificationSettings) GetCallsEnabled() bool {
	if x != nil {
		return x.CallsEnabled
	}
	return false
}

func (x *NotificationSettings) GetSystemNotificationsEnabled() bool {
	if x != nil {
		return x.SystemNotificationsEnabled
	}
	return false
}

func (x *NotificationSettings) GetSoundEnabled() bool {
	if x != nil {
		return x.SoundEnabled
	}
	return false
}

func (x *NotificationSettings) GetVibrationEnabled() bool {
	if x != nil {
		return x.VibrationEnabled
	}
	return false
}

func (x *NotificationSettings) GetQuietHoursStart() string {
	if x != nil {
		return x.QuietHoursStart
	}
	return ""
}

func (x *NotificationSettings) GetQuietHoursEnd() string {
	if x != nil {
		return x.QuietHoursEnd
	}
	return ""
}

func (x *NotificationSettings) GetQuietHoursEnabled() bool {
	if x != nil {
		return x.QuietHoursEnabled
	}
	return false
}

func (x *NotificationSettings) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Get notification settings request
type GetNotificationSettingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationSettingsRequest) Reset() {
	*x = GetNotificationSettingsRequest{}
	mi := &file_notification_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationSettingsRequest) ProtoMessage() {}

func (x *GetNotificationSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationSettingsRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{17}
}

func (x *GetNotificationSettingsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get notification settings response
type GetNotificationSettingsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Settings      *NotificationSettings  `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationSettingsResponse) Reset() {
	*x = GetNotificationSettingsResponse{}
	mi := &file_notification_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationSettingsResponse) ProtoMessage() {}

func (x *GetNotificationSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationSettingsResponse.ProtoReflect.Descriptor instead.
func (*GetNotificationSettingsResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{18}
}

func (x *GetNotificationSettingsResponse) GetSettings() *NotificationSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *GetNotificationSettingsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update notification settings request
type UpdateNotificationSettingsRequest struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	UserId                     string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PushEnabled                *bool                  `protobuf:"varint,2,opt,name=push_enabled,json=pushEnabled,proto3,oneof" json:"push_enabled,omitempty"`
	ContactRequestsEnabled     *bool                  `protobuf:"varint,3,opt,name=contact_requests_enabled,json=contactRequestsEnabled,proto3,oneof" json:"contact_requests_enabled,omitempty"`
	FriendRequestsEnabled      *bool                  `protobuf:"varint,4,opt,name=friend_requests_enabled,json=friendRequestsEnabled,proto3,oneof" json:"friend_requests_enabled,omitempty"`
	BubbleInvitesEnabled       *bool                  `protobuf:"varint,5,opt,name=bubble_invites_enabled,json=bubbleInvitesEnabled,proto3,oneof" json:"bubble_invites_enabled,omitempty"`
	MessagesEnabled            *bool                  `protobuf:"varint,6,opt,name=messages_enabled,json=messagesEnabled,proto3,oneof" json:"messages_enabled,omitempty"`
	CallsEnabled               *bool                  `protobuf:"varint,7,opt,name=calls_enabled,json=callsEnabled,proto3,oneof" json:"calls_enabled,omitempty"`
	SystemNotificationsEnabled *bool                  `protobuf:"varint,8,opt,name=system_notifications_enabled,json=systemNotificationsEnabled,proto3,oneof" json:"system_notifications_enabled,omitempty"`
	SoundEnabled               *bool                  `protobuf:"varint,9,opt,name=sound_enabled,json=soundEnabled,proto3,oneof" json:"sound_enabled,omitempty"`
	VibrationEnabled           *bool                  `protobuf:"varint,10,opt,name=vibration_enabled,json=vibrationEnabled,proto3,oneof" json:"vibration_enabled,omitempty"`
	QuietHoursStart            *string                `protobuf:"bytes,11,opt,name=quiet_hours_start,json=quietHoursStart,proto3,oneof" json:"quiet_hours_start,omitempty"`
	QuietHoursEnd              *string                `protobuf:"bytes,12,opt,name=quiet_hours_end,json=quietHoursEnd,proto3,oneof" json:"quiet_hours_end,omitempty"`
	QuietHoursEnabled          *bool                  `protobuf:"varint,13,opt,name=quiet_hours_enabled,json=quietHoursEnabled,proto3,oneof" json:"quiet_hours_enabled,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *UpdateNotificationSettingsRequest) Reset() {
	*x = UpdateNotificationSettingsRequest{}
	mi := &file_notification_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationSettingsRequest) ProtoMessage() {}

func (x *UpdateNotificationSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationSettingsRequest.ProtoReflect.Descriptor instead.
func (*UpdateNotificationSettingsRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateNotificationSettingsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateNotificationSettingsRequest) GetPushEnabled() bool {
	if x != nil && x.PushEnabled != nil {
		return *x.PushEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetContactRequestsEnabled() bool {
	if x != nil && x.ContactRequestsEnabled != nil {
		return *x.ContactRequestsEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetFriendRequestsEnabled() bool {
	if x != nil && x.FriendRequestsEnabled != nil {
		return *x.FriendRequestsEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetBubbleInvitesEnabled() bool {
	if x != nil && x.BubbleInvitesEnabled != nil {
		return *x.BubbleInvitesEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetMessagesEnabled() bool {
	if x != nil && x.MessagesEnabled != nil {
		return *x.MessagesEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetCallsEnabled() bool {
	if x != nil && x.CallsEnabled != nil {
		return *x.CallsEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetSystemNotificationsEnabled() bool {
	if x != nil && x.SystemNotificationsEnabled != nil {
		return *x.SystemNotificationsEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetSoundEnabled() bool {
	if x != nil && x.SoundEnabled != nil {
		return *x.SoundEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetVibrationEnabled() bool {
	if x != nil && x.VibrationEnabled != nil {
		return *x.VibrationEnabled
	}
	return false
}

func (x *UpdateNotificationSettingsRequest) GetQuietHoursStart() string {
	if x != nil && x.QuietHoursStart != nil {
		return *x.QuietHoursStart
	}
	return ""
}

func (x *UpdateNotificationSettingsRequest) GetQuietHoursEnd() string {
	if x != nil && x.QuietHoursEnd != nil {
		return *x.QuietHoursEnd
	}
	return ""
}

func (x *UpdateNotificationSettingsRequest) GetQuietHoursEnabled() bool {
	if x != nil && x.QuietHoursEnabled != nil {
		return *x.QuietHoursEnabled
	}
	return false
}

// Update notification settings response
type UpdateNotificationSettingsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Settings      *NotificationSettings  `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotificationSettingsResponse) Reset() {
	*x = UpdateNotificationSettingsResponse{}
	mi := &file_notification_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationSettingsResponse) ProtoMessage() {}

func (x *UpdateNotificationSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationSettingsResponse.ProtoReflect.Descriptor instead.
func (*UpdateNotificationSettingsResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateNotificationSettingsResponse) GetSettings() *NotificationSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *UpdateNotificationSettingsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get unread notification count request
type GetUnreadNotificationCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUnreadNotificationCountRequest) Reset() {
	*x = GetUnreadNotificationCountRequest{}
	mi := &file_notification_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUnreadNotificationCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnreadNotificationCountRequest) ProtoMessage() {}

func (x *GetUnreadNotificationCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnreadNotificationCountRequest.ProtoReflect.Descriptor instead.
func (*GetUnreadNotificationCountRequest) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{21}
}

func (x *GetUnreadNotificationCountRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get unread notification count response
type GetUnreadNotificationCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int32                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	CountByType   map[string]int32       `protobuf:"bytes,2,rep,name=count_by_type,json=countByType,proto3" json:"count_by_type,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUnreadNotificationCountResponse) Reset() {
	*x = GetUnreadNotificationCountResponse{}
	mi := &file_notification_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUnreadNotificationCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnreadNotificationCountResponse) ProtoMessage() {}

func (x *GetUnreadNotificationCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnreadNotificationCountResponse.ProtoReflect.Descriptor instead.
func (*GetUnreadNotificationCountResponse) Descriptor() ([]byte, []int) {
	return file_notification_proto_rawDescGZIP(), []int{22}
}

func (x *GetUnreadNotificationCountResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetUnreadNotificationCountResponse) GetCountByType() map[string]int32 {
	if x != nil {
		return x.CountByType
	}
	return nil
}

func (x *GetUnreadNotificationCountResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

var File_notification_proto protoreflect.FileDescriptor

const file_notification_proto_rawDesc = "" +
	"\n" +
	"\x12notification.proto\x12\x15hopen.notification.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"\xa9\x01\n" +
	"\x17RegisterFCMTokenRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfcm_token\x18\x02 \x01(\tR\bfcmToken\x12\x1b\n" +
	"\tdevice_id\x18\x03 \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\x04 \x01(\tR\bplatform\x12\x1f\n" +
	"\vapp_version\x18\x05 \x01(\tR\n" +
	"appVersion\"[\n" +
	"\x18RegisterFCMTokenResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"Q\n" +
	"\x19UnregisterFCMTokenRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfcm_token\x18\x02 \x01(\tR\bfcmToken\"]\n" +
	"\x1aUnregisterFCMTokenResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xb1\x03\n" +
	"\x1bSendPushNotificationRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x12\n" +
	"\x04body\x18\x03 \x01(\tR\x04body\x12P\n" +
	"\x04data\x18\x04 \x03(\v2<.hopen.notification.v1.SendPushNotificationRequest.DataEntryR\x04data\x125\n" +
	"\x04type\x18\x05 \x01(\x0e2!.hopen.common.v1.NotificationTypeR\x04type\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12\x14\n" +
	"\x05sound\x18\a \x01(\tR\x05sound\x12\x14\n" +
	"\x05badge\x18\b \x01(\x05R\x05badge\x12#\n" +
	"\rpriority_high\x18\t \x01(\bR\fpriorityHigh\x12\x1f\n" +
	"\vttl_seconds\x18\n" +
	" \x01(\x05R\n" +
	"ttlSeconds\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xbd\x01\n" +
	"\x1cSendPushNotificationResponse\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xbc\x03\n" +
	"\"SendPushNotificationToTopicRequest\x12\x14\n" +
	"\x05topic\x18\x01 \x01(\tR\x05topic\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x12\n" +
	"\x04body\x18\x03 \x01(\tR\x04body\x12W\n" +
	"\x04data\x18\x04 \x03(\v2C.hopen.notification.v1.SendPushNotificationToTopicRequest.DataEntryR\x04data\x125\n" +
	"\x04type\x18\x05 \x01(\x0e2!.hopen.common.v1.NotificationTypeR\x04type\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12\x14\n" +
	"\x05sound\x18\a \x01(\tR\x05sound\x12\x14\n" +
	"\x05badge\x18\b \x01(\x05R\x05badge\x12#\n" +
	"\rpriority_high\x18\t \x01(\bR\fpriorityHigh\x12\x1f\n" +
	"\vttl_seconds\x18\n" +
	" \x01(\x05R\n" +
	"ttlSeconds\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xcf\x01\n" +
	"#SendPushNotificationToTopicResponse\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12#\n" +
	"\rsuccess_count\x18\x02 \x01(\x05R\fsuccessCount\x12#\n" +
	"\rfailure_count\x18\x03 \x01(\x05R\ffailureCount\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xc1\x01\n" +
	"\x1bGetUserNotificationsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12!\n" +
	"\finclude_read\x18\x04 \x01(\bR\vincludeRead\x125\n" +
	"\x04type\x18\x05 \x01(\x0e2!.hopen.common.v1.NotificationTypeR\x04type\"\xe1\x01\n" +
	"\x1cGetUserNotificationsResponse\x12C\n" +
	"\rnotifications\x18\x01 \x03(\v2\x1d.hopen.common.v1.NotificationR\rnotifications\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"a\n" +
	"\x1dMarkNotificationAsReadRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12'\n" +
	"\x0fnotification_id\x18\x02 \x01(\tR\x0enotificationId\"a\n" +
	"\x1eMarkNotificationAsReadResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"<\n" +
	"!MarkAllNotificationsAsReadRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x8a\x01\n" +
	"\"MarkAllNotificationsAsReadResponse\x12#\n" +
	"\rupdated_count\x18\x01 \x01(\x05R\fupdatedCount\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"]\n" +
	"\x19DeleteNotificationRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12'\n" +
	"\x0fnotification_id\x18\x02 \x01(\tR\x0enotificationId\"]\n" +
	"\x1aDeleteNotificationResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x9d\x05\n" +
	"\x14NotificationSettings\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12!\n" +
	"\fpush_enabled\x18\x02 \x01(\bR\vpushEnabled\x128\n" +
	"\x18contact_requests_enabled\x18\x03 \x01(\bR\x16contactRequestsEnabled\x126\n" +
	"\x17friend_requests_enabled\x18\x04 \x01(\bR\x15friendRequestsEnabled\x124\n" +
	"\x16bubble_invites_enabled\x18\x05 \x01(\bR\x14bubbleInvitesEnabled\x12)\n" +
	"\x10messages_enabled\x18\x06 \x01(\bR\x0fmessagesEnabled\x12#\n" +
	"\rcalls_enabled\x18\a \x01(\bR\fcallsEnabled\x12@\n" +
	"\x1csystem_notifications_enabled\x18\b \x01(\bR\x1asystemNotificationsEnabled\x12#\n" +
	"\rsound_enabled\x18\t \x01(\bR\fsoundEnabled\x12+\n" +
	"\x11vibration_enabled\x18\n" +
	" \x01(\bR\x10vibrationEnabled\x12*\n" +
	"\x11quiet_hours_start\x18\v \x01(\tR\x0fquietHoursStart\x12&\n" +
	"\x0fquiet_hours_end\x18\f \x01(\tR\rquietHoursEnd\x12.\n" +
	"\x13quiet_hours_enabled\x18\r \x01(\bR\x11quietHoursEnabled\x129\n" +
	"\n" +
	"updated_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"9\n" +
	"\x1eGetNotificationSettingsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\xab\x01\n" +
	"\x1fGetNotificationSettingsResponse\x12G\n" +
	"\bsettings\x18\x01 \x01(\v2+.hopen.notification.v1.NotificationSettingsR\bsettings\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xc2\a\n" +
	"!UpdateNotificationSettingsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12&\n" +
	"\fpush_enabled\x18\x02 \x01(\bH\x00R\vpushEnabled\x88\x01\x01\x12=\n" +
	"\x18contact_requests_enabled\x18\x03 \x01(\bH\x01R\x16contactRequestsEnabled\x88\x01\x01\x12;\n" +
	"\x17friend_requests_enabled\x18\x04 \x01(\bH\x02R\x15friendRequestsEnabled\x88\x01\x01\x129\n" +
	"\x16bubble_invites_enabled\x18\x05 \x01(\bH\x03R\x14bubbleInvitesEnabled\x88\x01\x01\x12.\n" +
	"\x10messages_enabled\x18\x06 \x01(\bH\x04R\x0fmessagesEnabled\x88\x01\x01\x12(\n" +
	"\rcalls_enabled\x18\a \x01(\bH\x05R\fcallsEnabled\x88\x01\x01\x12E\n" +
	"\x1csystem_notifications_enabled\x18\b \x01(\bH\x06R\x1asystemNotificationsEnabled\x88\x01\x01\x12(\n" +
	"\rsound_enabled\x18\t \x01(\bH\aR\fsoundEnabled\x88\x01\x01\x120\n" +
	"\x11vibration_enabled\x18\n" +
	" \x01(\bH\bR\x10vibrationEnabled\x88\x01\x01\x12/\n" +
	"\x11quiet_hours_start\x18\v \x01(\tH\tR\x0fquietHoursStart\x88\x01\x01\x12+\n" +
	"\x0fquiet_hours_end\x18\f \x01(\tH\n" +
	"R\rquietHoursEnd\x88\x01\x01\x123\n" +
	"\x13quiet_hours_enabled\x18\r \x01(\bH\vR\x11quietHoursEnabled\x88\x01\x01B\x0f\n" +
	"\r_push_enabledB\x1b\n" +
	"\x19_contact_requests_enabledB\x1a\n" +
	"\x18_friend_requests_enabledB\x19\n" +
	"\x17_bubble_invites_enabledB\x13\n" +
	"\x11_messages_enabledB\x10\n" +
	"\x0e_calls_enabledB\x1f\n" +
	"\x1d_system_notifications_enabledB\x10\n" +
	"\x0e_sound_enabledB\x14\n" +
	"\x12_vibration_enabledB\x14\n" +
	"\x12_quiet_hours_startB\x12\n" +
	"\x10_quiet_hours_endB\x16\n" +
	"\x14_quiet_hours_enabled\"\xae\x01\n" +
	"\"UpdateNotificationSettingsResponse\x12G\n" +
	"\bsettings\x18\x01 \x01(\v2+.hopen.notification.v1.NotificationSettingsR\bsettings\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"<\n" +
	"!GetUnreadNotificationCountRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\xab\x02\n" +
	"\"GetUnreadNotificationCountResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x05R\x05count\x12n\n" +
	"\rcount_by_type\x18\x02 \x03(\v2J.hopen.notification.v1.GetUnreadNotificationCountResponse.CountByTypeEntryR\vcountByType\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\x1a>\n" +
	"\x10CountByTypeEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x012\xe8\v\n" +
	"\x13NotificationService\x12s\n" +
	"\x10RegisterFCMToken\x12..hopen.notification.v1.RegisterFCMTokenRequest\x1a/.hopen.notification.v1.RegisterFCMTokenResponse\x12y\n" +
	"\x12UnregisterFCMToken\x120.hopen.notification.v1.UnregisterFCMTokenRequest\x1a1.hopen.notification.v1.UnregisterFCMTokenResponse\x12\x7f\n" +
	"\x14SendPushNotification\x122.hopen.notification.v1.SendPushNotificationRequest\x1a3.hopen.notification.v1.SendPushNotificationResponse\x12\x94\x01\n" +
	"\x1bSendPushNotificationToTopic\x129.hopen.notification.v1.SendPushNotificationToTopicRequest\x1a:.hopen.notification.v1.SendPushNotificationToTopicResponse\x12\x7f\n" +
	"\x14GetUserNotifications\x122.hopen.notification.v1.GetUserNotificationsRequest\x1a3.hopen.notification.v1.GetUserNotificationsResponse\x12\x85\x01\n" +
	"\x16MarkNotificationAsRead\x124.hopen.notification.v1.MarkNotificationAsReadRequest\x1a5.hopen.notification.v1.MarkNotificationAsReadResponse\x12\x91\x01\n" +
	"\x1aMarkAllNotificationsAsRead\x128.hopen.notification.v1.MarkAllNotificationsAsReadRequest\x1a9.hopen.notification.v1.MarkAllNotificationsAsReadResponse\x12y\n" +
	"\x12DeleteNotification\x120.hopen.notification.v1.DeleteNotificationRequest\x1a1.hopen.notification.v1.DeleteNotificationResponse\x12\x88\x01\n" +
	"\x17GetNotificationSettings\x125.hopen.notification.v1.GetNotificationSettingsRequest\x1a6.hopen.notification.v1.GetNotificationSettingsResponse\x12\x91\x01\n" +
	"\x1aUpdateNotificationSettings\x128.hopen.notification.v1.UpdateNotificationSettingsRequest\x1a9.hopen.notification.v1.UpdateNotificationSettingsResponse\x12\x91\x01\n" +
	"\x1aGetUnreadNotificationCount\x128.hopen.notification.v1.GetUnreadNotificationCountRequest\x1a9.hopen.notification.v1.GetUnreadNotificationCountResponseBP\n" +
	"\x19com.hopen.notification.v1P\x01Z1hopenbackend/protos/gen/notification;notificationb\x06proto3"

var (
	file_notification_proto_rawDescOnce sync.Once
	file_notification_proto_rawDescData []byte
)

func file_notification_proto_rawDescGZIP() []byte {
	file_notification_proto_rawDescOnce.Do(func() {
		file_notification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_notification_proto_rawDesc), len(file_notification_proto_rawDesc)))
	})
	return file_notification_proto_rawDescData
}

var file_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_notification_proto_goTypes = []any{
	(*RegisterFCMTokenRequest)(nil),             // 0: hopen.notification.v1.RegisterFCMTokenRequest
	(*RegisterFCMTokenResponse)(nil),            // 1: hopen.notification.v1.RegisterFCMTokenResponse
	(*UnregisterFCMTokenRequest)(nil),           // 2: hopen.notification.v1.UnregisterFCMTokenRequest
	(*UnregisterFCMTokenResponse)(nil),          // 3: hopen.notification.v1.UnregisterFCMTokenResponse
	(*SendPushNotificationRequest)(nil),         // 4: hopen.notification.v1.SendPushNotificationRequest
	(*SendPushNotificationResponse)(nil),        // 5: hopen.notification.v1.SendPushNotificationResponse
	(*SendPushNotificationToTopicRequest)(nil),  // 6: hopen.notification.v1.SendPushNotificationToTopicRequest
	(*SendPushNotificationToTopicResponse)(nil), // 7: hopen.notification.v1.SendPushNotificationToTopicResponse
	(*GetUserNotificationsRequest)(nil),         // 8: hopen.notification.v1.GetUserNotificationsRequest
	(*GetUserNotificationsResponse)(nil),        // 9: hopen.notification.v1.GetUserNotificationsResponse
	(*MarkNotificationAsReadRequest)(nil),       // 10: hopen.notification.v1.MarkNotificationAsReadRequest
	(*MarkNotificationAsReadResponse)(nil),      // 11: hopen.notification.v1.MarkNotificationAsReadResponse
	(*MarkAllNotificationsAsReadRequest)(nil),   // 12: hopen.notification.v1.MarkAllNotificationsAsReadRequest
	(*MarkAllNotificationsAsReadResponse)(nil),  // 13: hopen.notification.v1.MarkAllNotificationsAsReadResponse
	(*DeleteNotificationRequest)(nil),           // 14: hopen.notification.v1.DeleteNotificationRequest
	(*DeleteNotificationResponse)(nil),          // 15: hopen.notification.v1.DeleteNotificationResponse
	(*NotificationSettings)(nil),                // 16: hopen.notification.v1.NotificationSettings
	(*GetNotificationSettingsRequest)(nil),      // 17: hopen.notification.v1.GetNotificationSettingsRequest
	(*GetNotificationSettingsResponse)(nil),     // 18: hopen.notification.v1.GetNotificationSettingsResponse
	(*UpdateNotificationSettingsRequest)(nil),   // 19: hopen.notification.v1.UpdateNotificationSettingsRequest
	(*UpdateNotificationSettingsResponse)(nil),  // 20: hopen.notification.v1.UpdateNotificationSettingsResponse
	(*GetUnreadNotificationCountRequest)(nil),   // 21: hopen.notification.v1.GetUnreadNotificationCountRequest
	(*GetUnreadNotificationCountResponse)(nil),  // 22: hopen.notification.v1.GetUnreadNotificationCountResponse
	nil,                           // 23: hopen.notification.v1.SendPushNotificationRequest.DataEntry
	nil,                           // 24: hopen.notification.v1.SendPushNotificationToTopicRequest.DataEntry
	nil,                           // 25: hopen.notification.v1.GetUnreadNotificationCountResponse.CountByTypeEntry
	(*common.ApiResponse)(nil),    // 26: hopen.common.v1.ApiResponse
	(common.NotificationType)(0),  // 27: hopen.common.v1.NotificationType
	(*common.Notification)(nil),   // 28: hopen.common.v1.Notification
	(*common.Pagination)(nil),     // 29: hopen.common.v1.Pagination
	(*timestamppb.Timestamp)(nil), // 30: google.protobuf.Timestamp
}
var file_notification_proto_depIdxs = []int32{
	26, // 0: hopen.notification.v1.RegisterFCMTokenResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 1: hopen.notification.v1.UnregisterFCMTokenResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	23, // 2: hopen.notification.v1.SendPushNotificationRequest.data:type_name -> hopen.notification.v1.SendPushNotificationRequest.DataEntry
	27, // 3: hopen.notification.v1.SendPushNotificationRequest.type:type_name -> hopen.common.v1.NotificationType
	26, // 4: hopen.notification.v1.SendPushNotificationResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	24, // 5: hopen.notification.v1.SendPushNotificationToTopicRequest.data:type_name -> hopen.notification.v1.SendPushNotificationToTopicRequest.DataEntry
	27, // 6: hopen.notification.v1.SendPushNotificationToTopicRequest.type:type_name -> hopen.common.v1.NotificationType
	26, // 7: hopen.notification.v1.SendPushNotificationToTopicResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 8: hopen.notification.v1.GetUserNotificationsRequest.type:type_name -> hopen.common.v1.NotificationType
	28, // 9: hopen.notification.v1.GetUserNotificationsResponse.notifications:type_name -> hopen.common.v1.Notification
	29, // 10: hopen.notification.v1.GetUserNotificationsResponse.pagination:type_name -> hopen.common.v1.Pagination
	26, // 11: hopen.notification.v1.GetUserNotificationsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 12: hopen.notification.v1.MarkNotificationAsReadResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 13: hopen.notification.v1.MarkAllNotificationsAsReadResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 14: hopen.notification.v1.DeleteNotificationResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	30, // 15: hopen.notification.v1.NotificationSettings.updated_at:type_name -> google.protobuf.Timestamp
	16, // 16: hopen.notification.v1.GetNotificationSettingsResponse.settings:type_name -> hopen.notification.v1.NotificationSettings
	26, // 17: hopen.notification.v1.GetNotificationSettingsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	16, // 18: hopen.notification.v1.UpdateNotificationSettingsResponse.settings:type_name -> hopen.notification.v1.NotificationSettings
	26, // 19: hopen.notification.v1.UpdateNotificationSettingsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 20: hopen.notification.v1.GetUnreadNotificationCountResponse.count_by_type:type_name -> hopen.notification.v1.GetUnreadNotificationCountResponse.CountByTypeEntry
	26, // 21: hopen.notification.v1.GetUnreadNotificationCountResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 22: hopen.notification.v1.NotificationService.RegisterFCMToken:input_type -> hopen.notification.v1.RegisterFCMTokenRequest
	2,  // 23: hopen.notification.v1.NotificationService.UnregisterFCMToken:input_type -> hopen.notification.v1.UnregisterFCMTokenRequest
	4,  // 24: hopen.notification.v1.NotificationService.SendPushNotification:input_type -> hopen.notification.v1.SendPushNotificationRequest
	6,  // 25: hopen.notification.v1.NotificationService.SendPushNotificationToTopic:input_type -> hopen.notification.v1.SendPushNotificationToTopicRequest
	8,  // 26: hopen.notification.v1.NotificationService.GetUserNotifications:input_type -> hopen.notification.v1.GetUserNotificationsRequest
	10, // 27: hopen.notification.v1.NotificationService.MarkNotificationAsRead:input_type -> hopen.notification.v1.MarkNotificationAsReadRequest
	12, // 28: hopen.notification.v1.NotificationService.MarkAllNotificationsAsRead:input_type -> hopen.notification.v1.MarkAllNotificationsAsReadRequest
	14, // 29: hopen.notification.v1.NotificationService.DeleteNotification:input_type -> hopen.notification.v1.DeleteNotificationRequest
	17, // 30: hopen.notification.v1.NotificationService.GetNotificationSettings:input_type -> hopen.notification.v1.GetNotificationSettingsRequest
	19, // 31: hopen.notification.v1.NotificationService.UpdateNotificationSettings:input_type -> hopen.notification.v1.UpdateNotificationSettingsRequest
	21, // 32: hopen.notification.v1.NotificationService.GetUnreadNotificationCount:input_type -> hopen.notification.v1.GetUnreadNotificationCountRequest
	1,  // 33: hopen.notification.v1.NotificationService.RegisterFCMToken:output_type -> hopen.notification.v1.RegisterFCMTokenResponse
	3,  // 34: hopen.notification.v1.NotificationService.UnregisterFCMToken:output_type -> hopen.notification.v1.UnregisterFCMTokenResponse
	5,  // 35: hopen.notification.v1.NotificationService.SendPushNotification:output_type -> hopen.notification.v1.SendPushNotificationResponse
	7,  // 36: hopen.notification.v1.NotificationService.SendPushNotificationToTopic:output_type -> hopen.notification.v1.SendPushNotificationToTopicResponse
	9,  // 37: hopen.notification.v1.NotificationService.GetUserNotifications:output_type -> hopen.notification.v1.GetUserNotificationsResponse
	11, // 38: hopen.notification.v1.NotificationService.MarkNotificationAsRead:output_type -> hopen.notification.v1.MarkNotificationAsReadResponse
	13, // 39: hopen.notification.v1.NotificationService.MarkAllNotificationsAsRead:output_type -> hopen.notification.v1.MarkAllNotificationsAsReadResponse
	15, // 40: hopen.notification.v1.NotificationService.DeleteNotification:output_type -> hopen.notification.v1.DeleteNotificationResponse
	18, // 41: hopen.notification.v1.NotificationService.GetNotificationSettings:output_type -> hopen.notification.v1.GetNotificationSettingsResponse
	20, // 42: hopen.notification.v1.NotificationService.UpdateNotificationSettings:output_type -> hopen.notification.v1.UpdateNotificationSettingsResponse
	22, // 43: hopen.notification.v1.NotificationService.GetUnreadNotificationCount:output_type -> hopen.notification.v1.GetUnreadNotificationCountResponse
	33, // [33:44] is the sub-list for method output_type
	22, // [22:33] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_notification_proto_init() }
func file_notification_proto_init() {
	if File_notification_proto != nil {
		return
	}
	file_notification_proto_msgTypes[19].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_notification_proto_rawDesc), len(file_notification_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_notification_proto_goTypes,
		DependencyIndexes: file_notification_proto_depIdxs,
		MessageInfos:      file_notification_proto_msgTypes,
	}.Build()
	File_notification_proto = out.File
	file_notification_proto_goTypes = nil
	file_notification_proto_depIdxs = nil
}
