// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: notification.proto

package notification

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	NotificationService_RegisterFCMToken_FullMethodName            = "/hopen.notification.v1.NotificationService/RegisterFCMToken"
	NotificationService_UnregisterFCMToken_FullMethodName          = "/hopen.notification.v1.NotificationService/UnregisterFCMToken"
	NotificationService_SendPushNotification_FullMethodName        = "/hopen.notification.v1.NotificationService/SendPushNotification"
	NotificationService_SendPushNotificationToTopic_FullMethodName = "/hopen.notification.v1.NotificationService/SendPushNotificationToTopic"
	NotificationService_GetUserNotifications_FullMethodName        = "/hopen.notification.v1.NotificationService/GetUserNotifications"
	NotificationService_MarkNotificationAsRead_FullMethodName      = "/hopen.notification.v1.NotificationService/MarkNotificationAsRead"
	NotificationService_MarkAllNotificationsAsRead_FullMethodName  = "/hopen.notification.v1.NotificationService/MarkAllNotificationsAsRead"
	NotificationService_DeleteNotification_FullMethodName          = "/hopen.notification.v1.NotificationService/DeleteNotification"
	NotificationService_GetNotificationSettings_FullMethodName     = "/hopen.notification.v1.NotificationService/GetNotificationSettings"
	NotificationService_UpdateNotificationSettings_FullMethodName  = "/hopen.notification.v1.NotificationService/UpdateNotificationSettings"
	NotificationService_GetUnreadNotificationCount_FullMethodName  = "/hopen.notification.v1.NotificationService/GetUnreadNotificationCount"
)

// NotificationServiceClient is the client API for NotificationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Notification service for push notifications and notification management
type NotificationServiceClient interface {
	// Register FCM token
	RegisterFCMToken(ctx context.Context, in *RegisterFCMTokenRequest, opts ...grpc.CallOption) (*RegisterFCMTokenResponse, error)
	// Unregister FCM token
	UnregisterFCMToken(ctx context.Context, in *UnregisterFCMTokenRequest, opts ...grpc.CallOption) (*UnregisterFCMTokenResponse, error)
	// Send push notification
	SendPushNotification(ctx context.Context, in *SendPushNotificationRequest, opts ...grpc.CallOption) (*SendPushNotificationResponse, error)
	// Send push notification to topic
	SendPushNotificationToTopic(ctx context.Context, in *SendPushNotificationToTopicRequest, opts ...grpc.CallOption) (*SendPushNotificationToTopicResponse, error)
	// Get user notifications
	GetUserNotifications(ctx context.Context, in *GetUserNotificationsRequest, opts ...grpc.CallOption) (*GetUserNotificationsResponse, error)
	// Mark notification as read
	MarkNotificationAsRead(ctx context.Context, in *MarkNotificationAsReadRequest, opts ...grpc.CallOption) (*MarkNotificationAsReadResponse, error)
	// Mark all notifications as read
	MarkAllNotificationsAsRead(ctx context.Context, in *MarkAllNotificationsAsReadRequest, opts ...grpc.CallOption) (*MarkAllNotificationsAsReadResponse, error)
	// Delete notification
	DeleteNotification(ctx context.Context, in *DeleteNotificationRequest, opts ...grpc.CallOption) (*DeleteNotificationResponse, error)
	// Get notification settings
	GetNotificationSettings(ctx context.Context, in *GetNotificationSettingsRequest, opts ...grpc.CallOption) (*GetNotificationSettingsResponse, error)
	// Update notification settings
	UpdateNotificationSettings(ctx context.Context, in *UpdateNotificationSettingsRequest, opts ...grpc.CallOption) (*UpdateNotificationSettingsResponse, error)
	// Get unread notification count
	GetUnreadNotificationCount(ctx context.Context, in *GetUnreadNotificationCountRequest, opts ...grpc.CallOption) (*GetUnreadNotificationCountResponse, error)
}

type notificationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationServiceClient(cc grpc.ClientConnInterface) NotificationServiceClient {
	return &notificationServiceClient{cc}
}

func (c *notificationServiceClient) RegisterFCMToken(ctx context.Context, in *RegisterFCMTokenRequest, opts ...grpc.CallOption) (*RegisterFCMTokenResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterFCMTokenResponse)
	err := c.cc.Invoke(ctx, NotificationService_RegisterFCMToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) UnregisterFCMToken(ctx context.Context, in *UnregisterFCMTokenRequest, opts ...grpc.CallOption) (*UnregisterFCMTokenResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnregisterFCMTokenResponse)
	err := c.cc.Invoke(ctx, NotificationService_UnregisterFCMToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) SendPushNotification(ctx context.Context, in *SendPushNotificationRequest, opts ...grpc.CallOption) (*SendPushNotificationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPushNotificationResponse)
	err := c.cc.Invoke(ctx, NotificationService_SendPushNotification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) SendPushNotificationToTopic(ctx context.Context, in *SendPushNotificationToTopicRequest, opts ...grpc.CallOption) (*SendPushNotificationToTopicResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPushNotificationToTopicResponse)
	err := c.cc.Invoke(ctx, NotificationService_SendPushNotificationToTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetUserNotifications(ctx context.Context, in *GetUserNotificationsRequest, opts ...grpc.CallOption) (*GetUserNotificationsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserNotificationsResponse)
	err := c.cc.Invoke(ctx, NotificationService_GetUserNotifications_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) MarkNotificationAsRead(ctx context.Context, in *MarkNotificationAsReadRequest, opts ...grpc.CallOption) (*MarkNotificationAsReadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkNotificationAsReadResponse)
	err := c.cc.Invoke(ctx, NotificationService_MarkNotificationAsRead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) MarkAllNotificationsAsRead(ctx context.Context, in *MarkAllNotificationsAsReadRequest, opts ...grpc.CallOption) (*MarkAllNotificationsAsReadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkAllNotificationsAsReadResponse)
	err := c.cc.Invoke(ctx, NotificationService_MarkAllNotificationsAsRead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) DeleteNotification(ctx context.Context, in *DeleteNotificationRequest, opts ...grpc.CallOption) (*DeleteNotificationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteNotificationResponse)
	err := c.cc.Invoke(ctx, NotificationService_DeleteNotification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetNotificationSettings(ctx context.Context, in *GetNotificationSettingsRequest, opts ...grpc.CallOption) (*GetNotificationSettingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetNotificationSettingsResponse)
	err := c.cc.Invoke(ctx, NotificationService_GetNotificationSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) UpdateNotificationSettings(ctx context.Context, in *UpdateNotificationSettingsRequest, opts ...grpc.CallOption) (*UpdateNotificationSettingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateNotificationSettingsResponse)
	err := c.cc.Invoke(ctx, NotificationService_UpdateNotificationSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetUnreadNotificationCount(ctx context.Context, in *GetUnreadNotificationCountRequest, opts ...grpc.CallOption) (*GetUnreadNotificationCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUnreadNotificationCountResponse)
	err := c.cc.Invoke(ctx, NotificationService_GetUnreadNotificationCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationServiceServer is the server API for NotificationService service.
// All implementations must embed UnimplementedNotificationServiceServer
// for forward compatibility.
//
// Notification service for push notifications and notification management
type NotificationServiceServer interface {
	// Register FCM token
	RegisterFCMToken(context.Context, *RegisterFCMTokenRequest) (*RegisterFCMTokenResponse, error)
	// Unregister FCM token
	UnregisterFCMToken(context.Context, *UnregisterFCMTokenRequest) (*UnregisterFCMTokenResponse, error)
	// Send push notification
	SendPushNotification(context.Context, *SendPushNotificationRequest) (*SendPushNotificationResponse, error)
	// Send push notification to topic
	SendPushNotificationToTopic(context.Context, *SendPushNotificationToTopicRequest) (*SendPushNotificationToTopicResponse, error)
	// Get user notifications
	GetUserNotifications(context.Context, *GetUserNotificationsRequest) (*GetUserNotificationsResponse, error)
	// Mark notification as read
	MarkNotificationAsRead(context.Context, *MarkNotificationAsReadRequest) (*MarkNotificationAsReadResponse, error)
	// Mark all notifications as read
	MarkAllNotificationsAsRead(context.Context, *MarkAllNotificationsAsReadRequest) (*MarkAllNotificationsAsReadResponse, error)
	// Delete notification
	DeleteNotification(context.Context, *DeleteNotificationRequest) (*DeleteNotificationResponse, error)
	// Get notification settings
	GetNotificationSettings(context.Context, *GetNotificationSettingsRequest) (*GetNotificationSettingsResponse, error)
	// Update notification settings
	UpdateNotificationSettings(context.Context, *UpdateNotificationSettingsRequest) (*UpdateNotificationSettingsResponse, error)
	// Get unread notification count
	GetUnreadNotificationCount(context.Context, *GetUnreadNotificationCountRequest) (*GetUnreadNotificationCountResponse, error)
	mustEmbedUnimplementedNotificationServiceServer()
}

// UnimplementedNotificationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNotificationServiceServer struct{}

func (UnimplementedNotificationServiceServer) RegisterFCMToken(context.Context, *RegisterFCMTokenRequest) (*RegisterFCMTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterFCMToken not implemented")
}
func (UnimplementedNotificationServiceServer) UnregisterFCMToken(context.Context, *UnregisterFCMTokenRequest) (*UnregisterFCMTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnregisterFCMToken not implemented")
}
func (UnimplementedNotificationServiceServer) SendPushNotification(context.Context, *SendPushNotificationRequest) (*SendPushNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPushNotification not implemented")
}
func (UnimplementedNotificationServiceServer) SendPushNotificationToTopic(context.Context, *SendPushNotificationToTopicRequest) (*SendPushNotificationToTopicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPushNotificationToTopic not implemented")
}
func (UnimplementedNotificationServiceServer) GetUserNotifications(context.Context, *GetUserNotificationsRequest) (*GetUserNotificationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserNotifications not implemented")
}
func (UnimplementedNotificationServiceServer) MarkNotificationAsRead(context.Context, *MarkNotificationAsReadRequest) (*MarkNotificationAsReadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkNotificationAsRead not implemented")
}
func (UnimplementedNotificationServiceServer) MarkAllNotificationsAsRead(context.Context, *MarkAllNotificationsAsReadRequest) (*MarkAllNotificationsAsReadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkAllNotificationsAsRead not implemented")
}
func (UnimplementedNotificationServiceServer) DeleteNotification(context.Context, *DeleteNotificationRequest) (*DeleteNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNotification not implemented")
}
func (UnimplementedNotificationServiceServer) GetNotificationSettings(context.Context, *GetNotificationSettingsRequest) (*GetNotificationSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotificationSettings not implemented")
}
func (UnimplementedNotificationServiceServer) UpdateNotificationSettings(context.Context, *UpdateNotificationSettingsRequest) (*UpdateNotificationSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNotificationSettings not implemented")
}
func (UnimplementedNotificationServiceServer) GetUnreadNotificationCount(context.Context, *GetUnreadNotificationCountRequest) (*GetUnreadNotificationCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUnreadNotificationCount not implemented")
}
func (UnimplementedNotificationServiceServer) mustEmbedUnimplementedNotificationServiceServer() {}
func (UnimplementedNotificationServiceServer) testEmbeddedByValue()                             {}

// UnsafeNotificationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationServiceServer will
// result in compilation errors.
type UnsafeNotificationServiceServer interface {
	mustEmbedUnimplementedNotificationServiceServer()
}

func RegisterNotificationServiceServer(s grpc.ServiceRegistrar, srv NotificationServiceServer) {
	// If the following call pancis, it indicates UnimplementedNotificationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&NotificationService_ServiceDesc, srv)
}

func _NotificationService_RegisterFCMToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterFCMTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).RegisterFCMToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_RegisterFCMToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).RegisterFCMToken(ctx, req.(*RegisterFCMTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_UnregisterFCMToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnregisterFCMTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).UnregisterFCMToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_UnregisterFCMToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).UnregisterFCMToken(ctx, req.(*UnregisterFCMTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_SendPushNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPushNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).SendPushNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_SendPushNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).SendPushNotification(ctx, req.(*SendPushNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_SendPushNotificationToTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPushNotificationToTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).SendPushNotificationToTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_SendPushNotificationToTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).SendPushNotificationToTopic(ctx, req.(*SendPushNotificationToTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetUserNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetUserNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_GetUserNotifications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetUserNotifications(ctx, req.(*GetUserNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_MarkNotificationAsRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkNotificationAsReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).MarkNotificationAsRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_MarkNotificationAsRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).MarkNotificationAsRead(ctx, req.(*MarkNotificationAsReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_MarkAllNotificationsAsRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkAllNotificationsAsReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).MarkAllNotificationsAsRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_MarkAllNotificationsAsRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).MarkAllNotificationsAsRead(ctx, req.(*MarkAllNotificationsAsReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_DeleteNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).DeleteNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_DeleteNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).DeleteNotification(ctx, req.(*DeleteNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetNotificationSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetNotificationSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_GetNotificationSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetNotificationSettings(ctx, req.(*GetNotificationSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_UpdateNotificationSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNotificationSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).UpdateNotificationSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_UpdateNotificationSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).UpdateNotificationSettings(ctx, req.(*UpdateNotificationSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetUnreadNotificationCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnreadNotificationCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetUnreadNotificationCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NotificationService_GetUnreadNotificationCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetUnreadNotificationCount(ctx, req.(*GetUnreadNotificationCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NotificationService_ServiceDesc is the grpc.ServiceDesc for NotificationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotificationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.notification.v1.NotificationService",
	HandlerType: (*NotificationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterFCMToken",
			Handler:    _NotificationService_RegisterFCMToken_Handler,
		},
		{
			MethodName: "UnregisterFCMToken",
			Handler:    _NotificationService_UnregisterFCMToken_Handler,
		},
		{
			MethodName: "SendPushNotification",
			Handler:    _NotificationService_SendPushNotification_Handler,
		},
		{
			MethodName: "SendPushNotificationToTopic",
			Handler:    _NotificationService_SendPushNotificationToTopic_Handler,
		},
		{
			MethodName: "GetUserNotifications",
			Handler:    _NotificationService_GetUserNotifications_Handler,
		},
		{
			MethodName: "MarkNotificationAsRead",
			Handler:    _NotificationService_MarkNotificationAsRead_Handler,
		},
		{
			MethodName: "MarkAllNotificationsAsRead",
			Handler:    _NotificationService_MarkAllNotificationsAsRead_Handler,
		},
		{
			MethodName: "DeleteNotification",
			Handler:    _NotificationService_DeleteNotification_Handler,
		},
		{
			MethodName: "GetNotificationSettings",
			Handler:    _NotificationService_GetNotificationSettings_Handler,
		},
		{
			MethodName: "UpdateNotificationSettings",
			Handler:    _NotificationService_UpdateNotificationSettings_Handler,
		},
		{
			MethodName: "GetUnreadNotificationCount",
			Handler:    _NotificationService_GetUnreadNotificationCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "notification.proto",
}
