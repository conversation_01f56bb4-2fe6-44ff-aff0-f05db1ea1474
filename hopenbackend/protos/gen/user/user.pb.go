// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user.proto

package user

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get user request
type GetUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get user response
type GetUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *common.User           `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	mi := &file_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserResponse) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GetUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update user request
type UpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      *string                `protobuf:"bytes,2,opt,name=username,proto3,oneof" json:"username,omitempty"`
	FirstName     *string                `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	LastName      *string                `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	AvatarUrl     *string                `protobuf:"bytes,5,opt,name=avatar_url,json=avatarUrl,proto3,oneof" json:"avatar_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateUserRequest) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *UpdateUserRequest) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *UpdateUserRequest) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *UpdateUserRequest) GetAvatarUrl() string {
	if x != nil && x.AvatarUrl != nil {
		return *x.AvatarUrl
	}
	return ""
}

// Update user response
type UpdateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *common.User           `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserResponse) Reset() {
	*x = UpdateUserResponse{}
	mi := &file_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserResponse) ProtoMessage() {}

func (x *UpdateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUserResponse) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdateUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Search users request
type SearchUsersRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Query          string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Page           int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IncludePrivate bool                   `protobuf:"varint,4,opt,name=include_private,json=includePrivate,proto3" json:"include_private,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchUsersRequest) Reset() {
	*x = SearchUsersRequest{}
	mi := &file_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUsersRequest) ProtoMessage() {}

func (x *SearchUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUsersRequest.ProtoReflect.Descriptor instead.
func (*SearchUsersRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{4}
}

func (x *SearchUsersRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchUsersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUsersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchUsersRequest) GetIncludePrivate() bool {
	if x != nil {
		return x.IncludePrivate
	}
	return false
}

// Search users response
type SearchUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*common.User         `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUsersResponse) Reset() {
	*x = SearchUsersResponse{}
	mi := &file_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUsersResponse) ProtoMessage() {}

func (x *SearchUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUsersResponse.ProtoReflect.Descriptor instead.
func (*SearchUsersResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{5}
}

func (x *SearchUsersResponse) GetUsers() []*common.User {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *SearchUsersResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchUsersResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Check username availability request
type CheckUsernameAvailabilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUsernameAvailabilityRequest) Reset() {
	*x = CheckUsernameAvailabilityRequest{}
	mi := &file_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUsernameAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUsernameAvailabilityRequest) ProtoMessage() {}

func (x *CheckUsernameAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUsernameAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*CheckUsernameAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{6}
}

func (x *CheckUsernameAvailabilityRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// Check username availability response
type CheckUsernameAvailabilityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Available     bool                   `protobuf:"varint,1,opt,name=available,proto3" json:"available,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUsernameAvailabilityResponse) Reset() {
	*x = CheckUsernameAvailabilityResponse{}
	mi := &file_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUsernameAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUsernameAvailabilityResponse) ProtoMessage() {}

func (x *CheckUsernameAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUsernameAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*CheckUsernameAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{7}
}

func (x *CheckUsernameAvailabilityResponse) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

func (x *CheckUsernameAvailabilityResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Check email availability request
type CheckEmailAvailabilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckEmailAvailabilityRequest) Reset() {
	*x = CheckEmailAvailabilityRequest{}
	mi := &file_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckEmailAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEmailAvailabilityRequest) ProtoMessage() {}

func (x *CheckEmailAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEmailAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*CheckEmailAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{8}
}

func (x *CheckEmailAvailabilityRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// Check email availability response
type CheckEmailAvailabilityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Available     bool                   `protobuf:"varint,1,opt,name=available,proto3" json:"available,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckEmailAvailabilityResponse) Reset() {
	*x = CheckEmailAvailabilityResponse{}
	mi := &file_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckEmailAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEmailAvailabilityResponse) ProtoMessage() {}

func (x *CheckEmailAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEmailAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*CheckEmailAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{9}
}

func (x *CheckEmailAvailabilityResponse) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

func (x *CheckEmailAvailabilityResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// User privacy settings
type UserPrivacySettings struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	UserId                string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IsPrivate             bool                   `protobuf:"varint,2,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	AllowSearchByEmail    bool                   `protobuf:"varint,3,opt,name=allow_search_by_email,json=allowSearchByEmail,proto3" json:"allow_search_by_email,omitempty"`
	AllowSearchByUsername bool                   `protobuf:"varint,4,opt,name=allow_search_by_username,json=allowSearchByUsername,proto3" json:"allow_search_by_username,omitempty"`
	ShowOnlineStatus      bool                   `protobuf:"varint,5,opt,name=show_online_status,json=showOnlineStatus,proto3" json:"show_online_status,omitempty"`
	ShowBubbleMembership  bool                   `protobuf:"varint,6,opt,name=show_bubble_membership,json=showBubbleMembership,proto3" json:"show_bubble_membership,omitempty"`
	UpdatedAt             *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UserPrivacySettings) Reset() {
	*x = UserPrivacySettings{}
	mi := &file_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserPrivacySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPrivacySettings) ProtoMessage() {}

func (x *UserPrivacySettings) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPrivacySettings.ProtoReflect.Descriptor instead.
func (*UserPrivacySettings) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{10}
}

func (x *UserPrivacySettings) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserPrivacySettings) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *UserPrivacySettings) GetAllowSearchByEmail() bool {
	if x != nil {
		return x.AllowSearchByEmail
	}
	return false
}

func (x *UserPrivacySettings) GetAllowSearchByUsername() bool {
	if x != nil {
		return x.AllowSearchByUsername
	}
	return false
}

func (x *UserPrivacySettings) GetShowOnlineStatus() bool {
	if x != nil {
		return x.ShowOnlineStatus
	}
	return false
}

func (x *UserPrivacySettings) GetShowBubbleMembership() bool {
	if x != nil {
		return x.ShowBubbleMembership
	}
	return false
}

func (x *UserPrivacySettings) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Get user privacy settings request
type GetUserPrivacySettingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPrivacySettingsRequest) Reset() {
	*x = GetUserPrivacySettingsRequest{}
	mi := &file_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPrivacySettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPrivacySettingsRequest) ProtoMessage() {}

func (x *GetUserPrivacySettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPrivacySettingsRequest.ProtoReflect.Descriptor instead.
func (*GetUserPrivacySettingsRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserPrivacySettingsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get user privacy settings response
type GetUserPrivacySettingsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Settings      *UserPrivacySettings   `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPrivacySettingsResponse) Reset() {
	*x = GetUserPrivacySettingsResponse{}
	mi := &file_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPrivacySettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPrivacySettingsResponse) ProtoMessage() {}

func (x *GetUserPrivacySettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPrivacySettingsResponse.ProtoReflect.Descriptor instead.
func (*GetUserPrivacySettingsResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{12}
}

func (x *GetUserPrivacySettingsResponse) GetSettings() *UserPrivacySettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *GetUserPrivacySettingsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update user privacy settings request
type UpdateUserPrivacySettingsRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	UserId                string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IsPrivate             *bool                  `protobuf:"varint,2,opt,name=is_private,json=isPrivate,proto3,oneof" json:"is_private,omitempty"`
	AllowSearchByEmail    *bool                  `protobuf:"varint,3,opt,name=allow_search_by_email,json=allowSearchByEmail,proto3,oneof" json:"allow_search_by_email,omitempty"`
	AllowSearchByUsername *bool                  `protobuf:"varint,4,opt,name=allow_search_by_username,json=allowSearchByUsername,proto3,oneof" json:"allow_search_by_username,omitempty"`
	ShowOnlineStatus      *bool                  `protobuf:"varint,5,opt,name=show_online_status,json=showOnlineStatus,proto3,oneof" json:"show_online_status,omitempty"`
	ShowBubbleMembership  *bool                  `protobuf:"varint,6,opt,name=show_bubble_membership,json=showBubbleMembership,proto3,oneof" json:"show_bubble_membership,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UpdateUserPrivacySettingsRequest) Reset() {
	*x = UpdateUserPrivacySettingsRequest{}
	mi := &file_user_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserPrivacySettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserPrivacySettingsRequest) ProtoMessage() {}

func (x *UpdateUserPrivacySettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserPrivacySettingsRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserPrivacySettingsRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateUserPrivacySettingsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateUserPrivacySettingsRequest) GetIsPrivate() bool {
	if x != nil && x.IsPrivate != nil {
		return *x.IsPrivate
	}
	return false
}

func (x *UpdateUserPrivacySettingsRequest) GetAllowSearchByEmail() bool {
	if x != nil && x.AllowSearchByEmail != nil {
		return *x.AllowSearchByEmail
	}
	return false
}

func (x *UpdateUserPrivacySettingsRequest) GetAllowSearchByUsername() bool {
	if x != nil && x.AllowSearchByUsername != nil {
		return *x.AllowSearchByUsername
	}
	return false
}

func (x *UpdateUserPrivacySettingsRequest) GetShowOnlineStatus() bool {
	if x != nil && x.ShowOnlineStatus != nil {
		return *x.ShowOnlineStatus
	}
	return false
}

func (x *UpdateUserPrivacySettingsRequest) GetShowBubbleMembership() bool {
	if x != nil && x.ShowBubbleMembership != nil {
		return *x.ShowBubbleMembership
	}
	return false
}

// Update user privacy settings response
type UpdateUserPrivacySettingsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Settings      *UserPrivacySettings   `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserPrivacySettingsResponse) Reset() {
	*x = UpdateUserPrivacySettingsResponse{}
	mi := &file_user_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserPrivacySettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserPrivacySettingsResponse) ProtoMessage() {}

func (x *UpdateUserPrivacySettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserPrivacySettingsResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserPrivacySettingsResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateUserPrivacySettingsResponse) GetSettings() *UserPrivacySettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *UpdateUserPrivacySettingsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Delete user request
type DeleteUserRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ConfirmationToken string                 `protobuf:"bytes,2,opt,name=confirmation_token,json=confirmationToken,proto3" json:"confirmation_token,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_user_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeleteUserRequest) GetConfirmationToken() string {
	if x != nil {
		return x.ConfirmationToken
	}
	return ""
}

// Delete user response
type DeleteUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserResponse) Reset() {
	*x = DeleteUserResponse{}
	mi := &file_user_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserResponse) ProtoMessage() {}

func (x *DeleteUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserResponse.ProtoReflect.Descriptor instead.
func (*DeleteUserResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// User statistics
type UserStats struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	UserId                string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TotalBubblesCreated   int32                  `protobuf:"varint,2,opt,name=total_bubbles_created,json=totalBubblesCreated,proto3" json:"total_bubbles_created,omitempty"`
	TotalBubblesJoined    int32                  `protobuf:"varint,3,opt,name=total_bubbles_joined,json=totalBubblesJoined,proto3" json:"total_bubbles_joined,omitempty"`
	TotalContacts         int32                  `protobuf:"varint,4,opt,name=total_contacts,json=totalContacts,proto3" json:"total_contacts,omitempty"`
	TotalFriends          int32                  `protobuf:"varint,5,opt,name=total_friends,json=totalFriends,proto3" json:"total_friends,omitempty"`
	TotalMessagesSent     int32                  `protobuf:"varint,6,opt,name=total_messages_sent,json=totalMessagesSent,proto3" json:"total_messages_sent,omitempty"`
	TotalMessagesReceived int32                  `protobuf:"varint,7,opt,name=total_messages_received,json=totalMessagesReceived,proto3" json:"total_messages_received,omitempty"`
	LastActiveAt          *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_active_at,json=lastActiveAt,proto3" json:"last_active_at,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UserStats) Reset() {
	*x = UserStats{}
	mi := &file_user_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStats) ProtoMessage() {}

func (x *UserStats) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStats.ProtoReflect.Descriptor instead.
func (*UserStats) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{17}
}

func (x *UserStats) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserStats) GetTotalBubblesCreated() int32 {
	if x != nil {
		return x.TotalBubblesCreated
	}
	return 0
}

func (x *UserStats) GetTotalBubblesJoined() int32 {
	if x != nil {
		return x.TotalBubblesJoined
	}
	return 0
}

func (x *UserStats) GetTotalContacts() int32 {
	if x != nil {
		return x.TotalContacts
	}
	return 0
}

func (x *UserStats) GetTotalFriends() int32 {
	if x != nil {
		return x.TotalFriends
	}
	return 0
}

func (x *UserStats) GetTotalMessagesSent() int32 {
	if x != nil {
		return x.TotalMessagesSent
	}
	return 0
}

func (x *UserStats) GetTotalMessagesReceived() int32 {
	if x != nil {
		return x.TotalMessagesReceived
	}
	return 0
}

func (x *UserStats) GetLastActiveAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActiveAt
	}
	return nil
}

func (x *UserStats) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// Get user stats request
type GetUserStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserStatsRequest) Reset() {
	*x = GetUserStatsRequest{}
	mi := &file_user_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatsRequest) ProtoMessage() {}

func (x *GetUserStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatsRequest.ProtoReflect.Descriptor instead.
func (*GetUserStatsRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{18}
}

func (x *GetUserStatsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get user stats response
type GetUserStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *UserStats             `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserStatsResponse) Reset() {
	*x = GetUserStatsResponse{}
	mi := &file_user_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatsResponse) ProtoMessage() {}

func (x *GetUserStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatsResponse.ProtoReflect.Descriptor instead.
func (*GetUserStatsResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{19}
}

func (x *GetUserStatsResponse) GetStats() *UserStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetUserStatsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Block user request
type BlockUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BlockedUserId string                 `protobuf:"bytes,2,opt,name=blocked_user_id,json=blockedUserId,proto3" json:"blocked_user_id,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserRequest) Reset() {
	*x = BlockUserRequest{}
	mi := &file_user_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserRequest) ProtoMessage() {}

func (x *BlockUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserRequest.ProtoReflect.Descriptor instead.
func (*BlockUserRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{20}
}

func (x *BlockUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BlockUserRequest) GetBlockedUserId() string {
	if x != nil {
		return x.BlockedUserId
	}
	return ""
}

func (x *BlockUserRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Block user response
type BlockUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserResponse) Reset() {
	*x = BlockUserResponse{}
	mi := &file_user_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserResponse) ProtoMessage() {}

func (x *BlockUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserResponse.ProtoReflect.Descriptor instead.
func (*BlockUserResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{21}
}

func (x *BlockUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Unblock user request
type UnblockUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BlockedUserId string                 `protobuf:"bytes,2,opt,name=blocked_user_id,json=blockedUserId,proto3" json:"blocked_user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockUserRequest) Reset() {
	*x = UnblockUserRequest{}
	mi := &file_user_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockUserRequest) ProtoMessage() {}

func (x *UnblockUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockUserRequest.ProtoReflect.Descriptor instead.
func (*UnblockUserRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{22}
}

func (x *UnblockUserRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UnblockUserRequest) GetBlockedUserId() string {
	if x != nil {
		return x.BlockedUserId
	}
	return ""
}

// Unblock user response
type UnblockUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockUserResponse) Reset() {
	*x = UnblockUserResponse{}
	mi := &file_user_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockUserResponse) ProtoMessage() {}

func (x *UnblockUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockUserResponse.ProtoReflect.Descriptor instead.
func (*UnblockUserResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{23}
}

func (x *UnblockUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get blocked users request
type GetBlockedUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockedUsersRequest) Reset() {
	*x = GetBlockedUsersRequest{}
	mi := &file_user_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockedUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockedUsersRequest) ProtoMessage() {}

func (x *GetBlockedUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockedUsersRequest.ProtoReflect.Descriptor instead.
func (*GetBlockedUsersRequest) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{24}
}

func (x *GetBlockedUsersRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetBlockedUsersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetBlockedUsersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get blocked users response
type GetBlockedUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BlockedUsers  []*common.User         `protobuf:"bytes,1,rep,name=blocked_users,json=blockedUsers,proto3" json:"blocked_users,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockedUsersResponse) Reset() {
	*x = GetBlockedUsersResponse{}
	mi := &file_user_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockedUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockedUsersResponse) ProtoMessage() {}

func (x *GetBlockedUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockedUsersResponse.ProtoReflect.Descriptor instead.
func (*GetBlockedUsersResponse) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{25}
}

func (x *GetBlockedUsersResponse) GetBlockedUsers() []*common.User {
	if x != nil {
		return x.BlockedUsers
	}
	return nil
}

func (x *GetBlockedUsersResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetBlockedUsersResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

var File_user_proto protoreflect.FileDescriptor

const file_user_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"user.proto\x12\rhopen.user.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\")\n" +
	"\x0eGetUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"}\n" +
	"\x0fGetUserResponse\x12)\n" +
	"\x04user\x18\x01 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xf0\x01\n" +
	"\x11UpdateUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1f\n" +
	"\busername\x18\x02 \x01(\tH\x00R\busername\x88\x01\x01\x12\"\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tH\x01R\tfirstName\x88\x01\x01\x12 \n" +
	"\tlast_name\x18\x04 \x01(\tH\x02R\blastName\x88\x01\x01\x12\"\n" +
	"\n" +
	"avatar_url\x18\x05 \x01(\tH\x03R\tavatarUrl\x88\x01\x01B\v\n" +
	"\t_usernameB\r\n" +
	"\v_first_nameB\f\n" +
	"\n" +
	"_last_nameB\r\n" +
	"\v_avatar_url\"\x80\x01\n" +
	"\x12UpdateUserResponse\x12)\n" +
	"\x04user\x18\x01 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x84\x01\n" +
	"\x12SearchUsersRequest\x12\x14\n" +
	"\x05query\x18\x01 \x01(\tR\x05query\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12'\n" +
	"\x0finclude_private\x18\x04 \x01(\bR\x0eincludePrivate\"\xc0\x01\n" +
	"\x13SearchUsersResponse\x12+\n" +
	"\x05users\x18\x01 \x03(\v2\x15.hopen.common.v1.UserR\x05users\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\">\n" +
	" CheckUsernameAvailabilityRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\"\x82\x01\n" +
	"!CheckUsernameAvailabilityResponse\x12\x1c\n" +
	"\tavailable\x18\x01 \x01(\bR\tavailable\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"5\n" +
	"\x1dCheckEmailAvailabilityRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\"\x7f\n" +
	"\x1eCheckEmailAvailabilityResponse\x12\x1c\n" +
	"\tavailable\x18\x01 \x01(\bR\tavailable\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xd8\x02\n" +
	"\x13UserPrivacySettings\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"is_private\x18\x02 \x01(\bR\tisPrivate\x121\n" +
	"\x15allow_search_by_email\x18\x03 \x01(\bR\x12allowSearchByEmail\x127\n" +
	"\x18allow_search_by_username\x18\x04 \x01(\bR\x15allowSearchByUsername\x12,\n" +
	"\x12show_online_status\x18\x05 \x01(\bR\x10showOnlineStatus\x124\n" +
	"\x16show_bubble_membership\x18\x06 \x01(\bR\x14showBubbleMembership\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"8\n" +
	"\x1dGetUserPrivacySettingsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\xa1\x01\n" +
	"\x1eGetUserPrivacySettingsResponse\x12>\n" +
	"\bsettings\x18\x01 \x01(\v2\".hopen.user.v1.UserPrivacySettingsR\bsettings\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xbb\x03\n" +
	" UpdateUserPrivacySettingsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\"\n" +
	"\n" +
	"is_private\x18\x02 \x01(\bH\x00R\tisPrivate\x88\x01\x01\x126\n" +
	"\x15allow_search_by_email\x18\x03 \x01(\bH\x01R\x12allowSearchByEmail\x88\x01\x01\x12<\n" +
	"\x18allow_search_by_username\x18\x04 \x01(\bH\x02R\x15allowSearchByUsername\x88\x01\x01\x121\n" +
	"\x12show_online_status\x18\x05 \x01(\bH\x03R\x10showOnlineStatus\x88\x01\x01\x129\n" +
	"\x16show_bubble_membership\x18\x06 \x01(\bH\x04R\x14showBubbleMembership\x88\x01\x01B\r\n" +
	"\v_is_privateB\x18\n" +
	"\x16_allow_search_by_emailB\x1b\n" +
	"\x19_allow_search_by_usernameB\x15\n" +
	"\x13_show_online_statusB\x19\n" +
	"\x17_show_bubble_membership\"\xa4\x01\n" +
	"!UpdateUserPrivacySettingsResponse\x12>\n" +
	"\bsettings\x18\x01 \x01(\v2\".hopen.user.v1.UserPrivacySettingsR\bsettings\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"[\n" +
	"\x11DeleteUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12-\n" +
	"\x12confirmation_token\x18\x02 \x01(\tR\x11confirmationToken\"U\n" +
	"\x12DeleteUserResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xbb\x03\n" +
	"\tUserStats\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x122\n" +
	"\x15total_bubbles_created\x18\x02 \x01(\x05R\x13totalBubblesCreated\x120\n" +
	"\x14total_bubbles_joined\x18\x03 \x01(\x05R\x12totalBubblesJoined\x12%\n" +
	"\x0etotal_contacts\x18\x04 \x01(\x05R\rtotalContacts\x12#\n" +
	"\rtotal_friends\x18\x05 \x01(\x05R\ftotalFriends\x12.\n" +
	"\x13total_messages_sent\x18\x06 \x01(\x05R\x11totalMessagesSent\x126\n" +
	"\x17total_messages_received\x18\a \x01(\x05R\x15totalMessagesReceived\x12@\n" +
	"\x0elast_active_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\flastActiveAt\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\".\n" +
	"\x13GetUserStatsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x87\x01\n" +
	"\x14GetUserStatsResponse\x12.\n" +
	"\x05stats\x18\x01 \x01(\v2\x18.hopen.user.v1.UserStatsR\x05stats\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"k\n" +
	"\x10BlockUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12&\n" +
	"\x0fblocked_user_id\x18\x02 \x01(\tR\rblockedUserId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"T\n" +
	"\x11BlockUserResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"U\n" +
	"\x12UnblockUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12&\n" +
	"\x0fblocked_user_id\x18\x02 \x01(\tR\rblockedUserId\"V\n" +
	"\x13UnblockUserResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"b\n" +
	"\x16GetBlockedUsersRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"\xd3\x01\n" +
	"\x17GetBlockedUsersResponse\x12:\n" +
	"\rblocked_users\x18\x01 \x03(\v2\x15.hopen.common.v1.UserR\fblockedUsers\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse2\xa2\t\n" +
	"\vUserService\x12H\n" +
	"\aGetUser\x12\x1d.hopen.user.v1.GetUserRequest\x1a\x1e.hopen.user.v1.GetUserResponse\x12Q\n" +
	"\n" +
	"UpdateUser\x12 .hopen.user.v1.UpdateUserRequest\x1a!.hopen.user.v1.UpdateUserResponse\x12T\n" +
	"\vSearchUsers\x12!.hopen.user.v1.SearchUsersRequest\x1a\".hopen.user.v1.SearchUsersResponse\x12~\n" +
	"\x19CheckUsernameAvailability\x12/.hopen.user.v1.CheckUsernameAvailabilityRequest\x1a0.hopen.user.v1.CheckUsernameAvailabilityResponse\x12u\n" +
	"\x16CheckEmailAvailability\x12,.hopen.user.v1.CheckEmailAvailabilityRequest\x1a-.hopen.user.v1.CheckEmailAvailabilityResponse\x12u\n" +
	"\x16GetUserPrivacySettings\x12,.hopen.user.v1.GetUserPrivacySettingsRequest\x1a-.hopen.user.v1.GetUserPrivacySettingsResponse\x12~\n" +
	"\x19UpdateUserPrivacySettings\x12/.hopen.user.v1.UpdateUserPrivacySettingsRequest\x1a0.hopen.user.v1.UpdateUserPrivacySettingsResponse\x12Q\n" +
	"\n" +
	"DeleteUser\x12 .hopen.user.v1.DeleteUserRequest\x1a!.hopen.user.v1.DeleteUserResponse\x12W\n" +
	"\fGetUserStats\x12\".hopen.user.v1.GetUserStatsRequest\x1a#.hopen.user.v1.GetUserStatsResponse\x12N\n" +
	"\tBlockUser\x12\x1f.hopen.user.v1.BlockUserRequest\x1a .hopen.user.v1.BlockUserResponse\x12T\n" +
	"\vUnblockUser\x12!.hopen.user.v1.UnblockUserRequest\x1a\".hopen.user.v1.UnblockUserResponse\x12`\n" +
	"\x0fGetBlockedUsers\x12%.hopen.user.v1.GetBlockedUsersRequest\x1a&.hopen.user.v1.GetBlockedUsersResponseB8\n" +
	"\x11com.hopen.user.v1P\x01Z!hopenbackend/protos/gen/user;userb\x06proto3"

var (
	file_user_proto_rawDescOnce sync.Once
	file_user_proto_rawDescData []byte
)

func file_user_proto_rawDescGZIP() []byte {
	file_user_proto_rawDescOnce.Do(func() {
		file_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_proto_rawDesc), len(file_user_proto_rawDesc)))
	})
	return file_user_proto_rawDescData
}

var file_user_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_user_proto_goTypes = []any{
	(*GetUserRequest)(nil),                    // 0: hopen.user.v1.GetUserRequest
	(*GetUserResponse)(nil),                   // 1: hopen.user.v1.GetUserResponse
	(*UpdateUserRequest)(nil),                 // 2: hopen.user.v1.UpdateUserRequest
	(*UpdateUserResponse)(nil),                // 3: hopen.user.v1.UpdateUserResponse
	(*SearchUsersRequest)(nil),                // 4: hopen.user.v1.SearchUsersRequest
	(*SearchUsersResponse)(nil),               // 5: hopen.user.v1.SearchUsersResponse
	(*CheckUsernameAvailabilityRequest)(nil),  // 6: hopen.user.v1.CheckUsernameAvailabilityRequest
	(*CheckUsernameAvailabilityResponse)(nil), // 7: hopen.user.v1.CheckUsernameAvailabilityResponse
	(*CheckEmailAvailabilityRequest)(nil),     // 8: hopen.user.v1.CheckEmailAvailabilityRequest
	(*CheckEmailAvailabilityResponse)(nil),    // 9: hopen.user.v1.CheckEmailAvailabilityResponse
	(*UserPrivacySettings)(nil),               // 10: hopen.user.v1.UserPrivacySettings
	(*GetUserPrivacySettingsRequest)(nil),     // 11: hopen.user.v1.GetUserPrivacySettingsRequest
	(*GetUserPrivacySettingsResponse)(nil),    // 12: hopen.user.v1.GetUserPrivacySettingsResponse
	(*UpdateUserPrivacySettingsRequest)(nil),  // 13: hopen.user.v1.UpdateUserPrivacySettingsRequest
	(*UpdateUserPrivacySettingsResponse)(nil), // 14: hopen.user.v1.UpdateUserPrivacySettingsResponse
	(*DeleteUserRequest)(nil),                 // 15: hopen.user.v1.DeleteUserRequest
	(*DeleteUserResponse)(nil),                // 16: hopen.user.v1.DeleteUserResponse
	(*UserStats)(nil),                         // 17: hopen.user.v1.UserStats
	(*GetUserStatsRequest)(nil),               // 18: hopen.user.v1.GetUserStatsRequest
	(*GetUserStatsResponse)(nil),              // 19: hopen.user.v1.GetUserStatsResponse
	(*BlockUserRequest)(nil),                  // 20: hopen.user.v1.BlockUserRequest
	(*BlockUserResponse)(nil),                 // 21: hopen.user.v1.BlockUserResponse
	(*UnblockUserRequest)(nil),                // 22: hopen.user.v1.UnblockUserRequest
	(*UnblockUserResponse)(nil),               // 23: hopen.user.v1.UnblockUserResponse
	(*GetBlockedUsersRequest)(nil),            // 24: hopen.user.v1.GetBlockedUsersRequest
	(*GetBlockedUsersResponse)(nil),           // 25: hopen.user.v1.GetBlockedUsersResponse
	(*common.User)(nil),                       // 26: hopen.common.v1.User
	(*common.ApiResponse)(nil),                // 27: hopen.common.v1.ApiResponse
	(*common.Pagination)(nil),                 // 28: hopen.common.v1.Pagination
	(*timestamppb.Timestamp)(nil),             // 29: google.protobuf.Timestamp
}
var file_user_proto_depIdxs = []int32{
	26, // 0: hopen.user.v1.GetUserResponse.user:type_name -> hopen.common.v1.User
	27, // 1: hopen.user.v1.GetUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 2: hopen.user.v1.UpdateUserResponse.user:type_name -> hopen.common.v1.User
	27, // 3: hopen.user.v1.UpdateUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 4: hopen.user.v1.SearchUsersResponse.users:type_name -> hopen.common.v1.User
	28, // 5: hopen.user.v1.SearchUsersResponse.pagination:type_name -> hopen.common.v1.Pagination
	27, // 6: hopen.user.v1.SearchUsersResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 7: hopen.user.v1.CheckUsernameAvailabilityResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 8: hopen.user.v1.CheckEmailAvailabilityResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	29, // 9: hopen.user.v1.UserPrivacySettings.updated_at:type_name -> google.protobuf.Timestamp
	10, // 10: hopen.user.v1.GetUserPrivacySettingsResponse.settings:type_name -> hopen.user.v1.UserPrivacySettings
	27, // 11: hopen.user.v1.GetUserPrivacySettingsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	10, // 12: hopen.user.v1.UpdateUserPrivacySettingsResponse.settings:type_name -> hopen.user.v1.UserPrivacySettings
	27, // 13: hopen.user.v1.UpdateUserPrivacySettingsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 14: hopen.user.v1.DeleteUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	29, // 15: hopen.user.v1.UserStats.last_active_at:type_name -> google.protobuf.Timestamp
	29, // 16: hopen.user.v1.UserStats.created_at:type_name -> google.protobuf.Timestamp
	17, // 17: hopen.user.v1.GetUserStatsResponse.stats:type_name -> hopen.user.v1.UserStats
	27, // 18: hopen.user.v1.GetUserStatsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 19: hopen.user.v1.BlockUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 20: hopen.user.v1.UnblockUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 21: hopen.user.v1.GetBlockedUsersResponse.blocked_users:type_name -> hopen.common.v1.User
	28, // 22: hopen.user.v1.GetBlockedUsersResponse.pagination:type_name -> hopen.common.v1.Pagination
	27, // 23: hopen.user.v1.GetBlockedUsersResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 24: hopen.user.v1.UserService.GetUser:input_type -> hopen.user.v1.GetUserRequest
	2,  // 25: hopen.user.v1.UserService.UpdateUser:input_type -> hopen.user.v1.UpdateUserRequest
	4,  // 26: hopen.user.v1.UserService.SearchUsers:input_type -> hopen.user.v1.SearchUsersRequest
	6,  // 27: hopen.user.v1.UserService.CheckUsernameAvailability:input_type -> hopen.user.v1.CheckUsernameAvailabilityRequest
	8,  // 28: hopen.user.v1.UserService.CheckEmailAvailability:input_type -> hopen.user.v1.CheckEmailAvailabilityRequest
	11, // 29: hopen.user.v1.UserService.GetUserPrivacySettings:input_type -> hopen.user.v1.GetUserPrivacySettingsRequest
	13, // 30: hopen.user.v1.UserService.UpdateUserPrivacySettings:input_type -> hopen.user.v1.UpdateUserPrivacySettingsRequest
	15, // 31: hopen.user.v1.UserService.DeleteUser:input_type -> hopen.user.v1.DeleteUserRequest
	18, // 32: hopen.user.v1.UserService.GetUserStats:input_type -> hopen.user.v1.GetUserStatsRequest
	20, // 33: hopen.user.v1.UserService.BlockUser:input_type -> hopen.user.v1.BlockUserRequest
	22, // 34: hopen.user.v1.UserService.UnblockUser:input_type -> hopen.user.v1.UnblockUserRequest
	24, // 35: hopen.user.v1.UserService.GetBlockedUsers:input_type -> hopen.user.v1.GetBlockedUsersRequest
	1,  // 36: hopen.user.v1.UserService.GetUser:output_type -> hopen.user.v1.GetUserResponse
	3,  // 37: hopen.user.v1.UserService.UpdateUser:output_type -> hopen.user.v1.UpdateUserResponse
	5,  // 38: hopen.user.v1.UserService.SearchUsers:output_type -> hopen.user.v1.SearchUsersResponse
	7,  // 39: hopen.user.v1.UserService.CheckUsernameAvailability:output_type -> hopen.user.v1.CheckUsernameAvailabilityResponse
	9,  // 40: hopen.user.v1.UserService.CheckEmailAvailability:output_type -> hopen.user.v1.CheckEmailAvailabilityResponse
	12, // 41: hopen.user.v1.UserService.GetUserPrivacySettings:output_type -> hopen.user.v1.GetUserPrivacySettingsResponse
	14, // 42: hopen.user.v1.UserService.UpdateUserPrivacySettings:output_type -> hopen.user.v1.UpdateUserPrivacySettingsResponse
	16, // 43: hopen.user.v1.UserService.DeleteUser:output_type -> hopen.user.v1.DeleteUserResponse
	19, // 44: hopen.user.v1.UserService.GetUserStats:output_type -> hopen.user.v1.GetUserStatsResponse
	21, // 45: hopen.user.v1.UserService.BlockUser:output_type -> hopen.user.v1.BlockUserResponse
	23, // 46: hopen.user.v1.UserService.UnblockUser:output_type -> hopen.user.v1.UnblockUserResponse
	25, // 47: hopen.user.v1.UserService.GetBlockedUsers:output_type -> hopen.user.v1.GetBlockedUsersResponse
	36, // [36:48] is the sub-list for method output_type
	24, // [24:36] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_user_proto_init() }
func file_user_proto_init() {
	if File_user_proto != nil {
		return
	}
	file_user_proto_msgTypes[2].OneofWrappers = []any{}
	file_user_proto_msgTypes[13].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_proto_rawDesc), len(file_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_proto_goTypes,
		DependencyIndexes: file_user_proto_depIdxs,
		MessageInfos:      file_user_proto_msgTypes,
	}.Build()
	File_user_proto = out.File
	file_user_proto_goTypes = nil
	file_user_proto_depIdxs = nil
}
