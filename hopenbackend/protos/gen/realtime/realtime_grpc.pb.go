// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: realtime.proto

package realtime

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RealtimeService_SendMessage_FullMethodName             = "/hopen.realtime.v1.RealtimeService/SendMessage"
	RealtimeService_GetChatMessages_FullMethodName         = "/hopen.realtime.v1.RealtimeService/GetChatMessages"
	RealtimeService_EditMessage_FullMethodName             = "/hopen.realtime.v1.RealtimeService/EditMessage"
	RealtimeService_DeleteMessage_FullMethodName           = "/hopen.realtime.v1.RealtimeService/DeleteMessage"
	RealtimeService_SendTypingIndicator_FullMethodName     = "/hopen.realtime.v1.RealtimeService/SendTypingIndicator"
	RealtimeService_GetConversations_FullMethodName        = "/hopen.realtime.v1.RealtimeService/GetConversations"
	RealtimeService_SendDirectMessage_FullMethodName       = "/hopen.realtime.v1.RealtimeService/SendDirectMessage"
	RealtimeService_GetConversationMessages_FullMethodName = "/hopen.realtime.v1.RealtimeService/GetConversationMessages"
	RealtimeService_SearchChatMessages_FullMethodName      = "/hopen.realtime.v1.RealtimeService/SearchChatMessages"
	RealtimeService_MarkMessageAsRead_FullMethodName       = "/hopen.realtime.v1.RealtimeService/MarkMessageAsRead"
)

// RealtimeServiceClient is the client API for RealtimeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Realtime service for chat and real-time messaging
type RealtimeServiceClient interface {
	// Send a message to a bubble
	SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error)
	// Get chat messages for a bubble
	GetChatMessages(ctx context.Context, in *GetChatMessagesRequest, opts ...grpc.CallOption) (*GetChatMessagesResponse, error)
	// Edit a message
	EditMessage(ctx context.Context, in *EditMessageRequest, opts ...grpc.CallOption) (*EditMessageResponse, error)
	// Delete a message
	DeleteMessage(ctx context.Context, in *DeleteMessageRequest, opts ...grpc.CallOption) (*DeleteMessageResponse, error)
	// Send typing indicator
	SendTypingIndicator(ctx context.Context, in *SendTypingIndicatorRequest, opts ...grpc.CallOption) (*SendTypingIndicatorResponse, error)
	// Get conversations
	GetConversations(ctx context.Context, in *GetConversationsRequest, opts ...grpc.CallOption) (*GetConversationsResponse, error)
	// Send direct message
	SendDirectMessage(ctx context.Context, in *SendDirectMessageRequest, opts ...grpc.CallOption) (*SendDirectMessageResponse, error)
	// Get conversation messages
	GetConversationMessages(ctx context.Context, in *GetConversationMessagesRequest, opts ...grpc.CallOption) (*GetConversationMessagesResponse, error)
	// Search chat messages
	SearchChatMessages(ctx context.Context, in *SearchChatMessagesRequest, opts ...grpc.CallOption) (*SearchChatMessagesResponse, error)
	// Mark message as read
	MarkMessageAsRead(ctx context.Context, in *MarkMessageAsReadRequest, opts ...grpc.CallOption) (*MarkMessageAsReadResponse, error)
}

type realtimeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRealtimeServiceClient(cc grpc.ClientConnInterface) RealtimeServiceClient {
	return &realtimeServiceClient{cc}
}

func (c *realtimeServiceClient) SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendMessageResponse)
	err := c.cc.Invoke(ctx, RealtimeService_SendMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) GetChatMessages(ctx context.Context, in *GetChatMessagesRequest, opts ...grpc.CallOption) (*GetChatMessagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChatMessagesResponse)
	err := c.cc.Invoke(ctx, RealtimeService_GetChatMessages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) EditMessage(ctx context.Context, in *EditMessageRequest, opts ...grpc.CallOption) (*EditMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EditMessageResponse)
	err := c.cc.Invoke(ctx, RealtimeService_EditMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) DeleteMessage(ctx context.Context, in *DeleteMessageRequest, opts ...grpc.CallOption) (*DeleteMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteMessageResponse)
	err := c.cc.Invoke(ctx, RealtimeService_DeleteMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) SendTypingIndicator(ctx context.Context, in *SendTypingIndicatorRequest, opts ...grpc.CallOption) (*SendTypingIndicatorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendTypingIndicatorResponse)
	err := c.cc.Invoke(ctx, RealtimeService_SendTypingIndicator_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) GetConversations(ctx context.Context, in *GetConversationsRequest, opts ...grpc.CallOption) (*GetConversationsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConversationsResponse)
	err := c.cc.Invoke(ctx, RealtimeService_GetConversations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) SendDirectMessage(ctx context.Context, in *SendDirectMessageRequest, opts ...grpc.CallOption) (*SendDirectMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendDirectMessageResponse)
	err := c.cc.Invoke(ctx, RealtimeService_SendDirectMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) GetConversationMessages(ctx context.Context, in *GetConversationMessagesRequest, opts ...grpc.CallOption) (*GetConversationMessagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConversationMessagesResponse)
	err := c.cc.Invoke(ctx, RealtimeService_GetConversationMessages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) SearchChatMessages(ctx context.Context, in *SearchChatMessagesRequest, opts ...grpc.CallOption) (*SearchChatMessagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchChatMessagesResponse)
	err := c.cc.Invoke(ctx, RealtimeService_SearchChatMessages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realtimeServiceClient) MarkMessageAsRead(ctx context.Context, in *MarkMessageAsReadRequest, opts ...grpc.CallOption) (*MarkMessageAsReadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkMessageAsReadResponse)
	err := c.cc.Invoke(ctx, RealtimeService_MarkMessageAsRead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RealtimeServiceServer is the server API for RealtimeService service.
// All implementations must embed UnimplementedRealtimeServiceServer
// for forward compatibility.
//
// Realtime service for chat and real-time messaging
type RealtimeServiceServer interface {
	// Send a message to a bubble
	SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error)
	// Get chat messages for a bubble
	GetChatMessages(context.Context, *GetChatMessagesRequest) (*GetChatMessagesResponse, error)
	// Edit a message
	EditMessage(context.Context, *EditMessageRequest) (*EditMessageResponse, error)
	// Delete a message
	DeleteMessage(context.Context, *DeleteMessageRequest) (*DeleteMessageResponse, error)
	// Send typing indicator
	SendTypingIndicator(context.Context, *SendTypingIndicatorRequest) (*SendTypingIndicatorResponse, error)
	// Get conversations
	GetConversations(context.Context, *GetConversationsRequest) (*GetConversationsResponse, error)
	// Send direct message
	SendDirectMessage(context.Context, *SendDirectMessageRequest) (*SendDirectMessageResponse, error)
	// Get conversation messages
	GetConversationMessages(context.Context, *GetConversationMessagesRequest) (*GetConversationMessagesResponse, error)
	// Search chat messages
	SearchChatMessages(context.Context, *SearchChatMessagesRequest) (*SearchChatMessagesResponse, error)
	// Mark message as read
	MarkMessageAsRead(context.Context, *MarkMessageAsReadRequest) (*MarkMessageAsReadResponse, error)
	mustEmbedUnimplementedRealtimeServiceServer()
}

// UnimplementedRealtimeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRealtimeServiceServer struct{}

func (UnimplementedRealtimeServiceServer) SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedRealtimeServiceServer) GetChatMessages(context.Context, *GetChatMessagesRequest) (*GetChatMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChatMessages not implemented")
}
func (UnimplementedRealtimeServiceServer) EditMessage(context.Context, *EditMessageRequest) (*EditMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditMessage not implemented")
}
func (UnimplementedRealtimeServiceServer) DeleteMessage(context.Context, *DeleteMessageRequest) (*DeleteMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMessage not implemented")
}
func (UnimplementedRealtimeServiceServer) SendTypingIndicator(context.Context, *SendTypingIndicatorRequest) (*SendTypingIndicatorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTypingIndicator not implemented")
}
func (UnimplementedRealtimeServiceServer) GetConversations(context.Context, *GetConversationsRequest) (*GetConversationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConversations not implemented")
}
func (UnimplementedRealtimeServiceServer) SendDirectMessage(context.Context, *SendDirectMessageRequest) (*SendDirectMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendDirectMessage not implemented")
}
func (UnimplementedRealtimeServiceServer) GetConversationMessages(context.Context, *GetConversationMessagesRequest) (*GetConversationMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConversationMessages not implemented")
}
func (UnimplementedRealtimeServiceServer) SearchChatMessages(context.Context, *SearchChatMessagesRequest) (*SearchChatMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchChatMessages not implemented")
}
func (UnimplementedRealtimeServiceServer) MarkMessageAsRead(context.Context, *MarkMessageAsReadRequest) (*MarkMessageAsReadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkMessageAsRead not implemented")
}
func (UnimplementedRealtimeServiceServer) mustEmbedUnimplementedRealtimeServiceServer() {}
func (UnimplementedRealtimeServiceServer) testEmbeddedByValue()                         {}

// UnsafeRealtimeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RealtimeServiceServer will
// result in compilation errors.
type UnsafeRealtimeServiceServer interface {
	mustEmbedUnimplementedRealtimeServiceServer()
}

func RegisterRealtimeServiceServer(s grpc.ServiceRegistrar, srv RealtimeServiceServer) {
	// If the following call pancis, it indicates UnimplementedRealtimeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RealtimeService_ServiceDesc, srv)
}

func _RealtimeService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_SendMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).SendMessage(ctx, req.(*SendMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_GetChatMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).GetChatMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_GetChatMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).GetChatMessages(ctx, req.(*GetChatMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_EditMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).EditMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_EditMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).EditMessage(ctx, req.(*EditMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_DeleteMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).DeleteMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_DeleteMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).DeleteMessage(ctx, req.(*DeleteMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_SendTypingIndicator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTypingIndicatorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).SendTypingIndicator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_SendTypingIndicator_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).SendTypingIndicator(ctx, req.(*SendTypingIndicatorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_GetConversations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConversationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).GetConversations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_GetConversations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).GetConversations(ctx, req.(*GetConversationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_SendDirectMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendDirectMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).SendDirectMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_SendDirectMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).SendDirectMessage(ctx, req.(*SendDirectMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_GetConversationMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConversationMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).GetConversationMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_GetConversationMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).GetConversationMessages(ctx, req.(*GetConversationMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_SearchChatMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchChatMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).SearchChatMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_SearchChatMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).SearchChatMessages(ctx, req.(*SearchChatMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealtimeService_MarkMessageAsRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkMessageAsReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealtimeServiceServer).MarkMessageAsRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RealtimeService_MarkMessageAsRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealtimeServiceServer).MarkMessageAsRead(ctx, req.(*MarkMessageAsReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RealtimeService_ServiceDesc is the grpc.ServiceDesc for RealtimeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RealtimeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.realtime.v1.RealtimeService",
	HandlerType: (*RealtimeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendMessage",
			Handler:    _RealtimeService_SendMessage_Handler,
		},
		{
			MethodName: "GetChatMessages",
			Handler:    _RealtimeService_GetChatMessages_Handler,
		},
		{
			MethodName: "EditMessage",
			Handler:    _RealtimeService_EditMessage_Handler,
		},
		{
			MethodName: "DeleteMessage",
			Handler:    _RealtimeService_DeleteMessage_Handler,
		},
		{
			MethodName: "SendTypingIndicator",
			Handler:    _RealtimeService_SendTypingIndicator_Handler,
		},
		{
			MethodName: "GetConversations",
			Handler:    _RealtimeService_GetConversations_Handler,
		},
		{
			MethodName: "SendDirectMessage",
			Handler:    _RealtimeService_SendDirectMessage_Handler,
		},
		{
			MethodName: "GetConversationMessages",
			Handler:    _RealtimeService_GetConversationMessages_Handler,
		},
		{
			MethodName: "SearchChatMessages",
			Handler:    _RealtimeService_SearchChatMessages_Handler,
		},
		{
			MethodName: "MarkMessageAsRead",
			Handler:    _RealtimeService_MarkMessageAsRead_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "realtime.proto",
}
