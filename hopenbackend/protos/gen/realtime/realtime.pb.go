// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: realtime.proto

package realtime

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	sync "hopenbackend/protos/gen/sync"
	reflect "reflect"
	sync1 "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Message represents a chat message
type Message struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageId     string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	BubbleId      string                 `protobuf:"bytes,2,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	SenderId      string                 `protobuf:"bytes,3,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	Content       string                 `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	MessageType   string                 `protobuf:"bytes,5,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	MediaUrl      *string                `protobuf:"bytes,6,opt,name=media_url,json=mediaUrl,proto3,oneof" json:"media_url,omitempty"`
	ReplyToId     *string                `protobuf:"bytes,7,opt,name=reply_to_id,json=replyToId,proto3,oneof" json:"reply_to_id,omitempty"`
	IsEdited      bool                   `protobuf:"varint,8,opt,name=is_edited,json=isEdited,proto3" json:"is_edited,omitempty"`
	IsDeleted     bool                   `protobuf:"varint,9,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_realtime_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{0}
}

func (x *Message) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *Message) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *Message) GetSenderId() string {
	if x != nil {
		return x.SenderId
	}
	return ""
}

func (x *Message) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Message) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *Message) GetMediaUrl() string {
	if x != nil && x.MediaUrl != nil {
		return *x.MediaUrl
	}
	return ""
}

func (x *Message) GetReplyToId() string {
	if x != nil && x.ReplyToId != nil {
		return *x.ReplyToId
	}
	return ""
}

func (x *Message) GetIsEdited() bool {
	if x != nil {
		return x.IsEdited
	}
	return false
}

func (x *Message) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *Message) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Message) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// ConversationMessage represents a direct message
type ConversationMessage struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MessageId      string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	ConversationId string                 `protobuf:"bytes,2,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	SenderId       string                 `protobuf:"bytes,3,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	RecipientId    string                 `protobuf:"bytes,4,opt,name=recipient_id,json=recipientId,proto3" json:"recipient_id,omitempty"`
	Content        string                 `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	MessageType    string                 `protobuf:"bytes,6,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	MediaUrl       *string                `protobuf:"bytes,7,opt,name=media_url,json=mediaUrl,proto3,oneof" json:"media_url,omitempty"`
	ReplyToId      *string                `protobuf:"bytes,8,opt,name=reply_to_id,json=replyToId,proto3,oneof" json:"reply_to_id,omitempty"`
	IsEdited       bool                   `protobuf:"varint,9,opt,name=is_edited,json=isEdited,proto3" json:"is_edited,omitempty"`
	IsDeleted      bool                   `protobuf:"varint,10,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	IsRead         bool                   `protobuf:"varint,11,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ConversationMessage) Reset() {
	*x = ConversationMessage{}
	mi := &file_realtime_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConversationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationMessage) ProtoMessage() {}

func (x *ConversationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationMessage.ProtoReflect.Descriptor instead.
func (*ConversationMessage) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{1}
}

func (x *ConversationMessage) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ConversationMessage) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *ConversationMessage) GetSenderId() string {
	if x != nil {
		return x.SenderId
	}
	return ""
}

func (x *ConversationMessage) GetRecipientId() string {
	if x != nil {
		return x.RecipientId
	}
	return ""
}

func (x *ConversationMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ConversationMessage) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *ConversationMessage) GetMediaUrl() string {
	if x != nil && x.MediaUrl != nil {
		return *x.MediaUrl
	}
	return ""
}

func (x *ConversationMessage) GetReplyToId() string {
	if x != nil && x.ReplyToId != nil {
		return *x.ReplyToId
	}
	return ""
}

func (x *ConversationMessage) GetIsEdited() bool {
	if x != nil {
		return x.IsEdited
	}
	return false
}

func (x *ConversationMessage) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ConversationMessage) GetIsRead() bool {
	if x != nil {
		return x.IsRead
	}
	return false
}

func (x *ConversationMessage) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ConversationMessage) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// SendMessageRequest represents a message sending request
type SendMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	MessageType   string                 `protobuf:"bytes,3,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	MediaUrl      *string                `protobuf:"bytes,4,opt,name=media_url,json=mediaUrl,proto3,oneof" json:"media_url,omitempty"`
	ReplyToId     *string                `protobuf:"bytes,5,opt,name=reply_to_id,json=replyToId,proto3,oneof" json:"reply_to_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	mi := &file_realtime_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{2}
}

func (x *SendMessageRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *SendMessageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendMessageRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *SendMessageRequest) GetMediaUrl() string {
	if x != nil && x.MediaUrl != nil {
		return *x.MediaUrl
	}
	return ""
}

func (x *SendMessageRequest) GetReplyToId() string {
	if x != nil && x.ReplyToId != nil {
		return *x.ReplyToId
	}
	return ""
}

// SendMessageResponse represents a message sending response
type SendMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *Message               `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageResponse) Reset() {
	*x = SendMessageResponse{}
	mi := &file_realtime_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResponse) ProtoMessage() {}

func (x *SendMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResponse.ProtoReflect.Descriptor instead.
func (*SendMessageResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{3}
}

func (x *SendMessageResponse) GetMessage() *Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *SendMessageResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// GetChatMessagesRequest represents a request to get chat messages
type GetChatMessagesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	PageState     string                 `protobuf:"bytes,3,opt,name=page_state,json=pageState,proto3" json:"page_state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatMessagesRequest) Reset() {
	*x = GetChatMessagesRequest{}
	mi := &file_realtime_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatMessagesRequest) ProtoMessage() {}

func (x *GetChatMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatMessagesRequest.ProtoReflect.Descriptor instead.
func (*GetChatMessagesRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{4}
}

func (x *GetChatMessagesRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *GetChatMessagesRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetChatMessagesRequest) GetPageState() string {
	if x != nil {
		return x.PageState
	}
	return ""
}

// GetChatMessagesResponse represents a response with chat messages
type GetChatMessagesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Messages      []*Message             `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	NextPageState *string                `protobuf:"bytes,3,opt,name=next_page_state,json=nextPageState,proto3,oneof" json:"next_page_state,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatMessagesResponse) Reset() {
	*x = GetChatMessagesResponse{}
	mi := &file_realtime_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatMessagesResponse) ProtoMessage() {}

func (x *GetChatMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatMessagesResponse.ProtoReflect.Descriptor instead.
func (*GetChatMessagesResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{5}
}

func (x *GetChatMessagesResponse) GetMessages() []*Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *GetChatMessagesResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetChatMessagesResponse) GetNextPageState() string {
	if x != nil && x.NextPageState != nil {
		return *x.NextPageState
	}
	return ""
}

func (x *GetChatMessagesResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// EditMessageRequest represents a request to edit a message
type EditMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageId     string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EditMessageRequest) Reset() {
	*x = EditMessageRequest{}
	mi := &file_realtime_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EditMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditMessageRequest) ProtoMessage() {}

func (x *EditMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditMessageRequest.ProtoReflect.Descriptor instead.
func (*EditMessageRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{6}
}

func (x *EditMessageRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *EditMessageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EditMessageRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// EditMessageResponse represents a response to edit a message
type EditMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EditMessageResponse) Reset() {
	*x = EditMessageResponse{}
	mi := &file_realtime_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EditMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditMessageResponse) ProtoMessage() {}

func (x *EditMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditMessageResponse.ProtoReflect.Descriptor instead.
func (*EditMessageResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{7}
}

func (x *EditMessageResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// DeleteMessageRequest represents a request to delete a message
type DeleteMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageId     string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMessageRequest) Reset() {
	*x = DeleteMessageRequest{}
	mi := &file_realtime_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessageRequest) ProtoMessage() {}

func (x *DeleteMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessageRequest.ProtoReflect.Descriptor instead.
func (*DeleteMessageRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteMessageRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *DeleteMessageRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// DeleteMessageResponse represents a response to delete a message
type DeleteMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMessageResponse) Reset() {
	*x = DeleteMessageResponse{}
	mi := &file_realtime_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessageResponse) ProtoMessage() {}

func (x *DeleteMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessageResponse.ProtoReflect.Descriptor instead.
func (*DeleteMessageResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteMessageResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// SendTypingIndicatorRequest represents a typing indicator request
type SendTypingIndicatorRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	IsTyping      bool                   `protobuf:"varint,2,opt,name=is_typing,json=isTyping,proto3" json:"is_typing,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTypingIndicatorRequest) Reset() {
	*x = SendTypingIndicatorRequest{}
	mi := &file_realtime_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTypingIndicatorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTypingIndicatorRequest) ProtoMessage() {}

func (x *SendTypingIndicatorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTypingIndicatorRequest.ProtoReflect.Descriptor instead.
func (*SendTypingIndicatorRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{10}
}

func (x *SendTypingIndicatorRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *SendTypingIndicatorRequest) GetIsTyping() bool {
	if x != nil {
		return x.IsTyping
	}
	return false
}

// SendTypingIndicatorResponse represents a typing indicator response
type SendTypingIndicatorResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTypingIndicatorResponse) Reset() {
	*x = SendTypingIndicatorResponse{}
	mi := &file_realtime_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTypingIndicatorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTypingIndicatorResponse) ProtoMessage() {}

func (x *SendTypingIndicatorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTypingIndicatorResponse.ProtoReflect.Descriptor instead.
func (*SendTypingIndicatorResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{11}
}

func (x *SendTypingIndicatorResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// GetConversationsRequest represents a request to get conversations
type GetConversationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         int32                  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConversationsRequest) Reset() {
	*x = GetConversationsRequest{}
	mi := &file_realtime_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConversationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationsRequest) ProtoMessage() {}

func (x *GetConversationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationsRequest.ProtoReflect.Descriptor instead.
func (*GetConversationsRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{12}
}

func (x *GetConversationsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// GetConversationsResponse represents a response with conversations
type GetConversationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Conversations []*sync.Conversation   `protobuf:"bytes,1,rep,name=conversations,proto3" json:"conversations,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConversationsResponse) Reset() {
	*x = GetConversationsResponse{}
	mi := &file_realtime_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConversationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationsResponse) ProtoMessage() {}

func (x *GetConversationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationsResponse.ProtoReflect.Descriptor instead.
func (*GetConversationsResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{13}
}

func (x *GetConversationsResponse) GetConversations() []*sync.Conversation {
	if x != nil {
		return x.Conversations
	}
	return nil
}

func (x *GetConversationsResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetConversationsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// SendDirectMessageRequest represents a direct message request
type SendDirectMessageRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ConversationId string                 `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Content        string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	MessageType    string                 `protobuf:"bytes,3,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	MediaUrl       *string                `protobuf:"bytes,4,opt,name=media_url,json=mediaUrl,proto3,oneof" json:"media_url,omitempty"`
	ReplyToId      *string                `protobuf:"bytes,5,opt,name=reply_to_id,json=replyToId,proto3,oneof" json:"reply_to_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SendDirectMessageRequest) Reset() {
	*x = SendDirectMessageRequest{}
	mi := &file_realtime_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendDirectMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendDirectMessageRequest) ProtoMessage() {}

func (x *SendDirectMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendDirectMessageRequest.ProtoReflect.Descriptor instead.
func (*SendDirectMessageRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{14}
}

func (x *SendDirectMessageRequest) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *SendDirectMessageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendDirectMessageRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *SendDirectMessageRequest) GetMediaUrl() string {
	if x != nil && x.MediaUrl != nil {
		return *x.MediaUrl
	}
	return ""
}

func (x *SendDirectMessageRequest) GetReplyToId() string {
	if x != nil && x.ReplyToId != nil {
		return *x.ReplyToId
	}
	return ""
}

// SendDirectMessageResponse represents a direct message response
type SendDirectMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *sync.Message          `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendDirectMessageResponse) Reset() {
	*x = SendDirectMessageResponse{}
	mi := &file_realtime_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendDirectMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendDirectMessageResponse) ProtoMessage() {}

func (x *SendDirectMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendDirectMessageResponse.ProtoReflect.Descriptor instead.
func (*SendDirectMessageResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{15}
}

func (x *SendDirectMessageResponse) GetMessage() *sync.Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *SendDirectMessageResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// GetConversationMessagesRequest represents a request to get conversation messages
type GetConversationMessagesRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ConversationId string                 `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Limit          int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	PageState      *string                `protobuf:"bytes,3,opt,name=page_state,json=pageState,proto3,oneof" json:"page_state,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetConversationMessagesRequest) Reset() {
	*x = GetConversationMessagesRequest{}
	mi := &file_realtime_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConversationMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationMessagesRequest) ProtoMessage() {}

func (x *GetConversationMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationMessagesRequest.ProtoReflect.Descriptor instead.
func (*GetConversationMessagesRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{16}
}

func (x *GetConversationMessagesRequest) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *GetConversationMessagesRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetConversationMessagesRequest) GetPageState() string {
	if x != nil && x.PageState != nil {
		return *x.PageState
	}
	return ""
}

// GetConversationMessagesResponse represents a response with conversation messages
type GetConversationMessagesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Messages      []*sync.Message        `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	NextPageState *string                `protobuf:"bytes,3,opt,name=next_page_state,json=nextPageState,proto3,oneof" json:"next_page_state,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConversationMessagesResponse) Reset() {
	*x = GetConversationMessagesResponse{}
	mi := &file_realtime_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConversationMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationMessagesResponse) ProtoMessage() {}

func (x *GetConversationMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationMessagesResponse.ProtoReflect.Descriptor instead.
func (*GetConversationMessagesResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{17}
}

func (x *GetConversationMessagesResponse) GetMessages() []*sync.Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *GetConversationMessagesResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetConversationMessagesResponse) GetNextPageState() string {
	if x != nil && x.NextPageState != nil {
		return *x.NextPageState
	}
	return ""
}

func (x *GetConversationMessagesResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// SearchChatMessagesRequest represents a request to search chat messages
type SearchChatMessagesRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ConversationId string                 `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Query          string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Limit          int32                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchChatMessagesRequest) Reset() {
	*x = SearchChatMessagesRequest{}
	mi := &file_realtime_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchChatMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchChatMessagesRequest) ProtoMessage() {}

func (x *SearchChatMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchChatMessagesRequest.ProtoReflect.Descriptor instead.
func (*SearchChatMessagesRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{18}
}

func (x *SearchChatMessagesRequest) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *SearchChatMessagesRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchChatMessagesRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// SearchChatMessagesResponse represents a response with search results
type SearchChatMessagesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Messages      []*Message             `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchChatMessagesResponse) Reset() {
	*x = SearchChatMessagesResponse{}
	mi := &file_realtime_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchChatMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchChatMessagesResponse) ProtoMessage() {}

func (x *SearchChatMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchChatMessagesResponse.ProtoReflect.Descriptor instead.
func (*SearchChatMessagesResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{19}
}

func (x *SearchChatMessagesResponse) GetMessages() []*Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *SearchChatMessagesResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SearchChatMessagesResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// MarkMessageAsReadRequest represents a request to mark a message as read
type MarkMessageAsReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageId     string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkMessageAsReadRequest) Reset() {
	*x = MarkMessageAsReadRequest{}
	mi := &file_realtime_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkMessageAsReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkMessageAsReadRequest) ProtoMessage() {}

func (x *MarkMessageAsReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkMessageAsReadRequest.ProtoReflect.Descriptor instead.
func (*MarkMessageAsReadRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{20}
}

func (x *MarkMessageAsReadRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

// MarkMessageAsReadResponse represents a response to mark a message as read
type MarkMessageAsReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkMessageAsReadResponse) Reset() {
	*x = MarkMessageAsReadResponse{}
	mi := &file_realtime_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkMessageAsReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkMessageAsReadResponse) ProtoMessage() {}

func (x *MarkMessageAsReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkMessageAsReadResponse.ProtoReflect.Descriptor instead.
func (*MarkMessageAsReadResponse) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{21}
}

func (x *MarkMessageAsReadResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

var File_realtime_proto protoreflect.FileDescriptor

const file_realtime_proto_rawDesc = "" +
	"\n" +
	"\x0erealtime.proto\x12\x11hopen.realtime.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\x1a\n" +
	"sync.proto\"\xb6\x03\n" +
	"\aMessage\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12\x1b\n" +
	"\tbubble_id\x18\x02 \x01(\tR\bbubbleId\x12\x1b\n" +
	"\tsender_id\x18\x03 \x01(\tR\bsenderId\x12\x18\n" +
	"\acontent\x18\x04 \x01(\tR\acontent\x12!\n" +
	"\fmessage_type\x18\x05 \x01(\tR\vmessageType\x12 \n" +
	"\tmedia_url\x18\x06 \x01(\tH\x00R\bmediaUrl\x88\x01\x01\x12#\n" +
	"\vreply_to_id\x18\a \x01(\tH\x01R\treplyToId\x88\x01\x01\x12\x1b\n" +
	"\tis_edited\x18\b \x01(\bR\bisEdited\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\t \x01(\bR\tisDeleted\x129\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB\f\n" +
	"\n" +
	"_media_urlB\x0e\n" +
	"\f_reply_to_id\"\x8a\x04\n" +
	"\x13ConversationMessage\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12'\n" +
	"\x0fconversation_id\x18\x02 \x01(\tR\x0econversationId\x12\x1b\n" +
	"\tsender_id\x18\x03 \x01(\tR\bsenderId\x12!\n" +
	"\frecipient_id\x18\x04 \x01(\tR\vrecipientId\x12\x18\n" +
	"\acontent\x18\x05 \x01(\tR\acontent\x12!\n" +
	"\fmessage_type\x18\x06 \x01(\tR\vmessageType\x12 \n" +
	"\tmedia_url\x18\a \x01(\tH\x00R\bmediaUrl\x88\x01\x01\x12#\n" +
	"\vreply_to_id\x18\b \x01(\tH\x01R\treplyToId\x88\x01\x01\x12\x1b\n" +
	"\tis_edited\x18\t \x01(\bR\bisEdited\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\n" +
	" \x01(\bR\tisDeleted\x12\x17\n" +
	"\ais_read\x18\v \x01(\bR\x06isRead\x129\n" +
	"\n" +
	"created_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB\f\n" +
	"\n" +
	"_media_urlB\x0e\n" +
	"\f_reply_to_id\"\xd3\x01\n" +
	"\x12SendMessageRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12!\n" +
	"\fmessage_type\x18\x03 \x01(\tR\vmessageType\x12 \n" +
	"\tmedia_url\x18\x04 \x01(\tH\x00R\bmediaUrl\x88\x01\x01\x12#\n" +
	"\vreply_to_id\x18\x05 \x01(\tH\x01R\treplyToId\x88\x01\x01B\f\n" +
	"\n" +
	"_media_urlB\x0e\n" +
	"\f_reply_to_id\"\x8c\x01\n" +
	"\x13SendMessageResponse\x124\n" +
	"\amessage\x18\x01 \x01(\v2\x1a.hopen.realtime.v1.MessageR\amessage\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"j\n" +
	"\x16GetChatMessagesRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\x1d\n" +
	"\n" +
	"page_state\x18\x03 \x01(\tR\tpageState\"\xe9\x01\n" +
	"\x17GetChatMessagesResponse\x126\n" +
	"\bmessages\x18\x01 \x03(\v2\x1a.hopen.realtime.v1.MessageR\bmessages\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12+\n" +
	"\x0fnext_page_state\x18\x03 \x01(\tH\x00R\rnextPageState\x88\x01\x01\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponseB\x12\n" +
	"\x10_next_page_state\"\x88\x01\n" +
	"\x12EditMessageRequest\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"V\n" +
	"\x13EditMessageResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"p\n" +
	"\x14DeleteMessageRequest\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"X\n" +
	"\x15DeleteMessageResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"V\n" +
	"\x1aSendTypingIndicatorRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x1b\n" +
	"\tis_typing\x18\x02 \x01(\bR\bisTyping\"^\n" +
	"\x1bSendTypingIndicatorResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"/\n" +
	"\x17GetConversationsRequest\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\x05R\x05limit\"\xb4\x01\n" +
	"\x18GetConversationsResponse\x12A\n" +
	"\rconversations\x18\x01 \x03(\v2\x1b.hopen.sync.v1.ConversationR\rconversations\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xe5\x01\n" +
	"\x18SendDirectMessageRequest\x12'\n" +
	"\x0fconversation_id\x18\x01 \x01(\tR\x0econversationId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12!\n" +
	"\fmessage_type\x18\x03 \x01(\tR\vmessageType\x12 \n" +
	"\tmedia_url\x18\x04 \x01(\tH\x00R\bmediaUrl\x88\x01\x01\x12#\n" +
	"\vreply_to_id\x18\x05 \x01(\tH\x01R\treplyToId\x88\x01\x01B\f\n" +
	"\n" +
	"_media_urlB\x0e\n" +
	"\f_reply_to_id\"\x8e\x01\n" +
	"\x19SendDirectMessageResponse\x120\n" +
	"\amessage\x18\x01 \x01(\v2\x16.hopen.sync.v1.MessageR\amessage\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x92\x01\n" +
	"\x1eGetConversationMessagesRequest\x12'\n" +
	"\x0fconversation_id\x18\x01 \x01(\tR\x0econversationId\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\"\n" +
	"\n" +
	"page_state\x18\x03 \x01(\tH\x00R\tpageState\x88\x01\x01B\r\n" +
	"\v_page_state\"\xed\x01\n" +
	"\x1fGetConversationMessagesResponse\x122\n" +
	"\bmessages\x18\x01 \x03(\v2\x16.hopen.sync.v1.MessageR\bmessages\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12+\n" +
	"\x0fnext_page_state\x18\x03 \x01(\tH\x00R\rnextPageState\x88\x01\x01\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponseB\x12\n" +
	"\x10_next_page_state\"p\n" +
	"\x19SearchChatMessagesRequest\x12'\n" +
	"\x0fconversation_id\x18\x01 \x01(\tR\x0econversationId\x12\x14\n" +
	"\x05query\x18\x02 \x01(\tR\x05query\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x05R\x05limit\"\xab\x01\n" +
	"\x1aSearchChatMessagesResponse\x126\n" +
	"\bmessages\x18\x01 \x03(\v2\x1a.hopen.realtime.v1.MessageR\bmessages\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"9\n" +
	"\x18MarkMessageAsReadRequest\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\"\\\n" +
	"\x19MarkMessageAsReadResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse2\xd4\b\n" +
	"\x0fRealtimeService\x12\\\n" +
	"\vSendMessage\x12%.hopen.realtime.v1.SendMessageRequest\x1a&.hopen.realtime.v1.SendMessageResponse\x12h\n" +
	"\x0fGetChatMessages\x12).hopen.realtime.v1.GetChatMessagesRequest\x1a*.hopen.realtime.v1.GetChatMessagesResponse\x12\\\n" +
	"\vEditMessage\x12%.hopen.realtime.v1.EditMessageRequest\x1a&.hopen.realtime.v1.EditMessageResponse\x12b\n" +
	"\rDeleteMessage\x12'.hopen.realtime.v1.DeleteMessageRequest\x1a(.hopen.realtime.v1.DeleteMessageResponse\x12t\n" +
	"\x13SendTypingIndicator\x12-.hopen.realtime.v1.SendTypingIndicatorRequest\x1a..hopen.realtime.v1.SendTypingIndicatorResponse\x12k\n" +
	"\x10GetConversations\x12*.hopen.realtime.v1.GetConversationsRequest\x1a+.hopen.realtime.v1.GetConversationsResponse\x12n\n" +
	"\x11SendDirectMessage\x12+.hopen.realtime.v1.SendDirectMessageRequest\x1a,.hopen.realtime.v1.SendDirectMessageResponse\x12\x80\x01\n" +
	"\x17GetConversationMessages\x121.hopen.realtime.v1.GetConversationMessagesRequest\x1a2.hopen.realtime.v1.GetConversationMessagesResponse\x12q\n" +
	"\x12SearchChatMessages\x12,.hopen.realtime.v1.SearchChatMessagesRequest\x1a-.hopen.realtime.v1.SearchChatMessagesResponse\x12n\n" +
	"\x11MarkMessageAsRead\x12+.hopen.realtime.v1.MarkMessageAsReadRequest\x1a,.hopen.realtime.v1.MarkMessageAsReadResponseBD\n" +
	"\x15com.hopen.realtime.v1P\x01Z)hopenbackend/protos/gen/realtime;realtimeb\x06proto3"

var (
	file_realtime_proto_rawDescOnce sync1.Once
	file_realtime_proto_rawDescData []byte
)

func file_realtime_proto_rawDescGZIP() []byte {
	file_realtime_proto_rawDescOnce.Do(func() {
		file_realtime_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_realtime_proto_rawDesc), len(file_realtime_proto_rawDesc)))
	})
	return file_realtime_proto_rawDescData
}

var file_realtime_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_realtime_proto_goTypes = []any{
	(*Message)(nil),                         // 0: hopen.realtime.v1.Message
	(*ConversationMessage)(nil),             // 1: hopen.realtime.v1.ConversationMessage
	(*SendMessageRequest)(nil),              // 2: hopen.realtime.v1.SendMessageRequest
	(*SendMessageResponse)(nil),             // 3: hopen.realtime.v1.SendMessageResponse
	(*GetChatMessagesRequest)(nil),          // 4: hopen.realtime.v1.GetChatMessagesRequest
	(*GetChatMessagesResponse)(nil),         // 5: hopen.realtime.v1.GetChatMessagesResponse
	(*EditMessageRequest)(nil),              // 6: hopen.realtime.v1.EditMessageRequest
	(*EditMessageResponse)(nil),             // 7: hopen.realtime.v1.EditMessageResponse
	(*DeleteMessageRequest)(nil),            // 8: hopen.realtime.v1.DeleteMessageRequest
	(*DeleteMessageResponse)(nil),           // 9: hopen.realtime.v1.DeleteMessageResponse
	(*SendTypingIndicatorRequest)(nil),      // 10: hopen.realtime.v1.SendTypingIndicatorRequest
	(*SendTypingIndicatorResponse)(nil),     // 11: hopen.realtime.v1.SendTypingIndicatorResponse
	(*GetConversationsRequest)(nil),         // 12: hopen.realtime.v1.GetConversationsRequest
	(*GetConversationsResponse)(nil),        // 13: hopen.realtime.v1.GetConversationsResponse
	(*SendDirectMessageRequest)(nil),        // 14: hopen.realtime.v1.SendDirectMessageRequest
	(*SendDirectMessageResponse)(nil),       // 15: hopen.realtime.v1.SendDirectMessageResponse
	(*GetConversationMessagesRequest)(nil),  // 16: hopen.realtime.v1.GetConversationMessagesRequest
	(*GetConversationMessagesResponse)(nil), // 17: hopen.realtime.v1.GetConversationMessagesResponse
	(*SearchChatMessagesRequest)(nil),       // 18: hopen.realtime.v1.SearchChatMessagesRequest
	(*SearchChatMessagesResponse)(nil),      // 19: hopen.realtime.v1.SearchChatMessagesResponse
	(*MarkMessageAsReadRequest)(nil),        // 20: hopen.realtime.v1.MarkMessageAsReadRequest
	(*MarkMessageAsReadResponse)(nil),       // 21: hopen.realtime.v1.MarkMessageAsReadResponse
	(*timestamppb.Timestamp)(nil),           // 22: google.protobuf.Timestamp
	(*common.ApiResponse)(nil),              // 23: hopen.common.v1.ApiResponse
	(*sync.Conversation)(nil),               // 24: hopen.sync.v1.Conversation
	(*sync.Message)(nil),                    // 25: hopen.sync.v1.Message
}
var file_realtime_proto_depIdxs = []int32{
	22, // 0: hopen.realtime.v1.Message.created_at:type_name -> google.protobuf.Timestamp
	22, // 1: hopen.realtime.v1.Message.updated_at:type_name -> google.protobuf.Timestamp
	22, // 2: hopen.realtime.v1.ConversationMessage.created_at:type_name -> google.protobuf.Timestamp
	22, // 3: hopen.realtime.v1.ConversationMessage.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 4: hopen.realtime.v1.SendMessageResponse.message:type_name -> hopen.realtime.v1.Message
	23, // 5: hopen.realtime.v1.SendMessageResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 6: hopen.realtime.v1.GetChatMessagesResponse.messages:type_name -> hopen.realtime.v1.Message
	23, // 7: hopen.realtime.v1.GetChatMessagesResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	22, // 8: hopen.realtime.v1.EditMessageRequest.created_at:type_name -> google.protobuf.Timestamp
	23, // 9: hopen.realtime.v1.EditMessageResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	22, // 10: hopen.realtime.v1.DeleteMessageRequest.created_at:type_name -> google.protobuf.Timestamp
	23, // 11: hopen.realtime.v1.DeleteMessageResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	23, // 12: hopen.realtime.v1.SendTypingIndicatorResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	24, // 13: hopen.realtime.v1.GetConversationsResponse.conversations:type_name -> hopen.sync.v1.Conversation
	23, // 14: hopen.realtime.v1.GetConversationsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 15: hopen.realtime.v1.SendDirectMessageResponse.message:type_name -> hopen.sync.v1.Message
	23, // 16: hopen.realtime.v1.SendDirectMessageResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 17: hopen.realtime.v1.GetConversationMessagesResponse.messages:type_name -> hopen.sync.v1.Message
	23, // 18: hopen.realtime.v1.GetConversationMessagesResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 19: hopen.realtime.v1.SearchChatMessagesResponse.messages:type_name -> hopen.realtime.v1.Message
	23, // 20: hopen.realtime.v1.SearchChatMessagesResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	23, // 21: hopen.realtime.v1.MarkMessageAsReadResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	2,  // 22: hopen.realtime.v1.RealtimeService.SendMessage:input_type -> hopen.realtime.v1.SendMessageRequest
	4,  // 23: hopen.realtime.v1.RealtimeService.GetChatMessages:input_type -> hopen.realtime.v1.GetChatMessagesRequest
	6,  // 24: hopen.realtime.v1.RealtimeService.EditMessage:input_type -> hopen.realtime.v1.EditMessageRequest
	8,  // 25: hopen.realtime.v1.RealtimeService.DeleteMessage:input_type -> hopen.realtime.v1.DeleteMessageRequest
	10, // 26: hopen.realtime.v1.RealtimeService.SendTypingIndicator:input_type -> hopen.realtime.v1.SendTypingIndicatorRequest
	12, // 27: hopen.realtime.v1.RealtimeService.GetConversations:input_type -> hopen.realtime.v1.GetConversationsRequest
	14, // 28: hopen.realtime.v1.RealtimeService.SendDirectMessage:input_type -> hopen.realtime.v1.SendDirectMessageRequest
	16, // 29: hopen.realtime.v1.RealtimeService.GetConversationMessages:input_type -> hopen.realtime.v1.GetConversationMessagesRequest
	18, // 30: hopen.realtime.v1.RealtimeService.SearchChatMessages:input_type -> hopen.realtime.v1.SearchChatMessagesRequest
	20, // 31: hopen.realtime.v1.RealtimeService.MarkMessageAsRead:input_type -> hopen.realtime.v1.MarkMessageAsReadRequest
	3,  // 32: hopen.realtime.v1.RealtimeService.SendMessage:output_type -> hopen.realtime.v1.SendMessageResponse
	5,  // 33: hopen.realtime.v1.RealtimeService.GetChatMessages:output_type -> hopen.realtime.v1.GetChatMessagesResponse
	7,  // 34: hopen.realtime.v1.RealtimeService.EditMessage:output_type -> hopen.realtime.v1.EditMessageResponse
	9,  // 35: hopen.realtime.v1.RealtimeService.DeleteMessage:output_type -> hopen.realtime.v1.DeleteMessageResponse
	11, // 36: hopen.realtime.v1.RealtimeService.SendTypingIndicator:output_type -> hopen.realtime.v1.SendTypingIndicatorResponse
	13, // 37: hopen.realtime.v1.RealtimeService.GetConversations:output_type -> hopen.realtime.v1.GetConversationsResponse
	15, // 38: hopen.realtime.v1.RealtimeService.SendDirectMessage:output_type -> hopen.realtime.v1.SendDirectMessageResponse
	17, // 39: hopen.realtime.v1.RealtimeService.GetConversationMessages:output_type -> hopen.realtime.v1.GetConversationMessagesResponse
	19, // 40: hopen.realtime.v1.RealtimeService.SearchChatMessages:output_type -> hopen.realtime.v1.SearchChatMessagesResponse
	21, // 41: hopen.realtime.v1.RealtimeService.MarkMessageAsRead:output_type -> hopen.realtime.v1.MarkMessageAsReadResponse
	32, // [32:42] is the sub-list for method output_type
	22, // [22:32] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_realtime_proto_init() }
func file_realtime_proto_init() {
	if File_realtime_proto != nil {
		return
	}
	file_realtime_proto_msgTypes[0].OneofWrappers = []any{}
	file_realtime_proto_msgTypes[1].OneofWrappers = []any{}
	file_realtime_proto_msgTypes[2].OneofWrappers = []any{}
	file_realtime_proto_msgTypes[5].OneofWrappers = []any{}
	file_realtime_proto_msgTypes[14].OneofWrappers = []any{}
	file_realtime_proto_msgTypes[16].OneofWrappers = []any{}
	file_realtime_proto_msgTypes[17].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_realtime_proto_rawDesc), len(file_realtime_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_realtime_proto_goTypes,
		DependencyIndexes: file_realtime_proto_depIdxs,
		MessageInfos:      file_realtime_proto_msgTypes,
	}.Build()
	File_realtime_proto = out.File
	file_realtime_proto_goTypes = nil
	file_realtime_proto_depIdxs = nil
}
