// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: auth.proto

package auth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Register user request
type RegisterUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	FirstName     string                 `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	DateOfBirth   string                 `protobuf:"bytes,6,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"` // Optional, format: YYYY-MM-DD
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterUserRequest) Reset() {
	*x = RegisterUserRequest{}
	mi := &file_auth_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterUserRequest) ProtoMessage() {}

func (x *RegisterUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterUserRequest.ProtoReflect.Descriptor instead.
func (*RegisterUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RegisterUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RegisterUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RegisterUserRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *RegisterUserRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *RegisterUserRequest) GetDateOfBirth() string {
	if x != nil {
		return x.DateOfBirth
	}
	return ""
}

// Register user response
type RegisterUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *UserInfo              `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterUserResponse) Reset() {
	*x = RegisterUserResponse{}
	mi := &file_auth_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterUserResponse) ProtoMessage() {}

func (x *RegisterUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterUserResponse.ProtoReflect.Descriptor instead.
func (*RegisterUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterUserResponse) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RegisterUserResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// User information
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	FirstName     string                 `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,6,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	DateOfBirth   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_auth_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{2}
}

func (x *UserInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UserInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UserInfo) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *UserInfo) GetDateOfBirth() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

// Get user profile request
type GetUserProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserProfileRequest) Reset() {
	*x = GetUserProfileRequest{}
	mi := &file_auth_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileRequest) ProtoMessage() {}

func (x *GetUserProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileRequest.ProtoReflect.Descriptor instead.
func (*GetUserProfileRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserProfileRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get user profile response
type GetUserProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *UserInfo              `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserProfileResponse) Reset() {
	*x = GetUserProfileResponse{}
	mi := &file_auth_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileResponse) ProtoMessage() {}

func (x *GetUserProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileResponse.ProtoReflect.Descriptor instead.
func (*GetUserProfileResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserProfileResponse) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GetUserProfileResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update user profile request
type UpdateUserProfileRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	UserId               string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username             string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	FirstName            string                 `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName             string                 `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	DisplayName          string                 `protobuf:"bytes,5,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	AvatarUrl            string                 `protobuf:"bytes,6,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	AvatarBucketName     string                 `protobuf:"bytes,7,opt,name=avatar_bucket_name,json=avatarBucketName,proto3" json:"avatar_bucket_name,omitempty"`
	AvatarObjectKey      string                 `protobuf:"bytes,8,opt,name=avatar_object_key,json=avatarObjectKey,proto3" json:"avatar_object_key,omitempty"`
	DateOfBirth          string                 `protobuf:"bytes,9,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"` // Optional, format: YYYY-MM-DD
	IsPrivate            bool                   `protobuf:"varint,10,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	NotificationSettings map[string]string      `protobuf:"bytes,11,rep,name=notification_settings,json=notificationSettings,proto3" json:"notification_settings,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UpdateUserProfileRequest) Reset() {
	*x = UpdateUserProfileRequest{}
	mi := &file_auth_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserProfileRequest) ProtoMessage() {}

func (x *UpdateUserProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserProfileRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserProfileRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateUserProfileRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetAvatarBucketName() string {
	if x != nil {
		return x.AvatarBucketName
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetAvatarObjectKey() string {
	if x != nil {
		return x.AvatarObjectKey
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetDateOfBirth() string {
	if x != nil {
		return x.DateOfBirth
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *UpdateUserProfileRequest) GetNotificationSettings() map[string]string {
	if x != nil {
		return x.NotificationSettings
	}
	return nil
}

// Update user profile response
type UpdateUserProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *UserInfo              `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserProfileResponse) Reset() {
	*x = UpdateUserProfileResponse{}
	mi := &file_auth_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserProfileResponse) ProtoMessage() {}

func (x *UpdateUserProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserProfileResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserProfileResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateUserProfileResponse) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdateUserProfileResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Validate session request
type ValidateSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionToken  string                 `protobuf:"bytes,1,opt,name=session_token,json=sessionToken,proto3" json:"session_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateSessionRequest) Reset() {
	*x = ValidateSessionRequest{}
	mi := &file_auth_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSessionRequest) ProtoMessage() {}

func (x *ValidateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSessionRequest.ProtoReflect.Descriptor instead.
func (*ValidateSessionRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{7}
}

func (x *ValidateSessionRequest) GetSessionToken() string {
	if x != nil {
		return x.SessionToken
	}
	return ""
}

// Validate session response
type ValidateSessionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionInfo   *SessionInfo           `protobuf:"bytes,1,opt,name=session_info,json=sessionInfo,proto3" json:"session_info,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateSessionResponse) Reset() {
	*x = ValidateSessionResponse{}
	mi := &file_auth_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSessionResponse) ProtoMessage() {}

func (x *ValidateSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSessionResponse.ProtoReflect.Descriptor instead.
func (*ValidateSessionResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{8}
}

func (x *ValidateSessionResponse) GetSessionInfo() *SessionInfo {
	if x != nil {
		return x.SessionInfo
	}
	return nil
}

func (x *ValidateSessionResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Session information
type SessionInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionId     string                 `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	Permissions   []string               `protobuf:"bytes,6,rep,name=permissions,proto3" json:"permissions,omitempty"`
	Roles         []string               `protobuf:"bytes,7,rep,name=roles,proto3" json:"roles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionInfo) Reset() {
	*x = SessionInfo{}
	mi := &file_auth_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionInfo) ProtoMessage() {}

func (x *SessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionInfo.ProtoReflect.Descriptor instead.
func (*SessionInfo) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{9}
}

func (x *SessionInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SessionInfo) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *SessionInfo) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *SessionInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SessionInfo) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *SessionInfo) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *SessionInfo) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

// Refresh token request
type RefreshTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	mi := &file_auth_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{10}
}

func (x *RefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// Refresh token response
type RefreshTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenResponse) Reset() {
	*x = RefreshTokenResponse{}
	mi := &file_auth_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResponse) ProtoMessage() {}

func (x *RefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*RefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{11}
}

func (x *RefreshTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *RefreshTokenResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *RefreshTokenResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *RefreshTokenResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Logout request
type LogoutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionId     string                 `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutRequest) Reset() {
	*x = LogoutRequest{}
	mi := &file_auth_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutRequest) ProtoMessage() {}

func (x *LogoutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutRequest.ProtoReflect.Descriptor instead.
func (*LogoutRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{12}
}

func (x *LogoutRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *LogoutRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// Logout response
type LogoutResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutResponse) Reset() {
	*x = LogoutResponse{}
	mi := &file_auth_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutResponse) ProtoMessage() {}

func (x *LogoutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutResponse.ProtoReflect.Descriptor instead.
func (*LogoutResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{13}
}

func (x *LogoutResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *LogoutResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get user permissions request
type GetUserPermissionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPermissionsRequest) Reset() {
	*x = GetUserPermissionsRequest{}
	mi := &file_auth_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPermissionsRequest) ProtoMessage() {}

func (x *GetUserPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPermissionsRequest.ProtoReflect.Descriptor instead.
func (*GetUserPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserPermissionsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get user permissions response
type GetUserPermissionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Permissions   []string               `protobuf:"bytes,1,rep,name=permissions,proto3" json:"permissions,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPermissionsResponse) Reset() {
	*x = GetUserPermissionsResponse{}
	mi := &file_auth_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPermissionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPermissionsResponse) ProtoMessage() {}

func (x *GetUserPermissionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPermissionsResponse.ProtoReflect.Descriptor instead.
func (*GetUserPermissionsResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserPermissionsResponse) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *GetUserPermissionsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Has permission request
type HasPermissionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Permission    string                 `protobuf:"bytes,2,opt,name=permission,proto3" json:"permission,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HasPermissionRequest) Reset() {
	*x = HasPermissionRequest{}
	mi := &file_auth_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HasPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasPermissionRequest) ProtoMessage() {}

func (x *HasPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasPermissionRequest.ProtoReflect.Descriptor instead.
func (*HasPermissionRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{16}
}

func (x *HasPermissionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *HasPermissionRequest) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

// Has permission response
type HasPermissionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasPermission bool                   `protobuf:"varint,1,opt,name=has_permission,json=hasPermission,proto3" json:"has_permission,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HasPermissionResponse) Reset() {
	*x = HasPermissionResponse{}
	mi := &file_auth_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HasPermissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasPermissionResponse) ProtoMessage() {}

func (x *HasPermissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasPermissionResponse.ProtoReflect.Descriptor instead.
func (*HasPermissionResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{17}
}

func (x *HasPermissionResponse) GetHasPermission() bool {
	if x != nil {
		return x.HasPermission
	}
	return false
}

func (x *HasPermissionResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get user roles request
type GetUserRolesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRolesRequest) Reset() {
	*x = GetUserRolesRequest{}
	mi := &file_auth_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRolesRequest) ProtoMessage() {}

func (x *GetUserRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRolesRequest.ProtoReflect.Descriptor instead.
func (*GetUserRolesRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{18}
}

func (x *GetUserRolesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get user roles response
type GetUserRolesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Roles         []string               `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRolesResponse) Reset() {
	*x = GetUserRolesResponse{}
	mi := &file_auth_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRolesResponse) ProtoMessage() {}

func (x *GetUserRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRolesResponse.ProtoReflect.Descriptor instead.
func (*GetUserRolesResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{19}
}

func (x *GetUserRolesResponse) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *GetUserRolesResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Validate MQTT connection request
type ValidateMqttConnectionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateMqttConnectionRequest) Reset() {
	*x = ValidateMqttConnectionRequest{}
	mi := &file_auth_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateMqttConnectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateMqttConnectionRequest) ProtoMessage() {}

func (x *ValidateMqttConnectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateMqttConnectionRequest.ProtoReflect.Descriptor instead.
func (*ValidateMqttConnectionRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{20}
}

func (x *ValidateMqttConnectionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateMqttConnectionRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ValidateMqttConnectionRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ValidateMqttConnectionRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// Validate MQTT connection response
type ValidateMqttConnectionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsValid       bool                   `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	AllowedTopics []string               `protobuf:"bytes,2,rep,name=allowed_topics,json=allowedTopics,proto3" json:"allowed_topics,omitempty"`
	TopicQos      map[string]int32       `protobuf:"bytes,3,rep,name=topic_qos,json=topicQos,proto3" json:"topic_qos,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,4,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateMqttConnectionResponse) Reset() {
	*x = ValidateMqttConnectionResponse{}
	mi := &file_auth_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateMqttConnectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateMqttConnectionResponse) ProtoMessage() {}

func (x *ValidateMqttConnectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateMqttConnectionResponse.ProtoReflect.Descriptor instead.
func (*ValidateMqttConnectionResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{21}
}

func (x *ValidateMqttConnectionResponse) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *ValidateMqttConnectionResponse) GetAllowedTopics() []string {
	if x != nil {
		return x.AllowedTopics
	}
	return nil
}

func (x *ValidateMqttConnectionResponse) GetTopicQos() map[string]int32 {
	if x != nil {
		return x.TopicQos
	}
	return nil
}

func (x *ValidateMqttConnectionResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get auth status request
type GetAuthStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthStatusRequest) Reset() {
	*x = GetAuthStatusRequest{}
	mi := &file_auth_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthStatusRequest) ProtoMessage() {}

func (x *GetAuthStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAuthStatusRequest) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{22}
}

func (x *GetAuthStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get auth status response
type GetAuthStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *AuthStatus            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthStatusResponse) Reset() {
	*x = GetAuthStatusResponse{}
	mi := &file_auth_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthStatusResponse) ProtoMessage() {}

func (x *GetAuthStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAuthStatusResponse) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{23}
}

func (x *GetAuthStatusResponse) GetStatus() *AuthStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAuthStatusResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Auth status
type AuthStatus struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IsAuthenticated bool                   `protobuf:"varint,2,opt,name=is_authenticated,json=isAuthenticated,proto3" json:"is_authenticated,omitempty"`
	IsActive        bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	LastLogin       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_login,json=lastLogin,proto3" json:"last_login,omitempty"`
	LoginMethod     string                 `protobuf:"bytes,5,opt,name=login_method,json=loginMethod,proto3" json:"login_method,omitempty"`
	ActiveSessions  []string               `protobuf:"bytes,6,rep,name=active_sessions,json=activeSessions,proto3" json:"active_sessions,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AuthStatus) Reset() {
	*x = AuthStatus{}
	mi := &file_auth_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthStatus) ProtoMessage() {}

func (x *AuthStatus) ProtoReflect() protoreflect.Message {
	mi := &file_auth_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthStatus.ProtoReflect.Descriptor instead.
func (*AuthStatus) Descriptor() ([]byte, []int) {
	return file_auth_proto_rawDescGZIP(), []int{24}
}

func (x *AuthStatus) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AuthStatus) GetIsAuthenticated() bool {
	if x != nil {
		return x.IsAuthenticated
	}
	return false
}

func (x *AuthStatus) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *AuthStatus) GetLastLogin() *timestamppb.Timestamp {
	if x != nil {
		return x.LastLogin
	}
	return nil
}

func (x *AuthStatus) GetLoginMethod() string {
	if x != nil {
		return x.LoginMethod
	}
	return ""
}

func (x *AuthStatus) GetActiveSessions() []string {
	if x != nil {
		return x.ActiveSessions
	}
	return nil
}

var File_auth_proto protoreflect.FileDescriptor

const file_auth_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"auth.proto\x12\rhopen.auth.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"\xc3\x01\n" +
	"\x13RegisterUserRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"first_name\x18\x04 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x05 \x01(\tR\blastName\x12\"\n" +
	"\rdate_of_birth\x18\x06 \x01(\tR\vdateOfBirth\"\x84\x01\n" +
	"\x14RegisterUserResponse\x12+\n" +
	"\x04user\x18\x01 \x01(\v2\x17.hopen.auth.v1.UserInfoR\x04user\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xe7\x01\n" +
	"\bUserInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"first_name\x18\x04 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x05 \x01(\tR\blastName\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x06 \x01(\tR\tavatarUrl\x12>\n" +
	"\rdate_of_birth\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vdateOfBirth\"0\n" +
	"\x15GetUserProfileRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x86\x01\n" +
	"\x16GetUserProfileResponse\x12+\n" +
	"\x04user\x18\x01 \x01(\v2\x17.hopen.auth.v1.UserInfoR\x04user\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xab\x04\n" +
	"\x18UpdateUserProfileRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1d\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x04 \x01(\tR\blastName\x12!\n" +
	"\fdisplay_name\x18\x05 \x01(\tR\vdisplayName\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x06 \x01(\tR\tavatarUrl\x12,\n" +
	"\x12avatar_bucket_name\x18\a \x01(\tR\x10avatarBucketName\x12*\n" +
	"\x11avatar_object_key\x18\b \x01(\tR\x0favatarObjectKey\x12\"\n" +
	"\rdate_of_birth\x18\t \x01(\tR\vdateOfBirth\x12\x1d\n" +
	"\n" +
	"is_private\x18\n" +
	" \x01(\bR\tisPrivate\x12v\n" +
	"\x15notification_settings\x18\v \x03(\v2A.hopen.auth.v1.UpdateUserProfileRequest.NotificationSettingsEntryR\x14notificationSettings\x1aG\n" +
	"\x19NotificationSettingsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x89\x01\n" +
	"\x19UpdateUserProfileResponse\x12+\n" +
	"\x04user\x18\x01 \x01(\v2\x17.hopen.auth.v1.UserInfoR\x04user\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"=\n" +
	"\x16ValidateSessionRequest\x12#\n" +
	"\rsession_token\x18\x01 \x01(\tR\fsessionToken\"\x99\x01\n" +
	"\x17ValidateSessionResponse\x12=\n" +
	"\fsession_info\x18\x01 \x01(\v2\x1a.hopen.auth.v1.SessionInfoR\vsessionInfo\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x90\x02\n" +
	"\vSessionInfo\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"session_id\x18\x02 \x01(\tR\tsessionId\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"expires_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x12 \n" +
	"\vpermissions\x18\x06 \x03(\tR\vpermissions\x12\x14\n" +
	"\x05roles\x18\a \x03(\tR\x05roles\":\n" +
	"\x13RefreshTokenRequest\x12#\n" +
	"\rrefresh_token\x18\x01 \x01(\tR\frefreshToken\"\xda\x01\n" +
	"\x14RefreshTokenResponse\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x02 \x01(\tR\frefreshToken\x129\n" +
	"\n" +
	"expires_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"G\n" +
	"\rLogoutRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"session_id\x18\x02 \x01(\tR\tsessionId\"k\n" +
	"\x0eLogoutResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"4\n" +
	"\x19GetUserPermissionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x7f\n" +
	"\x1aGetUserPermissionsResponse\x12 \n" +
	"\vpermissions\x18\x01 \x03(\tR\vpermissions\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"O\n" +
	"\x14HasPermissionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1e\n" +
	"\n" +
	"permission\x18\x02 \x01(\tR\n" +
	"permission\"\x7f\n" +
	"\x15HasPermissionResponse\x12%\n" +
	"\x0ehas_permission\x18\x01 \x01(\bR\rhasPermission\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\".\n" +
	"\x13GetUserRolesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"m\n" +
	"\x14GetUserRolesResponse\x12\x14\n" +
	"\x05roles\x18\x01 \x03(\tR\x05roles\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x8d\x01\n" +
	"\x1dValidateMqttConnectionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\"\xba\x02\n" +
	"\x1eValidateMqttConnectionResponse\x12\x19\n" +
	"\bis_valid\x18\x01 \x01(\bR\aisValid\x12%\n" +
	"\x0eallowed_topics\x18\x02 \x03(\tR\rallowedTopics\x12X\n" +
	"\ttopic_qos\x18\x03 \x03(\v2;.hopen.auth.v1.ValidateMqttConnectionResponse.TopicQosEntryR\btopicQos\x12?\n" +
	"\fapi_response\x18\x04 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\x1a;\n" +
	"\rTopicQosEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"/\n" +
	"\x14GetAuthStatusRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x8b\x01\n" +
	"\x15GetAuthStatusResponse\x121\n" +
	"\x06status\x18\x01 \x01(\v2\x19.hopen.auth.v1.AuthStatusR\x06status\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xf4\x01\n" +
	"\n" +
	"AuthStatus\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12)\n" +
	"\x10is_authenticated\x18\x02 \x01(\bR\x0fisAuthenticated\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\x129\n" +
	"\n" +
	"last_login\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tlastLogin\x12!\n" +
	"\flogin_method\x18\x05 \x01(\tR\vloginMethod\x12'\n" +
	"\x0factive_sessions\x18\x06 \x03(\tR\x0eactiveSessions2\xa2\b\n" +
	"\vAuthService\x12W\n" +
	"\fRegisterUser\x12\".hopen.auth.v1.RegisterUserRequest\x1a#.hopen.auth.v1.RegisterUserResponse\x12`\n" +
	"\x0fValidateSession\x12%.hopen.auth.v1.ValidateSessionRequest\x1a&.hopen.auth.v1.ValidateSessionResponse\x12W\n" +
	"\fRefreshToken\x12\".hopen.auth.v1.RefreshTokenRequest\x1a#.hopen.auth.v1.RefreshTokenResponse\x12E\n" +
	"\x06Logout\x12\x1c.hopen.auth.v1.LogoutRequest\x1a\x1d.hopen.auth.v1.LogoutResponse\x12]\n" +
	"\x0eGetUserProfile\x12$.hopen.auth.v1.GetUserProfileRequest\x1a%.hopen.auth.v1.GetUserProfileResponse\x12f\n" +
	"\x11UpdateUserProfile\x12'.hopen.auth.v1.UpdateUserProfileRequest\x1a(.hopen.auth.v1.UpdateUserProfileResponse\x12i\n" +
	"\x12GetUserPermissions\x12(.hopen.auth.v1.GetUserPermissionsRequest\x1a).hopen.auth.v1.GetUserPermissionsResponse\x12Z\n" +
	"\rHasPermission\x12#.hopen.auth.v1.HasPermissionRequest\x1a$.hopen.auth.v1.HasPermissionResponse\x12W\n" +
	"\fGetUserRoles\x12\".hopen.auth.v1.GetUserRolesRequest\x1a#.hopen.auth.v1.GetUserRolesResponse\x12u\n" +
	"\x16ValidateMqttConnection\x12,.hopen.auth.v1.ValidateMqttConnectionRequest\x1a-.hopen.auth.v1.ValidateMqttConnectionResponse\x12Z\n" +
	"\rGetAuthStatus\x12#.hopen.auth.v1.GetAuthStatusRequest\x1a$.hopen.auth.v1.GetAuthStatusResponseB8\n" +
	"\x11com.hopen.auth.v1P\x01Z!hopenbackend/protos/gen/auth;authb\x06proto3"

var (
	file_auth_proto_rawDescOnce sync.Once
	file_auth_proto_rawDescData []byte
)

func file_auth_proto_rawDescGZIP() []byte {
	file_auth_proto_rawDescOnce.Do(func() {
		file_auth_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_auth_proto_rawDesc), len(file_auth_proto_rawDesc)))
	})
	return file_auth_proto_rawDescData
}

var file_auth_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_auth_proto_goTypes = []any{
	(*RegisterUserRequest)(nil),            // 0: hopen.auth.v1.RegisterUserRequest
	(*RegisterUserResponse)(nil),           // 1: hopen.auth.v1.RegisterUserResponse
	(*UserInfo)(nil),                       // 2: hopen.auth.v1.UserInfo
	(*GetUserProfileRequest)(nil),          // 3: hopen.auth.v1.GetUserProfileRequest
	(*GetUserProfileResponse)(nil),         // 4: hopen.auth.v1.GetUserProfileResponse
	(*UpdateUserProfileRequest)(nil),       // 5: hopen.auth.v1.UpdateUserProfileRequest
	(*UpdateUserProfileResponse)(nil),      // 6: hopen.auth.v1.UpdateUserProfileResponse
	(*ValidateSessionRequest)(nil),         // 7: hopen.auth.v1.ValidateSessionRequest
	(*ValidateSessionResponse)(nil),        // 8: hopen.auth.v1.ValidateSessionResponse
	(*SessionInfo)(nil),                    // 9: hopen.auth.v1.SessionInfo
	(*RefreshTokenRequest)(nil),            // 10: hopen.auth.v1.RefreshTokenRequest
	(*RefreshTokenResponse)(nil),           // 11: hopen.auth.v1.RefreshTokenResponse
	(*LogoutRequest)(nil),                  // 12: hopen.auth.v1.LogoutRequest
	(*LogoutResponse)(nil),                 // 13: hopen.auth.v1.LogoutResponse
	(*GetUserPermissionsRequest)(nil),      // 14: hopen.auth.v1.GetUserPermissionsRequest
	(*GetUserPermissionsResponse)(nil),     // 15: hopen.auth.v1.GetUserPermissionsResponse
	(*HasPermissionRequest)(nil),           // 16: hopen.auth.v1.HasPermissionRequest
	(*HasPermissionResponse)(nil),          // 17: hopen.auth.v1.HasPermissionResponse
	(*GetUserRolesRequest)(nil),            // 18: hopen.auth.v1.GetUserRolesRequest
	(*GetUserRolesResponse)(nil),           // 19: hopen.auth.v1.GetUserRolesResponse
	(*ValidateMqttConnectionRequest)(nil),  // 20: hopen.auth.v1.ValidateMqttConnectionRequest
	(*ValidateMqttConnectionResponse)(nil), // 21: hopen.auth.v1.ValidateMqttConnectionResponse
	(*GetAuthStatusRequest)(nil),           // 22: hopen.auth.v1.GetAuthStatusRequest
	(*GetAuthStatusResponse)(nil),          // 23: hopen.auth.v1.GetAuthStatusResponse
	(*AuthStatus)(nil),                     // 24: hopen.auth.v1.AuthStatus
	nil,                                    // 25: hopen.auth.v1.UpdateUserProfileRequest.NotificationSettingsEntry
	nil,                                    // 26: hopen.auth.v1.ValidateMqttConnectionResponse.TopicQosEntry
	(*common.ApiResponse)(nil),             // 27: hopen.common.v1.ApiResponse
	(*timestamppb.Timestamp)(nil),          // 28: google.protobuf.Timestamp
}
var file_auth_proto_depIdxs = []int32{
	2,  // 0: hopen.auth.v1.RegisterUserResponse.user:type_name -> hopen.auth.v1.UserInfo
	27, // 1: hopen.auth.v1.RegisterUserResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	28, // 2: hopen.auth.v1.UserInfo.date_of_birth:type_name -> google.protobuf.Timestamp
	2,  // 3: hopen.auth.v1.GetUserProfileResponse.user:type_name -> hopen.auth.v1.UserInfo
	27, // 4: hopen.auth.v1.GetUserProfileResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 5: hopen.auth.v1.UpdateUserProfileRequest.notification_settings:type_name -> hopen.auth.v1.UpdateUserProfileRequest.NotificationSettingsEntry
	2,  // 6: hopen.auth.v1.UpdateUserProfileResponse.user:type_name -> hopen.auth.v1.UserInfo
	27, // 7: hopen.auth.v1.UpdateUserProfileResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	9,  // 8: hopen.auth.v1.ValidateSessionResponse.session_info:type_name -> hopen.auth.v1.SessionInfo
	27, // 9: hopen.auth.v1.ValidateSessionResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	28, // 10: hopen.auth.v1.SessionInfo.created_at:type_name -> google.protobuf.Timestamp
	28, // 11: hopen.auth.v1.SessionInfo.expires_at:type_name -> google.protobuf.Timestamp
	28, // 12: hopen.auth.v1.RefreshTokenResponse.expires_at:type_name -> google.protobuf.Timestamp
	27, // 13: hopen.auth.v1.RefreshTokenResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 14: hopen.auth.v1.LogoutResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 15: hopen.auth.v1.GetUserPermissionsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 16: hopen.auth.v1.HasPermissionResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 17: hopen.auth.v1.GetUserRolesResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 18: hopen.auth.v1.ValidateMqttConnectionResponse.topic_qos:type_name -> hopen.auth.v1.ValidateMqttConnectionResponse.TopicQosEntry
	27, // 19: hopen.auth.v1.ValidateMqttConnectionResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	24, // 20: hopen.auth.v1.GetAuthStatusResponse.status:type_name -> hopen.auth.v1.AuthStatus
	27, // 21: hopen.auth.v1.GetAuthStatusResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	28, // 22: hopen.auth.v1.AuthStatus.last_login:type_name -> google.protobuf.Timestamp
	0,  // 23: hopen.auth.v1.AuthService.RegisterUser:input_type -> hopen.auth.v1.RegisterUserRequest
	7,  // 24: hopen.auth.v1.AuthService.ValidateSession:input_type -> hopen.auth.v1.ValidateSessionRequest
	10, // 25: hopen.auth.v1.AuthService.RefreshToken:input_type -> hopen.auth.v1.RefreshTokenRequest
	12, // 26: hopen.auth.v1.AuthService.Logout:input_type -> hopen.auth.v1.LogoutRequest
	3,  // 27: hopen.auth.v1.AuthService.GetUserProfile:input_type -> hopen.auth.v1.GetUserProfileRequest
	5,  // 28: hopen.auth.v1.AuthService.UpdateUserProfile:input_type -> hopen.auth.v1.UpdateUserProfileRequest
	14, // 29: hopen.auth.v1.AuthService.GetUserPermissions:input_type -> hopen.auth.v1.GetUserPermissionsRequest
	16, // 30: hopen.auth.v1.AuthService.HasPermission:input_type -> hopen.auth.v1.HasPermissionRequest
	18, // 31: hopen.auth.v1.AuthService.GetUserRoles:input_type -> hopen.auth.v1.GetUserRolesRequest
	20, // 32: hopen.auth.v1.AuthService.ValidateMqttConnection:input_type -> hopen.auth.v1.ValidateMqttConnectionRequest
	22, // 33: hopen.auth.v1.AuthService.GetAuthStatus:input_type -> hopen.auth.v1.GetAuthStatusRequest
	1,  // 34: hopen.auth.v1.AuthService.RegisterUser:output_type -> hopen.auth.v1.RegisterUserResponse
	8,  // 35: hopen.auth.v1.AuthService.ValidateSession:output_type -> hopen.auth.v1.ValidateSessionResponse
	11, // 36: hopen.auth.v1.AuthService.RefreshToken:output_type -> hopen.auth.v1.RefreshTokenResponse
	13, // 37: hopen.auth.v1.AuthService.Logout:output_type -> hopen.auth.v1.LogoutResponse
	4,  // 38: hopen.auth.v1.AuthService.GetUserProfile:output_type -> hopen.auth.v1.GetUserProfileResponse
	6,  // 39: hopen.auth.v1.AuthService.UpdateUserProfile:output_type -> hopen.auth.v1.UpdateUserProfileResponse
	15, // 40: hopen.auth.v1.AuthService.GetUserPermissions:output_type -> hopen.auth.v1.GetUserPermissionsResponse
	17, // 41: hopen.auth.v1.AuthService.HasPermission:output_type -> hopen.auth.v1.HasPermissionResponse
	19, // 42: hopen.auth.v1.AuthService.GetUserRoles:output_type -> hopen.auth.v1.GetUserRolesResponse
	21, // 43: hopen.auth.v1.AuthService.ValidateMqttConnection:output_type -> hopen.auth.v1.ValidateMqttConnectionResponse
	23, // 44: hopen.auth.v1.AuthService.GetAuthStatus:output_type -> hopen.auth.v1.GetAuthStatusResponse
	34, // [34:45] is the sub-list for method output_type
	23, // [23:34] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_auth_proto_init() }
func file_auth_proto_init() {
	if File_auth_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_auth_proto_rawDesc), len(file_auth_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_proto_goTypes,
		DependencyIndexes: file_auth_proto_depIdxs,
		MessageInfos:      file_auth_proto_msgTypes,
	}.Build()
	File_auth_proto = out.File
	file_auth_proto_goTypes = nil
	file_auth_proto_depIdxs = nil
}
