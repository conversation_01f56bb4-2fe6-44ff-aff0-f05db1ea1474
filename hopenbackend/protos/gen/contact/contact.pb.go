// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: contact.proto

package contact

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Send contact request request
type SendContactRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FromUserId    string                 `protobuf:"bytes,1,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId      string                 `protobuf:"bytes,2,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendContactRequestRequest) Reset() {
	*x = SendContactRequestRequest{}
	mi := &file_contact_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendContactRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendContactRequestRequest) ProtoMessage() {}

func (x *SendContactRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendContactRequestRequest.ProtoReflect.Descriptor instead.
func (*SendContactRequestRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{0}
}

func (x *SendContactRequestRequest) GetFromUserId() string {
	if x != nil {
		return x.FromUserId
	}
	return ""
}

func (x *SendContactRequestRequest) GetToUserId() string {
	if x != nil {
		return x.ToUserId
	}
	return ""
}

func (x *SendContactRequestRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Send contact request response
type SendContactRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendContactRequestResponse) Reset() {
	*x = SendContactRequestResponse{}
	mi := &file_contact_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendContactRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendContactRequestResponse) ProtoMessage() {}

func (x *SendContactRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendContactRequestResponse.ProtoReflect.Descriptor instead.
func (*SendContactRequestResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{1}
}

func (x *SendContactRequestResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *SendContactRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Accept contact request request
type AcceptContactRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptContactRequestRequest) Reset() {
	*x = AcceptContactRequestRequest{}
	mi := &file_contact_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptContactRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptContactRequestRequest) ProtoMessage() {}

func (x *AcceptContactRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptContactRequestRequest.ProtoReflect.Descriptor instead.
func (*AcceptContactRequestRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{2}
}

func (x *AcceptContactRequestRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AcceptContactRequestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Accept contact request response
type AcceptContactRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contact       *common.Contact        `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptContactRequestResponse) Reset() {
	*x = AcceptContactRequestResponse{}
	mi := &file_contact_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptContactRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptContactRequestResponse) ProtoMessage() {}

func (x *AcceptContactRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptContactRequestResponse.ProtoReflect.Descriptor instead.
func (*AcceptContactRequestResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{3}
}

func (x *AcceptContactRequestResponse) GetContact() *common.Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *AcceptContactRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Reject contact request request
type RejectContactRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectContactRequestRequest) Reset() {
	*x = RejectContactRequestRequest{}
	mi := &file_contact_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectContactRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectContactRequestRequest) ProtoMessage() {}

func (x *RejectContactRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectContactRequestRequest.ProtoReflect.Descriptor instead.
func (*RejectContactRequestRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{4}
}

func (x *RejectContactRequestRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RejectContactRequestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RejectContactRequestRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Reject contact request response
type RejectContactRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectContactRequestResponse) Reset() {
	*x = RejectContactRequestResponse{}
	mi := &file_contact_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectContactRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectContactRequestResponse) ProtoMessage() {}

func (x *RejectContactRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectContactRequestResponse.ProtoReflect.Descriptor instead.
func (*RejectContactRequestResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{5}
}

func (x *RejectContactRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Cancel contact request request
type CancelContactRequestRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelContactRequestRequest) Reset() {
	*x = CancelContactRequestRequest{}
	mi := &file_contact_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelContactRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelContactRequestRequest) ProtoMessage() {}

func (x *CancelContactRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelContactRequestRequest.ProtoReflect.Descriptor instead.
func (*CancelContactRequestRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{6}
}

func (x *CancelContactRequestRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CancelContactRequestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Cancel contact request response
type CancelContactRequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelContactRequestResponse) Reset() {
	*x = CancelContactRequestResponse{}
	mi := &file_contact_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelContactRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelContactRequestResponse) ProtoMessage() {}

func (x *CancelContactRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelContactRequestResponse.ProtoReflect.Descriptor instead.
func (*CancelContactRequestResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{7}
}

func (x *CancelContactRequestResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Remove contact request
type RemoveContactRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ContactId     string                 `protobuf:"bytes,2,opt,name=contact_id,json=contactId,proto3" json:"contact_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveContactRequest) Reset() {
	*x = RemoveContactRequest{}
	mi := &file_contact_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveContactRequest) ProtoMessage() {}

func (x *RemoveContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveContactRequest.ProtoReflect.Descriptor instead.
func (*RemoveContactRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{8}
}

func (x *RemoveContactRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RemoveContactRequest) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

// Remove contact response
type RemoveContactResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveContactResponse) Reset() {
	*x = RemoveContactResponse{}
	mi := &file_contact_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveContactResponse) ProtoMessage() {}

func (x *RemoveContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveContactResponse.ProtoReflect.Descriptor instead.
func (*RemoveContactResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{9}
}

func (x *RemoveContactResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Block contact request
type BlockContactRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ContactId     string                 `protobuf:"bytes,2,opt,name=contact_id,json=contactId,proto3" json:"contact_id,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockContactRequest) Reset() {
	*x = BlockContactRequest{}
	mi := &file_contact_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockContactRequest) ProtoMessage() {}

func (x *BlockContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockContactRequest.ProtoReflect.Descriptor instead.
func (*BlockContactRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{10}
}

func (x *BlockContactRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BlockContactRequest) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *BlockContactRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Block contact response
type BlockContactResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockContactResponse) Reset() {
	*x = BlockContactResponse{}
	mi := &file_contact_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockContactResponse) ProtoMessage() {}

func (x *BlockContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockContactResponse.ProtoReflect.Descriptor instead.
func (*BlockContactResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{11}
}

func (x *BlockContactResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Unblock contact request
type UnblockContactRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ContactId     string                 `protobuf:"bytes,2,opt,name=contact_id,json=contactId,proto3" json:"contact_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockContactRequest) Reset() {
	*x = UnblockContactRequest{}
	mi := &file_contact_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockContactRequest) ProtoMessage() {}

func (x *UnblockContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockContactRequest.ProtoReflect.Descriptor instead.
func (*UnblockContactRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{12}
}

func (x *UnblockContactRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UnblockContactRequest) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

// Unblock contact response
type UnblockContactResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,1,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockContactResponse) Reset() {
	*x = UnblockContactResponse{}
	mi := &file_contact_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockContactResponse) ProtoMessage() {}

func (x *UnblockContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockContactResponse.ProtoReflect.Descriptor instead.
func (*UnblockContactResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{13}
}

func (x *UnblockContactResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get user contacts request
type GetUserContactsRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page           int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IncludeBlocked bool                   `protobuf:"varint,4,opt,name=include_blocked,json=includeBlocked,proto3" json:"include_blocked,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetUserContactsRequest) Reset() {
	*x = GetUserContactsRequest{}
	mi := &file_contact_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserContactsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserContactsRequest) ProtoMessage() {}

func (x *GetUserContactsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserContactsRequest.ProtoReflect.Descriptor instead.
func (*GetUserContactsRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserContactsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserContactsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUserContactsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetUserContactsRequest) GetIncludeBlocked() bool {
	if x != nil {
		return x.IncludeBlocked
	}
	return false
}

// Get user contacts response
type GetUserContactsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contacts      []*ContactWithUser     `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserContactsResponse) Reset() {
	*x = GetUserContactsResponse{}
	mi := &file_contact_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserContactsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserContactsResponse) ProtoMessage() {}

func (x *GetUserContactsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserContactsResponse.ProtoReflect.Descriptor instead.
func (*GetUserContactsResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserContactsResponse) GetContacts() []*ContactWithUser {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *GetUserContactsResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetUserContactsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Contact with user information
type ContactWithUser struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Contact           *common.Contact        `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	User              *common.User           `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	LastInteractionAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_interaction_at,json=lastInteractionAt,proto3" json:"last_interaction_at,omitempty"`
	MutualBubbles     int32                  `protobuf:"varint,4,opt,name=mutual_bubbles,json=mutualBubbles,proto3" json:"mutual_bubbles,omitempty"`
	MutualFriends     int32                  `protobuf:"varint,5,opt,name=mutual_friends,json=mutualFriends,proto3" json:"mutual_friends,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ContactWithUser) Reset() {
	*x = ContactWithUser{}
	mi := &file_contact_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactWithUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactWithUser) ProtoMessage() {}

func (x *ContactWithUser) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactWithUser.ProtoReflect.Descriptor instead.
func (*ContactWithUser) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{16}
}

func (x *ContactWithUser) GetContact() *common.Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *ContactWithUser) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ContactWithUser) GetLastInteractionAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastInteractionAt
	}
	return nil
}

func (x *ContactWithUser) GetMutualBubbles() int32 {
	if x != nil {
		return x.MutualBubbles
	}
	return 0
}

func (x *ContactWithUser) GetMutualFriends() int32 {
	if x != nil {
		return x.MutualFriends
	}
	return 0
}

// Get contact requests request
type GetContactRequestsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status        common.ContactStatus   `protobuf:"varint,2,opt,name=status,proto3,enum=hopen.common.v1.ContactStatus" json:"status,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactRequestsRequest) Reset() {
	*x = GetContactRequestsRequest{}
	mi := &file_contact_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactRequestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactRequestsRequest) ProtoMessage() {}

func (x *GetContactRequestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactRequestsRequest.ProtoReflect.Descriptor instead.
func (*GetContactRequestsRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{17}
}

func (x *GetContactRequestsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetContactRequestsRequest) GetStatus() common.ContactStatus {
	if x != nil {
		return x.Status
	}
	return common.ContactStatus(0)
}

func (x *GetContactRequestsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetContactRequestsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get contact requests response
type GetContactRequestsResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Requests      []*ContactRequestWithUser `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	Pagination    *common.Pagination        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse       `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactRequestsResponse) Reset() {
	*x = GetContactRequestsResponse{}
	mi := &file_contact_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactRequestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactRequestsResponse) ProtoMessage() {}

func (x *GetContactRequestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactRequestsResponse.ProtoReflect.Descriptor instead.
func (*GetContactRequestsResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{18}
}

func (x *GetContactRequestsResponse) GetRequests() []*ContactRequestWithUser {
	if x != nil {
		return x.Requests
	}
	return nil
}

func (x *GetContactRequestsResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetContactRequestsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Contact request with user information
type ContactRequestWithUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FromUserId    string                 `protobuf:"bytes,2,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId      string                 `protobuf:"bytes,3,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	FromUser      *common.User           `protobuf:"bytes,4,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Message       string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Status        common.ContactStatus   `protobuf:"varint,6,opt,name=status,proto3,enum=hopen.common.v1.ContactStatus" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactRequestWithUser) Reset() {
	*x = ContactRequestWithUser{}
	mi := &file_contact_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactRequestWithUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactRequestWithUser) ProtoMessage() {}

func (x *ContactRequestWithUser) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactRequestWithUser.ProtoReflect.Descriptor instead.
func (*ContactRequestWithUser) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{19}
}

func (x *ContactRequestWithUser) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContactRequestWithUser) GetFromUserId() string {
	if x != nil {
		return x.FromUserId
	}
	return ""
}

func (x *ContactRequestWithUser) GetToUserId() string {
	if x != nil {
		return x.ToUserId
	}
	return ""
}

func (x *ContactRequestWithUser) GetFromUser() *common.User {
	if x != nil {
		return x.FromUser
	}
	return nil
}

func (x *ContactRequestWithUser) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ContactRequestWithUser) GetStatus() common.ContactStatus {
	if x != nil {
		return x.Status
	}
	return common.ContactStatus(0)
}

func (x *ContactRequestWithUser) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ContactRequestWithUser) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Get contact request
type GetContactRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ContactId     string                 `protobuf:"bytes,1,opt,name=contact_id,json=contactId,proto3" json:"contact_id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactRequest) Reset() {
	*x = GetContactRequest{}
	mi := &file_contact_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactRequest) ProtoMessage() {}

func (x *GetContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactRequest.ProtoReflect.Descriptor instead.
func (*GetContactRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{20}
}

func (x *GetContactRequest) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *GetContactRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get contact response
type GetContactResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contact       *ContactWithUser       `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactResponse) Reset() {
	*x = GetContactResponse{}
	mi := &file_contact_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactResponse) ProtoMessage() {}

func (x *GetContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactResponse.ProtoReflect.Descriptor instead.
func (*GetContactResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{21}
}

func (x *GetContactResponse) GetContact() *ContactWithUser {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *GetContactResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Search contacts request
type SearchContactsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchContactsRequest) Reset() {
	*x = SearchContactsRequest{}
	mi := &file_contact_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchContactsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchContactsRequest) ProtoMessage() {}

func (x *SearchContactsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchContactsRequest.ProtoReflect.Descriptor instead.
func (*SearchContactsRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{22}
}

func (x *SearchContactsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SearchContactsRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchContactsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchContactsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Search contacts response
type SearchContactsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contacts      []*ContactWithUser     `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
	Pagination    *common.Pagination     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchContactsResponse) Reset() {
	*x = SearchContactsResponse{}
	mi := &file_contact_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchContactsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchContactsResponse) ProtoMessage() {}

func (x *SearchContactsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchContactsResponse.ProtoReflect.Descriptor instead.
func (*SearchContactsResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{23}
}

func (x *SearchContactsResponse) GetContacts() []*ContactWithUser {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *SearchContactsResponse) GetPagination() *common.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchContactsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get contact suggestions request
type GetContactSuggestionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	ExcludeIds    []string               `protobuf:"bytes,3,rep,name=exclude_ids,json=excludeIds,proto3" json:"exclude_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactSuggestionsRequest) Reset() {
	*x = GetContactSuggestionsRequest{}
	mi := &file_contact_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactSuggestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactSuggestionsRequest) ProtoMessage() {}

func (x *GetContactSuggestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactSuggestionsRequest.ProtoReflect.Descriptor instead.
func (*GetContactSuggestionsRequest) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{24}
}

func (x *GetContactSuggestionsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetContactSuggestionsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetContactSuggestionsRequest) GetExcludeIds() []string {
	if x != nil {
		return x.ExcludeIds
	}
	return nil
}

// Get contact suggestions response
type GetContactSuggestionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Suggestions   []*ContactSuggestion   `protobuf:"bytes,1,rep,name=suggestions,proto3" json:"suggestions,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactSuggestionsResponse) Reset() {
	*x = GetContactSuggestionsResponse{}
	mi := &file_contact_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactSuggestionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactSuggestionsResponse) ProtoMessage() {}

func (x *GetContactSuggestionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactSuggestionsResponse.ProtoReflect.Descriptor instead.
func (*GetContactSuggestionsResponse) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{25}
}

func (x *GetContactSuggestionsResponse) GetSuggestions() []*ContactSuggestion {
	if x != nil {
		return x.Suggestions
	}
	return nil
}

func (x *GetContactSuggestionsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Contact suggestion
type ContactSuggestion struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	User              *common.User           `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	RelevanceScore    float32                `protobuf:"fixed32,2,opt,name=relevance_score,json=relevanceScore,proto3" json:"relevance_score,omitempty"`
	Reasons           []string               `protobuf:"bytes,3,rep,name=reasons,proto3" json:"reasons,omitempty"`
	MutualConnections int32                  `protobuf:"varint,4,opt,name=mutual_connections,json=mutualConnections,proto3" json:"mutual_connections,omitempty"`
	MutualBubbles     int32                  `protobuf:"varint,5,opt,name=mutual_bubbles,json=mutualBubbles,proto3" json:"mutual_bubbles,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ContactSuggestion) Reset() {
	*x = ContactSuggestion{}
	mi := &file_contact_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactSuggestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactSuggestion) ProtoMessage() {}

func (x *ContactSuggestion) ProtoReflect() protoreflect.Message {
	mi := &file_contact_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactSuggestion.ProtoReflect.Descriptor instead.
func (*ContactSuggestion) Descriptor() ([]byte, []int) {
	return file_contact_proto_rawDescGZIP(), []int{26}
}

func (x *ContactSuggestion) GetUser() *common.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ContactSuggestion) GetRelevanceScore() float32 {
	if x != nil {
		return x.RelevanceScore
	}
	return 0
}

func (x *ContactSuggestion) GetReasons() []string {
	if x != nil {
		return x.Reasons
	}
	return nil
}

func (x *ContactSuggestion) GetMutualConnections() int32 {
	if x != nil {
		return x.MutualConnections
	}
	return 0
}

func (x *ContactSuggestion) GetMutualBubbles() int32 {
	if x != nil {
		return x.MutualBubbles
	}
	return 0
}

var File_contact_proto protoreflect.FileDescriptor

const file_contact_proto_rawDesc = "" +
	"\n" +
	"\rcontact.proto\x12\x10hopen.contact.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"u\n" +
	"\x19SendContactRequestRequest\x12 \n" +
	"\ffrom_user_id\x18\x01 \x01(\tR\n" +
	"fromUserId\x12\x1c\n" +
	"\n" +
	"to_user_id\x18\x02 \x01(\tR\btoUserId\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"|\n" +
	"\x1aSendContactRequestResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"U\n" +
	"\x1bAcceptContactRequestRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"\x93\x01\n" +
	"\x1cAcceptContactRequestResponse\x122\n" +
	"\acontact\x18\x01 \x01(\v2\x18.hopen.common.v1.ContactR\acontact\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"m\n" +
	"\x1bRejectContactRequestRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"_\n" +
	"\x1cRejectContactRequestResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"U\n" +
	"\x1bCancelContactRequestRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"_\n" +
	"\x1cCancelContactRequestResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"N\n" +
	"\x14RemoveContactRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"contact_id\x18\x02 \x01(\tR\tcontactId\"X\n" +
	"\x15RemoveContactResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"e\n" +
	"\x13BlockContactRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"contact_id\x18\x02 \x01(\tR\tcontactId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"W\n" +
	"\x14BlockContactResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"O\n" +
	"\x15UnblockContactRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"contact_id\x18\x02 \x01(\tR\tcontactId\"Y\n" +
	"\x16UnblockContactResponse\x12?\n" +
	"\fapi_response\x18\x01 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x8b\x01\n" +
	"\x16GetUserContactsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12'\n" +
	"\x0finclude_blocked\x18\x04 \x01(\bR\x0eincludeBlocked\"\xd6\x01\n" +
	"\x17GetUserContactsResponse\x12=\n" +
	"\bcontacts\x18\x01 \x03(\v2!.hopen.contact.v1.ContactWithUserR\bcontacts\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\x8a\x02\n" +
	"\x0fContactWithUser\x122\n" +
	"\acontact\x18\x01 \x01(\v2\x18.hopen.common.v1.ContactR\acontact\x12)\n" +
	"\x04user\x18\x02 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x12J\n" +
	"\x13last_interaction_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x11lastInteractionAt\x12%\n" +
	"\x0emutual_bubbles\x18\x04 \x01(\x05R\rmutualBubbles\x12%\n" +
	"\x0emutual_friends\x18\x05 \x01(\x05R\rmutualFriends\"\x9d\x01\n" +
	"\x19GetContactRequestsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x126\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1e.hopen.common.v1.ContactStatusR\x06status\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xe0\x01\n" +
	"\x1aGetContactRequestsResponse\x12D\n" +
	"\brequests\x18\x01 \x03(\v2(.hopen.contact.v1.ContactRequestWithUserR\brequests\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xe4\x02\n" +
	"\x16ContactRequestWithUser\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12 \n" +
	"\ffrom_user_id\x18\x02 \x01(\tR\n" +
	"fromUserId\x12\x1c\n" +
	"\n" +
	"to_user_id\x18\x03 \x01(\tR\btoUserId\x122\n" +
	"\tfrom_user\x18\x04 \x01(\v2\x15.hopen.common.v1.UserR\bfromUser\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x126\n" +
	"\x06status\x18\x06 \x01(\x0e2\x1e.hopen.common.v1.ContactStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"K\n" +
	"\x11GetContactRequest\x12\x1d\n" +
	"\n" +
	"contact_id\x18\x01 \x01(\tR\tcontactId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"\x92\x01\n" +
	"\x12GetContactResponse\x12;\n" +
	"\acontact\x18\x01 \x01(\v2!.hopen.contact.v1.ContactWithUserR\acontact\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"w\n" +
	"\x15SearchContactsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x14\n" +
	"\x05query\x18\x02 \x01(\tR\x05query\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xd5\x01\n" +
	"\x16SearchContactsResponse\x12=\n" +
	"\bcontacts\x18\x01 \x03(\v2!.hopen.contact.v1.ContactWithUserR\bcontacts\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.hopen.common.v1.PaginationR\n" +
	"pagination\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"n\n" +
	"\x1cGetContactSuggestionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\x1f\n" +
	"\vexclude_ids\x18\x03 \x03(\tR\n" +
	"excludeIds\"\xa7\x01\n" +
	"\x1dGetContactSuggestionsResponse\x12E\n" +
	"\vsuggestions\x18\x01 \x03(\v2#.hopen.contact.v1.ContactSuggestionR\vsuggestions\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xd7\x01\n" +
	"\x11ContactSuggestion\x12)\n" +
	"\x04user\x18\x01 \x01(\v2\x15.hopen.common.v1.UserR\x04user\x12'\n" +
	"\x0frelevance_score\x18\x02 \x01(\x02R\x0erelevanceScore\x12\x18\n" +
	"\areasons\x18\x03 \x03(\tR\areasons\x12-\n" +
	"\x12mutual_connections\x18\x04 \x01(\x05R\x11mutualConnections\x12%\n" +
	"\x0emutual_bubbles\x18\x05 \x01(\x05R\rmutualBubbles2\x9d\n" +
	"\n" +
	"\x0eContactService\x12o\n" +
	"\x12SendContactRequest\x12+.hopen.contact.v1.SendContactRequestRequest\x1a,.hopen.contact.v1.SendContactRequestResponse\x12u\n" +
	"\x14AcceptContactRequest\x12-.hopen.contact.v1.AcceptContactRequestRequest\x1a..hopen.contact.v1.AcceptContactRequestResponse\x12u\n" +
	"\x14RejectContactRequest\x12-.hopen.contact.v1.RejectContactRequestRequest\x1a..hopen.contact.v1.RejectContactRequestResponse\x12u\n" +
	"\x14CancelContactRequest\x12-.hopen.contact.v1.CancelContactRequestRequest\x1a..hopen.contact.v1.CancelContactRequestResponse\x12`\n" +
	"\rRemoveContact\x12&.hopen.contact.v1.RemoveContactRequest\x1a'.hopen.contact.v1.RemoveContactResponse\x12]\n" +
	"\fBlockContact\x12%.hopen.contact.v1.BlockContactRequest\x1a&.hopen.contact.v1.BlockContactResponse\x12c\n" +
	"\x0eUnblockContact\x12'.hopen.contact.v1.UnblockContactRequest\x1a(.hopen.contact.v1.UnblockContactResponse\x12f\n" +
	"\x0fGetUserContacts\x12(.hopen.contact.v1.GetUserContactsRequest\x1a).hopen.contact.v1.GetUserContactsResponse\x12o\n" +
	"\x12GetContactRequests\x12+.hopen.contact.v1.GetContactRequestsRequest\x1a,.hopen.contact.v1.GetContactRequestsResponse\x12W\n" +
	"\n" +
	"GetContact\x12#.hopen.contact.v1.GetContactRequest\x1a$.hopen.contact.v1.GetContactResponse\x12c\n" +
	"\x0eSearchContacts\x12'.hopen.contact.v1.SearchContactsRequest\x1a(.hopen.contact.v1.SearchContactsResponse\x12x\n" +
	"\x15GetContactSuggestions\x12..hopen.contact.v1.GetContactSuggestionsRequest\x1a/.hopen.contact.v1.GetContactSuggestionsResponseBA\n" +
	"\x14com.hopen.contact.v1P\x01Z'hopenbackend/protos/gen/contact;contactb\x06proto3"

var (
	file_contact_proto_rawDescOnce sync.Once
	file_contact_proto_rawDescData []byte
)

func file_contact_proto_rawDescGZIP() []byte {
	file_contact_proto_rawDescOnce.Do(func() {
		file_contact_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_contact_proto_rawDesc), len(file_contact_proto_rawDesc)))
	})
	return file_contact_proto_rawDescData
}

var file_contact_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_contact_proto_goTypes = []any{
	(*SendContactRequestRequest)(nil),     // 0: hopen.contact.v1.SendContactRequestRequest
	(*SendContactRequestResponse)(nil),    // 1: hopen.contact.v1.SendContactRequestResponse
	(*AcceptContactRequestRequest)(nil),   // 2: hopen.contact.v1.AcceptContactRequestRequest
	(*AcceptContactRequestResponse)(nil),  // 3: hopen.contact.v1.AcceptContactRequestResponse
	(*RejectContactRequestRequest)(nil),   // 4: hopen.contact.v1.RejectContactRequestRequest
	(*RejectContactRequestResponse)(nil),  // 5: hopen.contact.v1.RejectContactRequestResponse
	(*CancelContactRequestRequest)(nil),   // 6: hopen.contact.v1.CancelContactRequestRequest
	(*CancelContactRequestResponse)(nil),  // 7: hopen.contact.v1.CancelContactRequestResponse
	(*RemoveContactRequest)(nil),          // 8: hopen.contact.v1.RemoveContactRequest
	(*RemoveContactResponse)(nil),         // 9: hopen.contact.v1.RemoveContactResponse
	(*BlockContactRequest)(nil),           // 10: hopen.contact.v1.BlockContactRequest
	(*BlockContactResponse)(nil),          // 11: hopen.contact.v1.BlockContactResponse
	(*UnblockContactRequest)(nil),         // 12: hopen.contact.v1.UnblockContactRequest
	(*UnblockContactResponse)(nil),        // 13: hopen.contact.v1.UnblockContactResponse
	(*GetUserContactsRequest)(nil),        // 14: hopen.contact.v1.GetUserContactsRequest
	(*GetUserContactsResponse)(nil),       // 15: hopen.contact.v1.GetUserContactsResponse
	(*ContactWithUser)(nil),               // 16: hopen.contact.v1.ContactWithUser
	(*GetContactRequestsRequest)(nil),     // 17: hopen.contact.v1.GetContactRequestsRequest
	(*GetContactRequestsResponse)(nil),    // 18: hopen.contact.v1.GetContactRequestsResponse
	(*ContactRequestWithUser)(nil),        // 19: hopen.contact.v1.ContactRequestWithUser
	(*GetContactRequest)(nil),             // 20: hopen.contact.v1.GetContactRequest
	(*GetContactResponse)(nil),            // 21: hopen.contact.v1.GetContactResponse
	(*SearchContactsRequest)(nil),         // 22: hopen.contact.v1.SearchContactsRequest
	(*SearchContactsResponse)(nil),        // 23: hopen.contact.v1.SearchContactsResponse
	(*GetContactSuggestionsRequest)(nil),  // 24: hopen.contact.v1.GetContactSuggestionsRequest
	(*GetContactSuggestionsResponse)(nil), // 25: hopen.contact.v1.GetContactSuggestionsResponse
	(*ContactSuggestion)(nil),             // 26: hopen.contact.v1.ContactSuggestion
	(*common.ApiResponse)(nil),            // 27: hopen.common.v1.ApiResponse
	(*common.Contact)(nil),                // 28: hopen.common.v1.Contact
	(*common.Pagination)(nil),             // 29: hopen.common.v1.Pagination
	(*common.User)(nil),                   // 30: hopen.common.v1.User
	(*timestamppb.Timestamp)(nil),         // 31: google.protobuf.Timestamp
	(common.ContactStatus)(0),             // 32: hopen.common.v1.ContactStatus
}
var file_contact_proto_depIdxs = []int32{
	27, // 0: hopen.contact.v1.SendContactRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	28, // 1: hopen.contact.v1.AcceptContactRequestResponse.contact:type_name -> hopen.common.v1.Contact
	27, // 2: hopen.contact.v1.AcceptContactRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 3: hopen.contact.v1.RejectContactRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 4: hopen.contact.v1.CancelContactRequestResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 5: hopen.contact.v1.RemoveContactResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 6: hopen.contact.v1.BlockContactResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	27, // 7: hopen.contact.v1.UnblockContactResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	16, // 8: hopen.contact.v1.GetUserContactsResponse.contacts:type_name -> hopen.contact.v1.ContactWithUser
	29, // 9: hopen.contact.v1.GetUserContactsResponse.pagination:type_name -> hopen.common.v1.Pagination
	27, // 10: hopen.contact.v1.GetUserContactsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	28, // 11: hopen.contact.v1.ContactWithUser.contact:type_name -> hopen.common.v1.Contact
	30, // 12: hopen.contact.v1.ContactWithUser.user:type_name -> hopen.common.v1.User
	31, // 13: hopen.contact.v1.ContactWithUser.last_interaction_at:type_name -> google.protobuf.Timestamp
	32, // 14: hopen.contact.v1.GetContactRequestsRequest.status:type_name -> hopen.common.v1.ContactStatus
	19, // 15: hopen.contact.v1.GetContactRequestsResponse.requests:type_name -> hopen.contact.v1.ContactRequestWithUser
	29, // 16: hopen.contact.v1.GetContactRequestsResponse.pagination:type_name -> hopen.common.v1.Pagination
	27, // 17: hopen.contact.v1.GetContactRequestsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	30, // 18: hopen.contact.v1.ContactRequestWithUser.from_user:type_name -> hopen.common.v1.User
	32, // 19: hopen.contact.v1.ContactRequestWithUser.status:type_name -> hopen.common.v1.ContactStatus
	31, // 20: hopen.contact.v1.ContactRequestWithUser.created_at:type_name -> google.protobuf.Timestamp
	31, // 21: hopen.contact.v1.ContactRequestWithUser.updated_at:type_name -> google.protobuf.Timestamp
	16, // 22: hopen.contact.v1.GetContactResponse.contact:type_name -> hopen.contact.v1.ContactWithUser
	27, // 23: hopen.contact.v1.GetContactResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	16, // 24: hopen.contact.v1.SearchContactsResponse.contacts:type_name -> hopen.contact.v1.ContactWithUser
	29, // 25: hopen.contact.v1.SearchContactsResponse.pagination:type_name -> hopen.common.v1.Pagination
	27, // 26: hopen.contact.v1.SearchContactsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	26, // 27: hopen.contact.v1.GetContactSuggestionsResponse.suggestions:type_name -> hopen.contact.v1.ContactSuggestion
	27, // 28: hopen.contact.v1.GetContactSuggestionsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	30, // 29: hopen.contact.v1.ContactSuggestion.user:type_name -> hopen.common.v1.User
	0,  // 30: hopen.contact.v1.ContactService.SendContactRequest:input_type -> hopen.contact.v1.SendContactRequestRequest
	2,  // 31: hopen.contact.v1.ContactService.AcceptContactRequest:input_type -> hopen.contact.v1.AcceptContactRequestRequest
	4,  // 32: hopen.contact.v1.ContactService.RejectContactRequest:input_type -> hopen.contact.v1.RejectContactRequestRequest
	6,  // 33: hopen.contact.v1.ContactService.CancelContactRequest:input_type -> hopen.contact.v1.CancelContactRequestRequest
	8,  // 34: hopen.contact.v1.ContactService.RemoveContact:input_type -> hopen.contact.v1.RemoveContactRequest
	10, // 35: hopen.contact.v1.ContactService.BlockContact:input_type -> hopen.contact.v1.BlockContactRequest
	12, // 36: hopen.contact.v1.ContactService.UnblockContact:input_type -> hopen.contact.v1.UnblockContactRequest
	14, // 37: hopen.contact.v1.ContactService.GetUserContacts:input_type -> hopen.contact.v1.GetUserContactsRequest
	17, // 38: hopen.contact.v1.ContactService.GetContactRequests:input_type -> hopen.contact.v1.GetContactRequestsRequest
	20, // 39: hopen.contact.v1.ContactService.GetContact:input_type -> hopen.contact.v1.GetContactRequest
	22, // 40: hopen.contact.v1.ContactService.SearchContacts:input_type -> hopen.contact.v1.SearchContactsRequest
	24, // 41: hopen.contact.v1.ContactService.GetContactSuggestions:input_type -> hopen.contact.v1.GetContactSuggestionsRequest
	1,  // 42: hopen.contact.v1.ContactService.SendContactRequest:output_type -> hopen.contact.v1.SendContactRequestResponse
	3,  // 43: hopen.contact.v1.ContactService.AcceptContactRequest:output_type -> hopen.contact.v1.AcceptContactRequestResponse
	5,  // 44: hopen.contact.v1.ContactService.RejectContactRequest:output_type -> hopen.contact.v1.RejectContactRequestResponse
	7,  // 45: hopen.contact.v1.ContactService.CancelContactRequest:output_type -> hopen.contact.v1.CancelContactRequestResponse
	9,  // 46: hopen.contact.v1.ContactService.RemoveContact:output_type -> hopen.contact.v1.RemoveContactResponse
	11, // 47: hopen.contact.v1.ContactService.BlockContact:output_type -> hopen.contact.v1.BlockContactResponse
	13, // 48: hopen.contact.v1.ContactService.UnblockContact:output_type -> hopen.contact.v1.UnblockContactResponse
	15, // 49: hopen.contact.v1.ContactService.GetUserContacts:output_type -> hopen.contact.v1.GetUserContactsResponse
	18, // 50: hopen.contact.v1.ContactService.GetContactRequests:output_type -> hopen.contact.v1.GetContactRequestsResponse
	21, // 51: hopen.contact.v1.ContactService.GetContact:output_type -> hopen.contact.v1.GetContactResponse
	23, // 52: hopen.contact.v1.ContactService.SearchContacts:output_type -> hopen.contact.v1.SearchContactsResponse
	25, // 53: hopen.contact.v1.ContactService.GetContactSuggestions:output_type -> hopen.contact.v1.GetContactSuggestionsResponse
	42, // [42:54] is the sub-list for method output_type
	30, // [30:42] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_contact_proto_init() }
func file_contact_proto_init() {
	if File_contact_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_contact_proto_rawDesc), len(file_contact_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_contact_proto_goTypes,
		DependencyIndexes: file_contact_proto_depIdxs,
		MessageInfos:      file_contact_proto_msgTypes,
	}.Build()
	File_contact_proto = out.File
	file_contact_proto_goTypes = nil
	file_contact_proto_depIdxs = nil
}
