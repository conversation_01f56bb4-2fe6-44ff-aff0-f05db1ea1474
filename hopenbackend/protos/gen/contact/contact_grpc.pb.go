// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: contact.proto

package contact

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ContactService_SendContactRequest_FullMethodName    = "/hopen.contact.v1.ContactService/SendContactRequest"
	ContactService_AcceptContactRequest_FullMethodName  = "/hopen.contact.v1.ContactService/AcceptContactRequest"
	ContactService_RejectContactRequest_FullMethodName  = "/hopen.contact.v1.ContactService/RejectContactRequest"
	ContactService_CancelContactRequest_FullMethodName  = "/hopen.contact.v1.ContactService/CancelContactRequest"
	ContactService_RemoveContact_FullMethodName         = "/hopen.contact.v1.ContactService/RemoveContact"
	ContactService_BlockContact_FullMethodName          = "/hopen.contact.v1.ContactService/BlockContact"
	ContactService_UnblockContact_FullMethodName        = "/hopen.contact.v1.ContactService/UnblockContact"
	ContactService_GetUserContacts_FullMethodName       = "/hopen.contact.v1.ContactService/GetUserContacts"
	ContactService_GetContactRequests_FullMethodName    = "/hopen.contact.v1.ContactService/GetContactRequests"
	ContactService_GetContact_FullMethodName            = "/hopen.contact.v1.ContactService/GetContact"
	ContactService_SearchContacts_FullMethodName        = "/hopen.contact.v1.ContactService/SearchContacts"
	ContactService_GetContactSuggestions_FullMethodName = "/hopen.contact.v1.ContactService/GetContactSuggestions"
)

// ContactServiceClient is the client API for ContactService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Contact service for contact management
type ContactServiceClient interface {
	// Send contact request
	SendContactRequest(ctx context.Context, in *SendContactRequestRequest, opts ...grpc.CallOption) (*SendContactRequestResponse, error)
	// Accept contact request
	AcceptContactRequest(ctx context.Context, in *AcceptContactRequestRequest, opts ...grpc.CallOption) (*AcceptContactRequestResponse, error)
	// Reject contact request
	RejectContactRequest(ctx context.Context, in *RejectContactRequestRequest, opts ...grpc.CallOption) (*RejectContactRequestResponse, error)
	// Cancel contact request
	CancelContactRequest(ctx context.Context, in *CancelContactRequestRequest, opts ...grpc.CallOption) (*CancelContactRequestResponse, error)
	// Remove contact
	RemoveContact(ctx context.Context, in *RemoveContactRequest, opts ...grpc.CallOption) (*RemoveContactResponse, error)
	// Block contact
	BlockContact(ctx context.Context, in *BlockContactRequest, opts ...grpc.CallOption) (*BlockContactResponse, error)
	// Unblock contact
	UnblockContact(ctx context.Context, in *UnblockContactRequest, opts ...grpc.CallOption) (*UnblockContactResponse, error)
	// Get user contacts
	GetUserContacts(ctx context.Context, in *GetUserContactsRequest, opts ...grpc.CallOption) (*GetUserContactsResponse, error)
	// Get contact requests
	GetContactRequests(ctx context.Context, in *GetContactRequestsRequest, opts ...grpc.CallOption) (*GetContactRequestsResponse, error)
	// Get contact by ID
	GetContact(ctx context.Context, in *GetContactRequest, opts ...grpc.CallOption) (*GetContactResponse, error)
	// Search contacts
	SearchContacts(ctx context.Context, in *SearchContactsRequest, opts ...grpc.CallOption) (*SearchContactsResponse, error)
	// Get contact suggestions
	GetContactSuggestions(ctx context.Context, in *GetContactSuggestionsRequest, opts ...grpc.CallOption) (*GetContactSuggestionsResponse, error)
}

type contactServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewContactServiceClient(cc grpc.ClientConnInterface) ContactServiceClient {
	return &contactServiceClient{cc}
}

func (c *contactServiceClient) SendContactRequest(ctx context.Context, in *SendContactRequestRequest, opts ...grpc.CallOption) (*SendContactRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendContactRequestResponse)
	err := c.cc.Invoke(ctx, ContactService_SendContactRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) AcceptContactRequest(ctx context.Context, in *AcceptContactRequestRequest, opts ...grpc.CallOption) (*AcceptContactRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcceptContactRequestResponse)
	err := c.cc.Invoke(ctx, ContactService_AcceptContactRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) RejectContactRequest(ctx context.Context, in *RejectContactRequestRequest, opts ...grpc.CallOption) (*RejectContactRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RejectContactRequestResponse)
	err := c.cc.Invoke(ctx, ContactService_RejectContactRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) CancelContactRequest(ctx context.Context, in *CancelContactRequestRequest, opts ...grpc.CallOption) (*CancelContactRequestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelContactRequestResponse)
	err := c.cc.Invoke(ctx, ContactService_CancelContactRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) RemoveContact(ctx context.Context, in *RemoveContactRequest, opts ...grpc.CallOption) (*RemoveContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveContactResponse)
	err := c.cc.Invoke(ctx, ContactService_RemoveContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) BlockContact(ctx context.Context, in *BlockContactRequest, opts ...grpc.CallOption) (*BlockContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockContactResponse)
	err := c.cc.Invoke(ctx, ContactService_BlockContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) UnblockContact(ctx context.Context, in *UnblockContactRequest, opts ...grpc.CallOption) (*UnblockContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnblockContactResponse)
	err := c.cc.Invoke(ctx, ContactService_UnblockContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) GetUserContacts(ctx context.Context, in *GetUserContactsRequest, opts ...grpc.CallOption) (*GetUserContactsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserContactsResponse)
	err := c.cc.Invoke(ctx, ContactService_GetUserContacts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) GetContactRequests(ctx context.Context, in *GetContactRequestsRequest, opts ...grpc.CallOption) (*GetContactRequestsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetContactRequestsResponse)
	err := c.cc.Invoke(ctx, ContactService_GetContactRequests_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) GetContact(ctx context.Context, in *GetContactRequest, opts ...grpc.CallOption) (*GetContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetContactResponse)
	err := c.cc.Invoke(ctx, ContactService_GetContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) SearchContacts(ctx context.Context, in *SearchContactsRequest, opts ...grpc.CallOption) (*SearchContactsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchContactsResponse)
	err := c.cc.Invoke(ctx, ContactService_SearchContacts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactServiceClient) GetContactSuggestions(ctx context.Context, in *GetContactSuggestionsRequest, opts ...grpc.CallOption) (*GetContactSuggestionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetContactSuggestionsResponse)
	err := c.cc.Invoke(ctx, ContactService_GetContactSuggestions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContactServiceServer is the server API for ContactService service.
// All implementations must embed UnimplementedContactServiceServer
// for forward compatibility.
//
// Contact service for contact management
type ContactServiceServer interface {
	// Send contact request
	SendContactRequest(context.Context, *SendContactRequestRequest) (*SendContactRequestResponse, error)
	// Accept contact request
	AcceptContactRequest(context.Context, *AcceptContactRequestRequest) (*AcceptContactRequestResponse, error)
	// Reject contact request
	RejectContactRequest(context.Context, *RejectContactRequestRequest) (*RejectContactRequestResponse, error)
	// Cancel contact request
	CancelContactRequest(context.Context, *CancelContactRequestRequest) (*CancelContactRequestResponse, error)
	// Remove contact
	RemoveContact(context.Context, *RemoveContactRequest) (*RemoveContactResponse, error)
	// Block contact
	BlockContact(context.Context, *BlockContactRequest) (*BlockContactResponse, error)
	// Unblock contact
	UnblockContact(context.Context, *UnblockContactRequest) (*UnblockContactResponse, error)
	// Get user contacts
	GetUserContacts(context.Context, *GetUserContactsRequest) (*GetUserContactsResponse, error)
	// Get contact requests
	GetContactRequests(context.Context, *GetContactRequestsRequest) (*GetContactRequestsResponse, error)
	// Get contact by ID
	GetContact(context.Context, *GetContactRequest) (*GetContactResponse, error)
	// Search contacts
	SearchContacts(context.Context, *SearchContactsRequest) (*SearchContactsResponse, error)
	// Get contact suggestions
	GetContactSuggestions(context.Context, *GetContactSuggestionsRequest) (*GetContactSuggestionsResponse, error)
	mustEmbedUnimplementedContactServiceServer()
}

// UnimplementedContactServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedContactServiceServer struct{}

func (UnimplementedContactServiceServer) SendContactRequest(context.Context, *SendContactRequestRequest) (*SendContactRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendContactRequest not implemented")
}
func (UnimplementedContactServiceServer) AcceptContactRequest(context.Context, *AcceptContactRequestRequest) (*AcceptContactRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptContactRequest not implemented")
}
func (UnimplementedContactServiceServer) RejectContactRequest(context.Context, *RejectContactRequestRequest) (*RejectContactRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectContactRequest not implemented")
}
func (UnimplementedContactServiceServer) CancelContactRequest(context.Context, *CancelContactRequestRequest) (*CancelContactRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelContactRequest not implemented")
}
func (UnimplementedContactServiceServer) RemoveContact(context.Context, *RemoveContactRequest) (*RemoveContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveContact not implemented")
}
func (UnimplementedContactServiceServer) BlockContact(context.Context, *BlockContactRequest) (*BlockContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlockContact not implemented")
}
func (UnimplementedContactServiceServer) UnblockContact(context.Context, *UnblockContactRequest) (*UnblockContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnblockContact not implemented")
}
func (UnimplementedContactServiceServer) GetUserContacts(context.Context, *GetUserContactsRequest) (*GetUserContactsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserContacts not implemented")
}
func (UnimplementedContactServiceServer) GetContactRequests(context.Context, *GetContactRequestsRequest) (*GetContactRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContactRequests not implemented")
}
func (UnimplementedContactServiceServer) GetContact(context.Context, *GetContactRequest) (*GetContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContact not implemented")
}
func (UnimplementedContactServiceServer) SearchContacts(context.Context, *SearchContactsRequest) (*SearchContactsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchContacts not implemented")
}
func (UnimplementedContactServiceServer) GetContactSuggestions(context.Context, *GetContactSuggestionsRequest) (*GetContactSuggestionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContactSuggestions not implemented")
}
func (UnimplementedContactServiceServer) mustEmbedUnimplementedContactServiceServer() {}
func (UnimplementedContactServiceServer) testEmbeddedByValue()                        {}

// UnsafeContactServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ContactServiceServer will
// result in compilation errors.
type UnsafeContactServiceServer interface {
	mustEmbedUnimplementedContactServiceServer()
}

func RegisterContactServiceServer(s grpc.ServiceRegistrar, srv ContactServiceServer) {
	// If the following call pancis, it indicates UnimplementedContactServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ContactService_ServiceDesc, srv)
}

func _ContactService_SendContactRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendContactRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).SendContactRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_SendContactRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).SendContactRequest(ctx, req.(*SendContactRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_AcceptContactRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptContactRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).AcceptContactRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_AcceptContactRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).AcceptContactRequest(ctx, req.(*AcceptContactRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_RejectContactRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectContactRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).RejectContactRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_RejectContactRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).RejectContactRequest(ctx, req.(*RejectContactRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_CancelContactRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelContactRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).CancelContactRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_CancelContactRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).CancelContactRequest(ctx, req.(*CancelContactRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_RemoveContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).RemoveContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_RemoveContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).RemoveContact(ctx, req.(*RemoveContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_BlockContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).BlockContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_BlockContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).BlockContact(ctx, req.(*BlockContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_UnblockContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnblockContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).UnblockContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_UnblockContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).UnblockContact(ctx, req.(*UnblockContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_GetUserContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserContactsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).GetUserContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_GetUserContacts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).GetUserContacts(ctx, req.(*GetUserContactsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_GetContactRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).GetContactRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_GetContactRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).GetContactRequests(ctx, req.(*GetContactRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_GetContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).GetContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_GetContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).GetContact(ctx, req.(*GetContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_SearchContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchContactsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).SearchContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_SearchContacts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).SearchContacts(ctx, req.(*SearchContactsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactService_GetContactSuggestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactSuggestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactServiceServer).GetContactSuggestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactService_GetContactSuggestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactServiceServer).GetContactSuggestions(ctx, req.(*GetContactSuggestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ContactService_ServiceDesc is the grpc.ServiceDesc for ContactService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ContactService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.contact.v1.ContactService",
	HandlerType: (*ContactServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendContactRequest",
			Handler:    _ContactService_SendContactRequest_Handler,
		},
		{
			MethodName: "AcceptContactRequest",
			Handler:    _ContactService_AcceptContactRequest_Handler,
		},
		{
			MethodName: "RejectContactRequest",
			Handler:    _ContactService_RejectContactRequest_Handler,
		},
		{
			MethodName: "CancelContactRequest",
			Handler:    _ContactService_CancelContactRequest_Handler,
		},
		{
			MethodName: "RemoveContact",
			Handler:    _ContactService_RemoveContact_Handler,
		},
		{
			MethodName: "BlockContact",
			Handler:    _ContactService_BlockContact_Handler,
		},
		{
			MethodName: "UnblockContact",
			Handler:    _ContactService_UnblockContact_Handler,
		},
		{
			MethodName: "GetUserContacts",
			Handler:    _ContactService_GetUserContacts_Handler,
		},
		{
			MethodName: "GetContactRequests",
			Handler:    _ContactService_GetContactRequests_Handler,
		},
		{
			MethodName: "GetContact",
			Handler:    _ContactService_GetContact_Handler,
		},
		{
			MethodName: "SearchContacts",
			Handler:    _ContactService_SearchContacts_Handler,
		},
		{
			MethodName: "GetContactSuggestions",
			Handler:    _ContactService_GetContactSuggestions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "contact.proto",
}
