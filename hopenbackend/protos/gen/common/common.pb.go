// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Bubble status enum
type BubbleStatus int32

const (
	BubbleStatus_BUBBLE_STATUS_UNSPECIFIED BubbleStatus = 0
	BubbleStatus_BUBBLE_STATUS_ACTIVE      BubbleStatus = 1
	BubbleStatus_BUBBLE_STATUS_EXPIRED     BubbleStatus = 2
	BubbleStatus_BUBBLE_STATUS_DELETED     BubbleStatus = 3
)

// Enum value maps for BubbleStatus.
var (
	BubbleStatus_name = map[int32]string{
		0: "BUBBLE_STATUS_UNSPECIFIED",
		1: "BUBBLE_STATUS_ACTIVE",
		2: "BUBBLE_STATUS_EXPIRED",
		3: "BUBBLE_STATUS_DELETED",
	}
	BubbleStatus_value = map[string]int32{
		"BUBBLE_STATUS_UNSPECIFIED": 0,
		"BUBBLE_STATUS_ACTIVE":      1,
		"BUBBLE_STATUS_EXPIRED":     2,
		"BUBBLE_STATUS_DELETED":     3,
	}
)

func (x BubbleStatus) Enum() *BubbleStatus {
	p := new(BubbleStatus)
	*p = x
	return p
}

func (x BubbleStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BubbleStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[0].Descriptor()
}

func (BubbleStatus) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[0]
}

func (x BubbleStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BubbleStatus.Descriptor instead.
func (BubbleStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

// Contact status enum
type ContactStatus int32

const (
	ContactStatus_CONTACT_STATUS_UNSPECIFIED ContactStatus = 0
	ContactStatus_CONTACT_STATUS_PENDING     ContactStatus = 1
	ContactStatus_CONTACT_STATUS_ACCEPTED    ContactStatus = 2
	ContactStatus_CONTACT_STATUS_REJECTED    ContactStatus = 3
	ContactStatus_CONTACT_STATUS_BLOCKED     ContactStatus = 4
)

// Enum value maps for ContactStatus.
var (
	ContactStatus_name = map[int32]string{
		0: "CONTACT_STATUS_UNSPECIFIED",
		1: "CONTACT_STATUS_PENDING",
		2: "CONTACT_STATUS_ACCEPTED",
		3: "CONTACT_STATUS_REJECTED",
		4: "CONTACT_STATUS_BLOCKED",
	}
	ContactStatus_value = map[string]int32{
		"CONTACT_STATUS_UNSPECIFIED": 0,
		"CONTACT_STATUS_PENDING":     1,
		"CONTACT_STATUS_ACCEPTED":    2,
		"CONTACT_STATUS_REJECTED":    3,
		"CONTACT_STATUS_BLOCKED":     4,
	}
)

func (x ContactStatus) Enum() *ContactStatus {
	p := new(ContactStatus)
	*p = x
	return p
}

func (x ContactStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContactStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[1].Descriptor()
}

func (ContactStatus) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[1]
}

func (x ContactStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContactStatus.Descriptor instead.
func (ContactStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

// Friendship status enum
type FriendshipStatus int32

const (
	FriendshipStatus_FRIENDSHIP_STATUS_UNSPECIFIED FriendshipStatus = 0
	FriendshipStatus_FRIENDSHIP_STATUS_PENDING     FriendshipStatus = 1
	FriendshipStatus_FRIENDSHIP_STATUS_ACCEPTED    FriendshipStatus = 2
	FriendshipStatus_FRIENDSHIP_STATUS_REJECTED    FriendshipStatus = 3
	FriendshipStatus_FRIENDSHIP_STATUS_BLOCKED     FriendshipStatus = 4
)

// Enum value maps for FriendshipStatus.
var (
	FriendshipStatus_name = map[int32]string{
		0: "FRIENDSHIP_STATUS_UNSPECIFIED",
		1: "FRIENDSHIP_STATUS_PENDING",
		2: "FRIENDSHIP_STATUS_ACCEPTED",
		3: "FRIENDSHIP_STATUS_REJECTED",
		4: "FRIENDSHIP_STATUS_BLOCKED",
	}
	FriendshipStatus_value = map[string]int32{
		"FRIENDSHIP_STATUS_UNSPECIFIED": 0,
		"FRIENDSHIP_STATUS_PENDING":     1,
		"FRIENDSHIP_STATUS_ACCEPTED":    2,
		"FRIENDSHIP_STATUS_REJECTED":    3,
		"FRIENDSHIP_STATUS_BLOCKED":     4,
	}
)

func (x FriendshipStatus) Enum() *FriendshipStatus {
	p := new(FriendshipStatus)
	*p = x
	return p
}

func (x FriendshipStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FriendshipStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[2].Descriptor()
}

func (FriendshipStatus) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[2]
}

func (x FriendshipStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FriendshipStatus.Descriptor instead.
func (FriendshipStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

// Notification type enum
type NotificationType int32

const (
	NotificationType_NOTIFICATION_TYPE_UNSPECIFIED     NotificationType = 0
	NotificationType_NOTIFICATION_TYPE_CONTACT_REQUEST NotificationType = 1
	NotificationType_NOTIFICATION_TYPE_FRIEND_REQUEST  NotificationType = 2
	NotificationType_NOTIFICATION_TYPE_BUBBLE_INVITE   NotificationType = 3
	NotificationType_NOTIFICATION_TYPE_MESSAGE         NotificationType = 4
	NotificationType_NOTIFICATION_TYPE_CALL            NotificationType = 5
	NotificationType_NOTIFICATION_TYPE_SYSTEM          NotificationType = 6
)

// Enum value maps for NotificationType.
var (
	NotificationType_name = map[int32]string{
		0: "NOTIFICATION_TYPE_UNSPECIFIED",
		1: "NOTIFICATION_TYPE_CONTACT_REQUEST",
		2: "NOTIFICATION_TYPE_FRIEND_REQUEST",
		3: "NOTIFICATION_TYPE_BUBBLE_INVITE",
		4: "NOTIFICATION_TYPE_MESSAGE",
		5: "NOTIFICATION_TYPE_CALL",
		6: "NOTIFICATION_TYPE_SYSTEM",
	}
	NotificationType_value = map[string]int32{
		"NOTIFICATION_TYPE_UNSPECIFIED":     0,
		"NOTIFICATION_TYPE_CONTACT_REQUEST": 1,
		"NOTIFICATION_TYPE_FRIEND_REQUEST":  2,
		"NOTIFICATION_TYPE_BUBBLE_INVITE":   3,
		"NOTIFICATION_TYPE_MESSAGE":         4,
		"NOTIFICATION_TYPE_CALL":            5,
		"NOTIFICATION_TYPE_SYSTEM":          6,
	}
)

func (x NotificationType) Enum() *NotificationType {
	p := new(NotificationType)
	*p = x
	return p
}

func (x NotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[3].Descriptor()
}

func (NotificationType) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[3]
}

func (x NotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationType.Descriptor instead.
func (NotificationType) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{3}
}

// Common response wrapper for all API responses
type ApiResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ErrorCode     string                 `protobuf:"bytes,3,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiResponse) Reset() {
	*x = ApiResponse{}
	mi := &file_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiResponse) ProtoMessage() {}

func (x *ApiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiResponse.ProtoReflect.Descriptor instead.
func (*ApiResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *ApiResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ApiResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ApiResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ApiResponse) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

// User information used across services (no bio field as specified)
type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	FirstName     string                 `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	AvatarUrl     *string                `protobuf:"bytes,6,opt,name=avatar_url,json=avatarUrl,proto3,oneof" json:"avatar_url,omitempty"`
	BubbleId      *string                `protobuf:"bytes,7,opt,name=bubble_id,json=bubbleId,proto3,oneof" json:"bubble_id,omitempty"`
	IsOnline      bool                   `protobuf:"varint,8,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *User) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *User) GetAvatarUrl() string {
	if x != nil && x.AvatarUrl != nil {
		return *x.AvatarUrl
	}
	return ""
}

func (x *User) GetBubbleId() string {
	if x != nil && x.BubbleId != nil {
		return *x.BubbleId
	}
	return ""
}

func (x *User) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *User) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Bubble information used across services (no description field as specified)
type Bubble struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CreatorId     string                 `protobuf:"bytes,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Status        BubbleStatus           `protobuf:"varint,7,opt,name=status,proto3,enum=hopen.common.v1.BubbleStatus" json:"status,omitempty"`
	MemberCount   int32                  `protobuf:"varint,8,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	MemberIds     []string               `protobuf:"bytes,9,rep,name=member_ids,json=memberIds,proto3" json:"member_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bubble) Reset() {
	*x = Bubble{}
	mi := &file_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bubble) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bubble) ProtoMessage() {}

func (x *Bubble) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bubble.ProtoReflect.Descriptor instead.
func (*Bubble) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

func (x *Bubble) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Bubble) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Bubble) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *Bubble) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *Bubble) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Bubble) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Bubble) GetStatus() BubbleStatus {
	if x != nil {
		return x.Status
	}
	return BubbleStatus_BUBBLE_STATUS_UNSPECIFIED
}

func (x *Bubble) GetMemberCount() int32 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

func (x *Bubble) GetMemberIds() []string {
	if x != nil {
		return x.MemberIds
	}
	return nil
}

// Contact information
type Contact struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ContactUserId string                 `protobuf:"bytes,3,opt,name=contact_user_id,json=contactUserId,proto3" json:"contact_user_id,omitempty"`
	Status        ContactStatus          `protobuf:"varint,4,opt,name=status,proto3,enum=hopen.common.v1.ContactStatus" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Contact) Reset() {
	*x = Contact{}
	mi := &file_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{3}
}

func (x *Contact) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Contact) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Contact) GetContactUserId() string {
	if x != nil {
		return x.ContactUserId
	}
	return ""
}

func (x *Contact) GetStatus() ContactStatus {
	if x != nil {
		return x.Status
	}
	return ContactStatus_CONTACT_STATUS_UNSPECIFIED
}

func (x *Contact) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Contact) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Friendship information
type Friendship struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FriendId      string                 `protobuf:"bytes,3,opt,name=friend_id,json=friendId,proto3" json:"friend_id,omitempty"`
	Status        FriendshipStatus       `protobuf:"varint,4,opt,name=status,proto3,enum=hopen.common.v1.FriendshipStatus" json:"status,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Friendship) Reset() {
	*x = Friendship{}
	mi := &file_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Friendship) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Friendship) ProtoMessage() {}

func (x *Friendship) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Friendship.ProtoReflect.Descriptor instead.
func (*Friendship) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{4}
}

func (x *Friendship) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Friendship) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Friendship) GetFriendId() string {
	if x != nil {
		return x.FriendId
	}
	return ""
}

func (x *Friendship) GetStatus() FriendshipStatus {
	if x != nil {
		return x.Status
	}
	return FriendshipStatus_FRIENDSHIP_STATUS_UNSPECIFIED
}

func (x *Friendship) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Friendship) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Notification information
type Notification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Body          string                 `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`
	Type          NotificationType       `protobuf:"varint,5,opt,name=type,proto3,enum=hopen.common.v1.NotificationType" json:"type,omitempty"`
	Data          map[string]string      `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IsRead        bool                   `protobuf:"varint,7,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ReadAt        *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=read_at,json=readAt,proto3" json:"read_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notification) Reset() {
	*x = Notification{}
	mi := &file_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{5}
}

func (x *Notification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Notification) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Notification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Notification) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *Notification) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
}

func (x *Notification) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Notification) GetIsRead() bool {
	if x != nil {
		return x.IsRead
	}
	return false
}

func (x *Notification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Notification) GetReadAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadAt
	}
	return nil
}

// Pagination information
type Pagination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalCount    int32                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalPages    int32                  `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	HasNext       bool                   `protobuf:"varint,5,opt,name=has_next,json=hasNext,proto3" json:"has_next,omitempty"`
	HasPrev       bool                   `protobuf:"varint,6,opt,name=has_prev,json=hasPrev,proto3" json:"has_prev,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_common_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{6}
}

func (x *Pagination) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *Pagination) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *Pagination) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *Pagination) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *Pagination) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *Pagination) GetHasPrev() bool {
	if x != nil {
		return x.HasPrev
	}
	return false
}

// Error information
type Error struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Details       string                 `protobuf:"bytes,3,opt,name=details,proto3" json:"details,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,4,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Error) Reset() {
	*x = Error{}
	mi := &file_common_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{7}
}

func (x *Error) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Error) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *Error) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Empty response for operations that don't return data
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_common_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{8}
}

// Health check response
type HealthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Services      map[string]string      `protobuf:"bytes,4,rep,name=services,proto3" json:"services,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthResponse) Reset() {
	*x = HealthResponse{}
	mi := &file_common_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthResponse) ProtoMessage() {}

func (x *HealthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthResponse.ProtoReflect.Descriptor instead.
func (*HealthResponse) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{9}
}

func (x *HealthResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *HealthResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *HealthResponse) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *HealthResponse) GetServices() map[string]string {
	if x != nil {
		return x.Services
	}
	return nil
}

var File_common_proto protoreflect.FileDescriptor

const file_common_proto_rawDesc = "" +
	"\n" +
	"\fcommon.proto\x12\x0fhopen.common.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\x9a\x01\n" +
	"\vApiResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"error_code\x18\x03 \x01(\tR\terrorCode\x128\n" +
	"\ttimestamp\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\"\xfa\x02\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"first_name\x18\x04 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x05 \x01(\tR\blastName\x12\"\n" +
	"\n" +
	"avatar_url\x18\x06 \x01(\tH\x00R\tavatarUrl\x88\x01\x01\x12 \n" +
	"\tbubble_id\x18\a \x01(\tH\x01R\bbubbleId\x88\x01\x01\x12\x1b\n" +
	"\tis_online\x18\b \x01(\bR\bisOnline\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB\r\n" +
	"\v_avatar_urlB\f\n" +
	"\n" +
	"_bubble_id\"\xf5\x02\n" +
	"\x06Bubble\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"creator_id\x18\x03 \x01(\tR\tcreatorId\x129\n" +
	"\n" +
	"expires_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x125\n" +
	"\x06status\x18\a \x01(\x0e2\x1d.hopen.common.v1.BubbleStatusR\x06status\x12!\n" +
	"\fmember_count\x18\b \x01(\x05R\vmemberCount\x12\x1d\n" +
	"\n" +
	"member_ids\x18\t \x03(\tR\tmemberIds\"\x88\x02\n" +
	"\aContact\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12&\n" +
	"\x0fcontact_user_id\x18\x03 \x01(\tR\rcontactUserId\x126\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1e.hopen.common.v1.ContactStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x83\x02\n" +
	"\n" +
	"Friendship\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1b\n" +
	"\tfriend_id\x18\x03 \x01(\tR\bfriendId\x129\n" +
	"\x06status\x18\x04 \x01(\x0e2!.hopen.common.v1.FriendshipStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x97\x03\n" +
	"\fNotification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x12\n" +
	"\x04body\x18\x04 \x01(\tR\x04body\x125\n" +
	"\x04type\x18\x05 \x01(\x0e2!.hopen.common.v1.NotificationTypeR\x04type\x12;\n" +
	"\x04data\x18\x06 \x03(\v2'.hopen.common.v1.Notification.DataEntryR\x04data\x12\x17\n" +
	"\ais_read\x18\a \x01(\bR\x06isRead\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x123\n" +
	"\aread_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\x06readAt\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb5\x01\n" +
	"\n" +
	"Pagination\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vtotal_count\x18\x03 \x01(\x05R\n" +
	"totalCount\x12\x1f\n" +
	"\vtotal_pages\x18\x04 \x01(\x05R\n" +
	"totalPages\x12\x19\n" +
	"\bhas_next\x18\x05 \x01(\bR\ahasNext\x12\x19\n" +
	"\bhas_prev\x18\x06 \x01(\bR\ahasPrev\"\xce\x01\n" +
	"\x05Error\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\adetails\x18\x03 \x01(\tR\adetails\x12@\n" +
	"\bmetadata\x18\x04 \x03(\v2$.hopen.common.v1.Error.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\a\n" +
	"\x05Empty\"\x84\x02\n" +
	"\x0eHealthResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12I\n" +
	"\bservices\x18\x04 \x03(\v2-.hopen.common.v1.HealthResponse.ServicesEntryR\bservices\x1a;\n" +
	"\rServicesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*}\n" +
	"\fBubbleStatus\x12\x1d\n" +
	"\x19BUBBLE_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14BUBBLE_STATUS_ACTIVE\x10\x01\x12\x19\n" +
	"\x15BUBBLE_STATUS_EXPIRED\x10\x02\x12\x19\n" +
	"\x15BUBBLE_STATUS_DELETED\x10\x03*\xa1\x01\n" +
	"\rContactStatus\x12\x1e\n" +
	"\x1aCONTACT_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16CONTACT_STATUS_PENDING\x10\x01\x12\x1b\n" +
	"\x17CONTACT_STATUS_ACCEPTED\x10\x02\x12\x1b\n" +
	"\x17CONTACT_STATUS_REJECTED\x10\x03\x12\x1a\n" +
	"\x16CONTACT_STATUS_BLOCKED\x10\x04*\xb3\x01\n" +
	"\x10FriendshipStatus\x12!\n" +
	"\x1dFRIENDSHIP_STATUS_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19FRIENDSHIP_STATUS_PENDING\x10\x01\x12\x1e\n" +
	"\x1aFRIENDSHIP_STATUS_ACCEPTED\x10\x02\x12\x1e\n" +
	"\x1aFRIENDSHIP_STATUS_REJECTED\x10\x03\x12\x1d\n" +
	"\x19FRIENDSHIP_STATUS_BLOCKED\x10\x04*\x80\x02\n" +
	"\x10NotificationType\x12!\n" +
	"\x1dNOTIFICATION_TYPE_UNSPECIFIED\x10\x00\x12%\n" +
	"!NOTIFICATION_TYPE_CONTACT_REQUEST\x10\x01\x12$\n" +
	" NOTIFICATION_TYPE_FRIEND_REQUEST\x10\x02\x12#\n" +
	"\x1fNOTIFICATION_TYPE_BUBBLE_INVITE\x10\x03\x12\x1d\n" +
	"\x19NOTIFICATION_TYPE_MESSAGE\x10\x04\x12\x1a\n" +
	"\x16NOTIFICATION_TYPE_CALL\x10\x05\x12\x1c\n" +
	"\x18NOTIFICATION_TYPE_SYSTEM\x10\x06B>\n" +
	"\x13com.hopen.common.v1P\x01Z%hopenbackend/protos/gen/common;commonb\x06proto3"

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData []byte
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)))
	})
	return file_common_proto_rawDescData
}

var file_common_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_common_proto_goTypes = []any{
	(BubbleStatus)(0),             // 0: hopen.common.v1.BubbleStatus
	(ContactStatus)(0),            // 1: hopen.common.v1.ContactStatus
	(FriendshipStatus)(0),         // 2: hopen.common.v1.FriendshipStatus
	(NotificationType)(0),         // 3: hopen.common.v1.NotificationType
	(*ApiResponse)(nil),           // 4: hopen.common.v1.ApiResponse
	(*User)(nil),                  // 5: hopen.common.v1.User
	(*Bubble)(nil),                // 6: hopen.common.v1.Bubble
	(*Contact)(nil),               // 7: hopen.common.v1.Contact
	(*Friendship)(nil),            // 8: hopen.common.v1.Friendship
	(*Notification)(nil),          // 9: hopen.common.v1.Notification
	(*Pagination)(nil),            // 10: hopen.common.v1.Pagination
	(*Error)(nil),                 // 11: hopen.common.v1.Error
	(*Empty)(nil),                 // 12: hopen.common.v1.Empty
	(*HealthResponse)(nil),        // 13: hopen.common.v1.HealthResponse
	nil,                           // 14: hopen.common.v1.Notification.DataEntry
	nil,                           // 15: hopen.common.v1.Error.MetadataEntry
	nil,                           // 16: hopen.common.v1.HealthResponse.ServicesEntry
	(*timestamppb.Timestamp)(nil), // 17: google.protobuf.Timestamp
}
var file_common_proto_depIdxs = []int32{
	17, // 0: hopen.common.v1.ApiResponse.timestamp:type_name -> google.protobuf.Timestamp
	17, // 1: hopen.common.v1.User.created_at:type_name -> google.protobuf.Timestamp
	17, // 2: hopen.common.v1.User.updated_at:type_name -> google.protobuf.Timestamp
	17, // 3: hopen.common.v1.Bubble.expires_at:type_name -> google.protobuf.Timestamp
	17, // 4: hopen.common.v1.Bubble.created_at:type_name -> google.protobuf.Timestamp
	17, // 5: hopen.common.v1.Bubble.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 6: hopen.common.v1.Bubble.status:type_name -> hopen.common.v1.BubbleStatus
	1,  // 7: hopen.common.v1.Contact.status:type_name -> hopen.common.v1.ContactStatus
	17, // 8: hopen.common.v1.Contact.created_at:type_name -> google.protobuf.Timestamp
	17, // 9: hopen.common.v1.Contact.updated_at:type_name -> google.protobuf.Timestamp
	2,  // 10: hopen.common.v1.Friendship.status:type_name -> hopen.common.v1.FriendshipStatus
	17, // 11: hopen.common.v1.Friendship.created_at:type_name -> google.protobuf.Timestamp
	17, // 12: hopen.common.v1.Friendship.updated_at:type_name -> google.protobuf.Timestamp
	3,  // 13: hopen.common.v1.Notification.type:type_name -> hopen.common.v1.NotificationType
	14, // 14: hopen.common.v1.Notification.data:type_name -> hopen.common.v1.Notification.DataEntry
	17, // 15: hopen.common.v1.Notification.created_at:type_name -> google.protobuf.Timestamp
	17, // 16: hopen.common.v1.Notification.read_at:type_name -> google.protobuf.Timestamp
	15, // 17: hopen.common.v1.Error.metadata:type_name -> hopen.common.v1.Error.MetadataEntry
	17, // 18: hopen.common.v1.HealthResponse.timestamp:type_name -> google.protobuf.Timestamp
	16, // 19: hopen.common.v1.HealthResponse.services:type_name -> hopen.common.v1.HealthResponse.ServicesEntry
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	file_common_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		EnumInfos:         file_common_proto_enumTypes,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
