// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: presence.proto

package presence

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	common "hopenbackend/protos/gen/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Presence status enum
type PresenceStatus int32

const (
	PresenceStatus_PRESENCE_STATUS_UNSPECIFIED    PresenceStatus = 0
	PresenceStatus_PRESENCE_STATUS_ONLINE         PresenceStatus = 1
	PresenceStatus_PRESENCE_STATUS_AWAY           PresenceStatus = 2
	PresenceStatus_PRESENCE_STATUS_OFFLINE        PresenceStatus = 3
	PresenceStatus_PRESENCE_STATUS_BUSY           PresenceStatus = 4
	PresenceStatus_PRESENCE_STATUS_DO_NOT_DISTURB PresenceStatus = 5
)

// Enum value maps for PresenceStatus.
var (
	PresenceStatus_name = map[int32]string{
		0: "PRESENCE_STATUS_UNSPECIFIED",
		1: "PRESENCE_STATUS_ONLINE",
		2: "PRESENCE_STATUS_AWAY",
		3: "PRESENCE_STATUS_OFFLINE",
		4: "PRESENCE_STATUS_BUSY",
		5: "PRESENCE_STATUS_DO_NOT_DISTURB",
	}
	PresenceStatus_value = map[string]int32{
		"PRESENCE_STATUS_UNSPECIFIED":    0,
		"PRESENCE_STATUS_ONLINE":         1,
		"PRESENCE_STATUS_AWAY":           2,
		"PRESENCE_STATUS_OFFLINE":        3,
		"PRESENCE_STATUS_BUSY":           4,
		"PRESENCE_STATUS_DO_NOT_DISTURB": 5,
	}
)

func (x PresenceStatus) Enum() *PresenceStatus {
	p := new(PresenceStatus)
	*p = x
	return p
}

func (x PresenceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PresenceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_presence_proto_enumTypes[0].Descriptor()
}

func (PresenceStatus) Type() protoreflect.EnumType {
	return &file_presence_proto_enumTypes[0]
}

func (x PresenceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PresenceStatus.Descriptor instead.
func (PresenceStatus) EnumDescriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{0}
}

// Get user presence request
type GetUserPresenceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPresenceRequest) Reset() {
	*x = GetUserPresenceRequest{}
	mi := &file_presence_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPresenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPresenceRequest) ProtoMessage() {}

func (x *GetUserPresenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPresenceRequest.ProtoReflect.Descriptor instead.
func (*GetUserPresenceRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserPresenceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Get user presence response
type GetUserPresenceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Presence      *UserPresence          `protobuf:"bytes,1,opt,name=presence,proto3" json:"presence,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPresenceResponse) Reset() {
	*x = GetUserPresenceResponse{}
	mi := &file_presence_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPresenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPresenceResponse) ProtoMessage() {}

func (x *GetUserPresenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPresenceResponse.ProtoReflect.Descriptor instead.
func (*GetUserPresenceResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserPresenceResponse) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *GetUserPresenceResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Update user presence request
type UpdateUserPresenceRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status          PresenceStatus         `protobuf:"varint,2,opt,name=status,proto3,enum=hopen.presence.v1.PresenceStatus" json:"status,omitempty"`
	StatusMessage   string                 `protobuf:"bytes,3,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	IsTyping        bool                   `protobuf:"varint,4,opt,name=is_typing,json=isTyping,proto3" json:"is_typing,omitempty"`
	CurrentActivity string                 `protobuf:"bytes,5,opt,name=current_activity,json=currentActivity,proto3" json:"current_activity,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateUserPresenceRequest) Reset() {
	*x = UpdateUserPresenceRequest{}
	mi := &file_presence_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserPresenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserPresenceRequest) ProtoMessage() {}

func (x *UpdateUserPresenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserPresenceRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserPresenceRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateUserPresenceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateUserPresenceRequest) GetStatus() PresenceStatus {
	if x != nil {
		return x.Status
	}
	return PresenceStatus_PRESENCE_STATUS_UNSPECIFIED
}

func (x *UpdateUserPresenceRequest) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

func (x *UpdateUserPresenceRequest) GetIsTyping() bool {
	if x != nil {
		return x.IsTyping
	}
	return false
}

func (x *UpdateUserPresenceRequest) GetCurrentActivity() string {
	if x != nil {
		return x.CurrentActivity
	}
	return ""
}

// Update user presence response
type UpdateUserPresenceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Presence      *UserPresence          `protobuf:"bytes,1,opt,name=presence,proto3" json:"presence,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserPresenceResponse) Reset() {
	*x = UpdateUserPresenceResponse{}
	mi := &file_presence_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserPresenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserPresenceResponse) ProtoMessage() {}

func (x *UpdateUserPresenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserPresenceResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserPresenceResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUserPresenceResponse) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *UpdateUserPresenceResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get batch presence request
type GetBatchPresenceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserIds       []string               `protobuf:"bytes,1,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBatchPresenceRequest) Reset() {
	*x = GetBatchPresenceRequest{}
	mi := &file_presence_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBatchPresenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchPresenceRequest) ProtoMessage() {}

func (x *GetBatchPresenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchPresenceRequest.ProtoReflect.Descriptor instead.
func (*GetBatchPresenceRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{4}
}

func (x *GetBatchPresenceRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

// Get batch presence response
type GetBatchPresenceResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Presences     map[string]*UserPresence `protobuf:"bytes,1,rep,name=presences,proto3" json:"presences,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ApiResponse   *common.ApiResponse      `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBatchPresenceResponse) Reset() {
	*x = GetBatchPresenceResponse{}
	mi := &file_presence_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBatchPresenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchPresenceResponse) ProtoMessage() {}

func (x *GetBatchPresenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchPresenceResponse.ProtoReflect.Descriptor instead.
func (*GetBatchPresenceResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{5}
}

func (x *GetBatchPresenceResponse) GetPresences() map[string]*UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

func (x *GetBatchPresenceResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get online users request
type GetOnlineUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        int32                  `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineUsersRequest) Reset() {
	*x = GetOnlineUsersRequest{}
	mi := &file_presence_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineUsersRequest) ProtoMessage() {}

func (x *GetOnlineUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineUsersRequest.ProtoReflect.Descriptor instead.
func (*GetOnlineUsersRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{6}
}

func (x *GetOnlineUsersRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *GetOnlineUsersRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetOnlineUsersRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// Get online users response
type GetOnlineUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnlineUsers   []*UserPresence        `protobuf:"bytes,1,rep,name=online_users,json=onlineUsers,proto3" json:"online_users,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,3,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineUsersResponse) Reset() {
	*x = GetOnlineUsersResponse{}
	mi := &file_presence_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineUsersResponse) ProtoMessage() {}

func (x *GetOnlineUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineUsersResponse.ProtoReflect.Descriptor instead.
func (*GetOnlineUsersResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{7}
}

func (x *GetOnlineUsersResponse) GetOnlineUsers() []*UserPresence {
	if x != nil {
		return x.OnlineUsers
	}
	return nil
}

func (x *GetOnlineUsersResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetOnlineUsersResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get presence history request
type GetPresenceHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Limit         int32                  `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresenceHistoryRequest) Reset() {
	*x = GetPresenceHistoryRequest{}
	mi := &file_presence_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresenceHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresenceHistoryRequest) ProtoMessage() {}

func (x *GetPresenceHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresenceHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetPresenceHistoryRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{8}
}

func (x *GetPresenceHistoryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetPresenceHistoryRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetPresenceHistoryRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GetPresenceHistoryRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// Get presence history response
type GetPresenceHistoryResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	History       []*PresenceHistoryEntry `protobuf:"bytes,1,rep,name=history,proto3" json:"history,omitempty"`
	ApiResponse   *common.ApiResponse     `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresenceHistoryResponse) Reset() {
	*x = GetPresenceHistoryResponse{}
	mi := &file_presence_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresenceHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresenceHistoryResponse) ProtoMessage() {}

func (x *GetPresenceHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresenceHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetPresenceHistoryResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{9}
}

func (x *GetPresenceHistoryResponse) GetHistory() []*PresenceHistoryEntry {
	if x != nil {
		return x.History
	}
	return nil
}

func (x *GetPresenceHistoryResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Set user away request
type SetUserAwayRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	AwayMessage   string                 `protobuf:"bytes,2,opt,name=away_message,json=awayMessage,proto3" json:"away_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserAwayRequest) Reset() {
	*x = SetUserAwayRequest{}
	mi := &file_presence_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserAwayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserAwayRequest) ProtoMessage() {}

func (x *SetUserAwayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserAwayRequest.ProtoReflect.Descriptor instead.
func (*SetUserAwayRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{10}
}

func (x *SetUserAwayRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SetUserAwayRequest) GetAwayMessage() string {
	if x != nil {
		return x.AwayMessage
	}
	return ""
}

// Set user away response
type SetUserAwayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Presence      *UserPresence          `protobuf:"bytes,1,opt,name=presence,proto3" json:"presence,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserAwayResponse) Reset() {
	*x = SetUserAwayResponse{}
	mi := &file_presence_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserAwayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserAwayResponse) ProtoMessage() {}

func (x *SetUserAwayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserAwayResponse.ProtoReflect.Descriptor instead.
func (*SetUserAwayResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{11}
}

func (x *SetUserAwayResponse) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *SetUserAwayResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Set user online request
type SetUserOnlineRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StatusMessage string                 `protobuf:"bytes,2,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserOnlineRequest) Reset() {
	*x = SetUserOnlineRequest{}
	mi := &file_presence_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserOnlineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserOnlineRequest) ProtoMessage() {}

func (x *SetUserOnlineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserOnlineRequest.ProtoReflect.Descriptor instead.
func (*SetUserOnlineRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{12}
}

func (x *SetUserOnlineRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SetUserOnlineRequest) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

// Set user online response
type SetUserOnlineResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Presence      *UserPresence          `protobuf:"bytes,1,opt,name=presence,proto3" json:"presence,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserOnlineResponse) Reset() {
	*x = SetUserOnlineResponse{}
	mi := &file_presence_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserOnlineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserOnlineResponse) ProtoMessage() {}

func (x *SetUserOnlineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserOnlineResponse.ProtoReflect.Descriptor instead.
func (*SetUserOnlineResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{13}
}

func (x *SetUserOnlineResponse) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *SetUserOnlineResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Get presence stats request
type GetPresenceStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BubbleId      string                 `protobuf:"bytes,1,opt,name=bubble_id,json=bubbleId,proto3" json:"bubble_id,omitempty"`
	Period        string                 `protobuf:"bytes,2,opt,name=period,proto3" json:"period,omitempty"` // hourly, daily, weekly
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresenceStatsRequest) Reset() {
	*x = GetPresenceStatsRequest{}
	mi := &file_presence_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresenceStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresenceStatsRequest) ProtoMessage() {}

func (x *GetPresenceStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresenceStatsRequest.ProtoReflect.Descriptor instead.
func (*GetPresenceStatsRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{14}
}

func (x *GetPresenceStatsRequest) GetBubbleId() string {
	if x != nil {
		return x.BubbleId
	}
	return ""
}

func (x *GetPresenceStatsRequest) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

// Get presence stats response
type GetPresenceStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *PresenceStats         `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	ApiResponse   *common.ApiResponse    `protobuf:"bytes,2,opt,name=api_response,json=apiResponse,proto3" json:"api_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresenceStatsResponse) Reset() {
	*x = GetPresenceStatsResponse{}
	mi := &file_presence_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresenceStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresenceStatsResponse) ProtoMessage() {}

func (x *GetPresenceStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresenceStatsResponse.ProtoReflect.Descriptor instead.
func (*GetPresenceStatsResponse) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{15}
}

func (x *GetPresenceStatsResponse) GetStats() *PresenceStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetPresenceStatsResponse) GetApiResponse() *common.ApiResponse {
	if x != nil {
		return x.ApiResponse
	}
	return nil
}

// Subscribe to presence updates request
type SubscribeToPresenceUpdatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetUserIds []string               `protobuf:"bytes,2,rep,name=target_user_ids,json=targetUserIds,proto3" json:"target_user_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeToPresenceUpdatesRequest) Reset() {
	*x = SubscribeToPresenceUpdatesRequest{}
	mi := &file_presence_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeToPresenceUpdatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeToPresenceUpdatesRequest) ProtoMessage() {}

func (x *SubscribeToPresenceUpdatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeToPresenceUpdatesRequest.ProtoReflect.Descriptor instead.
func (*SubscribeToPresenceUpdatesRequest) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{16}
}

func (x *SubscribeToPresenceUpdatesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SubscribeToPresenceUpdatesRequest) GetTargetUserIds() []string {
	if x != nil {
		return x.TargetUserIds
	}
	return nil
}

// Presence update stream
type PresenceUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Presence      *UserPresence          `protobuf:"bytes,2,opt,name=presence,proto3" json:"presence,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	UpdateType    string                 `protobuf:"bytes,4,opt,name=update_type,json=updateType,proto3" json:"update_type,omitempty"` // status_change, activity_change, typing
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceUpdate) Reset() {
	*x = PresenceUpdate{}
	mi := &file_presence_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceUpdate) ProtoMessage() {}

func (x *PresenceUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceUpdate.ProtoReflect.Descriptor instead.
func (*PresenceUpdate) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{17}
}

func (x *PresenceUpdate) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PresenceUpdate) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *PresenceUpdate) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *PresenceUpdate) GetUpdateType() string {
	if x != nil {
		return x.UpdateType
	}
	return ""
}

// User presence information
type UserPresence struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status          PresenceStatus         `protobuf:"varint,2,opt,name=status,proto3,enum=hopen.presence.v1.PresenceStatus" json:"status,omitempty"`
	StatusMessage   string                 `protobuf:"bytes,3,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	LastSeen        *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_seen,json=lastSeen,proto3" json:"last_seen,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	IsOnline        bool                   `protobuf:"varint,6,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	IsTyping        bool                   `protobuf:"varint,7,opt,name=is_typing,json=isTyping,proto3" json:"is_typing,omitempty"`
	CurrentActivity string                 `protobuf:"bytes,8,opt,name=current_activity,json=currentActivity,proto3" json:"current_activity,omitempty"`
	CurrentBubbleId string                 `protobuf:"bytes,9,opt,name=current_bubble_id,json=currentBubbleId,proto3" json:"current_bubble_id,omitempty"`
	ActiveSessions  []string               `protobuf:"bytes,10,rep,name=active_sessions,json=activeSessions,proto3" json:"active_sessions,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UserPresence) Reset() {
	*x = UserPresence{}
	mi := &file_presence_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPresence) ProtoMessage() {}

func (x *UserPresence) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPresence.ProtoReflect.Descriptor instead.
func (*UserPresence) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{18}
}

func (x *UserPresence) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserPresence) GetStatus() PresenceStatus {
	if x != nil {
		return x.Status
	}
	return PresenceStatus_PRESENCE_STATUS_UNSPECIFIED
}

func (x *UserPresence) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

func (x *UserPresence) GetLastSeen() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSeen
	}
	return nil
}

func (x *UserPresence) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UserPresence) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *UserPresence) GetIsTyping() bool {
	if x != nil {
		return x.IsTyping
	}
	return false
}

func (x *UserPresence) GetCurrentActivity() string {
	if x != nil {
		return x.CurrentActivity
	}
	return ""
}

func (x *UserPresence) GetCurrentBubbleId() string {
	if x != nil {
		return x.CurrentBubbleId
	}
	return ""
}

func (x *UserPresence) GetActiveSessions() []string {
	if x != nil {
		return x.ActiveSessions
	}
	return nil
}

// Presence history entry
type PresenceHistoryEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status        PresenceStatus         `protobuf:"varint,2,opt,name=status,proto3,enum=hopen.presence.v1.PresenceStatus" json:"status,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	StatusMessage string                 `protobuf:"bytes,4,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	Activity      string                 `protobuf:"bytes,5,opt,name=activity,proto3" json:"activity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceHistoryEntry) Reset() {
	*x = PresenceHistoryEntry{}
	mi := &file_presence_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceHistoryEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceHistoryEntry) ProtoMessage() {}

func (x *PresenceHistoryEntry) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceHistoryEntry.ProtoReflect.Descriptor instead.
func (*PresenceHistoryEntry) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{19}
}

func (x *PresenceHistoryEntry) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PresenceHistoryEntry) GetStatus() PresenceStatus {
	if x != nil {
		return x.Status
	}
	return PresenceStatus_PRESENCE_STATUS_UNSPECIFIED
}

func (x *PresenceHistoryEntry) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *PresenceHistoryEntry) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

func (x *PresenceHistoryEntry) GetActivity() string {
	if x != nil {
		return x.Activity
	}
	return ""
}

// Presence statistics
type PresenceStats struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TotalUsers         int32                  `protobuf:"varint,1,opt,name=total_users,json=totalUsers,proto3" json:"total_users,omitempty"`
	OnlineUsers        int32                  `protobuf:"varint,2,opt,name=online_users,json=onlineUsers,proto3" json:"online_users,omitempty"`
	AwayUsers          int32                  `protobuf:"varint,3,opt,name=away_users,json=awayUsers,proto3" json:"away_users,omitempty"`
	OfflineUsers       int32                  `protobuf:"varint,4,opt,name=offline_users,json=offlineUsers,proto3" json:"offline_users,omitempty"`
	OnlinePercentage   float32                `protobuf:"fixed32,5,opt,name=online_percentage,json=onlinePercentage,proto3" json:"online_percentage,omitempty"`
	StatusDistribution map[string]int32       `protobuf:"bytes,6,rep,name=status_distribution,json=statusDistribution,proto3" json:"status_distribution,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MostActiveUsers    []string               `protobuf:"bytes,7,rep,name=most_active_users,json=mostActiveUsers,proto3" json:"most_active_users,omitempty"`
	LastUpdated        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PresenceStats) Reset() {
	*x = PresenceStats{}
	mi := &file_presence_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceStats) ProtoMessage() {}

func (x *PresenceStats) ProtoReflect() protoreflect.Message {
	mi := &file_presence_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceStats.ProtoReflect.Descriptor instead.
func (*PresenceStats) Descriptor() ([]byte, []int) {
	return file_presence_proto_rawDescGZIP(), []int{20}
}

func (x *PresenceStats) GetTotalUsers() int32 {
	if x != nil {
		return x.TotalUsers
	}
	return 0
}

func (x *PresenceStats) GetOnlineUsers() int32 {
	if x != nil {
		return x.OnlineUsers
	}
	return 0
}

func (x *PresenceStats) GetAwayUsers() int32 {
	if x != nil {
		return x.AwayUsers
	}
	return 0
}

func (x *PresenceStats) GetOfflineUsers() int32 {
	if x != nil {
		return x.OfflineUsers
	}
	return 0
}

func (x *PresenceStats) GetOnlinePercentage() float32 {
	if x != nil {
		return x.OnlinePercentage
	}
	return 0
}

func (x *PresenceStats) GetStatusDistribution() map[string]int32 {
	if x != nil {
		return x.StatusDistribution
	}
	return nil
}

func (x *PresenceStats) GetMostActiveUsers() []string {
	if x != nil {
		return x.MostActiveUsers
	}
	return nil
}

func (x *PresenceStats) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

var File_presence_proto protoreflect.FileDescriptor

const file_presence_proto_rawDesc = "" +
	"\n" +
	"\x0epresence.proto\x12\x11hopen.presence.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\fcommon.proto\"1\n" +
	"\x16GetUserPresenceRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"\x97\x01\n" +
	"\x17GetUserPresenceResponse\x12;\n" +
	"\bpresence\x18\x01 \x01(\v2\x1f.hopen.presence.v1.UserPresenceR\bpresence\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xde\x01\n" +
	"\x19UpdateUserPresenceRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x129\n" +
	"\x06status\x18\x02 \x01(\x0e2!.hopen.presence.v1.PresenceStatusR\x06status\x12%\n" +
	"\x0estatus_message\x18\x03 \x01(\tR\rstatusMessage\x12\x1b\n" +
	"\tis_typing\x18\x04 \x01(\bR\bisTyping\x12)\n" +
	"\x10current_activity\x18\x05 \x01(\tR\x0fcurrentActivity\"\x9a\x01\n" +
	"\x1aUpdateUserPresenceResponse\x12;\n" +
	"\bpresence\x18\x01 \x01(\v2\x1f.hopen.presence.v1.UserPresenceR\bpresence\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"4\n" +
	"\x17GetBatchPresenceRequest\x12\x19\n" +
	"\buser_ids\x18\x01 \x03(\tR\auserIds\"\x94\x02\n" +
	"\x18GetBatchPresenceResponse\x12X\n" +
	"\tpresences\x18\x01 \x03(\v2:.hopen.presence.v1.GetBatchPresenceResponse.PresencesEntryR\tpresences\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\x1a]\n" +
	"\x0ePresencesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x125\n" +
	"\x05value\x18\x02 \x01(\v2\x1f.hopen.presence.v1.UserPresenceR\x05value:\x028\x01\"b\n" +
	"\x15GetOnlineUsersRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x03 \x01(\x05R\x06offset\"\xbe\x01\n" +
	"\x16GetOnlineUsersResponse\x12B\n" +
	"\fonline_users\x18\x01 \x03(\v2\x1f.hopen.presence.v1.UserPresenceR\vonlineUsers\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\x12?\n" +
	"\fapi_response\x18\x03 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"\xbc\x01\n" +
	"\x19GetPresenceHistoryRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x14\n" +
	"\x05limit\x18\x04 \x01(\x05R\x05limit\"\xa0\x01\n" +
	"\x1aGetPresenceHistoryResponse\x12A\n" +
	"\ahistory\x18\x01 \x03(\v2'.hopen.presence.v1.PresenceHistoryEntryR\ahistory\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"P\n" +
	"\x12SetUserAwayRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12!\n" +
	"\faway_message\x18\x02 \x01(\tR\vawayMessage\"\x93\x01\n" +
	"\x13SetUserAwayResponse\x12;\n" +
	"\bpresence\x18\x01 \x01(\v2\x1f.hopen.presence.v1.UserPresenceR\bpresence\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"V\n" +
	"\x14SetUserOnlineRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12%\n" +
	"\x0estatus_message\x18\x02 \x01(\tR\rstatusMessage\"\x95\x01\n" +
	"\x15SetUserOnlineResponse\x12;\n" +
	"\bpresence\x18\x01 \x01(\v2\x1f.hopen.presence.v1.UserPresenceR\bpresence\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"N\n" +
	"\x17GetPresenceStatsRequest\x12\x1b\n" +
	"\tbubble_id\x18\x01 \x01(\tR\bbubbleId\x12\x16\n" +
	"\x06period\x18\x02 \x01(\tR\x06period\"\x93\x01\n" +
	"\x18GetPresenceStatsResponse\x126\n" +
	"\x05stats\x18\x01 \x01(\v2 .hopen.presence.v1.PresenceStatsR\x05stats\x12?\n" +
	"\fapi_response\x18\x02 \x01(\v2\x1c.hopen.common.v1.ApiResponseR\vapiResponse\"d\n" +
	"!SubscribeToPresenceUpdatesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12&\n" +
	"\x0ftarget_user_ids\x18\x02 \x03(\tR\rtargetUserIds\"\xc1\x01\n" +
	"\x0ePresenceUpdate\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12;\n" +
	"\bpresence\x18\x02 \x01(\v2\x1f.hopen.presence.v1.UserPresenceR\bpresence\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x1f\n" +
	"\vupdate_type\x18\x04 \x01(\tR\n" +
	"updateType\"\xb7\x03\n" +
	"\fUserPresence\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x129\n" +
	"\x06status\x18\x02 \x01(\x0e2!.hopen.presence.v1.PresenceStatusR\x06status\x12%\n" +
	"\x0estatus_message\x18\x03 \x01(\tR\rstatusMessage\x127\n" +
	"\tlast_seen\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\blastSeen\x129\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x1b\n" +
	"\tis_online\x18\x06 \x01(\bR\bisOnline\x12\x1b\n" +
	"\tis_typing\x18\a \x01(\bR\bisTyping\x12)\n" +
	"\x10current_activity\x18\b \x01(\tR\x0fcurrentActivity\x12*\n" +
	"\x11current_bubble_id\x18\t \x01(\tR\x0fcurrentBubbleId\x12'\n" +
	"\x0factive_sessions\x18\n" +
	" \x03(\tR\x0eactiveSessions\"\xe7\x01\n" +
	"\x14PresenceHistoryEntry\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x129\n" +
	"\x06status\x18\x02 \x01(\x0e2!.hopen.presence.v1.PresenceStatusR\x06status\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12%\n" +
	"\x0estatus_message\x18\x04 \x01(\tR\rstatusMessage\x12\x1a\n" +
	"\bactivity\x18\x05 \x01(\tR\bactivity\"\xe1\x03\n" +
	"\rPresenceStats\x12\x1f\n" +
	"\vtotal_users\x18\x01 \x01(\x05R\n" +
	"totalUsers\x12!\n" +
	"\fonline_users\x18\x02 \x01(\x05R\vonlineUsers\x12\x1d\n" +
	"\n" +
	"away_users\x18\x03 \x01(\x05R\tawayUsers\x12#\n" +
	"\roffline_users\x18\x04 \x01(\x05R\fofflineUsers\x12+\n" +
	"\x11online_percentage\x18\x05 \x01(\x02R\x10onlinePercentage\x12i\n" +
	"\x13status_distribution\x18\x06 \x03(\v28.hopen.presence.v1.PresenceStats.StatusDistributionEntryR\x12statusDistribution\x12*\n" +
	"\x11most_active_users\x18\a \x03(\tR\x0fmostActiveUsers\x12=\n" +
	"\flast_updated\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\vlastUpdated\x1aE\n" +
	"\x17StatusDistributionEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01*\xc2\x01\n" +
	"\x0ePresenceStatus\x12\x1f\n" +
	"\x1bPRESENCE_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16PRESENCE_STATUS_ONLINE\x10\x01\x12\x18\n" +
	"\x14PRESENCE_STATUS_AWAY\x10\x02\x12\x1b\n" +
	"\x17PRESENCE_STATUS_OFFLINE\x10\x03\x12\x18\n" +
	"\x14PRESENCE_STATUS_BUSY\x10\x04\x12\"\n" +
	"\x1ePRESENCE_STATUS_DO_NOT_DISTURB\x10\x052\xdd\a\n" +
	"\x0fPresenceService\x12h\n" +
	"\x0fGetUserPresence\x12).hopen.presence.v1.GetUserPresenceRequest\x1a*.hopen.presence.v1.GetUserPresenceResponse\x12q\n" +
	"\x12UpdateUserPresence\x12,.hopen.presence.v1.UpdateUserPresenceRequest\x1a-.hopen.presence.v1.UpdateUserPresenceResponse\x12k\n" +
	"\x10GetBatchPresence\x12*.hopen.presence.v1.GetBatchPresenceRequest\x1a+.hopen.presence.v1.GetBatchPresenceResponse\x12e\n" +
	"\x0eGetOnlineUsers\x12(.hopen.presence.v1.GetOnlineUsersRequest\x1a).hopen.presence.v1.GetOnlineUsersResponse\x12q\n" +
	"\x12GetPresenceHistory\x12,.hopen.presence.v1.GetPresenceHistoryRequest\x1a-.hopen.presence.v1.GetPresenceHistoryResponse\x12\\\n" +
	"\vSetUserAway\x12%.hopen.presence.v1.SetUserAwayRequest\x1a&.hopen.presence.v1.SetUserAwayResponse\x12b\n" +
	"\rSetUserOnline\x12'.hopen.presence.v1.SetUserOnlineRequest\x1a(.hopen.presence.v1.SetUserOnlineResponse\x12k\n" +
	"\x10GetPresenceStats\x12*.hopen.presence.v1.GetPresenceStatsRequest\x1a+.hopen.presence.v1.GetPresenceStatsResponse\x12w\n" +
	"\x1aSubscribeToPresenceUpdates\x124.hopen.presence.v1.SubscribeToPresenceUpdatesRequest\x1a!.hopen.presence.v1.PresenceUpdate0\x01BD\n" +
	"\x15com.hopen.presence.v1P\x01Z)hopenbackend/protos/gen/presence;presenceb\x06proto3"

var (
	file_presence_proto_rawDescOnce sync.Once
	file_presence_proto_rawDescData []byte
)

func file_presence_proto_rawDescGZIP() []byte {
	file_presence_proto_rawDescOnce.Do(func() {
		file_presence_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_presence_proto_rawDesc), len(file_presence_proto_rawDesc)))
	})
	return file_presence_proto_rawDescData
}

var file_presence_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_presence_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_presence_proto_goTypes = []any{
	(PresenceStatus)(0),                       // 0: hopen.presence.v1.PresenceStatus
	(*GetUserPresenceRequest)(nil),            // 1: hopen.presence.v1.GetUserPresenceRequest
	(*GetUserPresenceResponse)(nil),           // 2: hopen.presence.v1.GetUserPresenceResponse
	(*UpdateUserPresenceRequest)(nil),         // 3: hopen.presence.v1.UpdateUserPresenceRequest
	(*UpdateUserPresenceResponse)(nil),        // 4: hopen.presence.v1.UpdateUserPresenceResponse
	(*GetBatchPresenceRequest)(nil),           // 5: hopen.presence.v1.GetBatchPresenceRequest
	(*GetBatchPresenceResponse)(nil),          // 6: hopen.presence.v1.GetBatchPresenceResponse
	(*GetOnlineUsersRequest)(nil),             // 7: hopen.presence.v1.GetOnlineUsersRequest
	(*GetOnlineUsersResponse)(nil),            // 8: hopen.presence.v1.GetOnlineUsersResponse
	(*GetPresenceHistoryRequest)(nil),         // 9: hopen.presence.v1.GetPresenceHistoryRequest
	(*GetPresenceHistoryResponse)(nil),        // 10: hopen.presence.v1.GetPresenceHistoryResponse
	(*SetUserAwayRequest)(nil),                // 11: hopen.presence.v1.SetUserAwayRequest
	(*SetUserAwayResponse)(nil),               // 12: hopen.presence.v1.SetUserAwayResponse
	(*SetUserOnlineRequest)(nil),              // 13: hopen.presence.v1.SetUserOnlineRequest
	(*SetUserOnlineResponse)(nil),             // 14: hopen.presence.v1.SetUserOnlineResponse
	(*GetPresenceStatsRequest)(nil),           // 15: hopen.presence.v1.GetPresenceStatsRequest
	(*GetPresenceStatsResponse)(nil),          // 16: hopen.presence.v1.GetPresenceStatsResponse
	(*SubscribeToPresenceUpdatesRequest)(nil), // 17: hopen.presence.v1.SubscribeToPresenceUpdatesRequest
	(*PresenceUpdate)(nil),                    // 18: hopen.presence.v1.PresenceUpdate
	(*UserPresence)(nil),                      // 19: hopen.presence.v1.UserPresence
	(*PresenceHistoryEntry)(nil),              // 20: hopen.presence.v1.PresenceHistoryEntry
	(*PresenceStats)(nil),                     // 21: hopen.presence.v1.PresenceStats
	nil,                                       // 22: hopen.presence.v1.GetBatchPresenceResponse.PresencesEntry
	nil,                                       // 23: hopen.presence.v1.PresenceStats.StatusDistributionEntry
	(*common.ApiResponse)(nil),                // 24: hopen.common.v1.ApiResponse
	(*timestamppb.Timestamp)(nil),             // 25: google.protobuf.Timestamp
}
var file_presence_proto_depIdxs = []int32{
	19, // 0: hopen.presence.v1.GetUserPresenceResponse.presence:type_name -> hopen.presence.v1.UserPresence
	24, // 1: hopen.presence.v1.GetUserPresenceResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	0,  // 2: hopen.presence.v1.UpdateUserPresenceRequest.status:type_name -> hopen.presence.v1.PresenceStatus
	19, // 3: hopen.presence.v1.UpdateUserPresenceResponse.presence:type_name -> hopen.presence.v1.UserPresence
	24, // 4: hopen.presence.v1.UpdateUserPresenceResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	22, // 5: hopen.presence.v1.GetBatchPresenceResponse.presences:type_name -> hopen.presence.v1.GetBatchPresenceResponse.PresencesEntry
	24, // 6: hopen.presence.v1.GetBatchPresenceResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	19, // 7: hopen.presence.v1.GetOnlineUsersResponse.online_users:type_name -> hopen.presence.v1.UserPresence
	24, // 8: hopen.presence.v1.GetOnlineUsersResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	25, // 9: hopen.presence.v1.GetPresenceHistoryRequest.start_time:type_name -> google.protobuf.Timestamp
	25, // 10: hopen.presence.v1.GetPresenceHistoryRequest.end_time:type_name -> google.protobuf.Timestamp
	20, // 11: hopen.presence.v1.GetPresenceHistoryResponse.history:type_name -> hopen.presence.v1.PresenceHistoryEntry
	24, // 12: hopen.presence.v1.GetPresenceHistoryResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	19, // 13: hopen.presence.v1.SetUserAwayResponse.presence:type_name -> hopen.presence.v1.UserPresence
	24, // 14: hopen.presence.v1.SetUserAwayResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	19, // 15: hopen.presence.v1.SetUserOnlineResponse.presence:type_name -> hopen.presence.v1.UserPresence
	24, // 16: hopen.presence.v1.SetUserOnlineResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	21, // 17: hopen.presence.v1.GetPresenceStatsResponse.stats:type_name -> hopen.presence.v1.PresenceStats
	24, // 18: hopen.presence.v1.GetPresenceStatsResponse.api_response:type_name -> hopen.common.v1.ApiResponse
	19, // 19: hopen.presence.v1.PresenceUpdate.presence:type_name -> hopen.presence.v1.UserPresence
	25, // 20: hopen.presence.v1.PresenceUpdate.timestamp:type_name -> google.protobuf.Timestamp
	0,  // 21: hopen.presence.v1.UserPresence.status:type_name -> hopen.presence.v1.PresenceStatus
	25, // 22: hopen.presence.v1.UserPresence.last_seen:type_name -> google.protobuf.Timestamp
	25, // 23: hopen.presence.v1.UserPresence.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 24: hopen.presence.v1.PresenceHistoryEntry.status:type_name -> hopen.presence.v1.PresenceStatus
	25, // 25: hopen.presence.v1.PresenceHistoryEntry.timestamp:type_name -> google.protobuf.Timestamp
	23, // 26: hopen.presence.v1.PresenceStats.status_distribution:type_name -> hopen.presence.v1.PresenceStats.StatusDistributionEntry
	25, // 27: hopen.presence.v1.PresenceStats.last_updated:type_name -> google.protobuf.Timestamp
	19, // 28: hopen.presence.v1.GetBatchPresenceResponse.PresencesEntry.value:type_name -> hopen.presence.v1.UserPresence
	1,  // 29: hopen.presence.v1.PresenceService.GetUserPresence:input_type -> hopen.presence.v1.GetUserPresenceRequest
	3,  // 30: hopen.presence.v1.PresenceService.UpdateUserPresence:input_type -> hopen.presence.v1.UpdateUserPresenceRequest
	5,  // 31: hopen.presence.v1.PresenceService.GetBatchPresence:input_type -> hopen.presence.v1.GetBatchPresenceRequest
	7,  // 32: hopen.presence.v1.PresenceService.GetOnlineUsers:input_type -> hopen.presence.v1.GetOnlineUsersRequest
	9,  // 33: hopen.presence.v1.PresenceService.GetPresenceHistory:input_type -> hopen.presence.v1.GetPresenceHistoryRequest
	11, // 34: hopen.presence.v1.PresenceService.SetUserAway:input_type -> hopen.presence.v1.SetUserAwayRequest
	13, // 35: hopen.presence.v1.PresenceService.SetUserOnline:input_type -> hopen.presence.v1.SetUserOnlineRequest
	15, // 36: hopen.presence.v1.PresenceService.GetPresenceStats:input_type -> hopen.presence.v1.GetPresenceStatsRequest
	17, // 37: hopen.presence.v1.PresenceService.SubscribeToPresenceUpdates:input_type -> hopen.presence.v1.SubscribeToPresenceUpdatesRequest
	2,  // 38: hopen.presence.v1.PresenceService.GetUserPresence:output_type -> hopen.presence.v1.GetUserPresenceResponse
	4,  // 39: hopen.presence.v1.PresenceService.UpdateUserPresence:output_type -> hopen.presence.v1.UpdateUserPresenceResponse
	6,  // 40: hopen.presence.v1.PresenceService.GetBatchPresence:output_type -> hopen.presence.v1.GetBatchPresenceResponse
	8,  // 41: hopen.presence.v1.PresenceService.GetOnlineUsers:output_type -> hopen.presence.v1.GetOnlineUsersResponse
	10, // 42: hopen.presence.v1.PresenceService.GetPresenceHistory:output_type -> hopen.presence.v1.GetPresenceHistoryResponse
	12, // 43: hopen.presence.v1.PresenceService.SetUserAway:output_type -> hopen.presence.v1.SetUserAwayResponse
	14, // 44: hopen.presence.v1.PresenceService.SetUserOnline:output_type -> hopen.presence.v1.SetUserOnlineResponse
	16, // 45: hopen.presence.v1.PresenceService.GetPresenceStats:output_type -> hopen.presence.v1.GetPresenceStatsResponse
	18, // 46: hopen.presence.v1.PresenceService.SubscribeToPresenceUpdates:output_type -> hopen.presence.v1.PresenceUpdate
	38, // [38:47] is the sub-list for method output_type
	29, // [29:38] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_presence_proto_init() }
func file_presence_proto_init() {
	if File_presence_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_presence_proto_rawDesc), len(file_presence_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_presence_proto_goTypes,
		DependencyIndexes: file_presence_proto_depIdxs,
		EnumInfos:         file_presence_proto_enumTypes,
		MessageInfos:      file_presence_proto_msgTypes,
	}.Build()
	File_presence_proto = out.File
	file_presence_proto_goTypes = nil
	file_presence_proto_depIdxs = nil
}
