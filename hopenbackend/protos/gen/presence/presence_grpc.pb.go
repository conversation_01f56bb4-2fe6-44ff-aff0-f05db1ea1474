// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: presence.proto

package presence

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PresenceService_GetUserPresence_FullMethodName            = "/hopen.presence.v1.PresenceService/GetUserPresence"
	PresenceService_UpdateUserPresence_FullMethodName         = "/hopen.presence.v1.PresenceService/UpdateUserPresence"
	PresenceService_GetBatchPresence_FullMethodName           = "/hopen.presence.v1.PresenceService/GetBatchPresence"
	PresenceService_GetOnlineUsers_FullMethodName             = "/hopen.presence.v1.PresenceService/GetOnlineUsers"
	PresenceService_GetPresenceHistory_FullMethodName         = "/hopen.presence.v1.PresenceService/GetPresenceHistory"
	PresenceService_SetUserAway_FullMethodName                = "/hopen.presence.v1.PresenceService/SetUserAway"
	PresenceService_SetUserOnline_FullMethodName              = "/hopen.presence.v1.PresenceService/SetUserOnline"
	PresenceService_GetPresenceStats_FullMethodName           = "/hopen.presence.v1.PresenceService/GetPresenceStats"
	PresenceService_SubscribeToPresenceUpdates_FullMethodName = "/hopen.presence.v1.PresenceService/SubscribeToPresenceUpdates"
)

// PresenceServiceClient is the client API for PresenceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Presence service for user presence management
type PresenceServiceClient interface {
	// Get user presence
	GetUserPresence(ctx context.Context, in *GetUserPresenceRequest, opts ...grpc.CallOption) (*GetUserPresenceResponse, error)
	// Update user presence
	UpdateUserPresence(ctx context.Context, in *UpdateUserPresenceRequest, opts ...grpc.CallOption) (*UpdateUserPresenceResponse, error)
	// Get batch presence for multiple users
	GetBatchPresence(ctx context.Context, in *GetBatchPresenceRequest, opts ...grpc.CallOption) (*GetBatchPresenceResponse, error)
	// Get online users
	GetOnlineUsers(ctx context.Context, in *GetOnlineUsersRequest, opts ...grpc.CallOption) (*GetOnlineUsersResponse, error)
	// Get presence history
	GetPresenceHistory(ctx context.Context, in *GetPresenceHistoryRequest, opts ...grpc.CallOption) (*GetPresenceHistoryResponse, error)
	// Set user away
	SetUserAway(ctx context.Context, in *SetUserAwayRequest, opts ...grpc.CallOption) (*SetUserAwayResponse, error)
	// Set user online
	SetUserOnline(ctx context.Context, in *SetUserOnlineRequest, opts ...grpc.CallOption) (*SetUserOnlineResponse, error)
	// Get presence statistics
	GetPresenceStats(ctx context.Context, in *GetPresenceStatsRequest, opts ...grpc.CallOption) (*GetPresenceStatsResponse, error)
	// Subscribe to presence updates
	SubscribeToPresenceUpdates(ctx context.Context, in *SubscribeToPresenceUpdatesRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[PresenceUpdate], error)
}

type presenceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPresenceServiceClient(cc grpc.ClientConnInterface) PresenceServiceClient {
	return &presenceServiceClient{cc}
}

func (c *presenceServiceClient) GetUserPresence(ctx context.Context, in *GetUserPresenceRequest, opts ...grpc.CallOption) (*GetUserPresenceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserPresenceResponse)
	err := c.cc.Invoke(ctx, PresenceService_GetUserPresence_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) UpdateUserPresence(ctx context.Context, in *UpdateUserPresenceRequest, opts ...grpc.CallOption) (*UpdateUserPresenceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateUserPresenceResponse)
	err := c.cc.Invoke(ctx, PresenceService_UpdateUserPresence_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) GetBatchPresence(ctx context.Context, in *GetBatchPresenceRequest, opts ...grpc.CallOption) (*GetBatchPresenceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBatchPresenceResponse)
	err := c.cc.Invoke(ctx, PresenceService_GetBatchPresence_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) GetOnlineUsers(ctx context.Context, in *GetOnlineUsersRequest, opts ...grpc.CallOption) (*GetOnlineUsersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOnlineUsersResponse)
	err := c.cc.Invoke(ctx, PresenceService_GetOnlineUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) GetPresenceHistory(ctx context.Context, in *GetPresenceHistoryRequest, opts ...grpc.CallOption) (*GetPresenceHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPresenceHistoryResponse)
	err := c.cc.Invoke(ctx, PresenceService_GetPresenceHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) SetUserAway(ctx context.Context, in *SetUserAwayRequest, opts ...grpc.CallOption) (*SetUserAwayResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetUserAwayResponse)
	err := c.cc.Invoke(ctx, PresenceService_SetUserAway_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) SetUserOnline(ctx context.Context, in *SetUserOnlineRequest, opts ...grpc.CallOption) (*SetUserOnlineResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetUserOnlineResponse)
	err := c.cc.Invoke(ctx, PresenceService_SetUserOnline_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) GetPresenceStats(ctx context.Context, in *GetPresenceStatsRequest, opts ...grpc.CallOption) (*GetPresenceStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPresenceStatsResponse)
	err := c.cc.Invoke(ctx, PresenceService_GetPresenceStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceServiceClient) SubscribeToPresenceUpdates(ctx context.Context, in *SubscribeToPresenceUpdatesRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[PresenceUpdate], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &PresenceService_ServiceDesc.Streams[0], PresenceService_SubscribeToPresenceUpdates_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[SubscribeToPresenceUpdatesRequest, PresenceUpdate]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type PresenceService_SubscribeToPresenceUpdatesClient = grpc.ServerStreamingClient[PresenceUpdate]

// PresenceServiceServer is the server API for PresenceService service.
// All implementations must embed UnimplementedPresenceServiceServer
// for forward compatibility.
//
// Presence service for user presence management
type PresenceServiceServer interface {
	// Get user presence
	GetUserPresence(context.Context, *GetUserPresenceRequest) (*GetUserPresenceResponse, error)
	// Update user presence
	UpdateUserPresence(context.Context, *UpdateUserPresenceRequest) (*UpdateUserPresenceResponse, error)
	// Get batch presence for multiple users
	GetBatchPresence(context.Context, *GetBatchPresenceRequest) (*GetBatchPresenceResponse, error)
	// Get online users
	GetOnlineUsers(context.Context, *GetOnlineUsersRequest) (*GetOnlineUsersResponse, error)
	// Get presence history
	GetPresenceHistory(context.Context, *GetPresenceHistoryRequest) (*GetPresenceHistoryResponse, error)
	// Set user away
	SetUserAway(context.Context, *SetUserAwayRequest) (*SetUserAwayResponse, error)
	// Set user online
	SetUserOnline(context.Context, *SetUserOnlineRequest) (*SetUserOnlineResponse, error)
	// Get presence statistics
	GetPresenceStats(context.Context, *GetPresenceStatsRequest) (*GetPresenceStatsResponse, error)
	// Subscribe to presence updates
	SubscribeToPresenceUpdates(*SubscribeToPresenceUpdatesRequest, grpc.ServerStreamingServer[PresenceUpdate]) error
	mustEmbedUnimplementedPresenceServiceServer()
}

// UnimplementedPresenceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPresenceServiceServer struct{}

func (UnimplementedPresenceServiceServer) GetUserPresence(context.Context, *GetUserPresenceRequest) (*GetUserPresenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPresence not implemented")
}
func (UnimplementedPresenceServiceServer) UpdateUserPresence(context.Context, *UpdateUserPresenceRequest) (*UpdateUserPresenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserPresence not implemented")
}
func (UnimplementedPresenceServiceServer) GetBatchPresence(context.Context, *GetBatchPresenceRequest) (*GetBatchPresenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchPresence not implemented")
}
func (UnimplementedPresenceServiceServer) GetOnlineUsers(context.Context, *GetOnlineUsersRequest) (*GetOnlineUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineUsers not implemented")
}
func (UnimplementedPresenceServiceServer) GetPresenceHistory(context.Context, *GetPresenceHistoryRequest) (*GetPresenceHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPresenceHistory not implemented")
}
func (UnimplementedPresenceServiceServer) SetUserAway(context.Context, *SetUserAwayRequest) (*SetUserAwayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserAway not implemented")
}
func (UnimplementedPresenceServiceServer) SetUserOnline(context.Context, *SetUserOnlineRequest) (*SetUserOnlineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserOnline not implemented")
}
func (UnimplementedPresenceServiceServer) GetPresenceStats(context.Context, *GetPresenceStatsRequest) (*GetPresenceStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPresenceStats not implemented")
}
func (UnimplementedPresenceServiceServer) SubscribeToPresenceUpdates(*SubscribeToPresenceUpdatesRequest, grpc.ServerStreamingServer[PresenceUpdate]) error {
	return status.Errorf(codes.Unimplemented, "method SubscribeToPresenceUpdates not implemented")
}
func (UnimplementedPresenceServiceServer) mustEmbedUnimplementedPresenceServiceServer() {}
func (UnimplementedPresenceServiceServer) testEmbeddedByValue()                         {}

// UnsafePresenceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PresenceServiceServer will
// result in compilation errors.
type UnsafePresenceServiceServer interface {
	mustEmbedUnimplementedPresenceServiceServer()
}

func RegisterPresenceServiceServer(s grpc.ServiceRegistrar, srv PresenceServiceServer) {
	// If the following call pancis, it indicates UnimplementedPresenceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PresenceService_ServiceDesc, srv)
}

func _PresenceService_GetUserPresence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).GetUserPresence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_GetUserPresence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).GetUserPresence(ctx, req.(*GetUserPresenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_UpdateUserPresence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPresenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).UpdateUserPresence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_UpdateUserPresence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).UpdateUserPresence(ctx, req.(*UpdateUserPresenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_GetBatchPresence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBatchPresenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).GetBatchPresence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_GetBatchPresence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).GetBatchPresence(ctx, req.(*GetBatchPresenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_GetOnlineUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).GetOnlineUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_GetOnlineUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).GetOnlineUsers(ctx, req.(*GetOnlineUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_GetPresenceHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresenceHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).GetPresenceHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_GetPresenceHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).GetPresenceHistory(ctx, req.(*GetPresenceHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_SetUserAway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserAwayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).SetUserAway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_SetUserAway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).SetUserAway(ctx, req.(*SetUserAwayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_SetUserOnline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserOnlineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).SetUserOnline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_SetUserOnline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).SetUserOnline(ctx, req.(*SetUserOnlineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_GetPresenceStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresenceStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServiceServer).GetPresenceStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PresenceService_GetPresenceStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServiceServer).GetPresenceStats(ctx, req.(*GetPresenceStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresenceService_SubscribeToPresenceUpdates_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SubscribeToPresenceUpdatesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PresenceServiceServer).SubscribeToPresenceUpdates(m, &grpc.GenericServerStream[SubscribeToPresenceUpdatesRequest, PresenceUpdate]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type PresenceService_SubscribeToPresenceUpdatesServer = grpc.ServerStreamingServer[PresenceUpdate]

// PresenceService_ServiceDesc is the grpc.ServiceDesc for PresenceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PresenceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hopen.presence.v1.PresenceService",
	HandlerType: (*PresenceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserPresence",
			Handler:    _PresenceService_GetUserPresence_Handler,
		},
		{
			MethodName: "UpdateUserPresence",
			Handler:    _PresenceService_UpdateUserPresence_Handler,
		},
		{
			MethodName: "GetBatchPresence",
			Handler:    _PresenceService_GetBatchPresence_Handler,
		},
		{
			MethodName: "GetOnlineUsers",
			Handler:    _PresenceService_GetOnlineUsers_Handler,
		},
		{
			MethodName: "GetPresenceHistory",
			Handler:    _PresenceService_GetPresenceHistory_Handler,
		},
		{
			MethodName: "SetUserAway",
			Handler:    _PresenceService_SetUserAway_Handler,
		},
		{
			MethodName: "SetUserOnline",
			Handler:    _PresenceService_SetUserOnline_Handler,
		},
		{
			MethodName: "GetPresenceStats",
			Handler:    _PresenceService_GetPresenceStats_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SubscribeToPresenceUpdates",
			Handler:       _PresenceService_SubscribeToPresenceUpdates_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "presence.proto",
}
