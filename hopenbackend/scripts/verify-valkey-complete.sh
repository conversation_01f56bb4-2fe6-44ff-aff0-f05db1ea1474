#!/bin/bash

echo "🔍 Comprehensive Valkey migration verification..."

# Check for Redis imports (excluding comments and strings)
echo "Checking for Redis imports..."
REDIS_IMPORTS=$(grep -r "github.com/go-redis/redis" --include="*.go" . | grep -v "//")
if [ -n "$REDIS_IMPORTS" ]; then
    echo "❌ Found Redis imports:"
    echo "$REDIS_IMPORTS"
    exit 1
fi

# Check for Redis client usage
echo "Checking for Redis client usage..."
REDIS_CLIENT=$(grep -r "\*redis\.Client" --include="*.go" . | grep -v "//")
if [ -n "$REDIS_CLIENT" ]; then
    echo "❌ Found Redis client usage:"
    echo "$REDIS_CLIENT"
    exit 1
fi

# Check for redis.Nil usage
echo "Checking for redis.Nil usage..."
REDIS_NIL=$(grep -r "redis\.Nil" --include="*.go" . | grep -v "//")
if [ -n "$REDIS_NIL" ]; then
    echo "❌ Found redis.Nil usage:"
    echo "$REDIS_NIL"
    exit 1
fi

# Check for Valkey imports
echo "Checking for Valkey imports..."
VALKEY_IMPORTS=$(grep -r "github.com/valkey-io/valkey-go" --include="*.go" .)
if [ -z "$VALKEY_IMPORTS" ]; then
    echo "⚠️  No Valkey imports found - make sure you've added them"
fi

echo "✅ Valkey migration verification completed successfully!"
