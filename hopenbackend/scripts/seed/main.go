package main

import (
    "context"
    "fmt"
    "log"

    "github.com/google/uuid"
    "github.com/jackc/pgx/v5/pgxpool"

    "hopenbackend/pkg/config"
)

// seedUser inserts a demo user that can be used when running the local stack.
func seedUser(ctx context.Context, pool *pgxpool.Pool) error {
    demoEmail := "<EMAIL>"

    // Check existence
    var count int
    if err := pool.QueryRow(ctx, "SELECT COUNT(*) FROM users WHERE email=$1", demoEmail).Scan(&count); err != nil {
        return fmt.Errorf("failed to query users: %w", err)
    }
    if count > 0 {
        log.Printf("[seed] demo user already exists: %s", demoEmail)
        return nil
    }

    username := "demo"
    _, err := pool.Exec(ctx, `INSERT INTO users (id, email, username, is_active, is_private) VALUES ($1, $2, $3, true, false)`, uuid.NewString(), demoEmail, username)
    if err != nil {
        return fmt.Errorf("failed to insert demo user: %w", err)
    }

    log.Printf("[seed] inserted demo user: %s", demoEmail)
    return nil
}

func main() {
    // Load configuration
    cfg, err := config.Load()
    if err != nil {
        log.Fatalf("failed to load config: %v", err)
    }

    // Connect to PostgreSQL
    pgPool, err := pgxpool.New(context.Background(), cfg.Databases.PostgreSQL.GetDSN())
    if err != nil {
        log.Fatalf("failed to create pool: %v", err)
    }

    ctx := context.Background()

    // Create users table if not exists (minimal)
    if _, err := pgPool.Exec(ctx, `CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE,
        is_private BOOLEAN DEFAULT FALSE
    )`); err != nil {
        log.Fatalf("failed to ensure users table: %v", err)
    }

    // Seed demo data
    if err := seedUser(ctx, pgPool); err != nil {
        log.Fatalf("failed to seed demo data: %v", err)
    }

    log.Println("[seed] database seeding complete")
} 