-- Initialize databases for Hopen backend
-- This script creates the necessary databases and users

-- Create the main hopen database
CREATE DATABASE hopen;

-- Create the hopen_hydra database for Ory Hydra
CREATE DATABASE hopen_hydra;

-- Create hopen user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'hopen') THEN
        CREATE USER hopen WITH PASSWORD 'hopen123';
    END IF;
END
$$;

-- Grant privileges to hopen user
GRANT ALL PRIVILEGES ON DATABASE hopen TO hopen;
GRANT ALL PRIVILEGES ON DATABASE hopen_hydra TO hopen;

-- Connect to hopen database and create extensions
\c hopen;

-- Create necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO hopen;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hopen;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hopen;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO hopen;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO hopen;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO hopen;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO hopen;

-- Connect to hopen_hydra database
\c hopen_hydra;

-- Create necessary extensions for Hydra
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO hopen;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hopen;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hopen;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO hopen;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO hopen;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO hopen;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO hopen; 